# Refund flow at a glance

`CountryToReturnFeePricing` table contains the information needed to calculate the return fee amount per country. The data is being stored historically there OMS may calculates the return fee amount on different date.

When a customer returns an order line, the warehouse informs Bestseller with information regarding the order line and order. **OSIMC** produces an `OrderLineReturned` message and FCS consumes it. 

*FCS stores the above information on `OrderStatusUpdateInfo` table and update the order line quantity status to `RETURNED`. Then PMS consumes`RefundCreationRequested` message. Here, PMS calculates the propable return fee amount based on the **OrderCreationDate**. That is the reason why `CountryToReturnFeePricing` has an `effectiveDate` column.

## Brand specific return fees
There is a table named `BrandSpecificReturnFee` that holds the return fees that are specific to a brand in a market/country. The data in this table is saved historically so all the queries on this table should have `effectiveDate` column in their where clauses.
PMS first tries to read the return fee from `CountryToReturnFeePricing` table based on the `countryCode` and `orderDate` then It looks for an override on `BrandSpecificReturnFee` based on the `brandId` and `orderDate`.

PMS charges the customer a return fee only **once per order**.

## How to modify the return fee values for countries.
We should **NEVER** update any records in `CountryToReturnFeePricing` table. We need to `INSERT` some new records with a new `effectiveDate`.

See `V20240925160854__Include_return_fee_for_RS_BA_AD_MT_AL.sql` migration script for the reference.

## How to modify the return vat rate for countries.
If you want to update the return tax rate only for the orders placed after an effective date, then add a new record to `CountryToReturnFeePricing` and just deploy it.
But if you want to include refunds after an effective date for existing orders, then UPDATE the current record and deploy it at the beginning of the effective date.

See `V20241219131500__update_return_fee_tax_rate_in_SK.sql` migration script for the reference.

## How to see the current return fee based on the order date?
Set the @OrderDate variable and run the following query.

```sql
SET @OrderDate = '2024-12-27';

SELECT
    ctrfp.country,
    GREATEST(COALESCE(ctrfp.effectiveDate, DATE('1000-01-01')), COALESCE(bsrf.effectiveDate, DATE('1000-01-01'))) as 'effectiveDate',
        CASE
            WHEN bsrf.brand = NULL THEN NULL
            ELSE bsrf.brand
            END as 'brand',
        ctrfp.returnFeeAmount,
    CASE
        WHEN bsrf.returnFee = NULL THEN ctrfp.returnFeeAmount
        ELSE bsrf.returnFee
        END as 'refturnFeeOverride'
FROM CountryToReturnFeePricing ctrfp
         INNER JOIN (
    SELECT
        pricing.country,
        MAX(pricing.effectiveDate) as date
    FROM
        CountryToReturnFeePricing pricing
    WHERE
        pricing.effectiveDate < @OrderDate

    GROUP BY pricing.country
) maxTemp ON maxTemp.country = ctrfp.country AND ctrfp.effectiveDate = maxTemp.date

         LEFT JOIN (
    SELECT BrandSpecificReturnFee.* FROM BrandSpecificReturnFee
                                             INNER JOIN (
        SELECT brand, MAX(effectiveDate) as effectiveDate FROM BrandSpecificReturnFee
        WHERE effectiveDate < @OrderDate
        GROUP BY brand
    ) tmp ON tmp.brand = BrandSpecificReturnFee.brand AND tmp.effectiveDate = BrandSpecificReturnFee.effectiveDate
) bsrf ON bsrf.countryToReturnFeePricingId = ctrfp.id

ORDER BY ctrfp.country;
```
