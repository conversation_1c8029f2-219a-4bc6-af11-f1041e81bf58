version: 2
registries:
  terraform-bse:
    type: terraform-registry
    url: https://tfe.mng.bseint.io
    token: ${{secrets.SYS_TFE_TOKEN}}
updates:
  - package-ecosystem: "github-actions"
    # Workflow files stored in the
    # default location of `.github/workflows`
    directory: "/"
    schedule:
      interval: "daily"

  - package-ecosystem: "maven" # See documentation for possible values
    directory: "/" # Location of package manifests
    schedule:
      interval: "weekly"

  - package-ecosystem: "terraform"
    directory: "/infra/terraform/acc"
    registries: "*"
    schedule:
      interval: "weekly"
    labels:
      - "dependencies"
      - "terraform"
      - "acc"

  - package-ecosystem: "terraform"
    directory: "/infra/terraform/prod"
    registries: "*"
    schedule:
      interval: "weekly"
    labels:
      - "dependencies"
      - "terraform"
      - "prod"