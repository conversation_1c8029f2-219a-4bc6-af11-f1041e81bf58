name: Build
on:
  workflow_dispatch:
  push:
    branches:
      - master
    paths-ignore:
      - 'infra/**'
      - 'README.md'
      - '**/README.md'
  pull_request:
    branches:
      - master
    paths-ignore:
      - 'infra/**'
      - 'README.md'
      - '**/README.md'

jobs:
  Build:
    secrets: inherit
    uses: bestseller-ecom/bestseller-pipelines/.github/workflows/build-ecs-java.yml@v2
    with:
      project-name: pms
      environment: acc
      java-version: 21
      build-tool: maven
      java-distribution: corretto