name: Deploy
on:
  workflow_dispatch:
    inputs:
      environment:
        type: choice
        description: Select Environment to deploy
        options:
          - acc
          - prod
        required: true

jobs:
  DeployAcc:
    secrets: inherit
    if: github.event.inputs.environment == 'acc'
    uses: bestseller-ecom/bestseller-pipelines/.github/workflows/deploy-ecs.yml@v2
    with:
      project-name: pms
      owner: fulfilment
      cluster-name: logistics-acc
      environment: acc

  PushProdImage:
    secrets: inherit
    if: github.event.inputs.environment == 'prod'
    uses: bestseller-ecom/bestseller-pipelines/.github/workflows/push-ecs.yml@v2
    with:
      project-name: pms

  DeployProd:
    secrets: inherit
    if: github.event.inputs.environment == 'prod'
    needs: [ PushProdImage ]
    uses: bestseller-ecom/bestseller-pipelines/.github/workflows/deploy-ecs.yml@v2
    with:
      project-name: pms
      owner: fulfilment
      cluster-name: logistics-prod
      environment: prod
