{"shippingInformation": {"shippingAddress": {"addressLine1": "Test Street", "city": "Test City", "country": "DK", "firstName": "Test", "houseNumber": "123", "lastName": "User", "phoneNumber": "+4512345678", "zipcode": "1234"}, "shippingCharges": []}, "customerInformation": {"billingAddress": {"addressLine1": "Test Street", "city": "Test City", "country": "DK", "firstName": "Test", "houseNumber": "123", "lastName": "User", "phoneNumber": "+4512345678", "zipcode": "1234"}, "email": "<EMAIL>", "customerId": "123456", "isEmployee": false, "state": "LOGGED_IN", "customerLocale": "DK-da"}, "orderLines": [{"id": "597b1ea4-1eb0-4269-8974-4a38f786881d", "ean": "ean1", "lineNumber": 1, "productName": "Test Product", "quantity": 5, "retailPrice": 19.99, "discountedUnitPrice": 19.99, "vat": 0.25, "vatClass": "adult-clothing", "taxUnitPrice": 4.0, "virtualProduct": false, "discountedTotalPrice": 99.95, "brand": "bc"}], "orderDetails": {"shippingMethod": "STANDARD", "orderCreationDate": "2024-02-05T19:51:27.000Z", "carrier": "DHL", "carrierVariant": "HOME", "parcelShopType": "NONE", "orderType": "STANDARD", "shippingFeesEan": "STANDARD_SHIPPING", "shippingFeesName": "Shipping", "checkout": "bc", "brandedShipping": "BC", "market": "BSE-DACH", "channel": "storefront", "orderValue": "99.95"}, "payments": [{"method": "ADYEN_IDEAL", "subMethod": "ideal", "pspReference": "fa6c98f0-ed51-42ea-800e-23191f804ce3", "provider": "ADYEN", "amount": 99.95, "currency": "DKK", "state": "AUTH"}], "orderId": "ORDER-123", "ipAddress": "***************", "store": "DEMANDWARE", "isTest": false, "placedDate": "2024-02-05T19:51:27.000Z", "orderPromotions": []}