spring:
  cloud:
    stream:
      kafka:
        default:
          consumer:
            start-offset: earliest

      bindings:
        default:
          content-type: application/json
        #Consumer Kafka Topics
        paymentStatusUpdatedTestConsumer-in-0:
          destination: PaymentStatusUpdated
        paymentSettlementRequestTestConsumer-in-0:
          destination: PaymentSettlementRequest
        paymentRefundRequestTestConsumer-in-0:
          destination: PaymentRefundRequest
        refundStatusUpdatedTestConsumer-in-0:
          destination: RefundStatusUpdated
        inStoreReturnSettlementConsumer-in-0:
          destination: InStoreReturnSettlement
        paymentRefundRequestedConsumer-in-0:
          destination: PaymentRefundRequested
        paymentRefundFailedConsumer-in-0:
          destination: PaymentRefundFailed
        paymentRefundSucceededConsumer-in-0:
          destination: PaymentRefundSucceeded
        giftCardRefundResponseConsumer-in-0:
          destination: GiftCardRefundResponse

    function:
      definition: >
        paymentSettlementRequestTestConsumer;
        paymentRefundRequestTestConsumer;
        validOrderPlacedConsumer;
        orderFinalizedConsumer;
        paymentAuthorizedConsumer;
        paymentRejectedConsumer;
        paymentStatusUpdatedTestConsumer;
        paymentSettlementSucceededConsumer;
        paymentSettlementFailedConsumer;
        refundCreationRequestedConsumer;
        refundStatusUpdatedTestConsumer;
        inStoreReturnSettlementConsumer;
        paymentRefundRequestedConsumer;
        paymentRefundFailedConsumer;
        paymentRefundSucceededConsumer;
        giftCardRefundResponseConsumer;
        postPurchaseEventReceivedConsumer;

credentials:
  admin:
    username: admin
    password: "{noop}admin"

database-queue:
  refund-creation-requested-queue-properties:
    processing-settings:
      thread-count: 0
  in-store-return-settlement-queue-properties:
    processing-settings:
      thread-count: 0
  order-finalized-queue-properties:
    processing-settings:
      thread-count: 0
  payment-authorized-queue-properties:
    processing-settings:
      thread-count: 0
  payment-refund-failed-queue-properties:
    processing-settings:
      thread-count: 0
  payment-refund-requested-queue-properties:
    processing-settings:
      thread-count: 0
  payment-refund-succeeded-queue-properties:
    processing-settings:
      thread-count: 0
  payment-rejected-queue-properties:
    processing-settings:
      thread-count: 0
  payment-settlement-failed-queue-properties:
    processing-settings:
      thread-count: 0
  payment-settlement-succeeded-queue-properties:
    processing-settings:
      thread-count: 0
  valid-order-placed-queue-properties:
    processing-settings:
      thread-count: 0
  gift-card-refund-response-queue-properties:
    processing-settings:
      thread-count: 0

fcs:
  max-retry-attempts: 2
  retry-backoff: 1000