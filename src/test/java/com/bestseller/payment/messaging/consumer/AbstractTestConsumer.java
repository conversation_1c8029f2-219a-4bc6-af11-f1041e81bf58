package com.bestseller.payment.messaging.consumer;

import jakarta.validation.Valid;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Consumer;

@RequiredArgsConstructor
@Slf4j
@Getter
public abstract class AbstractTestConsumer<T> implements Consumer<@Valid T> {
    private final CopyOnWriteArrayList<T> messages = new CopyOnWriteArrayList<T>();

    @Override
    public void accept(@Valid T message) {
        log.info("Consuming {}: payload={}", message.getClass().getSimpleName(), message);
        messages.add(message);
        log.info("{} consumed successfully: payload={}", message.getClass().getSimpleName(), message);
    }

    public void clearMessages() {
        messages.clear();
    }

}
