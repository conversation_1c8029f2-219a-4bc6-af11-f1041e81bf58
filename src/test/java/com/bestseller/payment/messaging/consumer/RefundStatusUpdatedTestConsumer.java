package com.bestseller.payment.messaging.consumer;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundStatusUpdated.RefundStatusUpdated;
import jakarta.validation.Valid;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;


@Component
@RequiredArgsConstructor
@Getter
public class RefundStatusUpdatedTestConsumer extends AbstractTestConsumer<@Valid RefundStatusUpdated> {

}
