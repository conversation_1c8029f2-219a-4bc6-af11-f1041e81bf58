package com.bestseller.payment.messaging.consumer;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundSucceeded.PaymentRefundSucceeded;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;


@Component
@RequiredArgsConstructor
@Getter
public class PaymentRefundSucceededTestConsumer extends AbstractTestConsumer<PaymentRefundSucceeded> {

}
