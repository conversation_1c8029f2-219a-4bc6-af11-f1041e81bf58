package com.bestseller.payment.core.utils;

import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.utils.OrderGenerator;
import org.apache.commons.text.StringSubstitutor;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

class CsvGeneratorUtilTest {

    private static final PaymentState PAYMENT_STATE_IN_REVIEW = PaymentState.REVIEW;

    @Test
    void generateFileTest() throws IOException {
        // Create mock data
        Set<Order> mockOrders = createMockOrdersWithPaymentInReviewState();

        // Generate CSV file
        String fileName = "test_orders";
        File csvFile = CsvGeneratorUtil.generateFile(mockOrders, fileName);

        // Read contents from the generated CSV file and split into lines
        List<String> lines = Files.readAllLines(csvFile.toPath(), StandardCharsets.UTF_8);

        // Assert file is not empty and has at least two lines (header and one data line)
        assertFalse(lines.isEmpty());
        assertTrue(lines.size() >= 2);

        // Check if the header exists
        String expectedHeader = "\"Order ID,Order Date,Payment Status,Previous Payment Status,Payment ID," +
            "Authorised Amount,Processor ID,Payment Reference,Payment Type,Sub Method,Sub Method Name\"";
        String headers = lines.get(0);
        assertEquals(expectedHeader, headers);


        Map<String, String> parameters = new HashMap<>();
        parameters.put("orderId", OrderGenerator.ORDER_ID);
        parameters.put("orderDate", new SimpleDateFormat("yyyy-MM-dd").format(OrderGenerator.ORDER_DATE));
        parameters.put("paymentState", PAYMENT_STATE_IN_REVIEW.name());
        parameters.put("previousPaymentState", OrderGenerator.PREVIOUS_PAYMENT_STATE_START.name());
        parameters.put("paymentId", OrderGenerator.PAYMENT_ID.toString());
        parameters.put("authorisedAmount", OrderGenerator.AUTHORISED_AMOUNT);
        parameters.put("processorId", OrderGenerator.PROCESSOR_ID_ADYEN.name());
        parameters.put("pspReference", OrderGenerator.PSP_REFERENCE);
        parameters.put("paymentType", OrderGenerator.PAYMENT_TYPE_ADYEN_CARD.name());
        parameters.put("paymentSubMethod", OrderGenerator.SUB_METHOD);
        parameters.put("paymentSubMethodName", OrderGenerator.PAYMENT_METHOD.name());
        StringSubstitutor stringSubstitutor = new StringSubstitutor(parameters);

        // Check second line
        String expectedData = stringSubstitutor.replace("${orderId},${orderDate},${paymentState},${previousPaymentState},${paymentId},${authorisedAmount},${processorId},${pspReference},${paymentType},${paymentSubMethod},${paymentSubMethodName}");
        String data = lines.get(1);
        assertEquals(expectedData, data);
    }

    private Set<Order> createMockOrdersWithPaymentInReviewState() {
        Set<Order> orders = new HashSet<>();
        var order = OrderGenerator.createAdyenOrder();
        order.setPaymentStatus(PAYMENT_STATE_IN_REVIEW);
        orders.add(order);
        return orders;
    }
}
