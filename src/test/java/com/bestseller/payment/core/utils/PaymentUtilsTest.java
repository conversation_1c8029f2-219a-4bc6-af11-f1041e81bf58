package com.bestseller.payment.core.utils;

import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.math.BigDecimal;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;


class PaymentUtilsTest {

    @ParameterizedTest
    @MethodSource("toIntCentsData")
    void toIntCents(BigDecimal input, int output) {
        assertThat(PaymentUtils.toIntCents(input)).isEqualTo(output);
    }

    public static Stream<Arguments> toIntCentsData() {
        return Stream.of(
            Arguments.of(new BigDecimal("1"), 100),
            Arguments.of(new BigDecimal("0"), 0),
            Arguments.of(new BigDecimal("5.99"), 599),
            Arguments.of(new BigDecimal("5.98"), 598),
            Arguments.of(new BigDecimal("5.985"), 599),
            Arguments.of(new BigDecimal("5.9899999999999"), 599),
            Arguments.of(new BigDecimal("589.9899999999999"), 58999),
            Arguments.of(new BigDecimal("0.01"), 1),
            Arguments.of(new BigDecimal("0.1"), 10)
        );
    }



}