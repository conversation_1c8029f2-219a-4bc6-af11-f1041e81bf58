package com.bestseller.payment.core.service.payment;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.OrderPlaced;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementRequest.PaymentSettlementRequest;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.OrderDetails;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.Payment;
import com.bestseller.payment.adapter.stream.messagegenerator.PaymentStatusUpdatedGenerator;
import com.bestseller.payment.config.queue.paymentsettlementrequestproducer.AbstractPaymentSettlementRequestProducerQueueProducer;
import com.bestseller.payment.config.queue.paymentsettlementrequestproducer.AdyenPaymentSettlementRequestProducerQueueProducer;
import com.bestseller.payment.config.queue.paymentsettlementrequestproducer.KlarnaPaymentSettlementRequestProducerQueueProducer;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import com.bestseller.payment.core.domain.payment.AdyenBankPayment;
import com.bestseller.payment.core.domain.payment.AdyenCardPayment;
import com.bestseller.payment.core.domain.payment.GiftcardPayment;
import com.bestseller.payment.core.domain.payment.KlarnaPayment;
import com.bestseller.payment.core.domain.payment.NonePayment;
import com.bestseller.payment.core.exception.OrderNotFoundException;
import com.bestseller.payment.core.exception.PaymentValidationException;
import com.bestseller.payment.core.exception.StateTransitionException;
import com.bestseller.payment.core.service.order.OrderService;
import com.bestseller.payment.core.service.payment.factory.PaymentFactory;
import com.bestseller.payment.core.service.payment.factory.StorePaymentProcessorFactory;
import com.bestseller.payment.core.service.payment.storePaymentProcessor.OfflinePaymentProcessor;
import com.bestseller.payment.core.service.payment.storePaymentProcessor.OnlinePaymentProcessor;
import com.bestseller.payment.core.service.refund.EcomRefundService;
import com.bestseller.payment.core.validation.OrderPlacedPaymentValidator;
import com.bestseller.payment.utils.OrderGenerator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PaymentServiceTest {

    @InjectMocks
    private PaymentServiceImpl paymentService;

    @Mock
    private OrderPlacedPaymentValidator orderPlacedPaymentValidator;

    @Mock
    private QueueProducer<PaymentStatusUpdated> paymentStatusUpdatedProducerQueueProducer;

    @Mock
    private OrderService orderService;

    @Mock
    private PaymentFactory paymentFactory;

    @Mock
    StorePaymentProcessorFactory storePaymentProcessorFactory;

    @Mock
    AdyenPaymentSettlementRequestProducerQueueProducer adyenPaymentSettlementRequestProducer;

    @Mock
    KlarnaPaymentSettlementRequestProducerQueueProducer klarnaPaymentSettlementRequestProducer;

    @Mock
    EcomRefundService ecomRefundService;

    @Mock
    PaymentStatusUpdatedGenerator paymentStatusUpdatedGenerator;

    /**
     * Mockito does not differentiate between generic types due to type erasure in Java.
     * This means both QueueProducer&lt;PaymentStatusUpdated&gt; and list of QueueProducers are seen
     * as the same QueueProducer class at runtime.
     * To fix it two distinct mock objects are defined and injected explicitly into test class.
     */
    @BeforeEach
    void setUp() {
        List<AbstractPaymentSettlementRequestProducerQueueProducer<? extends PaymentInfo>> producers =
            List.of(adyenPaymentSettlementRequestProducer, klarnaPaymentSettlementRequestProducer);
        ReflectionTestUtils.setField(
            paymentService,
            "paymentSettlementRequestProducers",
            producers
        );
        ReflectionTestUtils.setField(
            paymentService,
            "paymentStatusUpdatedProducerQueueProducer",
            paymentStatusUpdatedProducerQueueProducer
        );
    }

    @Test
    public void testSetPaymentMethodForTradebyteStore() {
        OrderDetails orderDetails = new OrderDetails()
            .withOrderValue(new BigDecimal("10.0"));
        Order order = new Order();
        List<Payment> payments = Collections.emptyList();
        String country = "NL";

        NonePayment nonePayment = NonePayment.builder()
            .authorisedAmount("11.0")
            .processorId(ProcessorId.OFFLINE)
            .type(PaymentType.NONE)
            .build();
        order.setPayments(List.of(nonePayment));

        // Arrange
        OfflinePaymentProcessor offlinePaymentProcessor = mock(OfflinePaymentProcessor.class);
        when(storePaymentProcessorFactory.createPaymentProcessor(payments, country, order, orderDetails, paymentFactory))
            .thenReturn(offlinePaymentProcessor);
        when(offlinePaymentProcessor.evaluatePaymentMethod()).thenReturn(PaymentState.OFFLINE);

        // act
        paymentService.processPaymentMethodAndState(payments, country, orderDetails, order);

        //assert
        verify(offlinePaymentProcessor).evaluatePaymentMethod();
        assertEquals(PaymentState.OFFLINE, order.getPaymentStatus());
    }

    @Test
    public void testSetPaymentMethodForNonTradebyteStore_validTranstion_start_authorised() {
        List<Payment> payments = mock(List.class);
        String country = "NL";
        OrderDetails orderDetails = mock(OrderDetails.class);
        Order order = new Order();
        order.setOrderId("1234");
        order.setPaymentStatus(null);
        order.setPayments(getPaymentInfo());

        // Arrange
        OnlinePaymentProcessor onlinePaymentProcessor = mock(OnlinePaymentProcessor.class);
        when(storePaymentProcessorFactory.createPaymentProcessor(payments, country, order, orderDetails, paymentFactory))
            .thenReturn(onlinePaymentProcessor);
        when(onlinePaymentProcessor.evaluatePaymentMethod()).thenReturn(PaymentState.AUTHORISED);

        // test
        paymentService.processPaymentMethodAndState(payments, country, orderDetails, order);

        //assert
        verify(onlinePaymentProcessor).evaluatePaymentMethod();
        verify(storePaymentProcessorFactory).createPaymentProcessor(eq(payments), eq(country), eq(order), eq(orderDetails), any(PaymentFactory.class));
        assertEquals(PaymentState.START, order.getPrevPaymentStatus());
        assertEquals(PaymentState.AUTHORISED, order.getPaymentStatus());
    }

    @Test
    public void testSetPaymentMethodForNonTradebyteStore_validtransition_review_authorised() {
        List<Payment> payments = mock(List.class);
        String country = "NL";
        OrderDetails orderDetails = mock(OrderDetails.class);
        Order order = new Order();
        order.setOrderId("1234");
        order.setPrevPaymentStatus(PaymentState.START);
        order.setPaymentStatus(PaymentState.REVIEW);
        order.setPayments(getPaymentInfo());

        // Arrange
        OnlinePaymentProcessor onlinePaymentProcessor = mock(OnlinePaymentProcessor.class);
        when(storePaymentProcessorFactory.createPaymentProcessor(payments, country, order, orderDetails, paymentFactory))
            .thenReturn(onlinePaymentProcessor);
        when(onlinePaymentProcessor.evaluatePaymentMethod()).thenReturn(PaymentState.AUTHORISED);

        // test
        paymentService.processPaymentMethodAndState(payments, country, orderDetails, order);

        //assert
        verify(onlinePaymentProcessor).evaluatePaymentMethod();
        verify(storePaymentProcessorFactory).createPaymentProcessor(eq(payments), eq(country), eq(order), eq(orderDetails), any(PaymentFactory.class));
        assertEquals(PaymentState.REVIEW, order.getPrevPaymentStatus());
        assertEquals(PaymentState.AUTHORISED, order.getPaymentStatus());
    }

    @Test
    public void testSetPaymentMethodForNonTradebyteStore_validtransition_multipleGiftcards() {
        List<Payment> payments = mock(List.class);
        String country = "NL";
        OrderDetails orderDetails = mock(OrderDetails.class);
        Order order = new Order();
        order.setOrderId("1234");
        order.setPaymentStatus(null);
        order.setPayments(getPaymentInfoWithGiftCards());

        // Arrange
        OnlinePaymentProcessor onlinePaymentProcessor = mock(OnlinePaymentProcessor.class);
        when(storePaymentProcessorFactory.createPaymentProcessor(payments, country, order, orderDetails, paymentFactory))
            .thenReturn(onlinePaymentProcessor);
        when(onlinePaymentProcessor.evaluatePaymentMethod()).thenReturn(PaymentState.AUTHORISED);

        // test
        paymentService.processPaymentMethodAndState(payments, country, orderDetails, order);

        //assert
        verify(onlinePaymentProcessor).evaluatePaymentMethod();
        verify(storePaymentProcessorFactory).createPaymentProcessor(eq(payments), eq(country), eq(order), eq(orderDetails), any(PaymentFactory.class));
        assertEquals(PaymentState.START, order.getPrevPaymentStatus());
        assertEquals(PaymentState.AUTHORISED, order.getPaymentStatus());
    }

    @Test
    public void testSetPaymentMethodForNonTradebyteStore_invalidtransition_multipleGiftcards() {
        List<Payment> payments = mock(List.class);
        String country = "NL";
        OrderDetails orderDetails = mock(OrderDetails.class);
        Order order = new Order();
        order.setOrderId("1234");
        order.setPaymentStatus(PaymentState.REVIEW);
        order.setPayments(getPaymentInfoWithGiftCards());

        // Arrange
        OnlinePaymentProcessor onlinePaymentProcessor = mock(OnlinePaymentProcessor.class);
        when(storePaymentProcessorFactory.createPaymentProcessor(payments, country, order, orderDetails, paymentFactory))
            .thenReturn(onlinePaymentProcessor);
        when(onlinePaymentProcessor.evaluatePaymentMethod()).thenReturn(PaymentState.AUTHORISED);

        //test and assert
        assertThrows(StateTransitionException.class, () -> paymentService.processPaymentMethodAndState(payments, country, orderDetails, order));
    }

    @Test
    public void testSetPaymentMethodForNonTradebyteStore_validtransition_giftCardAndNoneGiftCard() {
        List<Payment> payments = mock(List.class);
        String country = "NL";
        OrderDetails orderDetails = mock(OrderDetails.class);
        Order order = new Order();
        order.setOrderId("1234");
        order.setPaymentStatus(null);
        order.setPayments(getPaymentInfoWithGiftCardAndNoneGiftCard());

        // Arrange
        OnlinePaymentProcessor onlinePaymentProcessor = mock(OnlinePaymentProcessor.class);
        when(storePaymentProcessorFactory.createPaymentProcessor(payments, country, order, orderDetails, paymentFactory))
            .thenReturn(onlinePaymentProcessor);
        when(onlinePaymentProcessor.evaluatePaymentMethod()).thenReturn(PaymentState.AUTHORISED);

        // test
        paymentService.processPaymentMethodAndState(payments, country, orderDetails, order);

        //assert
        verify(onlinePaymentProcessor).evaluatePaymentMethod();
        verify(storePaymentProcessorFactory).createPaymentProcessor(eq(payments), eq(country), eq(order), eq(orderDetails), any(PaymentFactory.class));
        assertEquals(PaymentState.START, order.getPrevPaymentStatus());
        assertEquals(PaymentState.AUTHORISED, order.getPaymentStatus());
    }

    @Test
    public void testSetPaymentMethodForNonTradebyteStore_validtransition_giftCardAndNoneGiftCard_review() {
        List<Payment> payments = mock(List.class);
        String country = "NL";
        OrderDetails orderDetails = mock(OrderDetails.class);
        Order order = new Order();
        order.setOrderId("1234");
        order.setPaymentStatus(PaymentState.REVIEW);
        order.setPayments(getPaymentInfoWithGiftCardAndNoneGiftCard());

        // Arrange
        OnlinePaymentProcessor onlinePaymentProcessor = mock(OnlinePaymentProcessor.class);
        when(storePaymentProcessorFactory.createPaymentProcessor(payments, country, order, orderDetails, paymentFactory))
            .thenReturn(onlinePaymentProcessor);
        when(onlinePaymentProcessor.evaluatePaymentMethod()).thenReturn(PaymentState.AUTHORISED);

        // test
        paymentService.processPaymentMethodAndState(payments, country, orderDetails, order);

        //assert
        verify(onlinePaymentProcessor).evaluatePaymentMethod();
        verify(storePaymentProcessorFactory).createPaymentProcessor(eq(payments), eq(country), eq(order), eq(orderDetails), any(PaymentFactory.class));
        assertEquals(PaymentState.REVIEW, order.getPrevPaymentStatus());
        assertEquals(PaymentState.AUTHORISED, order.getPaymentStatus());
    }

    @Test
    public void testSetPaymentMethodForNonTradebyteStore_invalidtransition_giftCardAndNoneGiftCard() {
        List<Payment> payments = mock(List.class);
        String country = "NL";
        OrderDetails orderDetails = mock(OrderDetails.class);
        Order order = new Order();
        order.setOrderId("1234");
        order.setPaymentStatus(PaymentState.CANCELLED);
        order.setPayments(getPaymentInfoWithGiftCardAndNoneGiftCard());

        // Arrange
        OnlinePaymentProcessor onlinePaymentProcessor = mock(OnlinePaymentProcessor.class);
        when(storePaymentProcessorFactory.createPaymentProcessor(payments, country, order, orderDetails, paymentFactory))
            .thenReturn(onlinePaymentProcessor);
        when(onlinePaymentProcessor.evaluatePaymentMethod()).thenReturn(PaymentState.AUTHORISED);

        //test and assert
        assertThrows(StateTransitionException.class, () -> paymentService.processPaymentMethodAndState(payments, country, orderDetails, order));
    }

    @Test
    public void testSetPaymentMethodForNonTradebyteStore_exception() {
        List<Payment> payments = mock(List.class);
        String country = "NL";
        OrderDetails orderDetails = mock(OrderDetails.class);
        Order order = new Order();
        order.setOrderId("1234");
        order.setPaymentStatus(null);
        order.setPayments(null);

        // Arrange
        OnlinePaymentProcessor onlinePaymentProcessor = mock(OnlinePaymentProcessor.class);
        when(storePaymentProcessorFactory.createPaymentProcessor(payments, country, order, orderDetails, paymentFactory))
            .thenReturn(onlinePaymentProcessor);
        when(onlinePaymentProcessor.evaluatePaymentMethod()).thenReturn(PaymentState.AUTHORISED);

        //test and assert
        assertThrows(PaymentValidationException.class, () -> paymentService.processPaymentMethodAndState(payments, country, orderDetails, order));
    }

    @Test
    public void testSetPaymentMethodForNonTradebyteStore_inValidtransition() {
        List<Payment> payments = mock(List.class);
        String country = "NL";
        OrderDetails orderDetails = mock(OrderDetails.class);
        Order order = new Order();
        order.setOrderId("1234");
        order.setPrevPaymentStatus(PaymentState.START);
        order.setPaymentStatus(PaymentState.CANCELLED);
        order.setPayments(getPaymentInfo());

        // Arrange
        OnlinePaymentProcessor onlinePaymentProcessor = mock(OnlinePaymentProcessor.class);
        when(storePaymentProcessorFactory.createPaymentProcessor(payments, country, order, orderDetails, paymentFactory))
            .thenReturn(onlinePaymentProcessor);
        when(onlinePaymentProcessor.evaluatePaymentMethod()).thenReturn(PaymentState.AUTHORISED);


        //test and assert
        assertThrows(StateTransitionException.class, () -> paymentService.processPaymentMethodAndState(payments, country, orderDetails, order));
    }

    @Test
    void testProcessOrderPlaced_Validated() {
        OrderPlaced orderPlaced = mock(OrderPlaced.class);
        paymentService.validateOrderPlaced(orderPlaced);
        verify(orderPlacedPaymentValidator).validate(orderPlaced);
    }

    @Test
    void testUpdatePaymentStatus() {
        String orderId = "ORDER_ID";
        ProcessorId processorId = ProcessorId.KLARNA_PAYMENTS;
        PaymentState paymentState = PaymentState.AUTHORISED;

        KlarnaPayment klarnaPayment = KlarnaPayment.builder()
            .paymentId(1)
            .orderId(orderId)
            .processorId(processorId)
            .authorisedAmount("12.0")
            .type(PaymentType.KLARNA)
            .build();

        List<PaymentInfo> payments = new ArrayList<>();
        payments.add(klarnaPayment);

        Order order = Order.builder()
            .orderId(orderId)
            .payments(payments)
            .paymentStatus((PaymentState.START))
            .build();


        when(orderService.getOrderById(orderId)).thenReturn(order);
        when(paymentStatusUpdatedGenerator.generate(order)).thenReturn(new PaymentStatusUpdated());
        paymentService.updatePaymentStatus(orderId, paymentState);
        verify(orderService).save(order);
        verify(paymentStatusUpdatedProducerQueueProducer).enqueue(any());
    }

    @Test
    void testUpdatePaymentStatus_noUpdate() {
        String orderId = "ORDER_ID";
        ProcessorId processorId = ProcessorId.ADYEN;
        PaymentState paymentState = PaymentState.AUTHORISED;

        KlarnaPayment klarnaPayment = KlarnaPayment.builder()
            .paymentId(1)
            .orderId(orderId)
            .processorId(processorId)
            .authorisedAmount("12.0")
            .build();

        List<PaymentInfo> payments = new ArrayList<>();
        payments.add(klarnaPayment);

        Order order = Order.builder()
            .orderId(orderId)
            .payments(payments)
            .paymentStatus(paymentState)
            .build();


        when(orderService.getOrderById(orderId)).thenReturn(order);
        paymentService.updatePaymentStatus(orderId, paymentState);
        verify(orderService, Mockito.never()).save(order);
        verify(paymentStatusUpdatedProducerQueueProducer, Mockito.never()).enqueue(any());
    }

    @Test
    void testUpdatePaymentStatus_orderNotAvailable() {
        String orderId = "ORDER_ID";
        PaymentState paymentState = PaymentState.AUTHORISED;

        when(orderService.getOrderById(orderId)).thenThrow(new OrderNotFoundException(orderId));
        assertThrows(OrderNotFoundException.class, () -> paymentService.updatePaymentStatus(orderId, paymentState));
    }

    @Test
    void testStartSettlementProcess_giftCardPayment() {
        // Arrange
        GiftcardPayment giftcardPayment = GiftcardPayment.builder()
            .paymentId(1)
            .orderId("1234")
            .processorId(ProcessorId.OPTICARD)
            .authorisedAmount("12.0")
            .type(PaymentType.GIFTCARD)
            .paymentReference("pspReference")
            .build();
        List<PaymentInfo> payments = new ArrayList<>();
        payments.add(giftcardPayment);
        Order order = Order.builder()
            .orderId("1234")
            .payments(payments)
            .paymentStatus(PaymentState.AUTHORISED)
            .build();

        doNothing().when(ecomRefundService).issueCancellationRefund(order);

        // Act
        paymentService.startSettlementProcess(order);

        // Assert
        verify(ecomRefundService).issueCancellationRefund(order);
        verify(adyenPaymentSettlementRequestProducer, never()).enqueue(order);
        verify(klarnaPaymentSettlementRequestProducer, never()).enqueue(order);
        verify(paymentStatusUpdatedProducerQueueProducer, never()).enqueue(any());
    }

    @Test
    void testStartSettlementProcess_adyenCardPayment() {
        // Arrange
        AdyenCardPayment adyenCardPayment = AdyenCardPayment.builder()
            .paymentId(1)
            .orderId("1234")
            .processorId(ProcessorId.ADYEN)
            .authorisedAmount("12.0")
            .type(PaymentType.ADYEN_CARD)
            .paymentReference("pspReference")
            .build();
        List<PaymentInfo> payments = new ArrayList<>();
        payments.add(adyenCardPayment);
        Order order = Order.builder()
            .orderId("1234")
            .payments(payments)
            .paymentStatus(PaymentState.AUTHORISED)
            .build();
        PaymentSettlementRequest paymentSettlementRequest = new PaymentSettlementRequest().withTotalAmount(1);

        when(adyenPaymentSettlementRequestProducer.enqueue(order)).thenReturn(paymentSettlementRequest);
        when(adyenPaymentSettlementRequestProducer.supports(order)).thenReturn(true);
        when(paymentStatusUpdatedGenerator.generate(order)).thenReturn(new PaymentStatusUpdated());


        // Act
        paymentService.startSettlementProcess(order);

        // Assert
        assertEquals(PaymentState.SETTLEMENT_REQUESTING, order.getPaymentStatus());
        verify(ecomRefundService, never()).issueCancellationRefund(order);
        verify(adyenPaymentSettlementRequestProducer).enqueue(order);
        verify(klarnaPaymentSettlementRequestProducer, never()).enqueue(order);
        verify(paymentStatusUpdatedProducerQueueProducer).enqueue(any());
    }

    @Test
    void testStartSettlementProcess_adyenBankPayment() {
        // Arrange
        AdyenBankPayment adyenBankPayment = AdyenBankPayment.builder()
            .paymentId(1)
            .orderId("1234")
            .processorId(ProcessorId.ADYEN)
            .authorisedAmount("12.0")
            .type(PaymentType.ADYEN_BANK)
            .paymentReference("pspReference")
            .build();
        List<PaymentInfo> payments = new ArrayList<>();
        payments.add(adyenBankPayment);
        Order order = Order.builder()
            .orderId("1234")
            .payments(payments)
            .paymentStatus(PaymentState.AUTHORISED)
            .build();

        doNothing().when(ecomRefundService).issueCancellationRefund(order);

        // Act
        paymentService.startSettlementProcess(order);

        // Assert
        verify(ecomRefundService).issueCancellationRefund(order);
        verify(adyenPaymentSettlementRequestProducer, never()).enqueue(order);
        verify(klarnaPaymentSettlementRequestProducer, never()).enqueue(order);
        verify(paymentStatusUpdatedProducerQueueProducer, never()).enqueue(any());
    }

    @Test
    void testStartSettlementProcess_klarnaWithGiftCardPayments() {
        // Arrange
        KlarnaPayment klarnaPayment = KlarnaPayment.builder()
            .paymentId(1)
            .orderId("1234")
            .processorId(ProcessorId.KLARNA_PAYMENTS)
            .authorisedAmount("12.0")
            .type(PaymentType.KLARNA)
            .paymentReference("pspReference")
            .build();
        List<PaymentInfo> payments = new ArrayList<>();
        payments.add(klarnaPayment);
        payments.addAll(getPaymentInfoWithGiftCards());
        Order order = Order.builder()
            .orderId("1234")
            .payments(payments)
            .paymentStatus(PaymentState.AUTHORISED)
            .build();
        PaymentSettlementRequest paymentSettlementRequest = new PaymentSettlementRequest().withTotalAmount(1);
        when(klarnaPaymentSettlementRequestProducer.enqueue(order)).thenReturn(paymentSettlementRequest);
        when(klarnaPaymentSettlementRequestProducer.supports(order)).thenReturn(true);
        when(paymentStatusUpdatedGenerator.generate(order)).thenReturn(new PaymentStatusUpdated());

        // Act
        paymentService.startSettlementProcess(order);

        // Assert
        assertEquals(PaymentState.SETTLEMENT_REQUESTING, order.getPaymentStatus());
        verify(ecomRefundService).issueCancellationRefund(order);
        verify(klarnaPaymentSettlementRequestProducer).enqueue(order);
        verify(adyenPaymentSettlementRequestProducer, never()).enqueue(order);
        verify(paymentStatusUpdatedProducerQueueProducer).enqueue(any());
    }

    @Test
    void testStartSettlementProcess_adyenCardWithGiftCardPayments() {
        // Arrange
        AdyenCardPayment adyenPayment = AdyenCardPayment.builder()
            .paymentId(1)
            .orderId("1234")
            .processorId(ProcessorId.ADYEN)
            .authorisedAmount("12.0")
            .type(PaymentType.ADYEN_CARD)
            .paymentReference("pspReference")
            .build();
        GiftcardPayment giftcardPayment = GiftcardPayment.builder()
            .paymentId(1)
            .orderId("1234")
            .processorId(ProcessorId.OPTICARD)
            .authorisedAmount("12.0")
            .type(PaymentType.GIFTCARD)
            .paymentReference("pspReference")
            .build();
        List<PaymentInfo> payments = new ArrayList<>();
        payments.add(adyenPayment);
        payments.add(giftcardPayment);
        Order order = Order.builder()
            .orderId("1234")
            .payments(payments)
            .paymentStatus(PaymentState.AUTHORISED)
            .build();
        PaymentSettlementRequest paymentSettlementRequest = new PaymentSettlementRequest().withTotalAmount(1);

        when(adyenPaymentSettlementRequestProducer.enqueue(order)).thenReturn(paymentSettlementRequest);
        when(adyenPaymentSettlementRequestProducer.supports(order)).thenReturn(true);
        when(paymentStatusUpdatedGenerator.generate(order)).thenReturn(new PaymentStatusUpdated());

        // Act
        paymentService.startSettlementProcess(order);

        // Assert
        assertEquals(PaymentState.SETTLEMENT_REQUESTING, order.getPaymentStatus());
        verify(ecomRefundService).issueCancellationRefund(order);
        verify(klarnaPaymentSettlementRequestProducer, never()).enqueue(order);
        verify(adyenPaymentSettlementRequestProducer).enqueue(order);
        verify(paymentStatusUpdatedProducerQueueProducer).enqueue(any());
    }

    @Test
    void testStartSettlementProcess_nonePayment() {
        // Arrange
        NonePayment nonePayment = NonePayment.builder()
            .paymentId(1)
            .orderId("1234")
            .processorId(ProcessorId.OFFLINE)
            .authorisedAmount("12.0")
            .type(PaymentType.NONE)
            .paymentReference("pspReference")
            .build();
        List<PaymentInfo> payments = new ArrayList<>();
        payments.add(nonePayment);
        Order order = Order.builder()
            .orderId("1234")
            .payments(payments)
            .paymentStatus(PaymentState.AUTHORISED)
            .build();

        // Act
        paymentService.startSettlementProcess(order);

        // Assert
        verify(ecomRefundService, never()).issueCancellationRefund(order);
        verify(klarnaPaymentSettlementRequestProducer, never()).enqueue(order);
        verify(adyenPaymentSettlementRequestProducer, never()).enqueue(order);
        verify(paymentStatusUpdatedProducerQueueProducer, never()).enqueue(any());
    }

    @Test
    void testStartSettlementProcess_noPayments() {
        // Arrange
        Order order = Order.builder()
            .orderId("1234")
            .paymentStatus(PaymentState.AUTHORISED)
            .build();
        // Act
        paymentService.startSettlementProcess(order);

        // Assert
        verify(ecomRefundService, never()).issueCancellationRefund(order);
        verify(klarnaPaymentSettlementRequestProducer, never()).enqueue(order);
        verify(adyenPaymentSettlementRequestProducer, never()).enqueue(order);
        verify(paymentStatusUpdatedProducerQueueProducer, never()).enqueue(any());
    }

    @Test
    void testStartSettlementProcess_paymentTypeIsNull() {
        // Arrange
        AdyenBankPayment adyenBankPayment = AdyenBankPayment.builder()
            .paymentId(1)
            .orderId("1234")
            .processorId(ProcessorId.ADYEN)
            .authorisedAmount("12.0")
            .paymentReference("pspReference")
            .build();
        adyenBankPayment.setType(null);
        List<PaymentInfo> payments = new ArrayList<>();
        payments.add(adyenBankPayment);
        Order order = Order.builder()
            .orderId("1234")
            .payments(payments)
            .paymentStatus(PaymentState.AUTHORISED)
            .build();

        // Act
        paymentService.startSettlementProcess(order);

        // Assert
        verify(ecomRefundService, never()).issueCancellationRefund(order);
        verify(klarnaPaymentSettlementRequestProducer, never()).enqueue(order);
        verify(adyenPaymentSettlementRequestProducer, never()).enqueue(order);
        verify(paymentStatusUpdatedProducerQueueProducer, never()).enqueue(any());
    }

    @Test
    void testStartSettlementProcess_alreadySettledPayment() {
        // Arrange
        AdyenBankPayment adyenBankPayment = AdyenBankPayment.builder()
            .paymentId(1)
            .orderId("1234")
            .processorId(ProcessorId.ADYEN)
            .authorisedAmount("12.0")
            .type(PaymentType.ADYEN_CARD)
            .paymentReference("pspReference")
            .build();
        List<PaymentInfo> payments = new ArrayList<>();
        payments.add(adyenBankPayment);
        Order order = Order.builder()
            .orderId("1234")
            .payments(payments)
            .paymentStatus(PaymentState.SETTLED)
            .build();

        // Act
        paymentService.startSettlementProcess(order);

        // Assert
        assertEquals(PaymentState.SETTLED, order.getPaymentStatus());
        verify(klarnaPaymentSettlementRequestProducer, never()).enqueue(order);
        verify(adyenPaymentSettlementRequestProducer, never()).enqueue(order);
        verify(paymentStatusUpdatedProducerQueueProducer, never()).enqueue(any());
    }

    @Test
    void testStartSettlementProcess_adyenPayment() {
        // Arrange
        Order order = OrderGenerator.createOrder();
        order.setPayments(
            List.of(
                AdyenBankPayment.builder()
                    .paymentId(1)
                    .orderId(order.getOrderId())
                    .processorId(ProcessorId.ADYEN)
                    .authorisedAmount("12.0")
                    .type(PaymentType.ADYEN_CARD)
                    .paymentReference("pspReference")
                    .build()
            )
        );
        PaymentSettlementRequest paymentSettlementRequest = new PaymentSettlementRequest().withTotalAmount(1);

        when(adyenPaymentSettlementRequestProducer.enqueue(order)).thenReturn(paymentSettlementRequest);
        when(adyenPaymentSettlementRequestProducer.supports(order)).thenReturn(true);
        when(paymentStatusUpdatedGenerator.generate(order)).thenReturn(new PaymentStatusUpdated());

        // Act
        paymentService.startSettlementProcess(order);

        // Assert
        assertEquals(PaymentState.SETTLEMENT_REQUESTING, order.getPaymentStatus());
        verify(adyenPaymentSettlementRequestProducer).enqueue(order);
        verify(klarnaPaymentSettlementRequestProducer, never()).enqueue(order);
        verify(paymentStatusUpdatedProducerQueueProducer).enqueue(any());
    }

    @Test
    void testStartSettlementProcess_klarnaPayment() {
        // Arrange
        Order order = OrderGenerator.createOrder();
        order.setPayments(
            List.of(
                KlarnaPayment.builder()
                    .paymentId(1)
                    .orderId(order.getOrderId())
                    .processorId(ProcessorId.KLARNA_PAYMENTS)
                    .authorisedAmount("12.0")
                    .type(PaymentType.KLARNA)
                    .paymentReference("pspReference")
                    .build()
            )
        );
        PaymentSettlementRequest paymentSettlementRequest = new PaymentSettlementRequest().withTotalAmount(1);

        when(klarnaPaymentSettlementRequestProducer.enqueue(order)).thenReturn(paymentSettlementRequest);
        when(klarnaPaymentSettlementRequestProducer.supports(order)).thenReturn(true);
        when(paymentStatusUpdatedGenerator.generate(order)).thenReturn(new PaymentStatusUpdated());

        // Act
        paymentService.startSettlementProcess(order);

        // Assert
        assertEquals(PaymentState.SETTLEMENT_REQUESTING, order.getPaymentStatus());
        verify(ecomRefundService, never()).issueCancellationRefund(order);
        verify(klarnaPaymentSettlementRequestProducer).enqueue(order);
        verify(adyenPaymentSettlementRequestProducer, never()).enqueue(order);
        verify(paymentStatusUpdatedProducerQueueProducer).enqueue(any());
    }

    @Test
    void testStartSettlementProcess_klarnaAllCancelled() {
        // Arrange
        Order order = OrderGenerator.createOrder();
        order.setPayments(
            List.of(
                KlarnaPayment.builder()
                    .paymentId(1)
                    .orderId(order.getOrderId())
                    .processorId(ProcessorId.KLARNA_PAYMENTS)
                    .authorisedAmount("12.0")
                    .type(PaymentType.KLARNA)
                    .paymentReference("pspReference")
                    .build()
            )
        );
        PaymentSettlementRequest paymentSettlementRequest = new PaymentSettlementRequest().withTotalAmount(0);

        when(klarnaPaymentSettlementRequestProducer.enqueue(order)).thenReturn(paymentSettlementRequest);
        when(klarnaPaymentSettlementRequestProducer.supports(order)).thenReturn(true);
        when(paymentStatusUpdatedGenerator.generate(order)).thenReturn(new PaymentStatusUpdated());

        // Act
        paymentService.startSettlementProcess(order);

        // Assert
        assertEquals(PaymentState.CANCELLED, order.getPaymentStatus());
        verify(ecomRefundService, never()).issueCancellationRefund(order);
        verify(klarnaPaymentSettlementRequestProducer).enqueue(order);
        verify(adyenPaymentSettlementRequestProducer, never()).enqueue(order);
        verify(paymentStatusUpdatedProducerQueueProducer).enqueue(any());
    }

    @Test
    void testStartSettlementProcess_AdyenAllCancelled() {
        // Arrange
        Order order = OrderGenerator.createOrder();
        order.setPayments(
            List.of(
                AdyenBankPayment.builder()
                    .paymentId(1)
                    .orderId(order.getOrderId())
                    .processorId(ProcessorId.ADYEN)
                    .authorisedAmount("12.0")
                    .type(PaymentType.ADYEN_CARD)
                    .paymentReference("pspReference")
                    .build()
            )
        );
        PaymentSettlementRequest paymentSettlementRequest = new PaymentSettlementRequest().withTotalAmount(0);

        when(adyenPaymentSettlementRequestProducer.enqueue(order)).thenReturn(paymentSettlementRequest);
        when(adyenPaymentSettlementRequestProducer.supports(order)).thenReturn(true);
        when(paymentStatusUpdatedGenerator.generate(order)).thenReturn(new PaymentStatusUpdated());

        // Act
        paymentService.startSettlementProcess(order);

        // Assert
        assertEquals(PaymentState.CANCELLED, order.getPaymentStatus());
        verify(ecomRefundService, never()).issueCancellationRefund(order);
        verify(adyenPaymentSettlementRequestProducer).enqueue(order);
        verify(klarnaPaymentSettlementRequestProducer, never()).enqueue(order);
        verify(paymentStatusUpdatedProducerQueueProducer).enqueue(any());
    }

    @Test
    void testUpdatePaymentToSettled_onlyGiftCardPayment() {
        // Arrange
        Order order = OrderGenerator.createOrder();
        order.setPayments(getPaymentInfoWithGiftCards());

        when(orderService.getOrderById(order.getOrderId())).thenReturn(order);

        // Act
        assertThrows(StateTransitionException.class,
            () -> paymentService.updatePaymentStatus(order.getOrderId(), PaymentState.SETTLED));

        // Assert
        verify(orderService, never()).save(order);
        verify(paymentStatusUpdatedProducerQueueProducer, never()).enqueue(any());
    }

    @Test
    void testUpdatePaymentToSettled_nonePayment() {
        // Arrange
        Order order = OrderGenerator.createOrder();
        order.setPayments(getPaymentInfo());

        when(orderService.getOrderById(order.getOrderId())).thenReturn(order);

        // Act
        assertThrows(StateTransitionException.class,
            () -> paymentService.updatePaymentStatus(order.getOrderId(), PaymentState.SETTLED));

        // Assert
        verify(orderService, never()).save(order);
        verify(paymentStatusUpdatedProducerQueueProducer, never()).enqueue(any());
    }

    @Test
    void testUpdatePaymentToSettled_adyenCardPayment() {
        // Arrange
        Order order = OrderGenerator.createOrder();
        order.setPaymentStatus(PaymentState.SETTLEMENT_REQUESTING);
        order.setPayments(getPaymentInfo());
        order.getPayments().get(0).setType(PaymentType.ADYEN_CARD);

        when(orderService.getOrderById(order.getOrderId())).thenReturn(order);
        when(paymentStatusUpdatedGenerator.generate(order)).thenReturn(new PaymentStatusUpdated());

        // Act
        paymentService.updatePaymentStatus(order.getOrderId(), PaymentState.SETTLED);

        // Assert
        verify(orderService).save(order);
        verify(paymentStatusUpdatedProducerQueueProducer).enqueue(any());
        assertEquals(PaymentState.SETTLED, order.getPaymentStatus());
    }

    @Test
    void testUpdatePaymentToSettled_klarnaPayment() {
        // Arrange
        Order order = OrderGenerator.createOrder();
        order.setPaymentStatus(PaymentState.SETTLEMENT_REQUESTING);
        KlarnaPayment klarnaPayment = KlarnaPayment.builder()
            .paymentId(1)
            .orderId(order.getOrderId())
            .processorId(ProcessorId.KLARNA_PAYMENTS)
            .type(PaymentType.KLARNA)
            .build();
        order.setPayments(List.of(klarnaPayment));


        when(orderService.getOrderById(order.getOrderId())).thenReturn(order);
        when(paymentStatusUpdatedGenerator.generate(order)).thenReturn(new PaymentStatusUpdated());

        // Act
        paymentService.updatePaymentStatus(order.getOrderId(), PaymentState.SETTLED);

        // Assert
        verify(orderService).save(order);
        verify(paymentStatusUpdatedProducerQueueProducer).enqueue(any());
        assertEquals(PaymentState.SETTLED, order.getPaymentStatus());
    }

    @Test
    void testValidateStateTransition_validTransition() {
        // Arrange
        Order order = OrderGenerator.createOrder();
        order.setPaymentStatus(PaymentState.AUTHORISED);

        when(orderService.getOrderById(order.getOrderId())).thenReturn(order);

        // Act
        var actual = paymentService.validateStateTransition(order.getOrderId(), PaymentState.CANCELLED);

        // Assert
        assertTrue(actual.isSuccess());
        assertNull(actual.getErrorMessages());
    }

    @Test
    void testValidateStateTransition_invalidTransition() {
        // Arrange
        Order order = OrderGenerator.createOrder();
        order.setPaymentStatus(PaymentState.SETTLED);

        when(orderService.getOrderById(order.getOrderId())).thenReturn(order);

        // Act
        var actual = paymentService.validateStateTransition(order.getOrderId(), PaymentState.CANCELLED);

        // Assert
        assertFalse(actual.isSuccess());
        assertEquals(1, actual.getErrorMessages().size());
        assertEquals("Transition from SETTLED to CANCELLED is not allowed in order %s.".formatted(order.getOrderId()),
            actual.getErrorMessages().getFirst());
    }

    private List<PaymentInfo> getPaymentInfo() {
        String orderId = "1234";
        ProcessorId processorId = ProcessorId.ADYEN;
        List<PaymentInfo> paymentInfos = new ArrayList<>();
        String pspReference = "pspReference";
        AdyenBankPayment accountPayment = AdyenBankPayment.builder()
            .paymentId(1)
            .orderId(orderId)
            .processorId(processorId)
            .authorisedAmount("12.0")
            .type(PaymentType.ADYEN_BANK)
            .paymentReference(pspReference)
            .build();
        paymentInfos.add(accountPayment);

        return paymentInfos;
    }

    private List<PaymentInfo> getPaymentInfoWithGiftCardAndNoneGiftCard() {
        List<PaymentInfo> paymentInfos = new ArrayList<>();
        KlarnaPayment accountPayment = KlarnaPayment
            .builder()
            .type(PaymentType.KLARNA)
            .paymentReference("3")
            .processorId(ProcessorId.KLARNA_PAYMENTS)
            .authorisedAmount("100")
            .build();

        paymentInfos.addAll(getPaymentInfoWithGiftCards());
        paymentInfos.add(accountPayment);

        return paymentInfos;

    }

    private List<PaymentInfo> getPaymentInfoWithGiftCards() {
        List<PaymentInfo> paymentInfos = new ArrayList<>();
        GiftcardPayment giftCardPaymentFirst = GiftcardPayment.builder()
            .type(PaymentType.GIFTCARD)
            .paymentReference("1")
            .processorId(ProcessorId.OPTICARD)
            .authorisedAmount("10").build();

        GiftcardPayment giftCardPaymentSecond = GiftcardPayment.builder()
            .type(PaymentType.GIFTCARD)
            .paymentReference("2")
            .processorId(ProcessorId.OPTICARD)
            .authorisedAmount("20").build();

        paymentInfos.add(giftCardPaymentFirst);
        paymentInfos.add(giftCardPaymentSecond);

        return paymentInfos;
    }
}

