package com.bestseller.payment.core.service.order;

import com.bestseller.dbqueue.core.api.EnqueueResult;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.config.QueueShardId;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderFinalized;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.adapter.stream.messagegenerator.PaymentStatusUpdatedGenerator;
import com.bestseller.payment.core.converter.OrderChargeMapper;
import com.bestseller.payment.core.converter.OrderLineMapper;
import com.bestseller.payment.core.converter.OrderPromotionMapper;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderCharge;
import com.bestseller.payment.core.domain.OrderLine;
import com.bestseller.payment.core.domain.enumeration.Brand;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.exception.InvalidMessageException;
import com.bestseller.payment.core.exception.MultipleNonGiftcardPaymentsException;
import com.bestseller.payment.core.exception.StateTransitionException;
import com.bestseller.payment.core.service.payment.PaymentService;
import com.bestseller.payment.core.service.price.PriceService;
import com.bestseller.payment.core.service.vat.OrderVatService;
import com.bestseller.payment.utils.JsonUtils;
import com.bestseller.payment.utils.OrderGenerator;
import com.bestseller.payment.utils.ValidOrderPlacedGenerator;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.*;

import static com.bestseller.payment.utils.ValidOrderPlacedGenerator.AddressProperties.COUNTRY_DK;
import static com.bestseller.payment.utils.ValidOrderPlacedGenerator.AddressProperties.COUNTRY_NL;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class OrderServiceTest {
    @InjectMocks
    private OrderServiceImpl orderService;

    @Mock
    private OrderRepository orderRepository;

    @Mock
    private PriceService priceService;

    @Mock
    private PaymentService paymentService;

    @Mock
    private QueueProducer<PaymentStatusUpdated> paymentStatusUpdatedProducerQueueProducer;

    @Mock
    private OrderLineMapper orderLineMapper;

    @Mock
    private OrderVatService orderVatService;

    @Mock
    private OrderChargeMapper orderChargeMapper;

    @Mock
    private OrderPromotionMapper orderPromotionMapper;

    @Mock
    private PaymentStatusUpdatedGenerator paymentStatusUpdatedGenerator;

    @Captor
    private ArgumentCaptor<Order> orderArgumentCaptor;

    @Test
    void testProcessValidOrderPlaced_NewOrder_NoneBse() {
        ValidOrderPlaced validOrderPlaced = ValidOrderPlacedGenerator.createTradebyteOrderPlacedMessageWithShippingInformation();
        testNonBseValidOrderPlaced(validOrderPlaced);
    }

    @Test
    void testProcessValidOrderPlaced_NoneBse_WithoutPayments() {
        ValidOrderPlaced validOrderPlaced = ValidOrderPlacedGenerator.createTradebyteOrderPlacedMessageWithoutPaymentInformation();
        testNonBseValidOrderPlaced(validOrderPlaced);
    }

    private void testNonBseValidOrderPlaced(ValidOrderPlaced validOrderPlaced) {
        //arrange
        when(orderRepository.findById(validOrderPlaced.getOrderId())).thenReturn(Optional.empty());
        doAnswer(invocationOnMock -> {
            Order orderArg = invocationOnMock.getArgument(3);
            orderArg.setPaymentStatus(PaymentState.OFFLINE);
            return null;
        }).when(paymentService).processPaymentMethodAndState(any(), any(), any(), any());
        when(paymentStatusUpdatedProducerQueueProducer.enqueue(any())).thenReturn(
            EnqueueResult.builder()
                .withEnqueueId(1L)
                .withShardId(new QueueShardId("main"))
                .build()
        );
        when(orderLineMapper.mapOrderLines(any())).thenReturn(List.of(OrderLine.builder().build()));
        doAnswer(invocation -> invocation.getArgument(0))
            .when(orderRepository).save(orderArgumentCaptor.capture());
        when(paymentStatusUpdatedGenerator.generate(any())).thenReturn(new PaymentStatusUpdated());

        //act
        orderService.processValidOrderPlaced(validOrderPlaced);

        //assert
        verify(orderRepository, times(1)).save(any(Order.class));
        verify(paymentService, times(1)).processPaymentMethodAndState(any(), any(), any(), any());
        verify(paymentStatusUpdatedProducerQueueProducer, times(1)).enqueue(any());
        verify(priceService, times(1)).processAndSetPriceFieldsForOrderLineAndTotalPaidAmount(any());
        verify(orderChargeMapper, times(1)).mapToOrderCharges(any(), any(Boolean.class));
        verify(orderLineMapper, times(1)).mapOrderLines(any());

        assertThat(orderArgumentCaptor.getValue())
            .extracting("brand", "billingCountryCode", "shippingCountryCode")
            .containsExactly(Brand.JJ, COUNTRY_DK, COUNTRY_NL);
    }

    @Test
    void testProcessValidOrderPlaced_ExistingOrder_None_Bse() {
        //arrange
        Order order = mock(Order.class);
        ValidOrderPlaced validOrderPlaced = mock(ValidOrderPlaced.class);
        when(orderRepository.findById(any())).thenReturn(Optional.ofNullable(order));

        //act
        orderService.processValidOrderPlaced(validOrderPlaced);

        //assert
        verify(orderRepository, never()).save(any(Order.class));
        verify(paymentService, never()).processPaymentMethodAndState(any(), any(), any(), any());
        verify(paymentStatusUpdatedProducerQueueProducer, never()).enqueue(any());
        verify(priceService, never()).processAndSetPriceFieldsForOrderLineAndTotalPaidAmount(any());
    }

    @Test
    void testProcessValidOrderPlaced_ExistingOrder_Bse() {
        //arrange
        Order order = mock(Order.class);
        ValidOrderPlaced validOrderPlaced = mock(ValidOrderPlaced.class);
        when(orderRepository.findById(any())).thenReturn(Optional.ofNullable(order));

        //act
        orderService.processValidOrderPlaced(validOrderPlaced);

        //assert
        verify(orderRepository, never()).save(any(Order.class));
        verify(paymentService, never()).processPaymentMethodAndState(any(), any(), any(), any());
        verify(paymentStatusUpdatedProducerQueueProducer, never()).enqueue(any());
        verify(priceService, never()).processAndSetPriceFieldsForOrderLineAndTotalPaidAmount(any());
        verify(orderLineMapper, never()).mapOrderLines(any());
        verify(priceService, never()).processAndSetPriceFieldsForOrderLineAndTotalPaidAmount(any());
    }

    @Test
    void testProcessValidOrderPlaced_NewOrder_Bse() {
        //arrange
        ValidOrderPlaced validOrderPlaced = ValidOrderPlacedGenerator.createBseOrderPlacedMessage();
        when(orderRepository.findById(validOrderPlaced.getOrderId())).thenReturn(Optional.empty());
        doAnswer(invocationOnMock -> {
            Order orderArg = invocationOnMock.getArgument(3);
            orderArg.setPaymentStatus(PaymentState.AUTHORISED);
            return null;
        }).when(paymentService).processPaymentMethodAndState(any(), any(), any(), any());
        when(orderLineMapper.mapOrderLines(any())).thenReturn(List.of(OrderLine.builder().build()));
        when(paymentStatusUpdatedGenerator.generate(any())).thenReturn(new PaymentStatusUpdated());

        //act
        orderService.processValidOrderPlaced(validOrderPlaced);

        //assert
        verify(orderRepository, times(1)).save(any(Order.class));
        verify(paymentService, times(1)).processPaymentMethodAndState(any(), any(), any(), any());
        verify(paymentStatusUpdatedProducerQueueProducer, times(1)).enqueue(any());
        verify(priceService, times(1)).processAndSetPriceFieldsForOrderLineAndTotalPaidAmount(any());
        verify(orderChargeMapper, times(1)).mapToOrderCharges(any(), any(Boolean.class));
        verify(orderLineMapper, times(1)).mapOrderLines(any());
    }

    @Test
    void testProcessValidOrderPlaced_WithDuplicateOrder_Bse() {
        //arrange
        ValidOrderPlaced validOrderPlaced = ValidOrderPlacedGenerator.createBseOrderPlacedMessage();
        validOrderPlaced.withOfflinePayment(true);
        validOrderPlaced.setOrderLines(new ArrayList<>(validOrderPlaced.getOrderLines()));
        validOrderPlaced.getOrderLines().add(validOrderPlaced.getOrderLines().get(0));
        when(orderRepository.findById(validOrderPlaced.getOrderId())).thenReturn(Optional.empty());
        doAnswer(invocationOnMock -> {
            Order orderArg = invocationOnMock.getArgument(3);
            orderArg.setPaymentStatus(PaymentState.AUTHORISED);
            return null;
        }).when(paymentService).processPaymentMethodAndState(any(), any(), any(), any());
        when(orderLineMapper.mapOrderLines(any())).thenReturn(List.of(OrderLine.builder().build()));
        when(paymentStatusUpdatedGenerator.generate(any())).thenReturn(new PaymentStatusUpdated());

        //act
        orderService.processValidOrderPlaced(validOrderPlaced);

        //assert
        verify(orderRepository, times(1)).save(any(Order.class));
        verify(paymentService, times(1)).processPaymentMethodAndState(any(), any(), any(), any());
        verify(paymentStatusUpdatedProducerQueueProducer, times(1)).enqueue(any());
        verify(priceService, times(1)).processAndSetPriceFieldsForOrderLineAndTotalPaidAmount(any());
        verify(orderChargeMapper, times(1)).mapToOrderCharges(any(), any(Boolean.class));
        verify(orderLineMapper, times(1)).mapOrderLines(any());
    }

    @Test
    void testProcessValidOrderPlaced_StateTransitionException() {
        // Arrange
        ValidOrderPlaced validOrderPlaced = ValidOrderPlacedGenerator.createBseOrderPlacedMessage();
        when(orderRepository.findById(validOrderPlaced.getOrderId())).thenReturn(Optional.empty());
        when(orderLineMapper.mapOrderLines(any())).thenReturn(List.of(OrderLine.builder().build()));


        // Simulate a StateTransitionException
        doThrow(new StateTransitionException("State transition error", validOrderPlaced.getOrderId())).
            when(paymentService).
            processPaymentMethodAndState(
                any(),
                any(),
                any(),
                any());
        // Act & handle exception
        orderService.processValidOrderPlaced(validOrderPlaced);
    }

    @Test
    void testProcessValidOrderPlaced_IllegalArgumentException() {
        // Arrange
        ValidOrderPlaced validOrderPlaced = ValidOrderPlacedGenerator.createBseOrderPlacedMessage();
        when(orderRepository.findById(validOrderPlaced.getOrderId())).thenReturn(Optional.empty());
        when(orderLineMapper.mapOrderLines(any())).thenReturn(List.of(OrderLine.builder().build()));

        // Simulate a StateTransitionException
        doThrow(new IllegalArgumentException("IllegalArgumentException")).
            when(paymentService).
            processPaymentMethodAndState(
                any(),
                any(),
                any(),
                any());
        // Act & handle exception
        orderService.processValidOrderPlaced(validOrderPlaced);
    }

    @Test
    void testProcessValidOrderPlaced_MultipleNonGiftcardPaymentsException() {
        // Arrange
        ValidOrderPlaced validOrderPlaced = ValidOrderPlacedGenerator.createBseOrderPlacedMessage();
        when(orderRepository.findById(validOrderPlaced.getOrderId())).thenReturn(Optional.empty());
        when(orderLineMapper.mapOrderLines(any())).thenReturn(List.of(OrderLine.builder().build()));

        // Simulate a StateTransitionException
        doThrow(new MultipleNonGiftcardPaymentsException("MultipleNonGiftcardPaymentsException")).
            when(paymentService).
            processPaymentMethodAndState(
                any(),
                any(),
                any(),
                any());
        // Act & handle exception
        orderService.processValidOrderPlaced(validOrderPlaced);
    }

    @Test
    void testGetOrdersWaitingForApproval() {
        // Arrange
        int numberOfDays = 5;
        Set<Order> mockOrders = new HashSet<>();
        mockOrders.add(OrderGenerator.createAdyenOrder());

        when(orderRepository.findAllByPaymentStatusEqualsAndLastModifiedTSLessThanEqual(
            any(PaymentState.class),
            any(Instant.class))).thenReturn(mockOrders);

        // Act
        Set<Order> result = orderService.getOrdersWaitingForApproval(numberOfDays);

        // Assert
        assertEquals(mockOrders.size(), result.size());
        assertTrue(result.containsAll(mockOrders));

        // Verify interactions
        verify(orderRepository).findAllByPaymentStatusEqualsAndLastModifiedTSLessThanEqual(any(PaymentState.class), any(Instant.class));
    }

    @Test
    void whenOrderFinalizedIsValid_ThenProcess() {
        // Arrange
        String validOrderId = "validOrderId";
        OrderFinalized orderFinalized = new OrderFinalized();
        orderFinalized.setOrderId(validOrderId);

        com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderLine orderLine = new com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderLine();
        orderLine.setEan("EAN1334");
        orderLine.setLineNumber(1);
        orderLine.setQuantityStatuses(List.of("DISPATCHED"));
        orderFinalized.setOrderLines(List.of(orderLine));

        Order order = OrderGenerator.createOrder();
        order.setOrderId(validOrderId);
        order.getOrderLines().getFirst().setEan(orderLine.getEan());
        order.getOrderLines().getFirst().setLineNumber(orderLine.getLineNumber());
        Mockito.when(orderRepository.existsById(validOrderId)).thenReturn(true);
        Mockito.when(orderRepository.findById(validOrderId)).thenReturn(Optional.of(order));

        // Act
        orderService.processOrderFinalizedMessage(orderFinalized);

        // Assert
        verify(paymentService).startSettlementProcess(order);
    }

    @Test
    void whenOrderFinalizedOrderNotExist_ThenNoFurtherProcess() {
        // Arrange
        String invalidOrderId = "notExistOrderId";
        OrderFinalized invalidOrderFinalized = new OrderFinalized(invalidOrderId, List.of(new com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderLine()));

        when(orderRepository.existsById(invalidOrderId)).thenReturn(false);

        // Act
        orderService.processOrderFinalizedMessage(invalidOrderFinalized);

        // Assert
        verify(paymentService, never()).startSettlementProcess(any());
    }

    @Test
    void testSaveOrderFinalized_invalidMessage() throws IOException {
        // Arrange
        final Order order = OrderGenerator.createOrder();
        final OrderFinalized orderFinalized = loadSampleOrderFinalized("/messages/orderFinalized_invalid.json");

        when(orderRepository.findById(order.getOrderId())).thenReturn(Optional.of(order));

        // Act & Assert
        Assertions.assertThrows(InvalidMessageException.class, () -> orderService.saveOrderFinalized(orderFinalized));
    }

    @Test
    void testSaveOrderFinalized_allOrderLinesDispatched() throws IOException {
        // Arrange
        final Order order = OrderGenerator.createOrder();
        final OrderFinalized orderFinalized = loadSampleOrderFinalized("/messages/orderFinalized_all_dispatched.json");

        when(orderRepository.findById(order.getOrderId())).thenReturn(Optional.of(order));
        when(orderRepository.save(order)).thenReturn(order);
        when(orderVatService.getNextOrderVatId(order)).thenReturn("vatOrderNumber");

        // Act
        orderService.saveOrderFinalized(orderFinalized);

        // Assert
        order.getOrderLines().forEach(orderLine -> {
            assertEquals(orderLine.getOpenQty(), orderLine.getOriginalQty());
        });
        assertThat(order.getVatOrderNumber()).isNotNull();
    }

    @Test
    void testSaveOrderFinalized_allOrderLinesCancelled() throws IOException {
        // Arrange
        final Order order = OrderGenerator.createOrder();
        final OrderFinalized orderFinalized = loadSampleOrderFinalized("/messages/orderFinalized_all_cancelled.json");

        when(orderRepository.findById(order.getOrderId())).thenReturn(Optional.of(order));
        when(orderRepository.save(order)).thenReturn(order);

        // Act
        orderService.saveOrderFinalized(orderFinalized);

        // Assert
        order.getOrderLines().forEach(orderLine -> {
            assertEquals(0, orderLine.getOpenQty());
        });
        order.getOrderCharges().forEach(orderCharge -> {
            assertEquals(Boolean.TRUE, orderCharge.getCancelled());
        });
        assertThat(order.getVatOrderNumber()).isNull();
    }

    @Test
    void testSaveOrderFinalized_oneOrderLineCancelled() throws IOException {
        // Arrange
        final Order order = OrderGenerator.createOrder();
        final OrderFinalized orderFinalized = loadSampleOrderFinalized("/messages/orderFinalized_one_cancelled_with_quantity_one.json");
        final String cancelledEan = "ean3";

        when(orderRepository.findById(order.getOrderId())).thenReturn(Optional.of(order));
        when(orderRepository.save(order)).thenReturn(order);
        when(orderVatService.getNextOrderVatId(order)).thenReturn("vatOrderNumber");

        // Act
        orderService.saveOrderFinalized(orderFinalized);

        // Assert
        order.getOrderLines()
            .stream()
            .filter(ol -> cancelledEan.equals(ol.getEan()))
            .forEach(orderLine -> {
                assertEquals(0, orderLine.getOpenQty());
            });
        order.getOrderLines()
            .stream()
            .filter(ol -> !cancelledEan.equals(ol.getEan()))
            .forEach(orderLine -> {
                assertEquals(orderLine.getOriginalQty(), orderLine.getOpenQty());
            });
        assertThat(order.getVatOrderNumber()).isNotNull();
    }

    @Test
    void testSaveOrderFinalized_oneItemWithQuantityMoreThanOneCancelled() throws IOException {
        // Arrange
        final Order order = OrderGenerator.createOrder();
        final OrderFinalized orderFinalized = loadSampleOrderFinalized("/messages/orderFinalized_one_cancelled_from_quantity_more_than_one.json");
        final String cancelledEan = "ean1";

        when(orderRepository.findById(order.getOrderId())).thenReturn(Optional.of(order));
        when(orderRepository.save(order)).thenReturn(order);
        when(orderVatService.getNextOrderVatId(order)).thenReturn("vatOrderNumber");

        // Act
        orderService.saveOrderFinalized(orderFinalized);

        // Assert
        order.getOrderLines()
            .stream()
            .filter(ol -> cancelledEan.equals(ol.getEan()))
            .forEach(orderLine -> {
                assertEquals(1, orderLine.getOpenQty());
            });
        assertThat(order.getVatOrderNumber()).isNotNull();
    }

    @Test
    void testSaveOrderFinalized_oneReturnedOrderLine() throws IOException {
        // Arrange
        final Order order = OrderGenerator.createOrder();
        final OrderFinalized orderFinalized = loadSampleOrderFinalized("/messages/orderFinalized_one_returned.json");
        final String returnedEan = "ean3";

        when(orderRepository.findById(order.getOrderId())).thenReturn(Optional.of(order));
        when(orderRepository.save(order)).thenReturn(order);
        when(orderVatService.getNextOrderVatId(order)).thenReturn("vatOrderNumber");

        // Act
        orderService.saveOrderFinalized(orderFinalized);

        // Assert
        order.getOrderLines().stream().filter(ol -> !returnedEan.equals(ol.getEan())).forEach(orderLine -> {
            assertEquals(orderLine.getOpenQty(), orderLine.getOriginalQty());
        });
        order.getOrderLines().stream().filter(ol -> returnedEan.equals(ol.getEan())).forEach(orderLine -> {
            assertEquals(orderLine.getOpenQty(), orderLine.getOriginalQty());
        });
        assertThat(order.getVatOrderNumber()).isNotNull();
    }

    @Test
    void testSaveOrderFinalized_cancelled_returned() throws IOException {
        // Arrange
        final Order order = OrderGenerator.createOrder();
        final OrderFinalized orderFinalized = loadSampleOrderFinalized("/messages/orderFinalized_cancelled_returned.json");
        final String halfCancelledHalfReturnedEan = "ean1";
        final String halfCancelledHalfDispatchedEan = "ean2";
        final String returnedEan = "ean3";

        when(orderRepository.findById(order.getOrderId())).thenReturn(Optional.of(order));
        when(orderRepository.save(order)).thenReturn(order);
        when(orderVatService.getNextOrderVatId(order)).thenReturn("vatOrderNumber");

        // Act
        orderService.saveOrderFinalized(orderFinalized);

        // Assert

        order.getOrderLines()
            .stream()
            .filter(ol -> halfCancelledHalfDispatchedEan.contains(ol.getEan()))
            .forEach(orderLine -> {
                assertEquals(1, orderLine.getOpenQty());
            });
        order.getOrderLines().stream().filter(ol -> halfCancelledHalfReturnedEan.contains(ol.getEan())).forEach(orderLine -> {
            assertEquals(1, orderLine.getOpenQty());
        });
        order.getOrderLines().stream().filter(ol -> returnedEan.contains(ol.getEan())).forEach(orderLine -> {
            assertEquals(orderLine.getOpenQty(), orderLine.getOriginalQty());
        });
        assertThat(order.getVatOrderNumber()).isNotNull();
    }

    @Test
    void testCancelOrder_updatePaymentStatus_toCancelled() {
        // Arrange
        String orderId = "orderId";

        Order existingOrder = new Order();
        existingOrder.setOrderId("orderId");
        com.bestseller.payment.core.domain.OrderLine orderLine = new com.bestseller.payment.core.domain.OrderLine();
        orderLine.setEan("ean1");
        orderLine.setOpenQty(1);
        existingOrder.setOrderLines(List.of(orderLine));
        existingOrder.setOrderCharges(List.of(new OrderCharge()));

        when(orderRepository.findById("orderId")).thenReturn(Optional.of(existingOrder));

        // Act
        orderService.cancelOrder(orderId);

        // Assert
        verify(orderRepository).save(existingOrder);
        verify(priceService).processAndSetPriceFieldsForOrderLineAndTotalPaidAmount(existingOrder);
        verify(paymentService).updatePaymentStatus("orderId", PaymentState.CANCELLED);
    }

    @Test
    void testProcessValidOrderPlaced_shouldMapCurrency_fromOrderDetails() {
        // Arrange
        ValidOrderPlaced validOrderPlaced = ValidOrderPlacedGenerator.createBseOrderPlacedMessage();
        validOrderPlaced.getOrderDetails().setCurrency("DKK");

        when(orderRepository.findById(validOrderPlaced.getOrderId())).thenReturn(Optional.empty());
        doAnswer(invocationOnMock -> {
            Order orderArg = invocationOnMock.getArgument(3);
            orderArg.setPaymentStatus(PaymentState.AUTHORISED);
            return null;
        }).when(paymentService).processPaymentMethodAndState(any(), any(), any(), any());
        when(orderLineMapper.mapOrderLines(any())).thenReturn(List.of(OrderLine.builder().build()));
        when(paymentStatusUpdatedGenerator.generate(any())).thenReturn(new PaymentStatusUpdated());

        // Act
        orderService.processValidOrderPlaced(validOrderPlaced);

        // Capture the Order that was saved
        ArgumentCaptor<Order> orderCaptor = ArgumentCaptor.forClass(Order.class);
        verify(orderRepository).save(orderCaptor.capture());

        Order capturedOrder = orderCaptor.getValue();

        // Assert
        assertEquals("DKK", capturedOrder.getCurrency(), "Currency should match the expected value"); // replace with expected values
    }

    @Test
    void testProcessValidOrderPlaced_shouldMapCurrency_fromPayment() {
        // Arrange
        ValidOrderPlaced validOrderPlaced = ValidOrderPlacedGenerator.createBseOrderPlacedMessage();

        when(orderRepository.findById(validOrderPlaced.getOrderId())).thenReturn(Optional.empty());
        doAnswer(invocationOnMock -> {
            Order orderArg = invocationOnMock.getArgument(3);
            orderArg.setPaymentStatus(PaymentState.AUTHORISED);
            return null;
        }).when(paymentService).processPaymentMethodAndState(any(), any(), any(), any());
        when(orderLineMapper.mapOrderLines(any())).thenReturn(List.of(OrderLine.builder().build()));
        when(paymentStatusUpdatedGenerator.generate(any())).thenReturn(new PaymentStatusUpdated());

        // Act
        orderService.processValidOrderPlaced(validOrderPlaced);

        // Capture the Order that was saved
        ArgumentCaptor<Order> orderCaptor = ArgumentCaptor.forClass(Order.class);
        verify(orderRepository).save(orderCaptor.capture());

        Order capturedOrder = orderCaptor.getValue();

        // Assert
        assertEquals("EUR", capturedOrder.getCurrency(), "Currency should match the expected value"); // replace with expected values
    }

    @Test
    void testSave() {
        // Arrange
        Order order = OrderGenerator.createOrder();

        // Act
        orderService.save(order);

        // Assert
        verify(orderRepository).save(order);
    }

    @Test
    void testSave_immediate() {
        // Arrange
        Order order = OrderGenerator.createOrder();

        // Act
        orderService.save(order, true);

        // Assert
        verify(orderRepository).saveAndFlush(order);
    }

    private OrderFinalized loadSampleOrderFinalized(String filename) throws IOException {
        final String orderFinalizedInJson = IOUtils.toString(Objects.requireNonNull(getClass().getResourceAsStream(filename)), StandardCharsets.UTF_8);
        return JsonUtils.deserialize(orderFinalizedInJson, OrderFinalized.class);
    }

}