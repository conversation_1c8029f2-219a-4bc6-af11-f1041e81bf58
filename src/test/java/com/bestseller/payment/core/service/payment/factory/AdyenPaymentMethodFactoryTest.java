package com.bestseller.payment.core.service.payment.factory;

import com.bestseller.payment.core.domain.enumeration.PaymentMethod;
import com.bestseller.payment.core.domain.payment.AdyenBankPayment;
import com.bestseller.payment.core.domain.payment.AdyenCardPayment;
import com.bestseller.payment.core.domain.payment.AdyenPayment;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

 class AdyenPaymentMethodFactoryTest {

    @Test
    public void testCreatePayment_AdyenCreditCard() {
        AdyenPayment payment = AdyenPaymentMethodFactory.createPayment(PaymentMethod.ADYEN_CREDIT_CARD, false);
        assertTrue(payment instanceof AdyenCardPayment);
    }
    @Test
    public void testCreatePayment_AdyenCreditCard_BCMS_true() {
        AdyenPayment payment = AdyenPaymentMethodFactory.createPayment(PaymentMethod.ADYEN_CREDIT_CARD, true);
        assertTrue(payment instanceof AdyenBankPayment);
    }

    @Test
    public void testCreatePayment_AdyenIDeal() {
        AdyenPayment payment = AdyenPaymentMethodFactory.createPayment(PaymentMethod.ADYEN_IDEAL, false);
        assertTrue(payment instanceof AdyenBankPayment);
    }

    @Test
    public void testCreatePayment_AdyenSofort() {
        AdyenPayment payment = AdyenPaymentMethodFactory.createPayment(PaymentMethod.ADYEN_SOFORT, false);
        assertTrue(payment instanceof AdyenBankPayment);
    }

    @Test
    public void testCreatePayment_AdyenPayPal() {
        AdyenPayment payment = AdyenPaymentMethodFactory.createPayment(PaymentMethod.ADYEN_PAYPAL, false);
        assertTrue(payment instanceof AdyenBankPayment);
    }

    @Test
    public void testCreatePayment_AdyenBCMC_True() {
        AdyenPayment payment = AdyenPaymentMethodFactory.createPayment(PaymentMethod.ADYEN_BCMC, true);
        assertTrue(payment instanceof AdyenBankPayment);
    }
    @Test
    public void testCreatePayment_AdyenBCMC_False() {
        AdyenPayment payment = AdyenPaymentMethodFactory.createPayment(PaymentMethod.ADYEN_BCMC, false);
        assertTrue(payment instanceof AdyenBankPayment);
    }


    @Test
    public void testCreatePayment_AdyenAfterpay() {
        AdyenPayment payment = AdyenPaymentMethodFactory.createPayment(PaymentMethod.ADYEN_AFTERPAY, false);
        assertTrue(payment instanceof AdyenCardPayment);
    }

    @Test
    public void testCreatePayment_AdyenRatepay() {
        AdyenPayment payment = AdyenPaymentMethodFactory.createPayment(PaymentMethod.ADYEN_RATEPAY, false);
        assertTrue(payment instanceof AdyenCardPayment);
    }

    @Test
    public void testCreatePayment_AdyenKlarna() {
        AdyenPayment payment = AdyenPaymentMethodFactory.createPayment(PaymentMethod.ADYEN_KLARNA, false);
        assertTrue(payment instanceof AdyenCardPayment);
    }

    @Test
    public void testCreatePayment_InvalidPaymentMethod() {
        assertThrows(IllegalArgumentException.class, () -> {
            AdyenPaymentMethodFactory.createPayment(null, false);
        });
    }

    @Test
    public void testCreatePayment_UnexpectedPaymentMethod() {
        assertThrows(IllegalArgumentException.class, () -> {
            AdyenPaymentMethodFactory.createPayment(PaymentMethod.ESW, false);
        });
    }
}