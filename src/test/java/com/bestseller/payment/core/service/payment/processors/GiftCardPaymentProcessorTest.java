package com.bestseller.payment.core.service.payment.processors;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.Payment;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.enumeration.PaymentMethod;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import com.bestseller.payment.core.exception.PaymentValidationException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class GiftCardPaymentProcessorTest {

    @InjectMocks
    private GiftCardPaymentProcessor giftCardPaymentProcessor;

    @Test
    public void createPayment_ReturnsGiftcardPayment() {
        PaymentMethod paymentMethod = PaymentMethod.OC_GIFTCARD;
        String subMethod = "Opticard";
        String billingCountryCode = "US";
        boolean isBcmc = false;

        PaymentInfo paymentInfo = giftCardPaymentProcessor.createPayment(
                paymentMethod, isBcmc, subMethod, billingCountryCode);

        assertEquals(PaymentType.GIFTCARD, paymentInfo.getType());
        assertEquals(ProcessorId.OPTICARD, paymentInfo.getProcessorId());
        assertEquals(subMethod, paymentInfo.getSubMethod());
    }

    @Test
    public void getProcessorId_ReturnsGiftCardProcessorId() {
        ProcessorId processorId = giftCardPaymentProcessor.getProcessorId();
        assertEquals(ProcessorId.OPTICARD, processorId);
    }

    @Test
    public void testValidate_success() {
        // Arrange
        Payment payment = new Payment()
                .withProvider("OPTICARD")
                .withMethod("OC_GIFTCARD")
                .withGiftCardNumber("gc-number-0001")
                .withPspReference("gc-number");
        String billingCountryCode = "NL";

        // Act and Assert
        assertDoesNotThrow(() -> giftCardPaymentProcessor.validate(payment, billingCountryCode));
    }

    @Test
    public void testValidate_error() {
        // Arrange
        Payment payment = new Payment()
                .withProvider("OPTICARD")
                .withMethod("OC_GIFTCARD")
                .withGiftCardNumber("");
        String billingCountryCode = "NL";

        // Act and Assert
        Assertions.assertThrows(PaymentValidationException.class, () -> giftCardPaymentProcessor.validate(payment, billingCountryCode));

        // arrange missing pspReference
        payment.setGiftCardNumber("gc-number");
        payment.setPspReference("");
        Assertions.assertThrows(PaymentValidationException.class, () -> giftCardPaymentProcessor.validate(payment, billingCountryCode));

    }
}

