package com.bestseller.payment.core.service.refund;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequest.PaymentRefundRequest;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundStatusUpdated.RefundStatusUpdated;
import com.bestseller.payment.adapter.stream.messagegenerator.BankPaymentRefundRequestGenerator;
import com.bestseller.payment.adapter.stream.messagegenerator.GiftCardPaymentRefundRequestGenerator;
import com.bestseller.payment.adapter.stream.messagegenerator.RefundStatusUpdatedGenerator;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderCharge;
import com.bestseller.payment.core.domain.OrderEntryAmount;
import com.bestseller.payment.core.domain.OverallTotal;
import com.bestseller.payment.core.domain.Refund;
import com.bestseller.payment.core.domain.RefundLine;
import com.bestseller.payment.core.domain.enumeration.ChargedRefundReason;
import com.bestseller.payment.core.domain.enumeration.EntryType;
import com.bestseller.payment.core.dto.OrderItemToRefund;
import com.bestseller.payment.core.dto.RefundCalculationResult;
import com.bestseller.payment.core.dto.RefundOptions;
import com.bestseller.payment.core.dto.RefundReason;
import com.bestseller.payment.core.dto.ReturnFeeCalculationResult;
import com.bestseller.payment.core.exception.DoubleRefundException;
import com.bestseller.payment.core.exception.OrderNotFoundException;
import com.bestseller.payment.core.service.order.OrderService;
import com.bestseller.payment.core.service.price.PriceService;
import com.bestseller.payment.utils.OrderGenerator;
import com.logistics.statetransition.RefundState;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import static com.bestseller.payment.utils.OrderGenerator.ORDER_LINE_DISCOUNT_AMOUNT;
import static com.bestseller.payment.utils.OrderGenerator.ORDER_LINE_PRICE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class EcomRefundServiceTest {

    @Spy
    @InjectMocks
    EcomRefundServiceImpl refundService;

    @Mock
    RefundCalculationService refundCalculationService;

    @Mock
    ReturnFeeService returnFeeService;

    @Mock
    OrderService orderService;

    @Mock
    PriceService priceService;

    @Mock
    QueueProducer<PaymentRefundRequest> paymentRefundRequestProducerQueueProducer;

    @Mock
    QueueProducer<RefundStatusUpdated> refundStatusUpdatedProducerQueueProducer;

    @Mock
    RefundStatusUpdatedGenerator refundStatusUpdatedGenerator;

    @Mock
    GiftCardPaymentRefundRequestGenerator giftCardPaymentRefundRequestGenerator;

    @Mock
    BankPaymentRefundRequestGenerator bankPaymentRefundRequestGenerator;

    /**
     * Mockito does not differentiate between generic types due to type erasure in Java.
     * This means both QueueProducer&lt;PaymentRefundRequest&gt; and QueueProducer&lt;RefundStatusUpdated&gt; are seen
     * as the same QueueProducer class at runtime.
     * To fix it two distinct mock objects are defined and injected explicitly into test class.
     */
    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(
            refundService,
            "paymentRefundRequestProducerQueueProducer",
            paymentRefundRequestProducerQueueProducer
        );
        ReflectionTestUtils.setField(
            refundService,
            "refundStatusUpdatedProducerQueueProducer",
            refundStatusUpdatedProducerQueueProducer
        );
    }

    @Test
    void test_refund_orderNotFound() {
        // Arrange
        String orderId = "orderId";
        when(orderService.getOrderById(orderId)).thenThrow(new OrderNotFoundException(orderId));

        // Act & Assert
        OrderNotFoundException exception = assertThrows(
            OrderNotFoundException.class,
            () -> refundService.refund(orderId, List.of(), RefundOptions.builder().build(), UUID.randomUUID().toString())
        );

        // Assert
        assert exception.getMessage().contains(orderId);
    }

    @Test
    void test_refund_mixedPayment_chargeReturnFee_refundShippingFee() {
        // Arrange
        RefundOptions refundOptions = RefundOptions.builder()
            .refundShippingFee(true)
            .chargeReturnFee(true)
            .refundReason(RefundReason.RETURN)
            .build();
        String requestId = UUID.randomUUID().toString();
        Order order = OrderGenerator.createOrder();
        List<OrderItemToRefund> itemToRefundList = List.of(
            OrderItemToRefund.builder()
                .ean(order.getOrderLines().get(0).getEan())
                .quantity(order.getOrderLines().get(0).getOpenQty())
                .build()
        );

        BigDecimal returnFee = BigDecimal.TEN;
        BigDecimal taxRate = BigDecimal.ONE;
        BigDecimal refundAmount = new BigDecimal("4.99");
        int amountToRefundByBankTransactionInCents = 100;
        int amountToRefundByGiftCardInCents = 200;

        when(orderService.getOrderById(order.getOrderId())).thenReturn(order);
        doAnswer(invocation -> {
            Refund refundArg = invocation.getArgument(0);
            OverallTotal refundTotal = OverallTotal.builder()
                .grossDiscountedTotal(refundAmount)
                .build();
            refundArg.setRefundTotal(refundTotal);  // Modify the captured Refund object
            return null;
        }).when(priceService).processAndSetPriceFieldsForRefundLineAndRefundTotalPaidAmount(any(Refund.class));
        when(returnFeeService.calculateReturnFee(
            order.getOrderDate(),
            order.getShippingCountryCode(),
            order.getBrand().getBrandAbbreviation(),
            refundAmount

        )).thenReturn(ReturnFeeCalculationResult.builder()
            .returnFee(returnFee)
            .taxRate(taxRate)
            .build());

        when(refundCalculationService.calculate(order, RefundReason.RETURN)).thenReturn(
            RefundCalculationResult.builder()
                .amountToRefundByBankTransactionInCents(amountToRefundByBankTransactionInCents)
                .amountToRefundByGiftCardInCents(amountToRefundByGiftCardInCents)
                .build());
        when(returnFeeService.createReturnFeeCharge(returnFee, taxRate)).thenReturn(OrderCharge.builder()
            .type(EntryType.RETURN_FEE)
            .build());

        when(giftCardPaymentRefundRequestGenerator.generate(any())).thenReturn(new PaymentRefundRequest());
        when(bankPaymentRefundRequestGenerator.generate(any())).thenReturn(new PaymentRefundRequest());
        when(refundStatusUpdatedGenerator.generate(any())).thenReturn(new RefundStatusUpdated());
        when(orderService.save(order, true)).thenReturn(order);

        // Act
        refundService.refund(order.getOrderId(), itemToRefundList, refundOptions, requestId);

        // Assert
        assert order.getRefunds().size() == 1;
        assert order.getOrderCharges().size() == 2;

        Refund refund = order.getRefunds().get(0);

        assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());

        order.getOrderCharges()
            .stream()
            .filter(orderCharge -> orderCharge.getType().equals(EntryType.SHIPMENT_FEE))
            .forEach(orderCharge -> {
                assertTrue(orderCharge.getRefunded());
            });
        verify(returnFeeService).calculateReturnFee(
            order.getOrderDate(),
            order.getShippingCountryCode(),
            order.getBrand().getBrandAbbreviation(),
            refundAmount
        );
        verify(returnFeeService).createReturnFeeCharge(returnFee, taxRate);
        verify(priceService, times(2)).processAndSetPriceFieldsForRefundLineAndRefundTotalPaidAmount(any());
        verify(orderService, times(2)).save(order, true);
        verify(refundCalculationService).calculate(order, RefundReason.RETURN);
        verify(paymentRefundRequestProducerQueueProducer, times(2)).enqueue(any());
        verify(refundStatusUpdatedProducerQueueProducer, times(2)).enqueue(any());
    }

    @Test
    void test_refund_mixedPayment_chargeReturnFee_aboveCountryThreshold_noRefundShippingFee() {
        // Arrange
        RefundOptions refundOptions = RefundOptions.builder()
            .refundShippingFee(true)
            .chargeReturnFee(true)
            .refundReason(RefundReason.RETURN)
            .build();
        String requestId = UUID.randomUUID().toString();
        Order order = OrderGenerator.createOrder();
        List<OrderItemToRefund> itemToRefundList = List.of(
            OrderItemToRefund.builder()
                .ean(order.getOrderLines().get(0).getEan())
                .quantity(order.getOrderLines().get(0).getOpenQty())
                .build()
        );

        BigDecimal returnFee = BigDecimal.ZERO;
        BigDecimal taxRate = BigDecimal.ZERO;
        BigDecimal refundAmount = new BigDecimal("49.99");
        int amountToRefundByBankTransactionInCents = 100;
        int amountToRefundByGiftCardInCents = 200;

        when(orderService.getOrderById(order.getOrderId())).thenReturn(order);
        doAnswer(invocation -> {
            Refund refundArg = invocation.getArgument(0);
            OverallTotal refundTotal = OverallTotal.builder()
                .grossDiscountedTotal(refundAmount)
                .build();
            refundArg.setRefundTotal(refundTotal);  // Modify the captured Refund object
            return null;
        }).when(priceService).processAndSetPriceFieldsForRefundLineAndRefundTotalPaidAmount(any(Refund.class));
        when(returnFeeService.calculateReturnFee(
            order.getOrderDate(),
            order.getShippingCountryCode(),
            order.getBrand().getBrandAbbreviation(),
            refundAmount

        )).thenReturn(ReturnFeeCalculationResult.builder()
            .returnFee(returnFee)
            .taxRate(taxRate)
            .build());

        when(refundCalculationService.calculate(order, RefundReason.RETURN)).thenReturn(
            RefundCalculationResult.builder()
                .amountToRefundByBankTransactionInCents(amountToRefundByBankTransactionInCents)
                .amountToRefundByGiftCardInCents(amountToRefundByGiftCardInCents)
                .build());

        when(giftCardPaymentRefundRequestGenerator.generate(any())).thenReturn(new PaymentRefundRequest());
        when(bankPaymentRefundRequestGenerator.generate(any())).thenReturn(new PaymentRefundRequest());
        when(refundStatusUpdatedGenerator.generate(any())).thenReturn(new RefundStatusUpdated());
        when(orderService.save(order, true)).thenReturn(order);

        // Act
        refundService.refund(order.getOrderId(), itemToRefundList, refundOptions, requestId);

        // Assert
        assert order.getRefunds().size() == 1;
        assert order.getOrderCharges().size() == 1;

        Refund refund = order.getRefunds().get(0);

        assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());

        order.getOrderCharges()
            .stream()
            .filter(orderCharge -> orderCharge.getType().equals(EntryType.SHIPMENT_FEE))
            .forEach(orderCharge -> {
                assertTrue(orderCharge.getRefunded());
            });
        verify(returnFeeService).calculateReturnFee(
            order.getOrderDate(),
            order.getShippingCountryCode(),
            order.getBrand().getBrandAbbreviation(),
            refundAmount
        );
        verify(returnFeeService, never()).createReturnFeeCharge(returnFee, taxRate);
        verify(priceService, times(2)).processAndSetPriceFieldsForRefundLineAndRefundTotalPaidAmount(any());
        verify(orderService, times(2)).save(order, true);
        verify(refundCalculationService).calculate(order, RefundReason.RETURN);
        verify(paymentRefundRequestProducerQueueProducer, times(2)).enqueue(any());
        verify(refundStatusUpdatedProducerQueueProducer, times(2)).enqueue(any());
    }

    @Test
    void test_saving_refund_with_a_refundReason() {
        // Arrange
        RefundOptions refundOptions = RefundOptions.builder()
            .refundShippingFee(true)
            .chargeReturnFee(true)
            .refundReason(RefundReason.RETURN)
            .build();
        String requestId = UUID.randomUUID().toString();
        Order order = OrderGenerator.createOrder();
        List<OrderItemToRefund> itemToRefundList = List.of(
            OrderItemToRefund.builder()
                .ean(order.getOrderLines().get(0).getEan())
                .quantity(order.getOrderLines().get(0).getOpenQty())
                .build()
        );

        BigDecimal returnFee = BigDecimal.TEN;
        BigDecimal taxRate = BigDecimal.ONE;
        BigDecimal refundAmount = new BigDecimal("4.99");
        int amountToRefundByBankTransactionInCents = 100;
        int amountToRefundByGiftCardInCents = 200;

        when(orderService.getOrderById(order.getOrderId())).thenReturn(order);
        doAnswer(invocation -> {
            Refund refundArg = invocation.getArgument(0);
            OverallTotal refundTotal = OverallTotal.builder()
                .grossDiscountedTotal(refundAmount)
                .build();
            refundArg.setRefundTotal(refundTotal);  // Modify the captured Refund object
            return null;
        }).when(priceService).processAndSetPriceFieldsForRefundLineAndRefundTotalPaidAmount(any(Refund.class));
        when(returnFeeService.calculateReturnFee(
            order.getOrderDate(),
            order.getShippingCountryCode(),
            order.getBrand().getBrandAbbreviation(),
            refundAmount
        )).thenReturn(ReturnFeeCalculationResult.builder()
            .returnFee(returnFee)
            .taxRate(taxRate)
            .build());

        when(refundCalculationService.calculate(order, RefundReason.RETURN)).thenReturn(
            RefundCalculationResult.builder()
                .amountToRefundByBankTransactionInCents(amountToRefundByBankTransactionInCents)
                .amountToRefundByGiftCardInCents(amountToRefundByGiftCardInCents)
                .build());

        when(returnFeeService.createReturnFeeCharge(returnFee, taxRate)).thenReturn(OrderCharge.builder()
            .type(EntryType.RETURN_FEE)
            .build());

        when(giftCardPaymentRefundRequestGenerator.generate(any())).thenReturn(new PaymentRefundRequest());
        when(bankPaymentRefundRequestGenerator.generate(any())).thenReturn(new PaymentRefundRequest());
        when(refundStatusUpdatedGenerator.generate(any())).thenReturn(new RefundStatusUpdated());
        when(orderService.save(order, true)).thenReturn(order);

        // Act
        refundService.refund(order.getOrderId(), itemToRefundList, refundOptions, requestId);

        // Assert
        assertEquals(1, order.getRefunds().size());
        assertEquals(2, order.getOrderCharges().size());

        Refund refund = order.getRefunds().get(0);

        assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());

        order.getOrderCharges()
            .stream()
            .filter(orderCharge -> orderCharge.getType().equals(EntryType.SHIPMENT_FEE))
            .forEach(orderCharge -> {
                assertTrue(orderCharge.getRefunded());
                assertEquals(ChargedRefundReason.SYSTEM, orderCharge.getRefundReason());
            });
        verify(returnFeeService).createReturnFeeCharge(returnFee, taxRate);
    }

    @Test
    void test_refund_mixedPayment_notChargeReturnFee_notRefundShippingFee() {
        // Arrange
        RefundOptions refundOptions = RefundOptions.builder()
            .refundShippingFee(false)
            .chargeReturnFee(false)
            .refundReason(RefundReason.RETURN)
            .build();
        String requestId = UUID.randomUUID().toString();
        Order order = OrderGenerator.createOrder();
        List<OrderItemToRefund> itemToRefundList = List.of(
            OrderItemToRefund.builder()
                .ean(order.getOrderLines().get(0).getEan())
                .quantity(order.getOrderLines().get(0).getOpenQty())
                .build()
        );

        int amountToRefundByBankTransactionInCents = 100;
        int amountToRefundByGiftCardInCents = 200;

        when(orderService.getOrderById(order.getOrderId())).thenReturn(order);

        when(refundCalculationService.calculate(order, RefundReason.RETURN)).thenReturn(
            RefundCalculationResult.builder()
                .amountToRefundByBankTransactionInCents(amountToRefundByBankTransactionInCents)
                .amountToRefundByGiftCardInCents(amountToRefundByGiftCardInCents)
                .build());


        when(giftCardPaymentRefundRequestGenerator.generate(any())).thenReturn(new PaymentRefundRequest());
        when(bankPaymentRefundRequestGenerator.generate(any())).thenReturn(new PaymentRefundRequest());
        when(refundStatusUpdatedGenerator.generate(any())).thenReturn(new RefundStatusUpdated());
        when(orderService.save(order, true)).thenReturn(order);

        // Act
        refundService.refund(order.getOrderId(), itemToRefundList, refundOptions, requestId);

        // Assert
        assert order.getRefunds().size() == 1;
        assert order.getOrderCharges().size() == 1;

        Refund refund = order.getRefunds().get(0);

        assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());

        verify(priceService, times(2)).processAndSetPriceFieldsForRefundLineAndRefundTotalPaidAmount(any());
        verify(orderService, times(2)).save(order, true);
        verify(refundCalculationService).calculate(order, RefundReason.RETURN);
        verify(paymentRefundRequestProducerQueueProducer, times(2)).enqueue(any());
        verify(refundStatusUpdatedProducerQueueProducer, times(2)).enqueue(any());
        verify(returnFeeService, never()).calculateReturnFee(any(), any(), any(), any());
        verify(returnFeeService, never()).createReturnFeeCharge(any(), any());
    }

    @Test
    void test_refund_giftCardPayment() {
        // Arrange
        RefundOptions refundOptions = RefundOptions.builder()
            .refundShippingFee(false)
            .chargeReturnFee(false)
            .refundReason(RefundReason.RETURN)
            .build();
        String requestId = UUID.randomUUID().toString();
        Order order = OrderGenerator.createOrder();
        List<OrderItemToRefund> itemToRefundList = List.of(
            OrderItemToRefund.builder()
                .ean(order.getOrderLines().get(0).getEan())
                .quantity(order.getOrderLines().get(0).getOpenQty())
                .build()
        );

        int amountToRefundByBankTransactionInCents = 0;
        int amountToRefundByGiftCardInCents = 200;

        when(orderService.getOrderById(order.getOrderId())).thenReturn(order);

        when(refundCalculationService.calculate(order, RefundReason.RETURN)).thenReturn(
            RefundCalculationResult.builder()
                .amountToRefundByBankTransactionInCents(amountToRefundByBankTransactionInCents)
                .amountToRefundByGiftCardInCents(amountToRefundByGiftCardInCents)
                .build());


        when(giftCardPaymentRefundRequestGenerator.generate(any())).thenReturn(new PaymentRefundRequest());
        when(orderService.save(order, true)).thenReturn(order);

        // Act
        refundService.refund(order.getOrderId(), itemToRefundList, refundOptions, requestId);

        // Assert
        assert order.getRefunds().size() == 1;

        Refund refund = order.getRefunds().get(0);

        assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());

        verify(priceService, times(2)).processAndSetPriceFieldsForRefundLineAndRefundTotalPaidAmount(any());
        verify(orderService, times(2)).save(order, true);
        verify(refundCalculationService).calculate(order, RefundReason.RETURN);
        verify(paymentRefundRequestProducerQueueProducer).enqueue(any());
        verify(refundStatusUpdatedProducerQueueProducer, never()).enqueue(any());
        verify(returnFeeService, never()).calculateReturnFee(any(), any(), any(), any());
        verify(returnFeeService, never()).createReturnFeeCharge(any(), any());
    }

    @Test
    void test_refund_bankOrCardPayment() {
        // Arrange
        RefundOptions refundOptions = RefundOptions.builder()
            .refundShippingFee(false)
            .chargeReturnFee(false)
            .refundReason(RefundReason.RETURN)
            .build();
        String requestId = UUID.randomUUID().toString();
        Order order = OrderGenerator.createOrder();
        List<OrderItemToRefund> itemToRefundList = List.of(
            OrderItemToRefund.builder()
                .ean(order.getOrderLines().get(0).getEan())
                .quantity(order.getOrderLines().get(0).getOpenQty())
                .build()
        );

        int amountToRefundByBankTransactionInCents = 100;
        int amountToRefundByGiftCardInCents = 0;

        when(orderService.getOrderById(order.getOrderId())).thenReturn(order);

        when(refundCalculationService.calculate(order, RefundReason.RETURN)).thenReturn(
            RefundCalculationResult.builder()
                .amountToRefundByBankTransactionInCents(amountToRefundByBankTransactionInCents)
                .amountToRefundByGiftCardInCents(amountToRefundByGiftCardInCents)
                .build());


        when(bankPaymentRefundRequestGenerator.generate(any())).thenReturn(new PaymentRefundRequest());
        when(refundStatusUpdatedGenerator.generate(any())).thenReturn(new RefundStatusUpdated());
        when(orderService.save(order, true)).thenReturn(order);

        // Act
        refundService.refund(order.getOrderId(), itemToRefundList, refundOptions, requestId);

        // Assert
        assert order.getRefunds().size() == 1;

        Refund refund = order.getRefunds().get(0);

        assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());

        verify(priceService, times(2)).processAndSetPriceFieldsForRefundLineAndRefundTotalPaidAmount(any());
        verify(orderService, times(2)).save(order, true);
        verify(refundCalculationService).calculate(order, RefundReason.RETURN);
        verify(paymentRefundRequestProducerQueueProducer, times(1)).enqueue(any());
        verify(refundStatusUpdatedProducerQueueProducer, times(2)).enqueue(any());
        verify(returnFeeService, never()).calculateReturnFee(any(), any(), any(), any());
        verify(returnFeeService, never()).createReturnFeeCharge(any(), any());
    }

    @Test
    void test_createRefundObject() {
        // Arrange
        String requestId = UUID.randomUUID().toString();
        Order order = OrderGenerator.createOrder();
        List<OrderItemToRefund> itemToRefundList = List.of(
            OrderItemToRefund.builder()
                .ean(order.getOrderLines().get(0).getEan())
                .quantity(order.getOrderLines().get(0).getOpenQty())
                .build()
        );

        // Act
        Refund refund = refundService.createRefundObject(order, itemToRefundList, requestId, BigDecimal.ZERO,
            RefundOptions.builder().refundReason(RefundReason.RETURN).build());

        // Assert
        assert refund.getRefundLines().size() == 1;
        assert refund.getRefundState().equals(RefundState.CREATED);
        assert refund.getOrder().equals(order);
        assert refund.getCsrInitials().equals("SYSTEM");
        assert refund.getRequestId().equals(requestId);
        assert refund.getRefundId() == null;
        assert refund.getRefundLines().get(0).getQuantity().equals(order.getOrderLines().get(0).getOpenQty());
        assert refund.getRefundLines().get(0).getOrderLine().equals(order.getOrderLines().get(0));
        assert refund.getRefundLines().get(0).getLineNumber().equals(String.valueOf(order.getOrderLines().get(0).getLineNumber()));
    }

    @Test
    void test_createRefundObject_doubleRefund() {
        String requestId = UUID.randomUUID().toString();
        Order order = OrderGenerator.createOrder();
        List<OrderItemToRefund> itemToRefundList = List.of(
            OrderItemToRefund.builder()
                .ean(order.getOrderLines().get(0).getEan())
                .quantity(order.getOrderLines().get(0).getOpenQty())
                .build()
        );

        // Act & Assert
        var refundOptions = RefundOptions.builder().refundReason(RefundReason.RETURN).build();
        Refund refund = refundService.createRefundObject(order, itemToRefundList, requestId, BigDecimal.ZERO,
            refundOptions);
        order.getRefunds().add(refund);
        assertThrows(DoubleRefundException.class, () -> refundService.createRefundObject(order, itemToRefundList,
            requestId, BigDecimal.ZERO, refundOptions));
    }

    @Test
    void test_createRefundObject_givenPreviouslyCancelledItem_refundReturn() {
        // Arrange
        String requestId = UUID.randomUUID().toString();
        Order order = OrderGenerator.createAdyenOrder();
        order.getOrderLines().get(0).setOpenQty(1);
        order.getOrderLines().get(0).setOriginalQty(2);
        List<OrderItemToRefund> itemToRefundList = List.of(
            OrderItemToRefund.builder()
                .ean(order.getOrderLines().get(0).getEan())
                .quantity(1)
                .build()
        );
        RefundLine refundLine = RefundLine.builder()
            .id(1)
            .lineNumber(order.getOrderLines().get(0).getLineNumber().toString())
            .quantity(1)
            .refundLineTotal(OrderEntryAmount.builder()
                .grossRetailUnitPrice(ORDER_LINE_PRICE)
                .grossDiscountedUnitPrice(ORDER_LINE_PRICE.subtract(ORDER_LINE_DISCOUNT_AMOUNT))
                .unitDiscount(ORDER_LINE_DISCOUNT_AMOUNT)
                .build())
            .orderLine(order.getOrderLines().get(0))
            .build();
        Refund cancellationRefund = Refund.builder()
            .refundId(UUID.randomUUID().toString())
            .refundState(RefundState.REFUND_SUCCESS)
            .refundLines(List.of(refundLine))
            .build();
        order.setRefunds(List.of(cancellationRefund));

        // Act
        // Return other item
        Refund refund = refundService.createRefundObject(order, itemToRefundList, requestId, BigDecimal.ZERO,
            RefundOptions.builder().refundReason(RefundReason.RETURN).build());
        // Assert
        assertThat(refund.getRefundLines().size()).isEqualTo(1);
        assertThat(refund.getRefundState()).isEqualTo(RefundState.CREATED);
        assertThat(refund.getOrder()).isEqualTo(order);
        assertThat(refund.getCsrInitials()).isEqualTo("SYSTEM");
        assertThat(refund.getRequestId()).isEqualTo(requestId);
        assertThat(refund.getRefundId()).isNull();
        assertThat(refund.getRefundLines().get(0).getQuantity())
            .isEqualTo(order.getOrderLines().get(0).getOpenQty());
        assertThat(refund.getRefundLines().get(0).getOrderLine())
            .isEqualTo(order.getOrderLines().get(0));
        assertThat(refund.getRefundLines().get(0).getLineNumber())
            .isEqualTo(String.valueOf(order.getOrderLines().get(0).getLineNumber()));
    }

    @Test
    void test_issueCancellationRefund_giftCardPayment_allCancelled() {
        // Arrange
        Order order = OrderGenerator.createOrder();
        order.getOrderLines()
            .forEach(orderLine -> orderLine.setOpenQty(0));
        order.getTotalPaidPrice()
            .setGrossDiscountedTotal(BigDecimal.ZERO);

        RefundOptions refundOptions = RefundOptions.builder()
            .refundShippingFee(true)
            .chargeReturnFee(false)
            .refundReason(RefundReason.CANCELLATION)
            .build();

        List<OrderItemToRefund> itemToRefundList = order.getOrderLines()
            .stream()
            .map(orderLine -> OrderItemToRefund.builder()
                .ean(orderLine.getEan())
                .quantity(orderLine.getOriginalQty())
                .build())
            .toList();

        ArgumentCaptor<RefundOptions> refundOptionsArgumentCaptor = ArgumentCaptor.forClass(RefundOptions.class);
        ArgumentCaptor<List<OrderItemToRefund>> itemToRefundListArgumentCaptor = ArgumentCaptor.forClass(List.class);

        doNothing().when(refundService).refund(any(), any(), any(), any());

        // Act
        refundService.issueCancellationRefund(order);

        // Assert
        verify(refundService).refund(
            eq(order.getOrderId()),
            itemToRefundListArgumentCaptor.capture(),
            refundOptionsArgumentCaptor.capture(),
            eq(null));

        assertThat(refundOptionsArgumentCaptor.getValue())
            .usingRecursiveComparison()
            .isEqualTo(refundOptions);

        assertThat(itemToRefundListArgumentCaptor.getValue())
            .usingRecursiveComparison()
            .isEqualTo(itemToRefundList);
        verify(returnFeeService, never()).calculateReturnFee(any(), any(), any(), any());
        verify(returnFeeService, never()).createReturnFeeCharge(any(), any());

    }

    @Test
    void test_issueCancellationRefund_giftCardPayment_noneCancelled() {
        // Arrange
        Order order = OrderGenerator.createOrder();

        // Act
        refundService.issueCancellationRefund(order);

        // Assert
        verify(refundService, never()).refund(any(), any(), any(), any());
    }

    @Test
    void test_issueCancellationRefund_giftCardPayment_partiallyCancelled_lessThanBankAuthorisedAmount() {
        // Arrange
        Order order = OrderGenerator.createOrder(true);
        order.getOrderLines()
            .stream()
            .skip(1)
            .forEach(orderLine -> orderLine.setOpenQty(0));

        order.getTotalPaidPrice()
            .setGrossDiscountedTotal(order
                .getOrderLines()
                .get(0)
                .getOrderLinePaidAmount()
                .getGrossDiscountedTotal());

        RefundOptions refundOptions = RefundOptions.builder()
            .refundShippingFee(false)
            .chargeReturnFee(false)
            .refundReason(RefundReason.CANCELLATION)
            .build();
        List<OrderItemToRefund> itemToRefundList = order.getOrderLines()
            .stream()
            .skip(1)
            .map(orderLine -> OrderItemToRefund.builder()
                .ean(orderLine.getEan())
                .quantity(orderLine.getOriginalQty())
                .build())
            .toList();

        ArgumentCaptor<RefundOptions> refundOptionsArgumentCaptor = ArgumentCaptor.forClass(RefundOptions.class);
        ArgumentCaptor<List<OrderItemToRefund>> itemToRefundListArgumentCaptor = ArgumentCaptor.forClass(List.class);

        doNothing().when(refundService).refund(any(), any(), any(), any());

        // Act
        refundService.issueCancellationRefund(order);

        // Assert
        verify(refundService).refund(
            eq(order.getOrderId()),
            itemToRefundListArgumentCaptor.capture(),
            refundOptionsArgumentCaptor.capture(),
            eq(null));

        assertThat(refundOptionsArgumentCaptor.getValue())
            .usingRecursiveComparison()
            .isEqualTo(refundOptions);

        assertThat(itemToRefundListArgumentCaptor.getValue())
            .usingRecursiveComparison()
            .isEqualTo(itemToRefundList);
        verify(returnFeeService, never()).calculateReturnFee(any(), any(), any(), any());
        verify(returnFeeService, never()).createReturnFeeCharge(any(), any());
    }

    @Test
    void test_issueCancellationRefund_giftCardPayment_partiallyCancelled_moreThanBankAuthorisedAmount() {
        // Arrange
        Order order = OrderGenerator.createOrder();
        order.getOrderLines()
            .stream()
            .skip(1)
            .forEach(orderLine -> orderLine.setOpenQty(0));

        order.getTotalPaidPrice()
            .setGrossDiscountedTotal(order
                .getOrderLines()
                .get(0)
                .getOrderLinePaidAmount()
                .getGrossDiscountedTotal());

        ArgumentCaptor<RefundOptions> refundOptionsArgumentCaptor = ArgumentCaptor.forClass(RefundOptions.class);
        ArgumentCaptor<List<OrderItemToRefund>> itemToRefundListArgumentCaptor = ArgumentCaptor.forClass(List.class);

        // Act
        refundService.issueCancellationRefund(order);

        // Assert
        verify(refundService, never()).refund(
            eq(order.getOrderId()),
            itemToRefundListArgumentCaptor.capture(),
            refundOptionsArgumentCaptor.capture(),
            eq(null));
    }

    @Test
    void test_issueCancellationRefund_giftCardPayment_cancellationRefundAlreadyIssued() {
        // Arrange
        Order order = OrderGenerator.createOrder();
        order.getRefunds().add(Refund.builder()
            .requestId(null)
            .build());

        // Act
        refundService.issueCancellationRefund(order);

        // Assert
        verify(refundService, never()).refund(any(), any(), any(), any());
    }
}
