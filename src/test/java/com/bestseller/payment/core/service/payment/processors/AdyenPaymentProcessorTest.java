package com.bestseller.payment.core.service.payment.processors;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.Payment;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.enumeration.PaymentMethod;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import com.bestseller.payment.core.domain.payment.AdyenBankPayment;
import com.bestseller.payment.core.domain.payment.AdyenCardPayment;
import com.bestseller.payment.core.exception.PaymentValidationException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;


@ExtendWith(MockitoExtension.class)
class AdyenPaymentProcessorTest {

    @InjectMocks
    private AdyenPaymentProcessor adyenPaymentProcessor;

    @ParameterizedTest
    @MethodSource("paymentMethodProvider")
    public void testCreatePayment_AdyenCard(PaymentMethod paymentMethod) {
        // Arrange
        String pspReference = "12345";
        boolean isBcmc = true;
        String subMethod = "Visa";
        String billingCountryCode = "US";

        // Act
        PaymentInfo payment = adyenPaymentProcessor.createPayment(paymentMethod, isBcmc, subMethod, billingCountryCode);

        // Assert
        assertNotNull(payment);
        assertTrue(payment instanceof AdyenBankPayment);
        assertEquals(ProcessorId.ADYEN, payment.getProcessorId());
        assertEquals(subMethod, payment.getSubMethod());
        assertEquals(PaymentType.ADYEN_BANK, payment.getType());
    }

    @Test
    public void testCreatePayment_AdyenCard_BCMC_false() {
        // Arrange
        String pspReference = "12345";
        boolean isBcmc = false;
        String subMethod = "Visa";
        String billingCountryCode = "US";

        // Act
        PaymentInfo payment = adyenPaymentProcessor.createPayment(PaymentMethod.ADYEN_CREDIT_CARD, isBcmc, subMethod, billingCountryCode);

        // Assert
        assertNotNull(payment);
        assertTrue(payment instanceof AdyenCardPayment);
        assertEquals(ProcessorId.ADYEN, payment.getProcessorId());
        assertEquals(subMethod, payment.getSubMethod());
        assertEquals(PaymentType.ADYEN_CARD, payment.getType());
    }

    @ParameterizedTest
    @MethodSource("getBCMC")
    public void testCreatePayment_AdyenCard_BCMC_False(boolean isBcmc) {
        // Arrange
        PaymentMethod paymentMethod = PaymentMethod.ADYEN_BCMC;
        String pspReference = "12345";
        String subMethod = "Visa";
        String billingCountryCode = "US";

        // Act
        PaymentInfo payment = adyenPaymentProcessor.createPayment(paymentMethod, isBcmc, subMethod, billingCountryCode);

        // Assert
        assertNotNull(payment);
        assertTrue(payment instanceof AdyenBankPayment);
        assertEquals(ProcessorId.ADYEN, payment.getProcessorId());
        assertEquals(subMethod, payment.getSubMethod());
        assertEquals(PaymentType.ADYEN_BANK, payment.getType());
    }

    @ParameterizedTest
    @MethodSource("paymentMethodProviderPay")
    public void testCreatePayment_AdyenCard_Pay(PaymentMethod paymentMethod) {
        // Arrange
        String pspReference = "12345";
        boolean isBcmc = false;
        String subMethod = "Visa";
        String billingCountryCode = "US";

        // Act
        PaymentInfo payment = adyenPaymentProcessor.createPayment(paymentMethod, isBcmc, subMethod, billingCountryCode);

        // Assert
        assertNotNull(payment);
        assertTrue(payment instanceof AdyenCardPayment);
        assertEquals(ProcessorId.ADYEN, payment.getProcessorId());
        assertEquals(subMethod, payment.getSubMethod());
        assertEquals(PaymentType.ADYEN_CARD, payment.getType());
    }

    @Test
    public void testValidate_success() {
        // Arrange
        Payment payment = new Payment().withPspReference("psp-reference").withMethod("ADYEN_IDEAL");
        String billingCountryCode = "NL";

        // Act and Assert
        assertDoesNotThrow(() -> adyenPaymentProcessor.validate(payment, billingCountryCode));
    }

    @Test
    public void testValidate_error() {
        // Arrange
        Payment payment = new Payment().withProvider("KLARNA_PAYMENTS").withMethod("KLARNA_ACCOUNT");
        String billingCountryCode = "NL";

        // Act and Assert
        Assertions.assertThrows(PaymentValidationException.class, () -> adyenPaymentProcessor.validate(payment, billingCountryCode));
    }

    private static Stream<PaymentMethod> paymentMethodProvider() {
        // Provide the different PaymentState values you want to test
        return Stream.of(PaymentMethod.ADYEN_CREDIT_CARD, PaymentMethod.ADYEN_IDEAL, PaymentMethod.ADYEN_SOFORT, PaymentMethod.ADYEN_PAYPAL, PaymentMethod.ADYEN_BCMC);
    }

    private static Stream<Boolean> getBCMC() {
        // Provide the different PaymentState values you want to test
        return Stream.of(true, false);
    }

    private static Stream<PaymentMethod> paymentMethodProviderPay() {
        // Provide the different PaymentState values you want to test
        return Stream.of(PaymentMethod.ADYEN_KLARNA, PaymentMethod.ADYEN_RATEPAY, PaymentMethod.ADYEN_AFTERPAY);
    }
}