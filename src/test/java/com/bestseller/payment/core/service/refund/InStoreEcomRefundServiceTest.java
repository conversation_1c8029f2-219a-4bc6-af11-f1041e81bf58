package com.bestseller.payment.core.service.refund;


import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inStoreReturnSettlement.InStoreReturnSettlement;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inStoreReturnSettlement.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequest.PaymentRefundRequest;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundStatusUpdated.RefundStatusUpdated;
import com.bestseller.payment.adapter.api.RefundOptionsService;
import com.bestseller.payment.adapter.stream.messagegenerator.BankPaymentRefundRequestGenerator;
import com.bestseller.payment.adapter.stream.messagegenerator.GiftCardPaymentRefundRequestGenerator;
import com.bestseller.payment.adapter.stream.messagegenerator.RefundStatusUpdatedGenerator;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderCharge;
import com.bestseller.payment.core.domain.OverallTotal;
import com.bestseller.payment.core.domain.Refund;
import com.bestseller.payment.core.domain.enumeration.ChargedRefundReason;
import com.bestseller.payment.core.domain.enumeration.EntryType;
import com.bestseller.payment.core.dto.OrderItemToRefund;
import com.bestseller.payment.core.dto.RefundCalculationResult;
import com.bestseller.payment.core.dto.RefundOptions;
import com.bestseller.payment.core.dto.RefundOptionsResponse;
import com.bestseller.payment.core.dto.RefundReason;
import com.bestseller.payment.core.dto.ReturnFeeCalculationResult;
import com.bestseller.payment.core.exception.OrderNotFoundException;
import com.bestseller.payment.core.exception.RefundOptionsProviderServiceNotAvailable;
import com.bestseller.payment.core.service.order.OrderService;
import com.bestseller.payment.core.service.price.PriceService;
import com.bestseller.payment.utils.OrderGenerator;
import com.logistics.statetransition.RefundState;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InStoreEcomRefundServiceTest {
    @Mock
    OrderService orderService;

    @Mock
    RefundOptionsService refundOptionsService;

    @Mock
    ReturnFeeService returnFeeService;

    @Mock
    RefundCalculationService refundCalculationService;

    @Mock
    PriceService priceService;

    @Mock
    QueueProducer<PaymentRefundRequest> paymentRefundRequestProducerQueueProducer;

    @Mock
    QueueProducer<RefundStatusUpdated> refundStatusUpdatedProducerQueueProducer;

    @Mock
    RefundStatusUpdatedGenerator refundStatusUpdatedGenerator;

    @Mock
    GiftCardPaymentRefundRequestGenerator giftCardPaymentRefundRequestGenerator;

    @Mock
    BankPaymentRefundRequestGenerator bankPaymentRefundRequestGenerator;

    @InjectMocks
    InStoreRefundServiceImpl inStoreRefundService;

    /**
     * Mockito does not differentiate between generic types due to type erasure in Java.
     * This means both QueueProducer&lt;PaymentRefundRequest&gt; and QueueProducer&lt;RefundStatusUpdated&gt; are seen
     * as the same QueueProducer class at runtime.
     * To fix it two distinct mock objects are defined and injected explicitly into test class.
     */
    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(
            inStoreRefundService,
            "paymentRefundRequestProducerQueueProducer",
            paymentRefundRequestProducerQueueProducer
        );
        ReflectionTestUtils.setField(
            inStoreRefundService,
            "refundStatusUpdatedProducerQueueProducer",
            refundStatusUpdatedProducerQueueProducer
        );
    }

    @Test
    void test_refund_shippingNotFound() {
        // Arrange
        String orderId = "orderId";

        // Act & Assert
        RefundOptionsProviderServiceNotAvailable exception = assertThrows(
            RefundOptionsProviderServiceNotAvailable.class,
            () -> inStoreRefundService.issueRefundForInStoreReturn(new InStoreReturnSettlement()
                .withTotalRefundAmount(BigDecimal.TEN)
                .withOrderId(orderId))
        );

        // Assert
        assert exception.getMessage().contains(orderId);
    }

    @Test
    void test_refund_orderNotFound() {
        // Arrange
        String orderId = "orderId";
        when(refundOptionsService.getRefundOptionsByOrderId(orderId)).thenReturn(Optional.of(RefundOptionsResponse.builder().build()));
        when(orderService.getOrderById(orderId)).thenThrow(new OrderNotFoundException(orderId));

        // Act & Assert
        OrderNotFoundException exception = assertThrows(
            OrderNotFoundException.class,
            () -> inStoreRefundService.issueRefundForInStoreReturn(new InStoreReturnSettlement()
                .withTotalRefundAmount(BigDecimal.TEN)
                .withOrderId(orderId))
        );

        // Assert
        assert exception.getMessage().contains(orderId);
    }

    @Test
    void test_refund_mixedPayment_chargeReturnFee_refundShippingFee() {
        // Arrange
        Long refundId = Long.valueOf(*********);

        Order order = OrderGenerator.createOrder();


        InStoreReturnSettlement inStoreReturnSettlement = new InStoreReturnSettlement()
            .withOrderId(order.getOrderId())
            .withRefundId(refundId)
            .withTotalRefundAmount(BigDecimal.TEN)
            .withOrderLines(List.of(
                new OrderLine().withEan(order.
                        getOrderLines().
                        get(0).
                        getEan()).
                    withQuantity(order.getOrderLines().
                        get(0).getOpenQty())
            ));

        BigDecimal returnFee = BigDecimal.TEN;
        BigDecimal taxRate = BigDecimal.ONE;
        int amountToRefundByBankTransactionInCents = 100;
        int amountToRefundByGiftCardInCents = 200;
        BigDecimal amountToRefund = new BigDecimal("4.99");

        when(refundOptionsService.getRefundOptionsByOrderId(order.getOrderId())).thenReturn(Optional.of(RefundOptionsResponse.builder()
            .chargeReturnFee(true).refundShippingFee(true).build()));

        when(orderService.getOrderById(order.getOrderId())).thenReturn(order);

        doAnswer(invocation -> {
            Refund refundArg = invocation.getArgument(0);
            OverallTotal refundTotal = OverallTotal.builder()
                .grossDiscountedTotal(amountToRefund)
                .build();
            refundArg.setRefundTotal(refundTotal);  // Modify the captured Refund object
            return null;
        }).when(priceService).processAndSetPriceFieldsForRefundLineAndRefundTotalPaidAmount(any(Refund.class));
        when(returnFeeService.calculateReturnFee(
            order.getOrderDate(),
            order.getShippingCountryCode(),
            order.getBrand().getBrandAbbreviation(),
            amountToRefund
        )).thenReturn(ReturnFeeCalculationResult.builder()
            .returnFee(returnFee)
            .taxRate(taxRate)
            .build());

        when(refundCalculationService.calculate(order, RefundReason.RETURN)).thenReturn(
            RefundCalculationResult.builder()
                .amountToRefundByBankTransactionInCents(amountToRefundByBankTransactionInCents)
                .amountToRefundByGiftCardInCents(amountToRefundByGiftCardInCents)
                .build());

        when(returnFeeService.createReturnFeeCharge(returnFee, taxRate)).thenReturn(OrderCharge.builder()
            .type(EntryType.RETURN_FEE)
            .build());

        when(giftCardPaymentRefundRequestGenerator.generate(any())).thenReturn(new PaymentRefundRequest());
        when(bankPaymentRefundRequestGenerator.generate(any())).thenReturn(new PaymentRefundRequest());
        when(refundStatusUpdatedGenerator.generate(any())).thenReturn(new RefundStatusUpdated());
        when(orderService.save(order, true)).thenReturn(order);

        // Act
        inStoreRefundService.issueRefundForInStoreReturn(inStoreReturnSettlement);

        // Assert
        assert order.getRefunds().size() == 1;
        assert order.getOrderCharges().size() == 2;

        Refund refund = order.getRefunds().get(0);

        assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());

        order.getOrderCharges()
            .stream()
            .filter(orderCharge -> orderCharge.getType().equals(EntryType.SHIPMENT_FEE))
            .forEach(orderCharge -> {
                assertTrue(orderCharge.getRefunded());
            });
        verify(returnFeeService).calculateReturnFee(
            order.getOrderDate(),
            order.getShippingCountryCode(),
            order.getBrand().getBrandAbbreviation(),
            amountToRefund
        );
        verify(returnFeeService).createReturnFeeCharge(returnFee, taxRate);
        verify(priceService, times(2)).processAndSetPriceFieldsForRefundLineAndRefundTotalPaidAmount(any());
        verify(orderService, times(2)).save(order, true);
        verify(refundCalculationService).calculate(order, RefundReason.RETURN);
        verify(paymentRefundRequestProducerQueueProducer, times(2)).enqueue(any());
        verify(refundStatusUpdatedProducerQueueProducer, times(2)).enqueue(any());
    }

    @Test
    void test_refund_mixedPayment_chargeReturnFee_aboveCountryThreshold_noRefundShippingFee() {
        // Arrange
        Long refundId = Long.valueOf(*********);

        Order order = OrderGenerator.createOrder();


        InStoreReturnSettlement inStoreReturnSettlement = new InStoreReturnSettlement()
            .withOrderId(order.getOrderId())
            .withRefundId(refundId)
            .withTotalRefundAmount(BigDecimal.TEN)
            .withOrderLines(List.of(
                new OrderLine().withEan(order.
                        getOrderLines().
                        get(0).
                        getEan()).
                    withQuantity(order.getOrderLines().
                        get(0).getOpenQty())
            ));

        BigDecimal returnFee = BigDecimal.ZERO;
        BigDecimal taxRate = BigDecimal.ZERO;
        int amountToRefundByBankTransactionInCents = 100;
        int amountToRefundByGiftCardInCents = 200;
        BigDecimal amountToRefund = new BigDecimal("49.99");

        when(refundOptionsService.getRefundOptionsByOrderId(order.getOrderId())).thenReturn(Optional.of(RefundOptionsResponse.builder()
            .chargeReturnFee(true).refundShippingFee(true).build()));

        when(orderService.getOrderById(order.getOrderId())).thenReturn(order);

        doAnswer(invocation -> {
            Refund refundArg = invocation.getArgument(0);
            OverallTotal refundTotal = OverallTotal.builder()
                .grossDiscountedTotal(amountToRefund)
                .build();
            refundArg.setRefundTotal(refundTotal);  // Modify the captured Refund object
            return null;
        }).when(priceService).processAndSetPriceFieldsForRefundLineAndRefundTotalPaidAmount(any(Refund.class));
        when(returnFeeService.calculateReturnFee(
            order.getOrderDate(),
            order.getShippingCountryCode(),
            order.getBrand().getBrandAbbreviation(),
            amountToRefund
        )).thenReturn(ReturnFeeCalculationResult.builder()
            .returnFee(returnFee)
            .taxRate(taxRate)
            .build());

        when(refundCalculationService.calculate(order, RefundReason.RETURN)).thenReturn(
            RefundCalculationResult.builder()
                .amountToRefundByBankTransactionInCents(amountToRefundByBankTransactionInCents)
                .amountToRefundByGiftCardInCents(amountToRefundByGiftCardInCents)
                .build());

        when(giftCardPaymentRefundRequestGenerator.generate(any())).thenReturn(new PaymentRefundRequest());
        when(bankPaymentRefundRequestGenerator.generate(any())).thenReturn(new PaymentRefundRequest());
        when(refundStatusUpdatedGenerator.generate(any())).thenReturn(new RefundStatusUpdated());
        when(orderService.save(order, true)).thenReturn(order);

        // Act
        inStoreRefundService.issueRefundForInStoreReturn(inStoreReturnSettlement);

        // Assert
        assert order.getRefunds().size() == 1;
        assert order.getOrderCharges().size() == 1;

        Refund refund = order.getRefunds().get(0);

        assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());

        order.getOrderCharges()
            .stream()
            .filter(orderCharge -> orderCharge.getType().equals(EntryType.SHIPMENT_FEE))
            .forEach(orderCharge -> {
                assertTrue(orderCharge.getRefunded());
            });
        verify(returnFeeService).calculateReturnFee(
            order.getOrderDate(),
            order.getShippingCountryCode(),
            order.getBrand().getBrandAbbreviation(),
            amountToRefund
        );
        verify(returnFeeService, never()).createReturnFeeCharge(returnFee, taxRate);
        verify(priceService, times(2)).processAndSetPriceFieldsForRefundLineAndRefundTotalPaidAmount(any());
        verify(orderService, times(2)).save(order, true);
        verify(refundCalculationService).calculate(order, RefundReason.RETURN);
        verify(paymentRefundRequestProducerQueueProducer, times(2)).enqueue(any());
        verify(refundStatusUpdatedProducerQueueProducer, times(2)).enqueue(any());
    }

    @Test
    void test_refund_reason() {
        // Arrange
        Long refundId = Long.valueOf(*********);

        Order order = OrderGenerator.createOrder();


        InStoreReturnSettlement inStoreReturnSettlement = new InStoreReturnSettlement()
            .withOrderId(order.getOrderId())
            .withRefundId(refundId)
            .withTotalRefundAmount(BigDecimal.TEN)
            .withOrderLines(List.of(
                new OrderLine().withEan(order.
                        getOrderLines().
                        get(0).
                        getEan()).
                    withQuantity(order.getOrderLines().
                        get(0).getOpenQty())
            ));

        BigDecimal returnFee = BigDecimal.TEN;
        BigDecimal taxRate = BigDecimal.ONE;
        int amountToRefundByBankTransactionInCents = 100;
        int amountToRefundByGiftCardInCents = 200;
        BigDecimal amountToRefund = new BigDecimal("4.99");

        when(refundOptionsService.getRefundOptionsByOrderId(order.getOrderId())).thenReturn(Optional.of(RefundOptionsResponse.builder()
            .chargeReturnFee(true).refundShippingFee(true).build()));

        when(orderService.getOrderById(order.getOrderId())).thenReturn(order);

        doAnswer(invocation -> {
            Refund refundArg = invocation.getArgument(0);
            OverallTotal refundTotal = OverallTotal.builder()
                .grossDiscountedTotal(amountToRefund)
                .build();
            refundArg.setRefundTotal(refundTotal);  // Modify the captured Refund object
            return null;
        }).when(priceService).processAndSetPriceFieldsForRefundLineAndRefundTotalPaidAmount(any(Refund.class));

        when(returnFeeService.calculateReturnFee(
            order.getOrderDate(),
            order.getShippingCountryCode(),
            order.getBrand().getBrandAbbreviation(),
            amountToRefund
        )).thenReturn(ReturnFeeCalculationResult.builder()
            .returnFee(returnFee)
            .taxRate(taxRate)
            .build());

        when(refundCalculationService.calculate(order, RefundReason.RETURN)).thenReturn(
            RefundCalculationResult.builder()
                .amountToRefundByBankTransactionInCents(amountToRefundByBankTransactionInCents)
                .amountToRefundByGiftCardInCents(amountToRefundByGiftCardInCents)
                .build());

        when(returnFeeService.createReturnFeeCharge(returnFee, taxRate)).thenReturn(OrderCharge.builder()
            .type(EntryType.RETURN_FEE)
            .build());

        when(giftCardPaymentRefundRequestGenerator.generate(any())).thenReturn(new PaymentRefundRequest());
        when(bankPaymentRefundRequestGenerator.generate(any())).thenReturn(new PaymentRefundRequest());
        when(refundStatusUpdatedGenerator.generate(any())).thenReturn(new RefundStatusUpdated());
        when(orderService.save(order, true)).thenReturn(order);

        // Act
        inStoreRefundService.issueRefundForInStoreReturn(inStoreReturnSettlement);

        // Assert
        assert order.getRefunds().size() == 1;
        assert order.getOrderCharges().size() == 2;

        Refund refund = order.getRefunds().get(0);

        assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());

        order.getOrderCharges()
            .stream()
            .filter(orderCharge -> orderCharge.getType().equals(EntryType.SHIPMENT_FEE))
            .forEach(orderCharge -> {
                assertTrue(orderCharge.getRefunded());
                assertEquals(ChargedRefundReason.RETURNED_ITEMS_IN_STORE, orderCharge.getRefundReason());
            });
    }

    @Test
    void test_refund_mixedPayment_notChargeReturnFee_notRefundShippingFee() {
        // Arrange
        Long refundId = Long.valueOf(*********);

        Order order = OrderGenerator.createOrder();


        InStoreReturnSettlement inStoreReturnSettlement = new InStoreReturnSettlement()
            .withOrderId(order.getOrderId())
            .withRefundId(refundId)
            .withTotalRefundAmount(BigDecimal.TEN)
            .withOrderLines(List.of(
                new OrderLine().withEan(order.
                        getOrderLines().
                        get(0).
                        getEan()).
                    withQuantity(order.getOrderLines().
                        get(0).getOpenQty())
            ));

        int amountToRefundByBankTransactionInCents = 100;
        int amountToRefundByGiftCardInCents = 200;

        when(refundOptionsService.getRefundOptionsByOrderId(order.getOrderId())).thenReturn(Optional.of(RefundOptionsResponse.builder()
            .chargeReturnFee(false).refundShippingFee(false).build()));

        when(orderService.getOrderById(order.getOrderId())).thenReturn(order);


        when(refundCalculationService.calculate(order, RefundReason.RETURN)).thenReturn(
            RefundCalculationResult.builder()
                .amountToRefundByBankTransactionInCents(amountToRefundByBankTransactionInCents)
                .amountToRefundByGiftCardInCents(amountToRefundByGiftCardInCents)
                .build());


        when(giftCardPaymentRefundRequestGenerator.generate(any())).thenReturn(new PaymentRefundRequest());
        when(bankPaymentRefundRequestGenerator.generate(any())).thenReturn(new PaymentRefundRequest());
        when(refundStatusUpdatedGenerator.generate(any())).thenReturn(new RefundStatusUpdated());
        when(orderService.save(order, true)).thenReturn(order);

        // Act
        inStoreRefundService.issueRefundForInStoreReturn(inStoreReturnSettlement);

        // Assert
        assert order.getRefunds().size() == 1;
        assert order.getOrderCharges().size() == 1;

        Refund refund = order.getRefunds().get(0);

        assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());

        order.getOrderCharges()
            .stream()
            .filter(orderCharge -> orderCharge.getType().equals(EntryType.SHIPMENT_FEE))
            .forEach(orderCharge -> {
                assertFalse(orderCharge.getRefunded());
            });
        verify(returnFeeService, times(0)).createReturnFeeCharge(any(), any());
        verify(returnFeeService, times(0)).calculateReturnFee(any(), any(), any(), any());
        verify(priceService, times(2)).processAndSetPriceFieldsForRefundLineAndRefundTotalPaidAmount(any());
        verify(orderService, times(2)).save(order, true);
        verify(refundCalculationService).calculate(order, RefundReason.RETURN);
        verify(paymentRefundRequestProducerQueueProducer, times(2)).enqueue(any());
        verify(refundStatusUpdatedProducerQueueProducer, times(2)).enqueue(any());
    }

    @Test
    void test_refund_giftCardPayment() {
        // Arrange
        Long refundId = Long.valueOf(*********);

        Order order = OrderGenerator.createOrder();

        InStoreReturnSettlement inStoreReturnSettlement = new InStoreReturnSettlement()
            .withOrderId(order.getOrderId())
            .withRefundId(refundId)
            .withTotalRefundAmount(BigDecimal.TEN)
            .withOrderLines(List.of(
                new OrderLine().withEan(order.
                        getOrderLines().
                        get(0).
                        getEan()).
                    withQuantity(order.getOrderLines().
                        get(0).getOpenQty())
            ));

        int amountToRefundByBankTransactionInCents = 0;
        int amountToRefundByGiftCardInCents = 200;

        when(refundOptionsService.getRefundOptionsByOrderId(order.getOrderId())).thenReturn(Optional.of(RefundOptionsResponse.builder()
            .chargeReturnFee(false).refundShippingFee(false).build()));

        when(orderService.getOrderById(order.getOrderId())).thenReturn(order);


        when(refundCalculationService.calculate(order, RefundReason.RETURN)).thenReturn(
            RefundCalculationResult.builder()
                .amountToRefundByBankTransactionInCents(amountToRefundByBankTransactionInCents)
                .amountToRefundByGiftCardInCents(amountToRefundByGiftCardInCents)
                .build());

        when(giftCardPaymentRefundRequestGenerator.generate(any())).thenReturn(new PaymentRefundRequest());
        when(orderService.save(order, true)).thenReturn(order);

        // Act
        inStoreRefundService.issueRefundForInStoreReturn(inStoreReturnSettlement);

        // Assert
        assert order.getRefunds().size() == 1;
        assert order.getOrderCharges().size() == 1;

        Refund refund = order.getRefunds().get(0);

        assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());

        order.getOrderCharges()
            .stream()
            .filter(orderCharge -> orderCharge.getType().equals(EntryType.SHIPMENT_FEE))
            .forEach(orderCharge -> {
                assertFalse(orderCharge.getRefunded());
            });
        verify(returnFeeService, times(0)).createReturnFeeCharge(any(), any());
        verify(returnFeeService, times(0)).calculateReturnFee(any(), any(), any(), any());
        verify(priceService, times(2)).processAndSetPriceFieldsForRefundLineAndRefundTotalPaidAmount(any());
        verify(orderService, times(2)).save(order, true);
        verify(refundCalculationService).calculate(order, RefundReason.RETURN);
        verify(paymentRefundRequestProducerQueueProducer).enqueue(any());
        verify(refundStatusUpdatedProducerQueueProducer, never()).enqueue(any());
    }

    @Test
    void test_refund_bankOrCardPayment() {
        // Arrange
        Long refundId = Long.valueOf(*********);

        Order order = OrderGenerator.createOrder();

        InStoreReturnSettlement inStoreReturnSettlement = new InStoreReturnSettlement()
            .withOrderId(order.getOrderId())
            .withRefundId(refundId)
            .withTotalRefundAmount(BigDecimal.TEN)
            .withOrderLines(List.of(
                new OrderLine().withEan(order.
                        getOrderLines().
                        get(0).
                        getEan()).
                    withQuantity(order.getOrderLines().
                        get(0).getOpenQty())
            ));

        int amountToRefundByBankTransactionInCents = 100;
        int amountToRefundByGiftCardInCents = 0;

        when(refundOptionsService.getRefundOptionsByOrderId(order.getOrderId())).thenReturn(Optional.of(RefundOptionsResponse.builder()
            .chargeReturnFee(false).refundShippingFee(false).build()));

        when(orderService.getOrderById(order.getOrderId())).thenReturn(order);

        when(refundCalculationService.calculate(order, RefundReason.RETURN)).thenReturn(
            RefundCalculationResult.builder()
                .amountToRefundByBankTransactionInCents(amountToRefundByBankTransactionInCents)
                .amountToRefundByGiftCardInCents(amountToRefundByGiftCardInCents)
                .build());


        when(bankPaymentRefundRequestGenerator.generate(any())).thenReturn(new PaymentRefundRequest());
        when(refundStatusUpdatedGenerator.generate(any())).thenReturn(new RefundStatusUpdated());
        when(orderService.save(order, true)).thenReturn(order);

        // Act
        inStoreRefundService.issueRefundForInStoreReturn(inStoreReturnSettlement);

        // Assert
        assert order.getRefunds().size() == 1;
        assert order.getOrderCharges().size() == 1;

        Refund refund = order.getRefunds().get(0);

        assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());

        order.getOrderCharges()
            .stream()
            .filter(orderCharge -> orderCharge.getType().equals(EntryType.SHIPMENT_FEE))
            .forEach(orderCharge -> {
                assertFalse(orderCharge.getRefunded());
            });
        verify(returnFeeService, times(0)).createReturnFeeCharge(any(), any());
        verify(returnFeeService, times(0)).calculateReturnFee(any(), any(), any(), any());
        verify(priceService, times(2)).processAndSetPriceFieldsForRefundLineAndRefundTotalPaidAmount(any());
        verify(orderService, times(2)).save(order, true);
        verify(refundCalculationService).calculate(order, RefundReason.RETURN);
        verify(paymentRefundRequestProducerQueueProducer).enqueue(any());
        verify(refundStatusUpdatedProducerQueueProducer, times(2)).enqueue(any());
    }

    @Test
    void test_createRefundObject_equal_Refund_Amount() {
        // Arrange
        Long refundId = Long.valueOf(*********);

        String requestId = refundId.toString();

        Order order = OrderGenerator.createOrder();


        // Act
        Refund refund = inStoreRefundService.createRefundObject(order,
            List.of(OrderItemToRefund.builder()
                    .ean(order.getOrderLines().get(0).getEan())
                    .quantity(1)
                    .build(),
                OrderItemToRefund.builder()
                    .ean(order.getOrderLines().get(1).getEan())
                    .quantity(1)
                    .build()
            ),
            requestId,
            BigDecimal.valueOf(10),
            RefundOptions.builder().refundReason(RefundReason.RETURN).build());

        // Assert
        assert refund.getRefundLines().size() == 2;
        assert refund.getRefundState().equals(RefundState.CREATED);
        assert refund.getOrder().equals(order);
        assert refund.getCsrInitials().equals("SYSTEM");
        assert refund.getRequestId().equals(requestId);
        assert refund.getRefundId() == null;
        assert refund.getRefundLines().get(0).getQuantity() == 1;
        assert refund.getRefundLines().get(0).getLineNumber().equals(order.getOrderLines().get(0).getLineNumber().toString());
        assert refund.getRefundLines().get(1).getQuantity() == 1;
        assert refund.getRefundLines().get(1).getLineNumber().equals(order.getOrderLines().get(1).getLineNumber().toString());

        assert refund.getRefundLines().get(0).getRefundLineTotal().getGrossDiscountedUnitPrice().equals(BigDecimal.valueOf(5));
        assert refund.getRefundLines().get(1).getRefundLineTotal().getGrossDiscountedUnitPrice().equals(BigDecimal.valueOf(5));

    }

    @Test
    void test_createRefundObject_not_equal_refund_amount() {
        // Arrange
        Long refundId = Long.valueOf(*********);

        String requestId = refundId.toString();

        Order order = OrderGenerator.createOrder();


        // Act
        Refund refund = inStoreRefundService.createRefundObject(order,
            List.of(OrderItemToRefund.builder()
                    .ean(order.getOrderLines().get(0).getEan())
                    .quantity(2)
                    .build(),
                OrderItemToRefund.builder()
                    .ean(order.getOrderLines().get(1).getEan())
                    .quantity(2)
                    .build()
            ),
            requestId,
            BigDecimal.valueOf(10),
            RefundOptions.builder().refundReason(RefundReason.RETURN).build());

        // Assert
        assert refund.getRefundLines().size() == 2;
        assert refund.getRefundState().equals(RefundState.CREATED);
        assert refund.getOrder().equals(order);
        assert refund.getCsrInitials().equals("SYSTEM");
        assert refund.getRequestId().equals(requestId);
        assert refund.getRefundId() == null;
        assert refund.getRefundLines().get(0).getQuantity() == 2;
        assert refund.getRefundLines().get(0).getLineNumber().equals(order.getOrderLines().get(0).getLineNumber().toString());
        assert refund.getRefundLines().get(1).getQuantity() == 2;
        assert refund.getRefundLines().get(1).getLineNumber().equals(order.getOrderLines().get(1).getLineNumber().toString());

        assert refund.getRefundLines().get(0).getRefundLineTotal().getGrossDiscountedUnitPrice().equals(BigDecimal.valueOf(1));
        assert refund.getRefundLines().get(1).getRefundLineTotal().getGrossDiscountedUnitPrice().equals(BigDecimal.valueOf(3));

    }

    @Test
    void test_issueRefundForInStoreReturn_zeroAmountToRefund() {
        // Arrange
        Long refundId = Long.valueOf(*********);

        Order order = OrderGenerator.createOrder();

        InStoreReturnSettlement inStoreReturnSettlement = new InStoreReturnSettlement()
            .withOrderId(order.getOrderId())
            .withTotalRefundAmount(BigDecimal.ZERO);

        // Act
        inStoreRefundService.issueRefundForInStoreReturn(inStoreReturnSettlement);

        // Assert
        verify(orderService, never()).getOrderById(any());
        verify(priceService, never()).processAndSetPriceFieldsForRefundLineAndRefundTotalPaidAmount(any());
        verify(returnFeeService, never()).calculateReturnFee(any(), any(), any(), any());
        verify(refundCalculationService, never()).calculate(any(), any());
        verify(paymentRefundRequestProducerQueueProducer, never()).enqueue(any());
        verify(refundStatusUpdatedProducerQueueProducer, never()).enqueue(any());
    }
}
