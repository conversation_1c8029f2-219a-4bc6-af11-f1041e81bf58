package com.bestseller.payment.core.service.refund;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.giftCardRefundResponse.GiftCardRefundResponse;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCompleted.RefundCompleted;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundStatusUpdated.RefundStatusUpdated;
import com.bestseller.payment.adapter.repository.RefundRepository;
import com.bestseller.payment.adapter.stream.messagegenerator.RefundCompletedGenerator;
import com.bestseller.payment.adapter.stream.messagegenerator.RefundStatusUpdatedGenerator;
import com.bestseller.payment.core.domain.enumeration.RefundType;
import com.bestseller.payment.core.exception.InvalidRefundStateTransitionException;
import com.bestseller.payment.core.exception.RefundNotFoundException;
import com.bestseller.payment.core.service.vat.OrderVatService;
import com.bestseller.payment.utils.RefundGenerator;
import com.logistics.statetransition.RefundState;
import com.logistics.statetransition.RefundStateTransition;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class RefundServiceTest {
    RefundServiceImpl refundService;

    @Mock
    RefundRepository refundRepository;

    @Mock
    QueueProducer<RefundCompleted> refundCompletedProducerQueueProducer;

    @Mock
    QueueProducer<RefundStatusUpdated> refundStatusUpdatedProducerQueueProducer;

    @Mock
    RefundStatusUpdatedGenerator refundStatusUpdatedGenerator;

    @Mock
    RefundCompletedGenerator refundCompletedGenerator;

    @Mock
    OrderVatService orderVatService;

    @BeforeEach
    public void setUp() {
        refundService = spy(
            new RefundServiceImpl(refundRepository,
                refundCompletedProducerQueueProducer,
                refundStatusUpdatedProducerQueueProducer,
                refundStatusUpdatedGenerator,
                refundCompletedGenerator,
                orderVatService
            ));
    }

    @Test
    void test_isDuplicateRequest_true() {
        // Arrange
        var refundId = 1;
        when(refundRepository.getRefundStateById(refundId))
            .thenReturn(Optional.of(RefundState.REFUND_SUCCESS));

        // Act
        boolean actualValue = refundService.isDuplicateRequest(refundId, RefundState.REFUND_REQUESTED);

        // Assert
        verify(refundRepository).getRefundStateById(refundId);
        assertTrue(actualValue);
    }

    @Test
    void test_isDuplicateRequest_false() {
        // Arrange
        var refundId = 1;
        when(refundRepository.getRefundStateById(refundId)).thenReturn(Optional.of(RefundState.REFUND_REQUESTED));

        // Act
        boolean actualValue = refundService.isDuplicateRequest(refundId, RefundState.REFUND_SUCCESS);

        // Assert
        verify(refundRepository).getRefundStateById(refundId);
        assertFalse(actualValue);
    }

    @Test
    void test_isDuplicateRequest_success_to_failed() {
        // Arrange
        var refundId = 1;
        when(refundRepository.getRefundStateById(refundId)).thenReturn(Optional.of(RefundState.REFUND_SUCCESS));

        // Act
        boolean actualValue = refundService.isDuplicateRequest(refundId, RefundState.REFUND_FAILED);

        // Assert
        verify(refundRepository).getRefundStateById(refundId);
        assertFalse(actualValue);
    }

    @Test
    void test_isDuplicateRequest_failed_to_success() {
        // Arrange
        var refundId = 1;
        when(refundRepository.getRefundStateById(refundId)).thenReturn(Optional.of(RefundState.REFUND_FAILED));

        // Act
        boolean actualValue = refundService.isDuplicateRequest(refundId, RefundState.REFUND_SUCCESS);

        // Assert
        verify(refundRepository).getRefundStateById(refundId);
        assertFalse(actualValue);
    }

    @Test
    void test_isDuplicateRequest_refundNotFound() {
        // Arrange
        var refundId = 1;
        when(refundRepository.getRefundStateById(refundId)).thenReturn(Optional.empty());

        // Act
        var exception = assertThrows(RefundNotFoundException.class,
            () -> refundService.isDuplicateRequest(refundId, RefundState.REFUND_SUCCESS));

        // Assert
        verify(refundRepository).getRefundStateById(refundId);
        assertEquals("Refund not found with id: 1", exception.getMessage());
    }

    @Test
    void test_isExists_true() {
        // Arrange
        var refund = RefundGenerator.createRefund();
        var refundId = refund.getId();
        when(refundRepository.existsById(refundId.intValue())).thenReturn(true);

        // Act
        boolean actualValue = refundService.isExists(refundId);

        // Assert
        verify(refundRepository).existsById(refundId.intValue());
        assertTrue(actualValue);
    }

    @Test
    void test_isExists_false() {
        // Arrange
        var refund = RefundGenerator.createRefund();
        var refundId = refund.getId();
        when(refundRepository.existsById(refundId.intValue())).thenReturn(false);

        // Act
        boolean actualValue = refundService.isExists(refundId);

        // Assert
        verify(refundRepository).existsById(refundId.intValue());
        assertFalse(actualValue);
    }

    @Test
    void test_updateRefundStatus_noChange() {
        // Arrange
        var refund = RefundGenerator.createRefund();
        var refundId = refund.getId();
        var targetRefundStatus = RefundState.REFUND_REQUESTED;
        when(refundRepository.findById(refundId.intValue())).thenReturn(Optional.of(refund));

        // Act
        refundService.updateRefundStatus(refundId, targetRefundStatus);

        // Assert
        verify(refundRepository).findById(refundId.intValue());
        verify(refundRepository, never()).save(refund);
        verify(refundCompletedGenerator, never()).generate(refund);
        verify(refundCompletedProducerQueueProducer, never()).enqueue(any());
        verify(refundStatusUpdatedGenerator, never()).generate(refund);
        verify(refundStatusUpdatedProducerQueueProducer, never()).enqueue(any());
        verify(orderVatService, never()).getNextRefundVatId(any());
        verify(orderVatService, never()).getNextRefundInStoreVatId(any(), anyLong());
    }

    @Test
    void test_updateRefundStatus_invalidTransition() {
        // Arrange
        var refund = RefundGenerator.createRefund();
        var refundId = refund.getId();
        var targetRefundStatus = RefundState.REFUND_SUCCESS;
        when(refundRepository.findById(refundId.intValue())).thenReturn(Optional.of(refund));

        try (MockedStatic<RefundStateTransition> refundStateTransitionMockedStatic = mockStatic(RefundStateTransition.class)) {
            refundStateTransitionMockedStatic.when(
                    () -> RefundStateTransition.isTransitionAllowed(refund.getRefundState(), targetRefundStatus))
                .thenReturn(false);

            // Act
            var exception = assertThrows(InvalidRefundStateTransitionException.class,
                () -> refundService.updateRefundStatus(refundId, targetRefundStatus));

            // Assert
            verify(refundRepository).findById(refundId.intValue());
            assertEquals("Invalid refund state transition from REFUND_REQUESTED to REFUND_SUCCESS "
                + "for refund: 1. orderId: OL12345678", exception.getMessage());
            verify(refundRepository, never()).save(any());
            verify(refundCompletedGenerator, never()).generate(refund);
            verify(refundCompletedProducerQueueProducer, never()).enqueue(any());
            verify(refundStatusUpdatedGenerator, never()).generate(refund);
            verify(refundStatusUpdatedProducerQueueProducer, never()).enqueue(any());
            verify(orderVatService, never()).getNextRefundVatId(any());
            verify(orderVatService, never()).getNextRefundInStoreVatId(any(), anyLong());
        }
    }

    @Test
    void test_updateRefundStatus_change() {
        // Arrange
        var refund = RefundGenerator.createRefund();
        var refundId = refund.getId();
        var targetRefundStatus = RefundState.REFUND_SUCCESS;
        when(refundRepository.findById(refundId.intValue())).thenReturn(Optional.of(refund));
        when(refundStatusUpdatedGenerator.generate(refund)).thenReturn(new RefundStatusUpdated());

        try (MockedStatic<RefundStateTransition> refundStateTransitionMockedStatic = mockStatic(RefundStateTransition.class)) {
            refundStateTransitionMockedStatic.when(
                    () -> RefundStateTransition.isTransitionAllowed(refund.getRefundState(), targetRefundStatus))
                .thenReturn(true);

            // Act
            refundService.updateRefundStatus(refundId, targetRefundStatus);

            // Assert
            verify(refundRepository).findById(refundId.intValue());
            verify(refundRepository).save(refund);
            verify(refundCompletedGenerator, never()).generate(refund);
            verify(refundCompletedProducerQueueProducer, never()).enqueue(any());
            verify(refundStatusUpdatedGenerator).generate(refund);
            verify(refundStatusUpdatedProducerQueueProducer).enqueue(any());
            verify(orderVatService).getNextRefundVatId(refund.getOrder());
            verify(orderVatService, never()).getNextRefundInStoreVatId(any(), anyLong());
        }
    }

    @Test
    void test_updateRefundStatus_refundNotFound() {
        // Arrange
        var refundId = Long.valueOf(1);
        var targetRefundStatus = RefundState.REFUND_SUCCESS;
        when(refundRepository.findById(refundId.intValue())).thenReturn(Optional.empty());

        // Act
        var exception = assertThrows(RefundNotFoundException.class,
            () -> refundService.updateRefundStatus(refundId.intValue(), targetRefundStatus));

        // Assert
        verify(refundRepository).findById(refundId.intValue());
        assertEquals("Refund not found with id: 1", exception.getMessage());
        verify(refundRepository, never()).save(any());
        verify(refundCompletedGenerator, never()).generate(any());
        verify(refundCompletedProducerQueueProducer, never()).enqueue(any());
        verify(refundStatusUpdatedGenerator, never()).generate(any());
        verify(refundStatusUpdatedProducerQueueProducer, never()).enqueue(any());
        verify(orderVatService, never()).getNextRefundVatId(any());
        verify(orderVatService, never()).getNextRefundInStoreVatId(any(), anyLong());
    }

    @Test
    void test_updateRefundStatus_inStoreRefund() {
        // Arrange
        var refund = RefundGenerator.createRefund();
        refund.setRequestId("123456");
        var refundId = refund.getId();
        var targetRefundStatus = RefundState.REFUND_SUCCESS;
        when(refundRepository.findById(refundId.intValue())).thenReturn(Optional.of(refund));
        when(refundStatusUpdatedGenerator.generate(refund)).thenReturn(new RefundStatusUpdated());
        when(refundCompletedGenerator.generate(refund)).thenReturn(new RefundCompleted());

        // Act
        refundService.updateRefundStatus(refundId, targetRefundStatus);

        // Assert
        verify(refundRepository).findById(refundId.intValue());
        verify(refundRepository).save(refund);
        verify(refundCompletedGenerator).generate(refund);
        verify(refundCompletedProducerQueueProducer).enqueue(any());
        verify(refundStatusUpdatedGenerator).generate(refund);
        verify(refundStatusUpdatedProducerQueueProducer).enqueue(any());
        verify(orderVatService).getNextRefundInStoreVatId(refund.getOrder(), Long.parseLong(refund.getRequestId()));
        verify(orderVatService, never()).getNextRefundVatId(any());
    }

    @Test
    void test_updateRefundStatus_inStoreRefundWithVatNumber() {
        // Arrange
        var refund = RefundGenerator.createRefund();
        refund.setRequestId("123456");
        refund.setRefundId("RF-1-REF-");
        var refundId = refund.getId();
        var targetRefundStatus = RefundState.REFUND_SUCCESS;
        when(refundRepository.findById(refundId.intValue())).thenReturn(Optional.of(refund));
        when(refundStatusUpdatedGenerator.generate(refund)).thenReturn(new RefundStatusUpdated());
        when(refundCompletedGenerator.generate(refund)).thenReturn(new RefundCompleted());

        // Act
        refundService.updateRefundStatus(refundId, targetRefundStatus);

        // Assert
        verify(refundRepository).findById(refundId.intValue());
        verify(refundRepository).save(refund);
        verify(refundCompletedGenerator).generate(refund);
        verify(refundCompletedProducerQueueProducer).enqueue(any());
        verify(refundStatusUpdatedGenerator).generate(refund);
        verify(refundStatusUpdatedProducerQueueProducer).enqueue(any());
        verify(orderVatService, never()).getNextRefundInStoreVatId(refund.getOrder(), Long.parseLong(refund.getRequestId()));
        verify(orderVatService, never()).getNextRefundVatId(any());
    }
    @Test
    void test_updateRefundStatus_inStoreRefundNotFinalState() {
        // Arrange
        var refund = RefundGenerator.createRefund();
        var refundId = refund.getId();
        refund.setRefundState(RefundState.REFUND_REQUESTING);
        var targetRefundStatus = RefundState.REFUND_REQUESTED;
        when(refundRepository.findById(refundId.intValue())).thenReturn(Optional.of(refund));
        when(refundStatusUpdatedGenerator.generate(refund)).thenReturn(new RefundStatusUpdated());

        // Act
        refundService.updateRefundStatus(refundId, targetRefundStatus);

        // Assert
        verify(refundRepository).findById(refundId.intValue());
        verify(refundRepository).save(refund);
        verify(refundCompletedGenerator, never()).generate(refund);
        verify(refundCompletedProducerQueueProducer, never()).enqueue(any());
        verify(refundStatusUpdatedGenerator).generate(refund);
        verify(refundStatusUpdatedProducerQueueProducer).enqueue(any());
        verify(orderVatService, never()).getNextRefundVatId(any());
        verify(orderVatService, never()).getNextRefundInStoreVatId(any(), anyLong());
    }

    @Test
    void test_updateRefundStatus_notInStoreRefund() {
        // Arrange
        var refund = RefundGenerator.createRefund();
        var refundId = refund.getId();
        var targetRefundStatus = RefundState.REFUND_SUCCESS;
        when(refundRepository.findById(refundId.intValue())).thenReturn(Optional.of(refund));
        when(refundStatusUpdatedGenerator.generate(refund)).thenReturn(new RefundStatusUpdated());

        // Act
        refundService.updateRefundStatus(refundId, targetRefundStatus);

        // Assert
        verify(refundRepository).findById(refundId.intValue());
        verify(refundRepository).save(refund);
        verify(refundCompletedGenerator, never()).generate(refund);
        verify(refundCompletedProducerQueueProducer, never()).enqueue(any());
        verify(refundStatusUpdatedGenerator).generate(refund);
        verify(refundStatusUpdatedProducerQueueProducer).enqueue(any());
        verify(orderVatService).getNextRefundVatId(refund.getOrder());
        verify(orderVatService, never()).getNextRefundInStoreVatId(any(), anyLong());
    }

    @Test
    void test_updateRefundStatus_refundByGiftCard() {
        // Arrange
        var refund = RefundGenerator.createRefund();
        refund.setRefundType(RefundType.GIFT_CARD);
        var refundId = refund.getId();
        var targetRefundStatus = RefundState.REFUND_SUCCESS;
        when(refundRepository.findById(refundId)).thenReturn(Optional.of(refund));

        // Act
        refundService.updateRefundStatus(refundId, targetRefundStatus);

        // Assert
        verify(refundRepository).findById(refundId);
        verify(refundRepository).save(refund);
        verify(refundCompletedGenerator, never()).generate(refund);
        verify(refundCompletedProducerQueueProducer, never()).enqueue(any());
        verify(refundStatusUpdatedGenerator, never()).generate(refund);
        verify(refundStatusUpdatedProducerQueueProducer, never()).enqueue(any());
        verify(orderVatService).getNextRefundVatId(refund.getOrder());
        verify(orderVatService, never()).getNextRefundInStoreVatId(any(), anyLong());
    }

    @Test
    void test_processGiftCardRefundResponse_success() {
        // Arrange
        var correlationId = UUID.randomUUID();
        var message = new GiftCardRefundResponse()
            .withOrderId("orderId")
            .withCorrelationId(correlationId.toString())
            .withStatus(true);
        var refund = RefundGenerator.createRefund();
        refund.setRefundType(RefundType.GIFT_CARD);
        when(refundRepository.getRefundByOrderOrderIdAndGiftCardCorrelationId("orderId", correlationId))
            .thenReturn(Optional.of(refund));
        when(refundRepository.findById(refund.getId())).thenReturn(Optional.of(refund));

        // Act
        refundService.processGiftCardRefundResponse(message);

        // Assert
        verify(refundService).updateRefundStatus(refund.getId(), RefundState.REFUND_SUCCESS);
        verify(refundRepository).findById(refund.getId());
        verify(refundRepository).save(refund);
        verify(orderVatService).getNextRefundVatId(refund.getOrder());
        verify(orderVatService, never()).getNextRefundInStoreVatId(any(), anyLong());
        verify(refundCompletedProducerQueueProducer, never()).enqueue(any());
        verify(refundStatusUpdatedGenerator, never()).generate(refund);
        verify(refundStatusUpdatedProducerQueueProducer, never()).enqueue(any());
    }

    @Test
    void test_processGiftCardRefundResponse_failed() {
        // Arrange
        var correlationId = UUID.randomUUID();
        var message = new GiftCardRefundResponse()
            .withOrderId("orderId")
            .withCorrelationId(correlationId.toString())
            .withStatus(false);
        var refund = RefundGenerator.createRefund();
        refund.setRefundType(RefundType.GIFT_CARD);
        when(refundRepository.getRefundByOrderOrderIdAndGiftCardCorrelationId("orderId", correlationId))
            .thenReturn(Optional.of(refund));
        when(refundRepository.findById(refund.getId())).thenReturn(Optional.of(refund));

        // Act
        refundService.processGiftCardRefundResponse(message);

        // Assert
        verify(refundService).updateRefundStatus(refund.getId(), RefundState.REFUND_FAILED);
        verify(refundRepository).findById(refund.getId());
        verify(refundRepository).save(refund);
        verify(orderVatService, never()).getNextRefundVatId(refund.getOrder());
        verify(orderVatService, never()).getNextRefundInStoreVatId(any(), anyLong());
        verify(refundCompletedProducerQueueProducer, never()).enqueue(any());
        verify(refundStatusUpdatedGenerator, never()).generate(refund);
        verify(refundStatusUpdatedProducerQueueProducer, never()).enqueue(any());
    }

    @Test
    void test_processGiftCardRefundResponse_nullRefundType() {
        // Arrange
        var correlationId = UUID.randomUUID();
        var message = new GiftCardRefundResponse()
            .withOrderId("orderId")
            .withCorrelationId(correlationId.toString())
            .withStatus(true);
        var refund = RefundGenerator.createRefund();
        refund.setRefundType(null);
        when(refundRepository.getRefundByOrderOrderIdAndGiftCardCorrelationId("orderId", correlationId))
            .thenReturn(Optional.of(refund));

        // Act
        refundService.processGiftCardRefundResponse(message);

        // Assert
        verify(refundService, never()).updateRefundStatus(anyInt(), any());
    }

    @MethodSource("nonGifCardTypes")
    @ParameterizedTest
    void test_processGiftCardRefundResponse_nonGiftCardRefundType(RefundType refundType) {
        // Arrange
        var correlationId = UUID.randomUUID();
        var message = new GiftCardRefundResponse()
            .withOrderId("orderId")
            .withCorrelationId(correlationId.toString())
            .withStatus(true);
        var refund = RefundGenerator.createRefund();
        refund.setRefundType(refundType);
        when(refundRepository.getRefundByOrderOrderIdAndGiftCardCorrelationId("orderId", correlationId))
            .thenReturn(Optional.of(refund));

        // Act
        refundService.processGiftCardRefundResponse(message);

        // Assert
        verify(refundService, never()).updateRefundStatus(anyInt(), any());
    }

    @Test
    void test_isStateTransitionAllowed() {
        // Arrange
        var refund = RefundGenerator.createRefund();
        var refundId = refund.getId();
        when(refundRepository.getRefundStateById(refundId)).thenReturn(Optional.of(refund.getRefundState()));


        try (MockedStatic<RefundStateTransition> refundStateTransitionMockedStatic = mockStatic(RefundStateTransition.class)) {
            refundStateTransitionMockedStatic.when(
                    () -> RefundStateTransition.isTransitionAllowed(refund.getRefundState(), RefundState.REFUND_SUCCESS))
                .thenReturn(true);

            // Act
            boolean actualValue = refundService.isStateTransitionAllowed(refundId, RefundState.REFUND_SUCCESS);

            // Assert
            verify(refundRepository).getRefundStateById(refundId);
            assertTrue(actualValue);
            refundStateTransitionMockedStatic.verify(
                () -> RefundStateTransition.isTransitionAllowed(refund.getRefundState(), RefundState.REFUND_SUCCESS));
        }
    }

    private static Stream<Arguments> nonGifCardTypes() {
        return Stream.of(
            Arguments.of(RefundType.BANK_TRANSACTION),
            Arguments.of(RefundType.MIXED),
            Arguments.of(RefundType.NONE)
        );
    }

}
