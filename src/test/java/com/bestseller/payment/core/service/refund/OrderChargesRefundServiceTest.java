package com.bestseller.payment.core.service.refund;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequest.PaymentRefundRequest;
import com.bestseller.payment.adapter.stream.producer.RefundStatusUpdatedProducer;
import com.bestseller.payment.adapter.stream.validator.PaymentValidation;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.dto.OrderChargesRefundRequest;
import com.bestseller.payment.core.dto.OrderItemToRefund;
import com.bestseller.payment.core.dto.RefundOptions;
import com.bestseller.payment.core.dto.RefundReason;
import com.bestseller.payment.core.service.order.OrderService;
import com.bestseller.payment.core.service.price.PriceService;
import com.bestseller.payment.utils.OrderGenerator;
import com.logistics.statetransition.RefundState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class OrderChargesRefundServiceTest {
    @Spy
    @InjectMocks
    private OrderChargesRefundServiceImpl orderChargesRefundService;

    @Mock
    private OrderService orderService;

    @Mock
    private RefundCalculationService refundCalculationService;

    @Mock
    private ReturnFeeService returnFeeService;

    @Mock
    private PriceService priceService;

    @Mock
    private QueueProducer<PaymentRefundRequest> paymentRefundRequestProducerQueueProducer;

    @Mock
    private RefundStatusUpdatedProducer refundStatusUpdatedProducer;

    @Mock
    private PaymentValidation paymentValidation;

    @Test
    void testGetChargedRefundReason() {
        // Act & Assert
        assertThrows(UnsupportedOperationException.class, () -> orderChargesRefundService.getChargedRefundReason());
    }

    @Test
    void testCreateRefundObject() {
        // Arrange
        var order = OrderGenerator.createOrder();
        var requestId = "requestId";
        var refundOptions = RefundOptions.builder()
            .csrInitials("csrInitials")
            .build();

        // Act
        var result = orderChargesRefundService.createRefundObject(order,
            null,
            requestId,
            null,
            refundOptions);

        // Assert
        assertThat(result)
            .isNotNull()
            .hasFieldOrPropertyWithValue("refundLines", new ArrayList<>())
            .hasFieldOrPropertyWithValue("refundState", RefundState.CREATED)
            .hasFieldOrPropertyWithValue("order", order)
            .hasFieldOrPropertyWithValue("csrInitials", "csrInitials")
            .hasFieldOrPropertyWithValue("refundId", null)
            .hasFieldOrPropertyWithValue("refundCharges", new ArrayList<>())
            .hasFieldOrPropertyWithValue("requestId", "requestId");
    }

    @Test
    void testRefundOrderCharges() {
        // Arrange
        var orderChargesRefundRequest = OrderChargesRefundRequest.builder()
            .orderId("orderId")
            .build();
        var order = OrderGenerator.createOrder();
        order.setVatOrderNumber("vatOrderNumber");
        order.setPaymentStatus(PaymentState.SETTLED);
        List<OrderItemToRefund> emptyItemsToRefund = new ArrayList<>();
        var refundOptions = RefundOptions.builder()
            .orderChargeRefundRequests(orderChargesRefundRequest.orderChargesRefundRequestList())
            .chargeReturnFee(false)
            .refundShippingFee(false)
            .refundReason(RefundReason.MANUAL_ORDER_CHARGE_REFUND)
            .csrInitials(orderChargesRefundRequest.csrInitials())
            .build();

        when(paymentValidation.validateRefundRequest(order))
            .thenReturn(true);

        when(orderService.getOrderById(orderChargesRefundRequest.orderId()))
            .thenReturn(order);

        doNothing().when(orderChargesRefundService).processRefund(any(),
            any(),
            any(),
            any(),
            any());

        // Act
        orderChargesRefundService.refundOrderCharges(orderChargesRefundRequest);

        // Assert
        verify(orderChargesRefundService).processRefund(eq(order),
            any(),
            eq(emptyItemsToRefund),
            eq(BigDecimal.ZERO),
            eq(refundOptions));
    }
}
