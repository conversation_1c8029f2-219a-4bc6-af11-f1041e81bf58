package com.bestseller.payment.core.service.refund;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequest.PaymentRefundRequest;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundStatusUpdated.RefundStatusUpdated;
import com.bestseller.payment.adapter.stream.messagegenerator.BankPaymentRefundRequestGenerator;
import com.bestseller.payment.adapter.stream.messagegenerator.GiftCardPaymentRefundRequestGenerator;
import com.bestseller.payment.adapter.stream.messagegenerator.RefundStatusUpdatedGenerator;
import com.bestseller.payment.core.domain.CustomerRefundChoice;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderLine;
import com.bestseller.payment.core.domain.OverallTotal;
import com.bestseller.payment.core.domain.Refund;
import com.bestseller.payment.core.domain.RefundLine;
import com.bestseller.payment.core.domain.enumeration.Brand;
import com.bestseller.payment.core.domain.enumeration.ChargedRefundReason;
import com.bestseller.payment.core.domain.enumeration.CustomerRefundMethod;
import com.bestseller.payment.core.dto.GiftCardRefundSource;
import com.bestseller.payment.core.dto.OrderItemToRefund;
import com.bestseller.payment.core.dto.RefundCalculationResult;
import com.bestseller.payment.core.dto.RefundOptions;
import com.bestseller.payment.core.dto.RefundReason;
import com.bestseller.payment.core.dto.ReturnFeeCalculationResult;
import com.bestseller.payment.core.service.order.OrderService;
import com.bestseller.payment.core.service.price.PriceService;
import com.logistics.statetransition.RefundState;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RefundCreationServiceTest {
    @Mock
    OrderService orderService;

    @Mock
    ReturnFeeService returnFeeService;

    @Mock
    PriceService priceService;

    @Mock
    RefundCalculationService refundCalculationService;

    @Mock
    QueueProducer<PaymentRefundRequest> paymentRefundRequestProducerQueueProducer;

    @Mock
    QueueProducer<RefundStatusUpdated> refundStatusUpdatedProducerQueueProducer;

    @Mock
    RefundStatusUpdatedGenerator refundStatusUpdatedGenerator;

    @Mock
    GiftCardPaymentRefundRequestGenerator giftCardPaymentRefundRequestGenerator;

    @Mock
    BankPaymentRefundRequestGenerator bankPaymentRefundRequestGenerator;

    RefundCreationService refundCreationService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        refundCreationService = spy(new RefundCreationService(
            orderService,
            returnFeeService,
            priceService,
            refundCalculationService,
            paymentRefundRequestProducerQueueProducer,
            refundStatusUpdatedProducerQueueProducer,
            refundStatusUpdatedGenerator,
            giftCardPaymentRefundRequestGenerator,
            bankPaymentRefundRequestGenerator
        ) {
            @Override
            protected Refund createRefundObject(Order loadedOrder,
                                                List<OrderItemToRefund> orderItemToRefundList,
                                                String requestId,
                                                BigDecimal totalRefundAmount,
                                                RefundOptions refundOptions) {
                return Refund.builder()
                    .refundId("refundId")
                    .refundState(RefundState.CREATED)
                    .requestId(requestId)
                    .csrInitials(SYSTEM_USER)
                    .refundLines(List.of())
                    .build();
            }

            @Override
            protected ChargedRefundReason getChargedRefundReason() {
                return ChargedRefundReason.RETURNED_ITEMS_IN_STORE;
            }
        });
    }

    @ParameterizedTest
    @MethodSource("refundAmountProvider")
    void baseRefund_validOrder_refundsItems_Bank(int bankAmount, int giftCardAmount) {
        // arrange
        Order order = Order.builder().refunds(new ArrayList<>()).build();
        String requestId = "requestId";
        List<OrderItemToRefund> orderItemToRefundList = List.of(OrderItemToRefund.builder().build());
        BigDecimal totalRefundAmount = BigDecimal.TEN;
        RefundOptions refundOptions = RefundOptions.builder()
            .refundShippingFee(false)
            .chargeReturnFee(false)
            .build();
        when(refundCalculationService.calculate(any(), any())).thenReturn(RefundCalculationResult.builder()
            .amountToRefundByBankTransactionInCents(bankAmount).amountToRefundByGiftCardInCents(giftCardAmount).build());
        when(bankPaymentRefundRequestGenerator.generate(any())).thenReturn(new PaymentRefundRequest());
        when(refundStatusUpdatedGenerator.generate(any())).thenReturn(new RefundStatusUpdated());
        when(orderService.save(order, true)).thenReturn(order);

        // act
        refundCreationService.processRefund(order, requestId, orderItemToRefundList, totalRefundAmount, refundOptions);

        // assert
        verify(refundCreationService).createRefundObject(order, orderItemToRefundList, requestId, totalRefundAmount,
            refundOptions);
        verify(orderService, times(2)).save(any(), eq(true));
        verify(priceService, times(2)).processAndSetPriceFieldsForRefundLineAndRefundTotalPaidAmount(any());
        verify(refundCalculationService).calculate(any(), any());
        verify(refundStatusUpdatedProducerQueueProducer, times(2)).enqueue(any());
        verify(paymentRefundRequestProducerQueueProducer).enqueue(any());
        verify(returnFeeService, never()).calculateReturnFee(any(), any(), any(), any());
    }

    @Test
    void baseRefund_validOrder_refundsItems_GiftCard() {
        // arrange
        Order order = Order.builder().refunds(new ArrayList<>()).build();
        String requestId = "requestId";
        List<OrderItemToRefund> orderItemToRefundList = List.of(OrderItemToRefund.builder().build());
        BigDecimal totalRefundAmount = BigDecimal.TEN;
        RefundOptions refundOptions = RefundOptions.builder()
            .refundReason(RefundReason.RETURN)
            .refundShippingFee(false)
            .chargeReturnFee(false)
            .build();
        when(refundCalculationService.calculate(any(), any())).thenReturn(RefundCalculationResult.builder()
            .amountToRefundByBankTransactionInCents(0).amountToRefundByGiftCardInCents(10).build());
        when(giftCardPaymentRefundRequestGenerator.generate(any())).thenReturn(new PaymentRefundRequest());
        when(orderService.save(order, true)).thenReturn(order);

        // act
        refundCreationService.processRefund(order, requestId, orderItemToRefundList, totalRefundAmount, refundOptions);

        // assert
        verify(refundCreationService).createRefundObject(order, orderItemToRefundList, requestId, totalRefundAmount,
            refundOptions);
        verify(orderService, times(2)).save(any(), eq(true));
        verify(priceService, times(2)).processAndSetPriceFieldsForRefundLineAndRefundTotalPaidAmount(any());
        verify(refundCalculationService).calculate(any(), any());
        verify(refundStatusUpdatedProducerQueueProducer, never()).enqueue(any());
        verify(paymentRefundRequestProducerQueueProducer).enqueue(any());
        verify(returnFeeService, never()).calculateReturnFee(any(), any(), any(), any());
    }

    @Test
    void baseRefund_validOrder_refundsItems() {
        // arrange
        Order order = Order.builder().refunds(new ArrayList<>()).brand(Brand.JJ).build();
        String requestId = "requestId";
        List<OrderItemToRefund> orderItemToRefundList = List.of(OrderItemToRefund.builder().build());
        BigDecimal totalRefundAmount = BigDecimal.TEN;
        RefundOptions refundOptions = RefundOptions.builder()
            .refundReason(RefundReason.RETURN)
            .refundShippingFee(false)
            .chargeReturnFee(true)
            .build();

        doAnswer(invocation -> {
            Refund refundArg = invocation.getArgument(0);
            OverallTotal refundTotal = OverallTotal.builder()
                .grossDiscountedTotal(new BigDecimal(BigInteger.TEN))
                .build();
            refundArg.setRefundTotal(refundTotal);  // Modify the captured Refund object
            return null;
        }).when(priceService).processAndSetPriceFieldsForRefundLineAndRefundTotalPaidAmount(any(Refund.class));

        when(returnFeeService.calculateReturnFee(any(), any(), any(), any())).thenReturn(ReturnFeeCalculationResult.builder().returnFee(BigDecimal.ZERO).taxRate(BigDecimal.TEN).build());

        when(refundCalculationService.calculate(any(), any())).thenReturn(RefundCalculationResult.builder()
            .amountToRefundByBankTransactionInCents(10).amountToRefundByGiftCardInCents(0).build());
        when(bankPaymentRefundRequestGenerator.generate(any())).thenReturn(new PaymentRefundRequest());
        when(refundStatusUpdatedGenerator.generate(any())).thenReturn(new RefundStatusUpdated());
        when(orderService.save(order, true)).thenReturn(order);

        // act
        refundCreationService.processRefund(order, requestId, orderItemToRefundList, totalRefundAmount, refundOptions);

        // assert
        verify(refundCreationService).createRefundObject(order, orderItemToRefundList, requestId, totalRefundAmount,
            refundOptions);
        verify(orderService, times(2)).save(any(), eq(true));
        verify(priceService, times(2)).processAndSetPriceFieldsForRefundLineAndRefundTotalPaidAmount(any());
        verify(refundCalculationService).calculate(any(), any());
        verify(refundStatusUpdatedProducerQueueProducer, times(2)).enqueue(any());
        verify(paymentRefundRequestProducerQueueProducer).enqueue(any());
        verify(returnFeeService).calculateReturnFee(any(), any(), any(), any());
    }

    private static Stream<Arguments> refundAmountProvider() {
        return Stream.of(
            Arguments.of(10, 0),
            Arguments.of(100, 0)
        );
    }

    @Test
    void processRefund_withCustomerGiftCardChoice_setsCustomerPreference() {
        // arrange
        Order order = Order.builder().refunds(new ArrayList<>()).build();
        String requestId = "requestId";
        List<OrderItemToRefund> orderItemToRefundList = List.of(OrderItemToRefund.builder().build());
        BigDecimal totalRefundAmount = BigDecimal.TEN;
        RefundOptions refundOptions = RefundOptions.builder()
            .refundReason(RefundReason.RETURN)
            .refundShippingFee(false)
            .chargeReturnFee(false)
            .build();

        // Create refund with customer gift card choice
        Refund refundWithGiftCardChoice = Refund.builder()
            .refundId("refundId")
            .refundState(RefundState.CREATED)
            .requestId(requestId)
            .csrInitials("csrInitials")
            .refundLines(List.of(
                RefundLine.builder()
                    .quantity(1)
                    .orderLine(OrderLine.builder()
                        .ean("ean1")
                        .customerRefundChoices(List.of(
                            CustomerRefundChoice.builder()
                                .customerRefundMethod(CustomerRefundMethod.GIFT_CARD)
                                .isUsed(false)
                                .build()
                        ))
                        .build())
                    .build()
            ))
            .build();

        when(refundCalculationService.calculate(any(), any())).thenReturn(RefundCalculationResult.builder()
            .amountToRefundByBankTransactionInCents(0)
            .amountToRefundByGiftCardInCents(100)
            .build());

        // Mock the saved order to return the refund with gift card choice
        Order savedOrder = Order.builder()
            .refunds(List.of(refundWithGiftCardChoice))
            .build();
        when(orderService.save(order, true)).thenReturn(savedOrder);

        // act
        refundCreationService.processRefund(order, requestId, orderItemToRefundList, totalRefundAmount, refundOptions);

        // assert
        verify(giftCardPaymentRefundRequestGenerator).generate(any());
        verify(giftCardPaymentRefundRequestGenerator).generate(argThat(refundDto ->
            refundDto.giftCardRefundSource() == GiftCardRefundSource.CUSTOMER_PREFERENCE));
    }

    @Test
    void processRefund_withoutCustomerGiftCardChoice_setsOriginalPayment() {
        // arrange
        Order order = Order.builder().refunds(new ArrayList<>()).build();
        String requestId = "requestId";
        List<OrderItemToRefund> orderItemToRefundList = List.of(OrderItemToRefund.builder().build());
        BigDecimal totalRefundAmount = BigDecimal.TEN;
        RefundOptions refundOptions = RefundOptions.builder()
            .refundReason(RefundReason.RETURN)
            .refundShippingFee(false)
            .chargeReturnFee(false)
            .build();

        Refund refundWithoutGiftCardChoice = Refund.builder()
            .refundId("refundId")
            .refundState(RefundState.CREATED)
            .requestId(requestId)
            .csrInitials("csrInitials")
            .refundLines(List.of(
                RefundLine.builder()
                    .quantity(1)
                    .orderLine(OrderLine.builder()
                        .ean("ean1")
                        .customerRefundChoices(List.of(
                            CustomerRefundChoice.builder()
                                .customerRefundMethod(CustomerRefundMethod.REFUND)
                                .isUsed(false)
                                .build()
                        ))
                        .build())
                    .build()
            ))
            .build();

        when(refundCalculationService.calculate(any(), any())).thenReturn(RefundCalculationResult.builder()
            .amountToRefundByBankTransactionInCents(0)
            .amountToRefundByGiftCardInCents(100)
            .build());

        Order savedOrder = Order.builder()
            .refunds(List.of(refundWithoutGiftCardChoice))
            .build();
        when(orderService.save(order, true)).thenReturn(savedOrder);

        // act
        refundCreationService.processRefund(order, requestId, orderItemToRefundList, totalRefundAmount, refundOptions);

        // assert
        verify(giftCardPaymentRefundRequestGenerator).generate(any());
        verify(giftCardPaymentRefundRequestGenerator).generate(argThat(refundDto ->
            refundDto.giftCardRefundSource() == GiftCardRefundSource.ORIGINAL_PAYMENT));
    }

    @Test
    void processRefund_withEmptyCustomerRefundChoices_setsOriginalPayment() {
        // arrange
        Order order = Order.builder().refunds(new ArrayList<>()).build();
        String requestId = "requestId";
        List<OrderItemToRefund> orderItemToRefundList = List.of(OrderItemToRefund.builder().build());
        BigDecimal totalRefundAmount = BigDecimal.TEN;
        RefundOptions refundOptions = RefundOptions.builder()
            .refundReason(RefundReason.RETURN)
            .refundShippingFee(false)
            .chargeReturnFee(false)
            .build();

        // Create refund with empty customer refund choices
        Refund refundWithEmptyChoices = Refund.builder()
            .refundId("refundId")
            .refundState(RefundState.CREATED)
            .requestId(requestId)
            .csrInitials("csrInitials")
            .refundLines(List.of(
                RefundLine.builder()
                    .quantity(1)
                    .orderLine(OrderLine.builder()
                        .ean("ean1")
                        .customerRefundChoices(List.of()) // Empty list
                        .build())
                    .build()
            ))
            .build();

        when(refundCalculationService.calculate(any(), any())).thenReturn(RefundCalculationResult.builder()
            .amountToRefundByBankTransactionInCents(0)
            .amountToRefundByGiftCardInCents(100)
            .build());

        // Mock the saved order to return the refund with empty choices
        Order savedOrder = Order.builder()
            .refunds(List.of(refundWithEmptyChoices))
            .build();
        when(orderService.save(order, true)).thenReturn(savedOrder);

        // act
        refundCreationService.processRefund(order, requestId, orderItemToRefundList, totalRefundAmount, refundOptions);

        // assert
        verify(giftCardPaymentRefundRequestGenerator).generate(any());
        verify(giftCardPaymentRefundRequestGenerator).generate(argThat(refundDto ->
            refundDto.giftCardRefundSource() == GiftCardRefundSource.ORIGINAL_PAYMENT));
    }
}
