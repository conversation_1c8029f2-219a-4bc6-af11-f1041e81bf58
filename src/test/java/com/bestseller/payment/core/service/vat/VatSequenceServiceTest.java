package com.bestseller.payment.core.service.vat;

import com.bestseller.payment.adapter.repository.VatSequenceRepository;
import com.bestseller.payment.core.domain.enumeration.VatType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class VatSequenceServiceTest {

    VatSequenceServiceImpl vatSequenceService;

    @Mock
    VatSequenceRepository vatSequenceRepository;

    @BeforeEach
    void setUp() {
        vatSequenceService =
            new VatSequenceServiceImpl(9, "-REF-", vatSequenceRepository);
    }

    @Test
    void getNextOrderVatId() {
        // Arrange
        String countryCode = "US";
        int year = 2024;
        Date orderDate = new GregorianCalendar(year, Calendar.FEBRUARY, 11).getTime();
        String expectedValue = "US241000000001";

        when(vatSequenceRepository.updateSequenceAndGetLastId(countryCode, year, VatType.ORDER.name())).thenReturn(1L);

        // Act
        var actualValue = vatSequenceService.getNextOrderVatId(countryCode, orderDate);

        // Assert
        verify(vatSequenceRepository).updateSequenceAndGetLastId(countryCode, year, VatType.ORDER.name());
        assertThat(actualValue).isEqualTo(expectedValue);
    }

    @Test
    void getNextRefundVatId() {
        // Arrange
        String countryCode = "US";
        int year = 2024;
        Date orderDate = new GregorianCalendar(year, Calendar.FEBRUARY, 11).getTime();
        String expectedValue = "US241000000001";

        when(vatSequenceRepository.updateSequenceAndGetLastId(countryCode, year, VatType.REFUND.name())).thenReturn(1L);

        // Act
        var actualValue = vatSequenceService.getNextRefundVatId(countryCode, orderDate);

        // Assert
        verify(vatSequenceRepository).updateSequenceAndGetLastId(countryCode, year, VatType.REFUND.name());
        assertThat(actualValue).isEqualTo(expectedValue);
    }

    @Test
    void getNextRefundInStoreVatId() {
        // Arrange
        String countryCode = "US";
        int refundId = 1;

        // Act
        var actual = vatSequenceService.getNextRefundInStoreVatId(countryCode, refundId);

        // Assert
        assertThat(actual).isEqualTo("US-REF-1");
    }
}
