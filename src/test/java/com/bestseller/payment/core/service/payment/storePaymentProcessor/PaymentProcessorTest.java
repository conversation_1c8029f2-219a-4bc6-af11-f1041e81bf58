package com.bestseller.payment.core.service.payment.storePaymentProcessor;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.Payment;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.enumeration.PaymentMethod;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import com.bestseller.payment.core.domain.payment.KlarnaPayment;
import com.bestseller.payment.core.exception.MultipleNonGiftcardPaymentsException;
import com.bestseller.payment.core.service.payment.factory.PaymentFactory;
import com.bestseller.payment.utils.ValidOrderPlacedGenerator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PaymentProcessorTest {

    @Mock
    private PaymentFactory paymentFactory;
    private OnlinePaymentProcessor onlinePaymentProcessor;
    private Order order;

    @BeforeEach
    public void setUp() {
        // Initialize the concrete subclass with necessary dependencies
        order = new Order(/* initialize Order as needed */);
        ValidOrderPlaced validOrderPlaced = ValidOrderPlacedGenerator.createBseOrderPlacedMessage();
        Payment paymentKlarnaInvoice = new Payment()
                .withProvider(ProcessorId.KLARNA_PAYMENTS.name())
                .withMethod(PaymentMethod.KLARNA_INVOICE.name())
                .withState(PaymentState.AUTHORISED.name());
        Payment paymentKlarnaAccount = new Payment()
                .withProvider(ProcessorId.KLARNA_PAYMENTS.name())
                .withMethod(PaymentMethod.KLARNA_ACCOUNT.name())
                .withState(PaymentState.SETTLED.name());
        validOrderPlaced.withPayments(List.of(paymentKlarnaInvoice, paymentKlarnaAccount));
        onlinePaymentProcessor = new OnlinePaymentProcessor(validOrderPlaced.getPayments(), "Country", order, paymentFactory);
    }

    @Test
    public void testSetPaymentMethod() {
        //Arrange
        ValidOrderPlaced validOrderPlaced = ValidOrderPlacedGenerator.createBseOrderPlacedMessage();
        Payment paymentKlarnaInvoice = new Payment()
                .withProvider(ProcessorId.KLARNA_PAYMENTS.name())
                .withMethod(PaymentMethod.KLARNA_INVOICE.name())
                .withState(PaymentState.AUTHORISED.name());
        Payment paymentKlarnaAccount = new Payment()
                .withProvider(ProcessorId.KLARNA_PAYMENTS.name())
                .withMethod(PaymentMethod.KLARNA_ACCOUNT.name())
                .withState(PaymentState.SETTLED.name());
        validOrderPlaced.withPayments(List.of(paymentKlarnaInvoice, paymentKlarnaAccount));
        Order order = new Order();
        KlarnaPayment klarnaPayment = new KlarnaPayment();
        klarnaPayment.setType(PaymentType.KLARNA);

        when(paymentFactory.createPayment(any(), any(), any(), any(), any(), any())).thenReturn(klarnaPayment);

        // Act & Assert
        assertThrows(MultipleNonGiftcardPaymentsException.class, () -> onlinePaymentProcessor.evaluatePaymentMethod());
    }
}
