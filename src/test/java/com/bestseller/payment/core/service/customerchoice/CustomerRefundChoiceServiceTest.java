package com.bestseller.payment.core.service.customerchoice;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.postPurchaseEventReceived.PostPurchaseEventReceived;
import com.bestseller.interfacecontractannotator.model.postpurchaseeventreceived.ReturnCreatedPayload;
import com.bestseller.interfacecontractannotator.model.postpurchaseeventreceived.ReturnRequest;
import com.bestseller.payment.adapter.repository.CustomerRefundChoiceRepository;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.core.domain.CustomerRefundChoice;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderLine;
import com.bestseller.payment.core.domain.enumeration.CustomerRefundMethod;
import com.bestseller.payment.core.exception.MoreQuantityRequestedToRefundException;
import com.bestseller.payment.core.exception.OrderLineNotFoundException;
import com.bestseller.payment.core.exception.OrderNotFoundException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CustomerRefundChoiceServiceTest {

    @Mock
    private CustomerRefundChoiceRepository customerRefundChoiceRepository;

    @Mock
    private OrderRepository orderRepository;

    @InjectMocks
    private CustomerRefundChoiceService customerRefundChoiceService;

    @Captor
    private ArgumentCaptor<List<CustomerRefundChoice>> customerChoiceCaptor;

    @Test
    void processPostPurchaseEvent_givenValidEvent_createsCustomerChoices() {
        // Arrange
        String orderId = "order123";
        String returnId = "return456";
        String ean = "1234567890123";
        String refundMethod = "refund";
        int quantity = 2;

        PostPurchaseEventReceived event = createPostPurchaseEventReceived(orderId, returnId, ean, quantity, refundMethod);

        OrderLine orderLine = OrderLine.builder()
            .orderEntryId(123)
            .ean(ean)
            .originalQty(10)
            .openQty(5)
            .build();

        Order order = Order.builder()
            .orderId(orderId)
            .orderLines(List.of(orderLine))
            .build();

        when(orderRepository.findById(orderId)).thenReturn(Optional.of(order));
        when(customerRefundChoiceRepository.saveAll(anyList())).thenAnswer(invocation -> invocation.getArgument(0));

        // Act
        customerRefundChoiceService.processPostPurchaseEvent(event);

        // Assert
        verify(orderRepository, times(1)).findById(orderId);
        verify(customerRefundChoiceRepository, times(1)).saveAll(customerChoiceCaptor.capture());

        List<CustomerRefundChoice> savedCustomerRefundChoices = customerChoiceCaptor.getValue();
        assertThat(savedCustomerRefundChoices).hasSize(quantity);

        for (CustomerRefundChoice customerRefundChoice : savedCustomerRefundChoices) {
            assertThat(customerRefundChoice.getOrderLine()).isEqualTo(orderLine);
            assertThat(customerRefundChoice.getCustomerRefundMethod()).isEqualTo(CustomerRefundMethod.REFUND);
            assertThat(customerRefundChoice.getReturnId()).isEqualTo(returnId);
        }
    }

    @Test
    void processPostPurchaseEvent_givenUnknownOrder_throwsOrderNotFoundException() {
        // Arrange
        String orderId = "nonexistent";
        PostPurchaseEventReceived event = createPostPurchaseEventReceived(orderId, "return123", "1234567890123", 1, "refund");

        when(orderRepository.findById(orderId)).thenReturn(Optional.empty());

        // Act & Assert
        OrderNotFoundException exception = assertThrows(OrderNotFoundException.class,
            () -> customerRefundChoiceService.processPostPurchaseEvent(event));

        assertThat(exception.getMessage()).isEqualTo(String.format("Order %s not found", orderId));
        verify(orderRepository, times(1)).findById(orderId);
        verify(customerRefundChoiceRepository, times(0)).saveAll(any());
    }

    @Test
    void processPostPurchaseEvent_givenUnknownOrderLine_throwsOrderLineNotFoundException() {
        // Arrange
        String orderId = "order123";
        String returnId = "return456";
        String ean = "1234567890123";
        String nonExistentEan = "9999999999999";

        PostPurchaseEventReceived event = createPostPurchaseEventReceived(orderId, returnId, nonExistentEan, 1, "refund");

        OrderLine orderLine = OrderLine.builder()
            .orderEntryId(123)
            .ean(ean)
            .originalQty(5)
            .build();

        Order order = Order.builder()
            .orderId(orderId)
            .orderLines(List.of(orderLine))
            .build();

        when(orderRepository.findById(orderId)).thenReturn(Optional.of(order));

        // Act & Assert
        OrderLineNotFoundException exception = assertThrows(OrderLineNotFoundException.class,
            () -> customerRefundChoiceService.processPostPurchaseEvent(event));

        assertThat(exception.getMessage()).isEqualTo("Order line not found for EAN: " + nonExistentEan);
        verify(orderRepository, times(1)).findById(orderId);
        verify(customerRefundChoiceRepository, times(0)).saveAll(any());
    }

    @Test
    void processPostPurchaseEvent_givenExcessiveQuantity_throwsMoreQuantityRequestedToRefund() {
        // Arrange
        String orderId = "order123";
        String returnId = "return456";
        String ean = "1234567890123";
        String refundMethod = "refund";
        int requestedQuantity = 5;
        int availableQuantity = 3;

        PostPurchaseEventReceived event = createPostPurchaseEventReceived(orderId, returnId, ean, requestedQuantity, refundMethod);

        OrderLine orderLine = OrderLine.builder()
            .orderEntryId(123)
            .ean(ean)
            .openQty(availableQuantity)
            .build();

        Order order = Order.builder()
            .orderId(orderId)
            .orderLines(List.of(orderLine))
            .build();

        when(orderRepository.findById(orderId)).thenReturn(Optional.of(order));

        // Act & Assert
        MoreQuantityRequestedToRefundException exception = assertThrows(MoreQuantityRequestedToRefundException.class,
            () -> customerRefundChoiceService.processPostPurchaseEvent(event));

        assertThat(exception.getMessage()).contains(ean);
        assertThat(exception.getMessage()).contains(String.valueOf(requestedQuantity));
        assertThat(exception.getMessage()).contains(String.valueOf(availableQuantity));
        verify(orderRepository, times(1)).findById(orderId);
        verify(customerRefundChoiceRepository, times(0)).saveAll(any());
    }

    @Test
    void processPostPurchaseEvent_givenQuantityPlusThePersistedQuantityOnTable_throwsMoreQuantityRequestedToRefund() {
        // Arrange
        String orderId = "order123";
        String returnId = "return456";
        String ean = "1234567890123";
        String refundMethod = "refund";
        int requestedQuantity = 2;
        int availableQuantity = 3;
        int originalQty = 5;

        PostPurchaseEventReceived event = createPostPurchaseEventReceived(orderId, returnId, ean, requestedQuantity, refundMethod);

        OrderLine orderLine = OrderLine.builder()
            .orderEntryId(123)
            .ean(ean)
            .openQty(availableQuantity)
            .originalQty(originalQty)
            .build();

        Order order = Order.builder()
            .orderId(orderId)
            .orderLines(List.of(orderLine))
            .build();

        when(orderRepository.findById(orderId)).thenReturn(Optional.of(order));
        when(customerRefundChoiceRepository.countByOrderEntryId(orderId, orderLine.getOrderEntryId())).thenReturn(2);

        // Act & Assert
        MoreQuantityRequestedToRefundException exception = assertThrows(MoreQuantityRequestedToRefundException.class,
            () -> customerRefundChoiceService.processPostPurchaseEvent(event));

        assertThat(exception.getMessage()).contains(ean);
        assertThat(exception.getMessage()).contains(String.valueOf(requestedQuantity));
        assertThat(exception.getMessage()).contains(String.valueOf(availableQuantity));
        verify(orderRepository, times(1)).findById(orderId);
        verify(customerRefundChoiceRepository, times(0)).saveAll(any());
    }

    @Test
    void processPostPurchaseEvent_givenMultipleReturnItems_createsCustomerChoicesForAllItems() {
        // Arrange
        String orderId = "order123";
        String returnId = "return456";
        String refundMethod = "refund";

        // Create event with multiple return items
        ReturnRequest returnRequest1 = new ReturnRequest();
        returnRequest1.setEan("1234567890123");
        returnRequest1.setQuantity(2);

        ReturnRequest returnRequest2 = new ReturnRequest();
        returnRequest2.setEan("9876543210987");
        returnRequest2.setQuantity(1);

        ReturnCreatedPayload payload = new ReturnCreatedPayload();
        payload.setReturnId(returnId);
        payload.setReturnRequest(List.of(returnRequest1, returnRequest2));
        payload.setRefundMethod(refundMethod);

        PostPurchaseEventReceived event = new PostPurchaseEventReceived();
        event.setOrderId(orderId);
        event.setData(payload);


        OrderLine orderLine1 = OrderLine.builder()
            .orderEntryId(123)
            .ean("1234567890123")
            .originalQty(10)
            .openQty(5)
            .build();

        OrderLine orderLine2 = OrderLine.builder()
            .orderEntryId(456)
            .ean("9876543210987")
            .originalQty(10)
            .openQty(3)
            .build();

        Order order = Order.builder()
            .orderId(orderId)
            .orderLines(List.of(orderLine1, orderLine2))
            .build();

        when(orderRepository.findById(orderId)).thenReturn(Optional.of(order));
        when(customerRefundChoiceRepository.saveAll(anyList())).thenAnswer(invocation -> invocation.getArgument(0));

        // Act
        customerRefundChoiceService.processPostPurchaseEvent(event);

        // Assert
        verify(orderRepository, times(1)).findById(orderId);
        verify(customerRefundChoiceRepository, times(1)).saveAll(customerChoiceCaptor.capture()); // 2 + 1 = 3 total

        List<CustomerRefundChoice> savedCustomerRefundChoices = customerChoiceCaptor.getValue();
        assertThat(savedCustomerRefundChoices).hasSize(3);

        // Check first item (quantity 2)
        assertThat(savedCustomerRefundChoices.get(0).getOrderLine()).isEqualTo(orderLine1);
        assertThat(savedCustomerRefundChoices.get(0).getCustomerRefundMethod()).isEqualTo(CustomerRefundMethod.REFUND);
        assertThat(savedCustomerRefundChoices.get(0).getReturnId()).isEqualTo(returnId);

        assertThat(savedCustomerRefundChoices.get(1).getOrderLine()).isEqualTo(orderLine1);
        assertThat(savedCustomerRefundChoices.get(1).getCustomerRefundMethod()).isEqualTo(CustomerRefundMethod.REFUND);
        assertThat(savedCustomerRefundChoices.get(1).getReturnId()).isEqualTo(returnId);

        // Check second item (quantity 1)
        assertThat(savedCustomerRefundChoices.get(2).getOrderLine()).isEqualTo(orderLine2);
        assertThat(savedCustomerRefundChoices.get(2).getCustomerRefundMethod()).isEqualTo(CustomerRefundMethod.REFUND);
        assertThat(savedCustomerRefundChoices.get(2).getReturnId()).isEqualTo(returnId);
    }

    @Test
    void processPostPurchaseEvent_givenZeroQuantity_createsNoCustomerChoices() {
        // Arrange
        String orderId = "order123";
        String returnId = "return456";
        String ean = "1234567890123";
        String refundMethod = "giftCard";
        int quantity = 0;

        PostPurchaseEventReceived event = createPostPurchaseEventReceived(orderId, returnId, ean, quantity, refundMethod);

        OrderLine orderLine = OrderLine.builder()
            .orderEntryId(123)
            .ean(ean)
            .originalQty(10)
            .openQty(5)
            .build();

        Order order = Order.builder()
            .orderId(orderId)
            .orderLines(List.of(orderLine))
            .build();

        when(orderRepository.findById(orderId)).thenReturn(Optional.of(order));

        // Act
        customerRefundChoiceService.processPostPurchaseEvent(event);

        // Assert
        verify(orderRepository, times(1)).findById(orderId);
        verify(customerRefundChoiceRepository, times(0)).saveAll(any()); // No customer choices should be created
    }

    @Test
    void processPostPurchaseEvent_givenExactQuantityMatch_createsCustomerChoices() {
        // Arrange
        String orderId = "order123";
        String returnId = "return456";
        String ean = "1234567890123";
        String refundMethod = "refund";
        int quantity = 3;

        PostPurchaseEventReceived event = createPostPurchaseEventReceived(orderId, returnId, ean, quantity, refundMethod);

        OrderLine orderLine = OrderLine.builder()
            .orderEntryId(123)
            .ean(ean)
            .openQty(quantity) // Exact match
            .build();

        Order order = Order.builder()
            .orderId(orderId)
            .orderLines(List.of(orderLine))
            .build();

        when(orderRepository.findById(orderId)).thenReturn(Optional.of(order));
        when(customerRefundChoiceRepository.saveAll(anyList())).thenAnswer(invocation -> invocation.getArgument(0));

        // Act
        customerRefundChoiceService.processPostPurchaseEvent(event);

        // Assert
        verify(orderRepository, times(1)).findById(orderId);
        verify(customerRefundChoiceRepository, times(1)).saveAll(customerChoiceCaptor.capture());

        List<CustomerRefundChoice> savedCustomerRefundChoices = customerChoiceCaptor.getValue();
        assertThat(savedCustomerRefundChoices).hasSize(quantity);

        for (CustomerRefundChoice customerRefundChoice : savedCustomerRefundChoices) {
            assertThat(customerRefundChoice.getOrderLine()).isEqualTo(orderLine);
            assertThat(customerRefundChoice.getCustomerRefundMethod()).isEqualTo(CustomerRefundMethod.REFUND);
            assertThat(customerRefundChoice.getReturnId()).isEqualTo(returnId);
        }
    }

    private PostPurchaseEventReceived createPostPurchaseEventReceived(String orderId, String returnId, String ean, int quantity, String refundMethod) {
        ReturnRequest returnRequest = new ReturnRequest();
        returnRequest.setEan(ean);
        returnRequest.setQuantity(quantity);

        ReturnCreatedPayload payload = new ReturnCreatedPayload();
        payload.setReturnId(returnId);
        payload.setReturnRequest(List.of(returnRequest));
        payload.setRefundMethod(refundMethod);

        PostPurchaseEventReceived event = new PostPurchaseEventReceived();
        event.setOrderId(orderId);
        event.setData(payload);

        return event;
    }
}
