package com.bestseller.payment.core.service.payment.processors;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.Payment;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.enumeration.PaymentMethod;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import com.bestseller.payment.core.domain.payment.KlarnaPayment;
import com.bestseller.payment.core.exception.PaymentValidationException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

@ExtendWith(MockitoExtension.class)
class KlarnaPaymentProcessorTest {
    private static final String KLARNA_INVOICE_SUB_METHOD = "-1";

    @InjectMocks
    private KlarnaPaymentProcessor klarnaPaymentProcessor;


    @Test
    public void testCreatePayment_KlarnaInvoice() {
        // Arrange
        PaymentMethod paymentMethod = PaymentMethod.KLARNA_INVOICE;
        boolean isBcmc = false;
        String subMethod = null;
        String billingCountryCode = null;

        // Act
        PaymentInfo payment = klarnaPaymentProcessor.createPayment(paymentMethod, isBcmc, subMethod, billingCountryCode);

        // Assert
        Assertions.assertNotNull(payment);
        Assertions.assertTrue(payment instanceof KlarnaPayment);
        Assertions.assertEquals(ProcessorId.KLARNA_PAYMENTS, payment.getProcessorId());
        Assertions.assertEquals(KLARNA_INVOICE_SUB_METHOD, payment.getSubMethod());
        Assertions.assertEquals(PaymentType.KLARNA, payment.getType());
    }

    @Test
    public void testCreatePayment_KlarnaAccount() {
        // Arrange
        PaymentMethod paymentMethod = PaymentMethod.KLARNA_ACCOUNT;
        boolean isBcmc = true;
        String subMethod = null;
        String billingCountryCode = "DE";

        // Act
        PaymentInfo payment = klarnaPaymentProcessor.createPayment(paymentMethod, isBcmc, subMethod, billingCountryCode);

        // Assert
        Assertions.assertNotNull(payment);
        Assertions.assertTrue(payment instanceof KlarnaPayment);
        Assertions.assertEquals(ProcessorId.KLARNA_PAYMENTS, payment.getProcessorId());
        Assertions.assertEquals("1922", payment.getSubMethod()); // Replace with expected mapping
        Assertions.assertEquals(PaymentType.KLARNA, payment.getType());
    }

    @Test
    public void testCreatePayment_InvalidPaymentMethod() {
        // Arrange
        PaymentMethod paymentMethod = PaymentMethod.ADYEN_PAYPAL; // Invalid payment method
        boolean isBcmc = false;
        String subMethod = null;
        String billingCountryCode = null;

        // Act and Assert (expecting IllegalArgumentException)
        Assertions.assertThrows(PaymentValidationException.class,
                () -> klarnaPaymentProcessor.createPayment(paymentMethod, isBcmc, subMethod, billingCountryCode));
    }

    @Test
    public void testValidate_success() {
        // Arrange
        Payment payment = new Payment().withProvider("KLARNA_PAYMENTS").withMethod("KLARNA_ACCOUNT");
        String billingCountryCode = "NL";

        // Act and Assert
        assertDoesNotThrow(() -> klarnaPaymentProcessor.validate(payment, billingCountryCode));
    }

    @Test
    public void testValidate_error() {
        // Arrange
        Payment payment = new Payment().withProvider("KLARNA_PAYMENTS").withMethod("KLARNA_ACCOUNT");
        String billingCountryCode = "XX";

        // Act and Assert
        Assertions.assertThrows(PaymentValidationException.class, () -> klarnaPaymentProcessor.validate(payment, billingCountryCode));
    }
}