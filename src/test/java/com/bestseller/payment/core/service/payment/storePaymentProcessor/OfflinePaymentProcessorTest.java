package com.bestseller.payment.core.service.payment.storePaymentProcessor;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.Payment;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderCharge;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.enumeration.EntryType;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import com.bestseller.payment.core.domain.payment.NonePayment;
import com.bestseller.payment.utils.OrderGenerator;
import com.bestseller.payment.utils.ValidOrderPlacedGenerator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.List;

import static com.bestseller.payment.utils.ValidOrderPlacedGenerator.SHIPPING_FEES;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;


class OfflinePaymentProcessorTest {
    private OfflinePaymentProcessor bseStorePaymentProcessor;
    private Order order;
    private ValidOrderPlaced validOrderPlaced;

    @BeforeEach
    public void setup() {
        validOrderPlaced = ValidOrderPlacedGenerator.createTradebyteOrderPlacedMessageWithShippingInformation();
        order = new Order();
        OrderCharge orderCharge = new OrderCharge();
        orderCharge.setEan(EntryType.SHIPMENT_FEE.name());
        orderCharge.setCostPrice(SHIPPING_FEES);
        order.setOrderCharges(List.of(orderCharge));
        order.setOrderLines(OrderGenerator.createOrder().getOrderLines());
        bseStorePaymentProcessor = new OfflinePaymentProcessor(validOrderPlaced.getOrderDetails(), order);
    }

    @Test
    public void setPaymentMethod_Tradebyte() {
        // Act
        PaymentState returnedPaymentState = bseStorePaymentProcessor.evaluatePaymentMethod();

        // Assert
        assertNotNull(order.getPayments());
        assertEquals(1, order.getPayments().size());
        assertEquals(PaymentState.OFFLINE, returnedPaymentState);
        assertEquals(PaymentType.NONE, order.getPayments().get(0).getType());
        assertEquals(ProcessorId.OFFLINE, order.getPayments().get(0).getProcessorId());
        assertEquals(order.getPayments().stream().findFirst().get().getAuthorisedAmount(), new BigDecimal("48.90").toPlainString());
        assertInstanceOf(NonePayment.class, order.getPayments().getFirst());
    }

    @Test
    public void getPaymentState() {
        //Act
        PaymentState paymentState = bseStorePaymentProcessor.getPaymentState(null);

        //Assert
        assertEquals(PaymentState.OFFLINE, paymentState);
    }

    @Test
    public void createPayment_Tradebyte() {
        //Arrange
        Payment payment = new Payment()
                .withAmount(new BigDecimal(100));
        //Act
        PaymentInfo nonePayment = bseStorePaymentProcessor.createPayment(payment, null);

        //Assert
        assertEquals(PaymentType.NONE, nonePayment.getType());
        assertEquals(ProcessorId.OFFLINE, nonePayment.getProcessorId());
        assertEquals("100", nonePayment.getAuthorisedAmount());
    }


}
