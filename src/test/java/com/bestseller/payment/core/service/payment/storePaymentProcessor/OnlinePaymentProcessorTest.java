package com.bestseller.payment.core.service.payment.storePaymentProcessor;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.Payment;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.enumeration.PaymentMethod;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import com.bestseller.payment.core.domain.payment.AdyenBankPayment;
import com.bestseller.payment.core.domain.payment.GiftcardPayment;
import com.bestseller.payment.core.domain.payment.KlarnaPayment;
import com.bestseller.payment.core.exception.MultipleNonGiftcardPaymentsException;
import com.bestseller.payment.core.service.payment.factory.PaymentFactory;
import com.bestseller.payment.utils.ValidOrderPlacedGenerator;
import edu.emory.mathcs.backport.java.util.Collections;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OnlinePaymentProcessorTest {

    private OnlinePaymentProcessor onlinePaymentProcessor;
    @Captor
    private ArgumentCaptor<ProcessorId> processorIdArgumentCaptor;
    @Captor
    private ArgumentCaptor<PaymentMethod> paymentMethodArgumentCaptor;
    @Mock
    private PaymentFactory paymentFactory;

    private Order order;

    @ParameterizedTest
    @MethodSource("paymentStateProviderForValidTransition")
    public void createPayment_Klarna_Invoice_All_Valid_PaymentState(PaymentState paymentState) {
        // Arrange
        ValidOrderPlaced validOrderPlaced = ValidOrderPlacedGenerator.createBseOrderPlacedMessage();
        Payment payment = new Payment()
                .withProvider(ProcessorId.KLARNA_PAYMENTS.name())
                .withMethod(PaymentMethod.KLARNA_INVOICE.name())
                .withState(paymentState.getDwDescription());
        validOrderPlaced.withPayments(Collections.singletonList(payment));
        KlarnaPayment klarnaPayment = new KlarnaPayment();
        klarnaPayment.setType(PaymentType.KLARNA);
        onlinePaymentProcessor = new OnlinePaymentProcessor(validOrderPlaced.getPayments(), "Country", order, paymentFactory);

        when(paymentFactory.createPayment(processorIdArgumentCaptor.capture(), paymentMethodArgumentCaptor.capture(),
                any(), any(), any(), any())).thenReturn(klarnaPayment);

        // Act
        PaymentInfo paymentResult = onlinePaymentProcessor.createPayment(payment, "NL");

        // Assert
        verify(paymentFactory, times(1)).createPayment(any(), any(), any(), any(), any(), any());
        assertEquals(processorIdArgumentCaptor.getValue(), ProcessorId.KLARNA_PAYMENTS);
        assertEquals(paymentMethodArgumentCaptor.getValue(), PaymentMethod.KLARNA_INVOICE);
        assertTrue(paymentResult instanceof KlarnaPayment);
    }

    @ParameterizedTest
    @MethodSource("paymentStateProviderForValidTransition")
    public void createPayment_Klarna_All_Valid_PaymentState(PaymentState paymentState) {
        // Arrange
        ValidOrderPlaced validOrderPlaced = ValidOrderPlacedGenerator.createBseOrderPlacedMessage();
        Payment payment = new Payment()
                .withProvider(ProcessorId.KLARNA_PAYMENTS.name())
                .withMethod(PaymentMethod.KLARNA_ACCOUNT.name())
                .withState(paymentState.getDwDescription());
        validOrderPlaced.withPayments(Collections.singletonList(payment));
        KlarnaPayment klarnaPayment = new KlarnaPayment();
        klarnaPayment.setType(PaymentType.KLARNA);
        onlinePaymentProcessor = new OnlinePaymentProcessor(validOrderPlaced.getPayments(), "Country", order, paymentFactory);


        when(paymentFactory.createPayment(processorIdArgumentCaptor.capture(), paymentMethodArgumentCaptor.capture(),
                any(), any(), any(), any())).thenReturn(klarnaPayment);

        // Act
        PaymentInfo paymentResult = onlinePaymentProcessor.createPayment(payment, "NL");

        // Assert
        verify(paymentFactory, times(1)).createPayment(any(), any(), any(), any(), any(), any());
        assertEquals(processorIdArgumentCaptor.getValue(), ProcessorId.KLARNA_PAYMENTS);
        assertEquals(paymentMethodArgumentCaptor.getValue(), PaymentMethod.KLARNA_ACCOUNT);
        assertTrue(paymentResult instanceof KlarnaPayment);
    }

    @ParameterizedTest
    @MethodSource("paymentStateProviderForValidTransition")
    public void createPayment_Adyen_All_Valid_PaymentState(PaymentState paymentState) {
        // Arrange
        ValidOrderPlaced validOrderPlaced = ValidOrderPlacedGenerator.createBseOrderPlacedMessage();
        Payment payment = new Payment()
                .withProvider(ProcessorId.ADYEN.name())
                .withMethod(PaymentMethod.ADYEN_IDEAL.name())
                .withState(paymentState.getDwDescription());
        validOrderPlaced.withPayments(Collections.singletonList(payment));
        AdyenBankPayment adyenBankPayment = new AdyenBankPayment();
        adyenBankPayment.setType(PaymentType.ADYEN_BANK);
        onlinePaymentProcessor = new OnlinePaymentProcessor(validOrderPlaced.getPayments(), "Country", order, paymentFactory);


        when(paymentFactory.createPayment(processorIdArgumentCaptor.capture(), paymentMethodArgumentCaptor.capture(), any(),
                any(), any(), any())).thenReturn(adyenBankPayment);

        // Act
        PaymentInfo paymentResult = onlinePaymentProcessor.createPayment(payment, "NL");

        // Assert
        verify(paymentFactory, times(1)).createPayment(any(), any(), any(), any(), any(), any());
        assertEquals(processorIdArgumentCaptor.getValue(), ProcessorId.ADYEN);
        assertEquals(paymentMethodArgumentCaptor.getValue(), PaymentMethod.ADYEN_IDEAL);
        assertTrue(paymentResult instanceof AdyenBankPayment);
    }

    @ParameterizedTest
    @MethodSource("paymentStateProviderForValidTransition")
    public void setPaymentMethod_Klarna_Invoice_All_Valid_PaymentState(PaymentState paymentState) {
        // Arrange
        ValidOrderPlaced validOrderPlaced = ValidOrderPlacedGenerator.createBseOrderPlacedMessage();
        Payment payment = new Payment()
                .withProvider(ProcessorId.KLARNA_PAYMENTS.name())
                .withMethod(PaymentMethod.KLARNA_INVOICE.name())
                .withState(paymentState.getDwDescription());
        validOrderPlaced.withPayments(Collections.singletonList(payment));
        Order order = new Order();
        order.setOrderId(validOrderPlaced.getOrderId());
        KlarnaPayment klarnaPayment = new KlarnaPayment();
        klarnaPayment.setType(PaymentType.KLARNA);
        klarnaPayment.setProcessorId(ProcessorId.KLARNA_PAYMENTS);
        onlinePaymentProcessor = new OnlinePaymentProcessor(validOrderPlaced.getPayments(), "Country", order, paymentFactory);

        when(paymentFactory.createPayment(any(), any(), any(), any(), any(), any())).thenReturn(klarnaPayment);

        // Act
        PaymentState result = onlinePaymentProcessor.evaluatePaymentMethod();

        // Assert
        assertNotNull(order.getPayments());
        assertEquals(1, order.getPayments().size());
        assertEquals(paymentState, result);
        assertTrue(order.getPayments().stream().findFirst().get() instanceof KlarnaPayment);
    }

    @ParameterizedTest
    @MethodSource("paymentStateProviderForValidTransition")
    public void setPaymentMethod_Klarna_All_Valid_PaymentState(PaymentState paymentState) {
        // Arrange
        ValidOrderPlaced validOrderPlaced = ValidOrderPlacedGenerator.createBseOrderPlacedMessage();
        Payment payment = new Payment()
                .withProvider(ProcessorId.KLARNA_PAYMENTS.name())
                .withMethod(PaymentMethod.KLARNA_ACCOUNT.name())
                .withState(paymentState.getDwDescription());
        validOrderPlaced.withPayments(Collections.singletonList(payment));
        Order order = new Order();
        order.setOrderId(validOrderPlaced.getOrderId());
        KlarnaPayment klarnaPayment = new KlarnaPayment();
        klarnaPayment.setType(PaymentType.KLARNA);
        klarnaPayment.setProcessorId(ProcessorId.KLARNA_PAYMENTS);
        onlinePaymentProcessor = new OnlinePaymentProcessor(validOrderPlaced.getPayments(), "Country", order, paymentFactory);

        when(paymentFactory.createPayment(any(), any(), any(), any(), any(), any())).thenReturn(klarnaPayment);

        // Act
        PaymentState returnedPaymentState = onlinePaymentProcessor.evaluatePaymentMethod();

        // Assert
        assertNotNull(order.getPayments());
        assertEquals(1, order.getPayments().size());
        assertTrue(order.getPayments().stream().findFirst().get() instanceof KlarnaPayment);
        assertEquals(paymentState, returnedPaymentState);
    }

    @ParameterizedTest
    @MethodSource("paymentStateProviderForValidTransition")
    public void setPaymentMethod_Adyen_All_Valid_PaymentState(PaymentState paymentState) {
        // Arrange
        ValidOrderPlaced validOrderPlaced = ValidOrderPlacedGenerator.createBseOrderPlacedMessage();
        Payment payment = new Payment()
                .withProvider(ProcessorId.ADYEN.name())
                .withMethod(PaymentMethod.ADYEN_IDEAL.name())
                .withState(paymentState.getDwDescription());
        validOrderPlaced.withPayments(Collections.singletonList(payment));
        Order order = new Order();
        order.setOrderId(validOrderPlaced.getOrderId());
        AdyenBankPayment adyenBankPayment = new AdyenBankPayment();
        adyenBankPayment.setType(PaymentType.ADYEN_BANK);
        adyenBankPayment.setProcessorId(ProcessorId.ADYEN);
        onlinePaymentProcessor = new OnlinePaymentProcessor(validOrderPlaced.getPayments(), "Country", order, paymentFactory);

        when(paymentFactory.createPayment(any(), any(), any(), any(), any(), any())).thenReturn(adyenBankPayment);

        // Act
        PaymentState returnedPaymentState = onlinePaymentProcessor.evaluatePaymentMethod();

        // Assert
        assertNotNull(order.getPayments());
        assertEquals(1, order.getPayments().size());
        assertEquals(paymentState, returnedPaymentState);
        assertTrue(order.getPayments().stream().findFirst().get() instanceof AdyenBankPayment);
    }

    @Test
    public void setPaymentMethod_Prev_And_PaymentState_AreEqual() {
        // Arrange
        ValidOrderPlaced validOrderPlaced = ValidOrderPlacedGenerator.createBseOrderPlacedMessage();
        Payment payment = new Payment()
                .withProvider(ProcessorId.ADYEN.name())
                .withMethod(PaymentMethod.ADYEN_IDEAL.name())
                .withState(PaymentState.AUTHORISED.getDwDescription());
        validOrderPlaced.withPayments(Collections.singletonList(payment));
        Order order = new Order();
        order.setPaymentStatus(PaymentState.AUTHORISED);
        order.setOrderId(validOrderPlaced.getOrderId());
        AdyenBankPayment adyenBankPayment = new AdyenBankPayment();
        adyenBankPayment.setType(PaymentType.ADYEN_BANK);
        adyenBankPayment.setProcessorId(ProcessorId.ADYEN);

        onlinePaymentProcessor = new OnlinePaymentProcessor(validOrderPlaced.getPayments(), "Country", order, paymentFactory);

        when(paymentFactory.createPayment(any(), any(), any(), any(), any(), any())).thenReturn(adyenBankPayment);

        // Act
        PaymentState returnedPaymentState = onlinePaymentProcessor.evaluatePaymentMethod();

        // Assert
        assertNotNull(order.getPayments());
        assertEquals(1, order.getPayments().size());
        assertEquals(PaymentState.AUTHORISED, returnedPaymentState);
        assertTrue(order.getPayments().stream().findFirst().get() instanceof AdyenBankPayment);
    }

    @Test
    public void setPaymentMethod_Two_NoneGiftCardPayment() {
        //Arrange
        ValidOrderPlaced validOrderPlaced = ValidOrderPlacedGenerator.createBseOrderPlacedMessage();
        Payment paymentKlarnaInvoice = new Payment()
                .withProvider(ProcessorId.KLARNA_PAYMENTS.name())
                .withMethod(PaymentMethod.KLARNA_INVOICE.name())
                .withState(PaymentState.AUTHORISED.getDwDescription());
        Payment paymentKlarnaAccount = new Payment()
                .withProvider(ProcessorId.KLARNA_PAYMENTS.name())
                .withMethod(PaymentMethod.KLARNA_ACCOUNT.name())
                .withState(PaymentState.SETTLED.name());
        validOrderPlaced.withPayments(List.of(paymentKlarnaInvoice, paymentKlarnaAccount));
        Order order = new Order();
        KlarnaPayment klarnaPayment = new KlarnaPayment();
        klarnaPayment.setType(PaymentType.KLARNA);
        onlinePaymentProcessor = new OnlinePaymentProcessor(validOrderPlaced.getPayments(), "Country", order, paymentFactory);

        when(paymentFactory.createPayment(any(), any(), any(), any(), any(), any())).thenReturn(klarnaPayment);

        // Act & Assert
        assertThrows(MultipleNonGiftcardPaymentsException.class, () -> onlinePaymentProcessor.evaluatePaymentMethod());
    }

    @ParameterizedTest
    @MethodSource("paymentStateProvider")
    public void getPaymentState_When_Dm_Is_Authorised(PaymentState paymentState) {
        // Arrange
        ValidOrderPlaced validOrderPlaced = ValidOrderPlacedGenerator.createBseOrderPlacedMessage();
        Payment paymentKlarnaInvoice = new Payment()
                .withProvider(ProcessorId.ADYEN.name())
                .withMethod(PaymentMethod.ADYEN_CREDIT_CARD.name())
                .withState(paymentState.getDwDescription());
        validOrderPlaced.withPayments(Collections.singletonList(paymentKlarnaInvoice));
        Order order = new Order();
        AdyenBankPayment adyenBankPayment = new AdyenBankPayment();
        adyenBankPayment.setType(PaymentType.ADYEN_BANK);
        onlinePaymentProcessor = new OnlinePaymentProcessor(validOrderPlaced.getPayments(), "Country", order, paymentFactory);

        //Act
        PaymentState paymentStateResult = onlinePaymentProcessor.getPaymentState(validOrderPlaced.getPayments().stream().findFirst().get());

        // Act & Assert
        assertEquals(paymentStateResult, paymentState);
    }

    @Test
    public void setPaymentMethod_Two_Valid_Normal_Payment_And_GiftCard() {
        //Arrange
        ValidOrderPlaced validOrderPlaced = ValidOrderPlacedGenerator.createBseOrderPlacedMessage();
        Payment paymentKlarnaInvoice = new Payment()
                .withProvider(ProcessorId.KLARNA_PAYMENTS.name())
                .withMethod(PaymentMethod.KLARNA_INVOICE.name())
                .withState(PaymentState.AUTHORISED.getDwDescription());

        Payment paymentGiftCard = new Payment()
                .withProvider(ProcessorId.OPTICARD.name())
                .withMethod(PaymentMethod.OC_GIFTCARD.name())
                .withState(PaymentState.AUTHORISED.name());
        validOrderPlaced.withPayments(List.of(paymentKlarnaInvoice, paymentGiftCard));

        Order order = new Order();
        KlarnaPayment klarnaPayment = KlarnaPayment
                .builder()
                .type(PaymentType.KLARNA)
                .processorId(ProcessorId.KLARNA_PAYMENTS)
                .build();
        GiftcardPayment giftcardPayment = GiftcardPayment
                .builder()
                .processorId(ProcessorId.OPTICARD)
                .type(PaymentType.GIFTCARD)
                .build();

        onlinePaymentProcessor = new OnlinePaymentProcessor(validOrderPlaced.getPayments(), "Country", order, paymentFactory);


        when(paymentFactory.createPayment(eq(ProcessorId.KLARNA_PAYMENTS), any(), any(), any(), any(), any())).thenReturn(klarnaPayment);
        when(paymentFactory.createPayment(eq(ProcessorId.OPTICARD), any(), any(), any(), any(), any())).thenReturn(giftcardPayment);

        // Act
        PaymentState returnedPaymentState = onlinePaymentProcessor.evaluatePaymentMethod();

        // Assert
        assertNotNull(order.getPayments());
        assertEquals(2, order.getPayments().size());
        assertEquals(PaymentState.AUTHORISED, returnedPaymentState);
        assertTrue(order.getPayments().stream().findFirst().get() instanceof KlarnaPayment);
        assertTrue(order.getPayments().stream().skip(1).findFirst().get() instanceof GiftcardPayment);
    }

    @Test
    public void setPaymentMethod_All_GiftCard_Payments_Should_Combine_Into_One() {
        //Arrange
        ValidOrderPlaced validOrderPlaced = ValidOrderPlacedGenerator.createBseOrderPlacedMessage();


        Payment paymentGiftCardFirst = new Payment()
                .withProvider(ProcessorId.OPTICARD.name())
                .withMethod(PaymentMethod.OC_GIFTCARD.name())
                .withState(PaymentState.AUTHORISED.name())
                .withPspReference("11")
                .withAmount(new BigDecimal(10));


        Payment paymentGiftCardSecond = new Payment()
                .withProvider(ProcessorId.OPTICARD.name())
                .withMethod(PaymentMethod.OC_GIFTCARD.name())
                .withState(PaymentState.AUTHORISED.name())
                .withPspReference("22")
                .withAmount(new BigDecimal(20));

        Payment paymentGiftCardThird = new Payment()
                .withProvider(ProcessorId.OPTICARD.name())
                .withMethod(PaymentMethod.OC_GIFTCARD.name())
                .withState(PaymentState.AUTHORISED.name())
                .withPspReference("33")
                .withAmount(new BigDecimal(30));

        validOrderPlaced.withPayments(List.of(paymentGiftCardFirst, paymentGiftCardSecond, paymentGiftCardThird));

        Order order = new Order();

        GiftcardPayment giftCardPaymentFirst = GiftcardPayment.builder()
                .type(PaymentType.GIFTCARD)
                .paymentReference("1")
                .processorId(ProcessorId.OPTICARD)
                .authorisedAmount("10").build();


        GiftcardPayment giftCardPaymentSecond = GiftcardPayment.builder()
                .type(PaymentType.GIFTCARD)
                .paymentReference("2")
                .processorId(ProcessorId.OPTICARD)
                .authorisedAmount("20").build();
        GiftcardPayment giftCardPaymentThird = GiftcardPayment.builder()
                .type(PaymentType.GIFTCARD)
                .paymentReference("3")
                .processorId(ProcessorId.OPTICARD)
                .authorisedAmount("30").build();

        onlinePaymentProcessor = new OnlinePaymentProcessor(validOrderPlaced.getPayments(), "Country", order, paymentFactory);

        when(paymentFactory.createPayment(eq(ProcessorId.OPTICARD), any(), eq("11"), any(), any(), any())).thenReturn(giftCardPaymentFirst);
        when(paymentFactory.createPayment(eq(ProcessorId.OPTICARD), any(), eq("22"), any(), any(), any())).thenReturn(giftCardPaymentSecond);
        when(paymentFactory.createPayment(eq(ProcessorId.OPTICARD), any(), eq("33"), any(), any(), any())).thenReturn(giftCardPaymentThird);

        // Act
        PaymentState returnedPaymentState = onlinePaymentProcessor.evaluatePaymentMethod();

        // Assert
        assertNotNull(order.getPayments());
        assertEquals(1, order.getPayments().size());
        assertEquals(PaymentState.AUTHORISED, returnedPaymentState);
        assertTrue(order.getPayments().stream().findFirst().get() instanceof GiftcardPayment);
        assertEquals(order.getPayments().stream().findFirst().get().getPaymentReference(), "1");
        assertEquals(order.getPayments().stream().findFirst().get().getAuthorisedAmount(), "60");
    }

    @Test
    public void setPaymentMethod_Three_Payment_TwoGiftCard_OneNormal() {
        //Arrange
        ValidOrderPlaced validOrderPlaced = ValidOrderPlacedGenerator.createBseOrderPlacedMessage();


        Payment paymentGiftCardFirst = new Payment()
                .withProvider(ProcessorId.OPTICARD.name())
                .withMethod(PaymentMethod.OC_GIFTCARD.name())
                .withState(PaymentState.AUTHORISED.getDwDescription())
                .withPspReference("11")
                .withAmount(new BigDecimal(10));


        Payment paymentGiftCardSecond = new Payment()
                .withProvider(ProcessorId.OPTICARD.name())
                .withMethod(PaymentMethod.OC_GIFTCARD.name())
                .withState(PaymentState.AUTHORISED.getDwDescription())
                .withPspReference("22")
                .withAmount(new BigDecimal(20));

        Payment paymentKlarnaInvoice = new Payment()
                .withProvider(ProcessorId.KLARNA_PAYMENTS.name())
                .withMethod(PaymentMethod.KLARNA_INVOICE.name())
                .withState(PaymentState.AUTHORISED.getDwDescription());

        validOrderPlaced.withPayments(List.of(paymentGiftCardFirst, paymentGiftCardSecond, paymentKlarnaInvoice));

        Order order = new Order();

        GiftcardPayment giftCardPaymentFirst = GiftcardPayment.builder()
                .type(PaymentType.GIFTCARD)
                .paymentReference("1")
                .processorId(ProcessorId.OPTICARD)
                .authorisedAmount("10").build();


        GiftcardPayment giftCardPaymentSecond = GiftcardPayment.builder()
                .type(PaymentType.GIFTCARD)
                .paymentReference("2")
                .processorId(ProcessorId.OPTICARD)
                .authorisedAmount("20").build();

        KlarnaPayment klarnaPayment = KlarnaPayment
                .builder()
                .type(PaymentType.KLARNA)
                .paymentReference("3")
                .processorId(ProcessorId.KLARNA_PAYMENTS)
                .authorisedAmount("100")
                .build();

        onlinePaymentProcessor = new OnlinePaymentProcessor(validOrderPlaced.getPayments(), "Country", order, paymentFactory);

        when(paymentFactory.createPayment(eq(ProcessorId.OPTICARD), any(), eq("11"), any(), any(), any())).thenReturn(giftCardPaymentFirst);
        when(paymentFactory.createPayment(eq(ProcessorId.OPTICARD), any(), eq("22"), any(), any(), any())).thenReturn(giftCardPaymentSecond);
        when(paymentFactory.createPayment(eq(ProcessorId.KLARNA_PAYMENTS), any(), any(), any(), any(), any())).thenReturn(klarnaPayment);

        // Act
        PaymentState returnedPaymentState = onlinePaymentProcessor.evaluatePaymentMethod();

        // Assert
        assertNotNull(order.getPayments());
        assertEquals(2, order.getPayments().size());
        assertEquals(PaymentState.AUTHORISED, returnedPaymentState);

        assertTrue(order.getPayments().stream().findFirst().get() instanceof KlarnaPayment);
        assertTrue(order.getPayments().stream().skip(1).findFirst().get() instanceof GiftcardPayment);


        assertEquals(order.getPayments().stream().findFirst().get().getPaymentReference(), "3");
        assertEquals(order.getPayments().stream().skip(1).findFirst().get().getPaymentReference(), "1");


        assertEquals(order.getPayments().stream().findFirst().get().getAuthorisedAmount(), "100");
        assertEquals(order.getPayments().stream().skip(1).findFirst().get().getAuthorisedAmount(), "30");


    }


    private static Stream<PaymentState> paymentStateProviderForValidTransition() {
        // Provide the different PaymentState values
        return Stream.of(PaymentState.AUTHORISED, PaymentState.REVIEW);
    }

    private static Stream<PaymentState> paymentStateProvider() {
        // Provide the different PaymentState values
        return Stream.of(PaymentState.AUTHORISED, PaymentState.REVIEW, PaymentState.SETTLED);
    }
}
