package com.bestseller.payment.core.service.payment.factory;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.OrderDetails;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.Payment;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderCharge;
import com.bestseller.payment.core.domain.enumeration.EntryType;
import com.bestseller.payment.core.domain.enumeration.Platform;
import com.bestseller.payment.core.service.payment.storePaymentProcessor.OfflinePaymentProcessor;
import com.bestseller.payment.core.service.payment.storePaymentProcessor.OnlinePaymentProcessor;
import com.bestseller.payment.core.service.payment.storePaymentProcessor.PaymentProcessor;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

import static com.bestseller.payment.utils.ValidOrderPlacedGenerator.SHIPPING_FEES;
import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class PaymentProcessorFactoryImplTest {

    @Mock
    private PaymentFactory paymentFactory;

    @Test
    public void createPaymentProcessor_givenOfflinePaymentOrder_shouldReturnOfflinePaymentProcessorInstance() {
        // Initialize the mocks
        MockitoAnnotations.openMocks(this);

        PaymentProcessorFactoryImpl paymentProcessorFactory = new PaymentProcessorFactoryImpl();
        OrderDetails orderDetails = new OrderDetails()
                .withOrderValue(new BigDecimal(10));
        Order order = new Order();
        order.setOfflinePayment(true);
        OrderCharge orderCharge = new OrderCharge();
        orderCharge.setEan(EntryType.SHIPMENT_FEE.name());
        orderCharge.setCostPrice(SHIPPING_FEES);
        order.setOrderCharges(List.of(orderCharge));
        List<Payment> payments = Collections.emptyList();
        String country = "NL";
        String store = Platform.TRADEBYTE.name();

        // Call the createPaymentProcessor method to be tested
        PaymentProcessor paymentProcessor = paymentProcessorFactory.createPaymentProcessor(
                payments,
                country,
                order,
                orderDetails,
                paymentFactory
        );

        // Verify that the correct StorePaymentProcessor instance is returned
        // In this case, it should be an instance of TradebyteStorePaymentProcessor
        assertThat(paymentProcessor).isInstanceOf(OfflinePaymentProcessor.class);
    }

    @Test
    public void createPaymentProcessor_givenOnlinePaymentOrder_shouldReturnBseStorePaymentProcessorInstance() {
        // Initialize the mocks
        MockitoAnnotations.openMocks(this);

        PaymentProcessorFactoryImpl paymentProcessorFactory = new PaymentProcessorFactoryImpl();
        OrderDetails orderDetails = new OrderDetails();
        Order order = new Order();
        List<Payment> payments = Collections.emptyList();
        String country = "NL";
        String store = "Demandware";

        // Call the createPaymentProcessor method to be tested
        PaymentProcessor paymentProcessor = paymentProcessorFactory.createPaymentProcessor(
                payments,
                country,
                order,
                orderDetails,
                paymentFactory
        );

        // Verify that the correct StorePaymentProcessor instance is returned
        // In this case, it should be an instance of BseStorePaymentProcessor
        assertThat(paymentProcessor).isInstanceOf(OnlinePaymentProcessor.class);
    }
}
