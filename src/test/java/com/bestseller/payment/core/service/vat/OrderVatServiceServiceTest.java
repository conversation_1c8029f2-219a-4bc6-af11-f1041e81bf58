package com.bestseller.payment.core.service.vat;

import com.bestseller.payment.utils.OrderGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;
import java.util.Date;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class OrderVatServiceServiceTest {

    @InjectMocks
    private OrderVatServiceImpl orderVatService;

    @Mock
    private VatSequenceService vatSequenceService;

    @Mock
    private Clock cetClock;

    @Test
    void testGetNextOrderVatId() {
        // Arrange
        var order = OrderGenerator.createOrder();
        when(cetClock.getZone()).thenReturn(ZoneId.of("Europe/Amsterdam"));

        // Act
        orderVatService.getNextOrderVatId(order);

        // Assert
        verify(vatSequenceService).getNextOrderVatId(order.getShippingCountryCode(), order.getOrderDate());
    }

    @Test
    void testGetNextRefundVatId() {
        // Arrange
        var order = OrderGenerator.createOrder();
        String expectedVatId = "VAT12345";
        when(vatSequenceService.getNextRefundVatId(eq(order.getShippingCountryCode()), any(Date.class))).thenReturn(expectedVatId);
        when(cetClock.instant()).thenReturn(Instant.now());
        // Act
        String actualVatId = orderVatService.getNextRefundVatId(order);

        // Assert
        assertThat(expectedVatId).isEqualTo(actualVatId);
        verify(vatSequenceService).getNextRefundVatId(eq(order.getShippingCountryCode()), any(Date.class));
    }

    @Test
    void testGetNextRefundInStoreVatId() {
        // Arrange
        var order = OrderGenerator.createOrder();
        var refundId = 1L;

        // Act
        orderVatService.getNextRefundInStoreVatId(order, refundId);

        // Assert
        verify(vatSequenceService).getNextRefundInStoreVatId(order.getShippingCountryCode(), refundId);
    }

    @Test
    void testGetNextOrderVatId_noShippingCountryCode() {
        // Arrange
        var order = OrderGenerator.createOrder();
        order.setShippingCountryCode(null);
        when(cetClock.getZone()).thenReturn(ZoneId.of("CET"));
        // Act
        orderVatService.getNextOrderVatId(order);

        // Assert
        verify(vatSequenceService).getNextOrderVatId(order.getBillingCountryCode(), order.getOrderDate());
    }

    @Test
    void testGetNextRefundVatId_noShippingCountryCode() {
        // Arrange
        var order = OrderGenerator.createOrder();
        order.setShippingCountryCode(null);
        String expectedVatId = "VAT12345";
        when(vatSequenceService.getNextRefundVatId(eq(order.getBillingCountryCode()), any(Date.class))).thenReturn(expectedVatId);
        when(cetClock.instant()).thenReturn(Instant.now());

        // Act
        String actualVatId = orderVatService.getNextRefundVatId(order);

        // Assert
        assertThat(expectedVatId).isEqualTo(actualVatId);
        verify(vatSequenceService).getNextRefundVatId(eq(order.getBillingCountryCode()), any(Date.class));
    }

    @Test
    void testGetNextRefundInStoreVatId_noShippingCountryCode() {
        // Arrange
        var order = OrderGenerator.createOrder();
        order.setShippingCountryCode(null);
        var refundId = 1L;

        // Act
        orderVatService.getNextRefundInStoreVatId(order, refundId);

        // Assert
        verify(vatSequenceService).getNextRefundInStoreVatId(order.getBillingCountryCode(), refundId);
    }
}
