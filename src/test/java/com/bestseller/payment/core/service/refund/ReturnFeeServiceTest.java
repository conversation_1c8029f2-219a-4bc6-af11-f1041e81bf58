package com.bestseller.payment.core.service.refund;

import com.bestseller.payment.adapter.repository.CountryToReturnFeePricingRepository;
import com.bestseller.payment.core.domain.BrandSpecificReturnFee;
import com.bestseller.payment.core.domain.CountryToReturnFeePricing;
import com.bestseller.payment.core.dto.ReturnFeeCalculationResult;
import com.bestseller.payment.utils.DateUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ReturnFeeServiceTest {
    @InjectMocks
    ReturnFeeServiceImpl returnFeeService;

    @Mock
    CountryToReturnFeePricingRepository countryToReturnFeePricingRepository;

    @Test
    void testCalculateReturnFee_noBrandSpecificReturnFees() {
        // Arrange
        LocalDate orderDate = LocalDate.now();
        String country = "US";
        String brand = "brand";
        BigDecimal expectedReturnFeeAmount = BigDecimal.TEN;
        BigDecimal expectedTaxRate = BigDecimal.ONE;
        BigDecimal returnFeeThreshold = new BigDecimal("30.00");
        BigDecimal amountToRefund = new BigDecimal("10.00");

        CountryToReturnFeePricing countryToReturnFeePricing = CountryToReturnFeePricing.builder()
            .country(country)
            .returnFeeAmount(expectedReturnFeeAmount)
            .returnFeeTaxRate(expectedTaxRate)
            .effectiveDate(DateUtils.toDate(orderDate.minusDays(1)))
            .brandSpecificReturnFees(List.of())
            .threshold(returnFeeThreshold)
            .build();

        when(countryToReturnFeePricingRepository.findFirstByCountryAndEffectiveDateLessThanEqualOrderByEffectiveDateDesc(country, DateUtils.toDate(orderDate)))
            .thenReturn(Optional.of(countryToReturnFeePricing));

        // Act
        ReturnFeeCalculationResult actual = returnFeeService.calculateReturnFee(DateUtils.toDate(orderDate), country, brand, amountToRefund);

        // Assert
        assertEquals(expectedReturnFeeAmount, actual.returnFee());
        assertEquals(expectedTaxRate, actual.taxRate());
        assertTrue(actual.chargeable());
    }

    @Test
    void testCalculateReturnFee_brandSpecificReturnFees() {
        // Arrange
        LocalDate orderDate = LocalDate.now();
        String country = "US";
        String brand = "brand";
        BigDecimal expectedReturnFeeAmount = BigDecimal.ONE;
        BigDecimal expectedTaxRate = BigDecimal.ONE;
        BigDecimal returnFeeThreshold = new BigDecimal("30.00");
        BigDecimal amountToRefund = new BigDecimal("10.00");

        CountryToReturnFeePricing countryToReturnFeePricing = CountryToReturnFeePricing.builder()
            .country(country)
            .returnFeeAmount(BigDecimal.TEN)
            .returnFeeTaxRate(expectedTaxRate)
            .effectiveDate(DateUtils.toDate(orderDate.minusDays(1)))
            .threshold(returnFeeThreshold)
            .brandSpecificReturnFees(List.of(
                BrandSpecificReturnFee.builder()
                    .brand(brand)
                    .returnFee(expectedReturnFeeAmount)
                    .effectiveDate(DateUtils.toDate(orderDate.minusDays(1)))
                    .build(),
                BrandSpecificReturnFee.builder()
                    .brand(brand)
                    .returnFee(BigDecimal.ZERO)
                    .effectiveDate(DateUtils.toDate(orderDate.minusDays(2)))
                    .build()
            ))
            .build();

        when(countryToReturnFeePricingRepository.findFirstByCountryAndEffectiveDateLessThanEqualOrderByEffectiveDateDesc(country, DateUtils.toDate(orderDate)))
            .thenReturn(Optional.of(countryToReturnFeePricing));

        // Act
        ReturnFeeCalculationResult actual = returnFeeService.calculateReturnFee(DateUtils.toDate(orderDate), country, brand, amountToRefund);

        // Assert
        assertEquals(expectedReturnFeeAmount, actual.returnFee());
        assertEquals(expectedTaxRate, actual.taxRate());
        assertTrue(actual.chargeable());
    }

    @Test
    void testCalculateReturnFee_noCountryToReturnFeePricing() {
        // Arrange
        LocalDate orderDate = LocalDate.now();
        String country = "US";
        String brand = "brand";
        BigDecimal amountToRefund = new BigDecimal("10.00");

        when(countryToReturnFeePricingRepository.findFirstByCountryAndEffectiveDateLessThanEqualOrderByEffectiveDateDesc(country, DateUtils.toDate(orderDate)))
            .thenReturn(Optional.empty());

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            returnFeeService.calculateReturnFee(DateUtils.toDate(orderDate), country, brand, amountToRefund);
        });
        assertEquals("No return fee pricing found for country: US and order date: " + orderDate, exception.getMessage());
    }

    @Test
    void testCalculateReturnFee_noValidBrandSpecificReturnFees() {
        // Arrange
        LocalDate orderDate = LocalDate.now().minusMonths(1);
        String country = "US";
        String brand = "brand";
        BigDecimal expectedReturnFeeAmount = BigDecimal.TEN;
        BigDecimal expectedTaxRate = BigDecimal.ONE;
        BigDecimal returnFeeThreshold = new BigDecimal("30.00");
        BigDecimal amountToRefund = new BigDecimal("10.00");

        CountryToReturnFeePricing countryToReturnFeePricing = CountryToReturnFeePricing.builder()
            .country(country)
            .returnFeeAmount(expectedReturnFeeAmount)
            .returnFeeTaxRate(expectedTaxRate)
            .effectiveDate(DateUtils.toDate(orderDate.minusDays(1)))
            .threshold(returnFeeThreshold)
            .brandSpecificReturnFees(List.of(
                BrandSpecificReturnFee.builder()
                    .brand(brand)
                    .returnFee(BigDecimal.ONE)
                    .effectiveDate(DateUtils.toDate(orderDate.plusMonths(1)))
                    .build(),
                BrandSpecificReturnFee.builder()
                    .brand(brand)
                    .returnFee(BigDecimal.ZERO)
                    .effectiveDate(DateUtils.toDate(orderDate.plusMonths(2)))
                    .build()
            ))
            .build();

        when(countryToReturnFeePricingRepository.findFirstByCountryAndEffectiveDateLessThanEqualOrderByEffectiveDateDesc(country, DateUtils.toDate(orderDate)))
            .thenReturn(Optional.of(countryToReturnFeePricing));

        // Act
        ReturnFeeCalculationResult actual = returnFeeService.calculateReturnFee(DateUtils.toDate(orderDate), country, brand, amountToRefund);

        // Assert
        assertEquals(expectedReturnFeeAmount, actual.returnFee());
        assertEquals(expectedTaxRate, actual.taxRate());
        assertTrue(actual.chargeable());
    }

    @Test
    void testCreateReturnFeeCharge() {
        // Arrange
        BigDecimal returnFee = BigDecimal.TEN;
        BigDecimal taxRate = BigDecimal.ONE;

        // Act
        var result = returnFeeService.createReturnFeeCharge(returnFee, taxRate);

        // Assert
        assertEquals(returnFee, result.getChargeTotal().getGrossRetailUnitPrice());
        assertEquals(returnFee, result.getChargeTotal().getGrossDiscountedUnitPrice());
        assertEquals(taxRate, result.getTaxRate());
        assertEquals("RETURN_FEE", result.getEan());
        assertEquals("Return fee", result.getName());
        assertEquals(-1, result.getOpenQty());
        assertEquals(false, result.getCancelled());
    }


    @Test
    void testCalculateReturnFee_refundBelowCountryThreshold() {
        // Arrange
        LocalDate orderDate = LocalDate.now().minusMonths(1);
        String country = "US";
        String brand = "brand";
        BigDecimal expectedReturnFeeAmount = BigDecimal.ZERO;
        BigDecimal expectedTaxRate = BigDecimal.ZERO;
        BigDecimal returnFeeThreshold = new BigDecimal("30.00");
        BigDecimal amountToRefund = new BigDecimal("40.00");

        CountryToReturnFeePricing countryToReturnFeePricing = CountryToReturnFeePricing.builder()
            .country(country)
            .returnFeeAmount(expectedReturnFeeAmount)
            .returnFeeTaxRate(expectedTaxRate)
            .effectiveDate(DateUtils.toDate(orderDate.minusDays(1)))
            .threshold(returnFeeThreshold)
            .brandSpecificReturnFees(List.of(
                BrandSpecificReturnFee.builder()
                    .brand(brand)
                    .returnFee(BigDecimal.ONE)
                    .effectiveDate(DateUtils.toDate(orderDate.plusMonths(1)))
                    .build(),
                BrandSpecificReturnFee.builder()
                    .brand(brand)
                    .returnFee(BigDecimal.ZERO)
                    .effectiveDate(DateUtils.toDate(orderDate.plusMonths(2)))
                    .build()
            ))
            .build();

        when(countryToReturnFeePricingRepository.findFirstByCountryAndEffectiveDateLessThanEqualOrderByEffectiveDateDesc(country, DateUtils.toDate(orderDate)))
            .thenReturn(Optional.of(countryToReturnFeePricing));

        // Act
        ReturnFeeCalculationResult actual = returnFeeService.calculateReturnFee(DateUtils.toDate(orderDate), country, brand, amountToRefund);

        // Assert
        assertEquals(expectedReturnFeeAmount, actual.returnFee());
        assertEquals(expectedTaxRate, actual.taxRate());
        assertFalse(actual.chargeable());
    }
}
