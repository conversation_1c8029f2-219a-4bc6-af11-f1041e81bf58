package com.bestseller.payment.core.service.refund;

import com.bestseller.payment.core.domain.*;
import com.bestseller.payment.core.domain.enumeration.CustomerRefundMethod;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.payment.AdyenBankPayment;
import com.bestseller.payment.core.domain.payment.GiftcardPayment;
import com.bestseller.payment.core.dto.RefundCalculationResult;
import com.bestseller.payment.core.dto.RefundReason;
import com.bestseller.payment.utils.OrderGenerator;
import com.logistics.statetransition.RefundState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
class RefundCalculationServiceTest {

    @InjectMocks
    RefundCalculationServiceImpl refundCalculationService;

    @Test
    void testCalculate_firstRefund_mixPayments() {
        // Arrange
        String giftCardAmount = "10.00";
        String bankAmount = "20.00";
        Order order = OrderGenerator.createOrder();
        order.setPayments(List.of(
            AdyenBankPayment.builder().authorisedAmount(bankAmount).type(PaymentType.ADYEN_BANK).build(),
            GiftcardPayment.builder().authorisedAmount(giftCardAmount).type(PaymentType.GIFTCARD).build()
        ));

        order.setRefunds(new ArrayList<>());
        addRefund(order, new BigDecimal("25.00"), RefundState.CREATED);

        // Act
        RefundCalculationResult actual = refundCalculationService.calculate(order, RefundReason.RETURN);

        // Assert
        assertEquals(500, actual.amountToRefundByGiftCardInCents());
        assertEquals(2000, actual.amountToRefundByBankTransactionInCents());
    }

    @Test
    void testCalculate_firstRefund_mixPayment_refundAmountLessThanBankPayment() {
        // Arrange
        String giftCardAmount = "10.00";
        String bankAmount = "20.00";
        Order order = OrderGenerator.createOrder();
        order.setPayments(List.of(
            AdyenBankPayment.builder().authorisedAmount(bankAmount).type(PaymentType.ADYEN_BANK).build(),
            GiftcardPayment.builder().authorisedAmount(giftCardAmount).type(PaymentType.GIFTCARD).build()
        ));

        order.setRefunds(new ArrayList<>());
        addRefund(order, new BigDecimal("15.00"), RefundState.CREATED);

        // Act
        RefundCalculationResult actual = refundCalculationService.calculate(order, RefundReason.RETURN);

        // Assert
        assertEquals(0, actual.amountToRefundByGiftCardInCents());
        assertEquals(1500, actual.amountToRefundByBankTransactionInCents());
    }

    @Test
    void testCalculate_firstRefund_giftCardPayment() {
        // Arrange
        String giftCardAmount = "10.00";
        Order order = OrderGenerator.createOrder();
        order.setPayments(List.of(
            GiftcardPayment.builder().authorisedAmount(giftCardAmount).type(PaymentType.GIFTCARD).build()
        ));

        order.setRefunds(new ArrayList<>());
        addRefund(order, new BigDecimal("9.00"), RefundState.CREATED);

        // Act
        RefundCalculationResult actual = refundCalculationService.calculate(order, RefundReason.RETURN);

        // Assert
        assertEquals(900, actual.amountToRefundByGiftCardInCents());
        assertEquals(0, actual.amountToRefundByBankTransactionInCents());
    }

    @Test
    void testCalculate_firstRefund_bankPayment() {
        // Arrange
        String bankAmount = "10.00";
        Order order = OrderGenerator.createOrder();
        order.setPayments(List.of(
            AdyenBankPayment.builder().authorisedAmount(bankAmount).type(PaymentType.ADYEN_BANK).build()
        ));

        order.setRefunds(new ArrayList<>());
        addRefund(order, new BigDecimal("9.00"), RefundState.CREATED);

        // Act
        RefundCalculationResult actual = refundCalculationService.calculate(order, RefundReason.RETURN);

        // Assert
        assertEquals(0, actual.amountToRefundByGiftCardInCents());
        assertEquals(900, actual.amountToRefundByBankTransactionInCents());
    }

    @Test
    void testCalculate_firstRefund_requestMoreThanAuthorisedAmount() {
        // Arrange
        String bankAmount = "10.00";
        Order order = OrderGenerator.createOrder();
        order.setPayments(List.of(
            AdyenBankPayment.builder().authorisedAmount(bankAmount).type(PaymentType.ADYEN_BANK).build()
        ));

        order.setRefunds(new ArrayList<>());
        addRefund(order, new BigDecimal("19.00"), RefundState.CREATED);

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> refundCalculationService.calculate(order, RefundReason.RETURN));
    }

    @Test
    void testCalculate_notFirstRefund_mixPayments() {
        // Arrange
        String giftCardAmount = "10.00";
        String bankAmount = "20.00";
        Order order = OrderGenerator.createOrder();
        order.setPayments(List.of(
            AdyenBankPayment.builder().authorisedAmount(bankAmount).type(PaymentType.ADYEN_BANK).build(),
            GiftcardPayment.builder().authorisedAmount(giftCardAmount).type(PaymentType.GIFTCARD).build()
        ));

        order.setRefunds(new ArrayList<>());
        addRefund(order, new BigDecimal("25.00"), RefundState.CREATED);

        // Act
        RefundCalculationResult actual = refundCalculationService.calculate(order, RefundReason.RETURN);

        // Assert
        assertEquals(500, actual.amountToRefundByGiftCardInCents());
        assertEquals(2000, actual.amountToRefundByBankTransactionInCents());
    }

    @Test
    void testCalculate_notFirstRefund_mixPayment_refundAmountLessThanBankPayment() {
        // Arrange
        String giftCardAmount = "10.00";
        String bankAmount = "20.00";
        Order order = OrderGenerator.createOrder();
        order.setPayments(List.of(
            AdyenBankPayment.builder().authorisedAmount(bankAmount).type(PaymentType.ADYEN_BANK).build(),
            GiftcardPayment.builder().authorisedAmount(giftCardAmount).type(PaymentType.GIFTCARD).build()
        ));

        order.setRefunds(new ArrayList<>());
        addRefund(order, new BigDecimal("2.00"), RefundState.CREATED);

        // Act
        RefundCalculationResult actual = refundCalculationService.calculate(order, RefundReason.RETURN);

        // Assert
        assertEquals(0, actual.amountToRefundByGiftCardInCents());
        assertEquals(200, actual.amountToRefundByBankTransactionInCents());
    }

    @Test
    void testCalculate_notFirstRefund_giftCardPayment() {
        // Arrange
        String giftCardAmount = "10.00";
        Order order = OrderGenerator.createOrder();
        order.setPayments(List.of(
            GiftcardPayment.builder().authorisedAmount(giftCardAmount).type(PaymentType.GIFTCARD).build()
        ));

        order.setRefunds(new ArrayList<>());
        addRefund(order, new BigDecimal("4.00"), RefundState.CREATED);

        // Act
        RefundCalculationResult actual = refundCalculationService.calculate(order, RefundReason.RETURN);

        // Assert
        assertEquals(400, actual.amountToRefundByGiftCardInCents());
        assertEquals(0, actual.amountToRefundByBankTransactionInCents());
    }

    @Test
    void testCalculate_notFirstRefund_bankPayment() {
        // Arrange
        String bankAmount = "10.00";
        Order order = OrderGenerator.createOrder();
        order.setPayments(List.of(
            AdyenBankPayment.builder().authorisedAmount(bankAmount).type(PaymentType.ADYEN_BANK).build()
        ));

        order.setRefunds(new ArrayList<>());
        addRefund(order, new BigDecimal("1.00"), RefundState.CREATED);

        // Act
        RefundCalculationResult actual = refundCalculationService.calculate(order, RefundReason.RETURN);

        // Assert
        assertEquals(0, actual.amountToRefundByGiftCardInCents());
        assertEquals(100, actual.amountToRefundByBankTransactionInCents());
    }

    @Test
    void testCalculate_notFirstRefund_requestMoreThanAuthorisedAmount() {
        // Arrange
        String bankAmount = "10.00";
        Order order = OrderGenerator.createOrder();
        order.setPayments(List.of(
            AdyenBankPayment.builder().authorisedAmount(bankAmount).type(PaymentType.ADYEN_BANK).build()
        ));

        order.setRefunds(new ArrayList<>());
        addRefund(order, new BigDecimal("9.00"), RefundState.CREATED);
        addRefund(order, new BigDecimal("19.00"), RefundState.CREATED);

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> refundCalculationService.calculate(order, RefundReason.RETURN));
    }

    @Test
    void testCalculate_previousRefunds_different_states() {
        // Arrange
        String giftCardAmount = "10.00";
        String bankAmount = "20.00";
        Order order = OrderGenerator.createOrder();
        order.setPayments(List.of(
            AdyenBankPayment.builder().authorisedAmount(bankAmount).type(PaymentType.ADYEN_BANK).build(),
            GiftcardPayment.builder().authorisedAmount(giftCardAmount).type(PaymentType.GIFTCARD).build()
        ));

        order.setRefunds(new ArrayList<>());
        addRefund(order, new BigDecimal("2.00"), RefundState.REFUND_FAILED);
        addRefund(order, new BigDecimal("2.00"), RefundState.REFUND_SUCCESS);
        addRefund(order, new BigDecimal("2.00"), RefundState.REFUND_CANCELLED);
        addRefund(order, new BigDecimal("2.00"), RefundState.REFUND_REQUESTED);
        addRefund(order, new BigDecimal("2.00"), RefundState.REFUND_REQUESTING);
        addRefund(order, new BigDecimal("5.00"), RefundState.CREATED);

        // Act
        RefundCalculationResult actual = refundCalculationService.calculate(order, RefundReason.RETURN);

        // Assert
        assertEquals(0, actual.amountToRefundByGiftCardInCents());
        assertEquals(500, actual.amountToRefundByBankTransactionInCents());
    }

    @Test
    void testCalculate_invalid_setup() {
        // Arrange
        String giftCardAmount = "10.00";
        String bankAmount = "20.00";
        Order order = OrderGenerator.createOrder();
        order.setPayments(List.of(
            AdyenBankPayment.builder().authorisedAmount(bankAmount).type(PaymentType.ADYEN_BANK).build(),
            GiftcardPayment.builder().authorisedAmount(giftCardAmount).type(PaymentType.GIFTCARD).build()
        ));

        order.setRefunds(new ArrayList<>());
        addRefund(order, new BigDecimal("2.00"), RefundState.CREATED);
        addRefund(order, new BigDecimal("2.00"), RefundState.CREATED);

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> refundCalculationService.calculate(order, RefundReason.RETURN));
    }

    @Test
    void testCalculate_givenCustomerChoiceLessThanBankPayment_refundWithGiftCard() {
        // Arrange
        String giftCardAmount = "10.00";
        String bankAmount = "20.00";
        Order order = OrderGenerator.createOrder();
        order.setPayments(List.of(
            AdyenBankPayment.builder().authorisedAmount(bankAmount).type(PaymentType.ADYEN_BANK).build(),
            GiftcardPayment.builder().authorisedAmount(giftCardAmount).type(PaymentType.GIFTCARD).build()
        ));

        order.setRefunds(new ArrayList<>());
        addRefund(order, new BigDecimal("25.00"), RefundState.CREATED);

        order.getRefunds().forEach(refund -> refund.getRefundLines().add(
            RefundLine.builder()
                .quantity(1)
                .orderLine(OrderLine.builder()
                    .ean("ean1")
                    .customerRefundChoices(List.of(
                        CustomerRefundChoice.builder()
                            .customerRefundMethod(CustomerRefundMethod.GIFT_CARD)
                            .isUsed(false)
                            .build()
                    ))
                    .build())
                .refundLineTotal(OrderEntryAmount.builder()
                    .grossDiscountedUnitPrice(new BigDecimal("5"))
                    .build())
                .build()
        ));
        // Act
        RefundCalculationResult actual = refundCalculationService.calculate(order, RefundReason.RETURN);

        // Assert
        assertEquals(1000, actual.amountToRefundByGiftCardInCents());
        assertEquals(1500, actual.amountToRefundByBankTransactionInCents());
    }

    @Test
    void testCalculate_givenCustomerChoiceMoreThanBankPayment_refundWithGiftCard() {
        // Arrange
        String giftCardAmount = "10.00";
        String bankAmount = "10.00";
        Order order = OrderGenerator.createOrder();
        order.setPayments(List.of(
            AdyenBankPayment.builder().authorisedAmount(bankAmount).type(PaymentType.ADYEN_BANK).build(),
            GiftcardPayment.builder().authorisedAmount(giftCardAmount).type(PaymentType.GIFTCARD).build()
        ));

        order.setRefunds(new ArrayList<>());
        addRefund(order, new BigDecimal("20.00"), RefundState.CREATED);

        order.getRefunds().forEach(refund -> refund.getRefundLines().add(
            RefundLine.builder()
                .quantity(1)
                .orderLine(OrderLine.builder()
                    .ean("ean1")
                    .customerRefundChoices(List.of(
                        CustomerRefundChoice.builder()
                            .customerRefundMethod(CustomerRefundMethod.GIFT_CARD)
                            .isUsed(false)
                            .build()
                    ))
                    .build())
                .refundLineTotal(OrderEntryAmount.builder()
                    .grossDiscountedUnitPrice(new BigDecimal("15"))
                    .build())
                .build()
        ));
        // Act
        RefundCalculationResult actual = refundCalculationService.calculate(order, RefundReason.RETURN);

        // Assert
        assertEquals(2000, actual.amountToRefundByGiftCardInCents());
        assertEquals(0, actual.amountToRefundByBankTransactionInCents());
    }

    @Test
    void testCalculate_givenCustomerChoiceWithRefundChoice_ignoreRefundChoice() {
        // Arrange
        String giftCardAmount = "10.00";
        String bankAmount = "20.00";
        Order order = OrderGenerator.createOrder();
        order.setPayments(List.of(
            AdyenBankPayment.builder().authorisedAmount(bankAmount).type(PaymentType.ADYEN_BANK).build(),
            GiftcardPayment.builder().authorisedAmount(giftCardAmount).type(PaymentType.GIFTCARD).build()
        ));

        order.setRefunds(new ArrayList<>());
        addRefund(order, new BigDecimal("25.00"), RefundState.CREATED);

        order.getRefunds().forEach(refund -> refund.getRefundLines().add(
            RefundLine.builder()
                .quantity(1)
                .orderLine(OrderLine.builder()
                    .ean("ean1")
                    .customerRefundChoices(List.of(
                        CustomerRefundChoice.builder()
                            .customerRefundMethod(CustomerRefundMethod.REFUND)
                            .isUsed(false)
                            .build()
                    ))
                    .build())
                .refundLineTotal(OrderEntryAmount.builder()
                    .grossDiscountedUnitPrice(new BigDecimal("5"))
                    .build())
                .build()
        ));
        // Act
        RefundCalculationResult actual = refundCalculationService.calculate(order, RefundReason.RETURN);

        // Assert
        assertEquals(500, actual.amountToRefundByGiftCardInCents());
        assertEquals(2000, actual.amountToRefundByBankTransactionInCents());
    }

    @Test
    void testCalculate_givenCustomerChoiceWithUsedRefundChoice_ignoreRefundChoice() {
        // Arrange
        String giftCardAmount = "10.00";
        String bankAmount = "20.00";
        Order order = OrderGenerator.createOrder();
        order.setPayments(List.of(
            AdyenBankPayment.builder().authorisedAmount(bankAmount).type(PaymentType.ADYEN_BANK).build(),
            GiftcardPayment.builder().authorisedAmount(giftCardAmount).type(PaymentType.GIFTCARD).build()
        ));

        order.setRefunds(new ArrayList<>());
        addRefund(order, new BigDecimal("25.00"), RefundState.CREATED);

        order.getRefunds().forEach(refund -> refund.getRefundLines().add(
            RefundLine.builder()
                .quantity(1)
                .orderLine(OrderLine.builder()
                    .ean("ean1")
                    .customerRefundChoices(List.of(
                        CustomerRefundChoice.builder()
                            .customerRefundMethod(CustomerRefundMethod.GIFT_CARD)
                            .isUsed(true)
                            .build()
                    ))
                    .build())
                .refundLineTotal(OrderEntryAmount.builder()
                    .grossDiscountedUnitPrice(new BigDecimal("5"))
                    .build())
                .build()
        ));
        // Act
        RefundCalculationResult actual = refundCalculationService.calculate(order, RefundReason.RETURN);

        // Assert
        assertEquals(500, actual.amountToRefundByGiftCardInCents());
        assertEquals(2000, actual.amountToRefundByBankTransactionInCents());
    }

    private void addRefund(Order order, BigDecimal refundAmount, RefundState refundState) {
        var refund = Refund.builder()
            .refundState(refundState)
            .order(order)
            .csrInitials("SYSTEM")
            .refundId("refundId")
            .refundCharges(new ArrayList<>())
            .requestId(UUID.randomUUID().toString())
            .refundLines(new ArrayList<>())
            .refundTotal(OverallTotal.builder().grossDiscountedTotal(refundAmount).build())
            .build();
        order.getRefunds().add(refund);
    }
}
