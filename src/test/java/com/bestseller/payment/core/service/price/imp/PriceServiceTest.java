package com.bestseller.payment.core.service.price;

import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderCharge;
import com.bestseller.payment.core.domain.OrderEntryAmount;
import com.bestseller.payment.core.domain.OverallTotal;
import com.bestseller.payment.core.domain.Refund;
import com.bestseller.payment.core.domain.enumeration.EntryType;
import com.bestseller.payment.utils.JsonUtils;
import com.bestseller.payment.utils.OrderGenerator;
import com.bestseller.payment.utils.RefundGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Spy;
import org.mockito.internal.matchers.apachecommons.ReflectionEquals;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.bestseller.payment.utils.RefundGenerator.REFUND_LINE_GROSS_DISCOUNTED_UNIT_PRICE;
import static com.bestseller.payment.utils.RefundGenerator.REFUND_LINE_UNIT_VAT;
import static com.bestseller.payment.utils.RefundGenerator.RETURN_FEE;
import static com.bestseller.payment.utils.RefundGenerator.UNIT_VAT;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;


@Slf4j
@ExtendWith(MockitoExtension.class)
class PriceServiceTest {

    @Spy
    PriceServiceImp priceService;

    @Test
    void testProcessAndSetPriceFieldsForValidOrderPlacedWithoutDiscounts() {
        // arrange
        final BigDecimal originalGrossDiscountedTotal = new BigDecimal("10.00");
        com.bestseller.payment.core.domain.OrderLine orderLine = com.bestseller.payment.core.domain.OrderLine.builder()
            .standardRetailPrice(new BigDecimal("10.00"))
            .openQty(2)
            .originalQty(2)
            .taxRate(new BigDecimal("0.21"))
            .ean("1234567890123")
            .name("Test Product")
            .brand("Test Brand")
            .orderLinePaidAmount(OrderEntryAmount.builder()
                .grossRetailUnitPrice(new BigDecimal("10.00"))
                .originalGrossDiscountedTotal(originalGrossDiscountedTotal)
                .grossDiscountedUnitPrice(new BigDecimal("10.00")).build())
            .build();

        Order order = Order.builder()
            .orderLines(List.of(orderLine))
            .orderCharges(Collections.emptyList())
            .build();


        // act
        priceService.processAndSetPriceFieldsForOrderLineAndTotalPaidAmount(order);

        // assert
        assertEquals(new BigDecimal("20.00"), order.getTotalPaidPrice().getGrossSubTotal());

        assertEquals(new BigDecimal("20.00"), order.getTotalPaidPrice().getGrossDiscountedTotal());
        assertEquals(new BigDecimal("10.00"), order.getTotalPaidPrice().getOriginalGrossDiscountedTotal());

        assertEquals(new BigDecimal("10.00"), order.getOrderLines().get(0).getStandardRetailPrice());
        assertEquals(new BigDecimal("10.00"), order.getOrderLines().get(0).getOrderLinePaidAmount().getGrossRetailUnitPrice());
        assertEquals(new BigDecimal("10.00"), order.getOrderLines().get(0).getOrderLinePaidAmount().getGrossDiscountedUnitPrice());
        assertEquals(originalGrossDiscountedTotal,
            order.getOrderLines().get(0).getOrderLinePaidAmount().getOriginalGrossDiscountedTotal());
        assertEquals(new BigDecimal("20.00"),
            order.getOrderLines().get(0).getOrderLinePaidAmount().getGrossDiscountedTotal());
        assertEquals(new BigDecimal("1.74"), order.getOrderLines().get(0).getOrderLinePaidAmount().getUnitVAT());
        assertEquals(new BigDecimal("3.48"), order.getOrderLines().get(0).getOrderLinePaidAmount().getLineVAT());
        assertEquals(new BigDecimal("00.00"), order.getOrderLines().get(0).getOrderLinePaidAmount().getUnitDiscount());

        assertEquals(1, order.getOrderLines().size());
        assertEquals(2, order.getOrderLines().get(0).getOpenQty());
        assertEquals(2, order.getOrderLines().get(0).getOriginalQty());
    }

    @Test
    void testProcessAndSetPriceFieldsForValidOrderPlacedWithDiscounts() {
        final BigDecimal originalGrossDiscountedTotal = new BigDecimal("9.50");
        // arrange
        com.bestseller.payment.core.domain.OrderLine orderLine = com.bestseller.payment.core.domain.OrderLine.builder()
            .standardRetailPrice(new BigDecimal("10.00"))
            .openQty(2)
            .originalQty(2)
            .taxRate(new BigDecimal("0.21"))
            .ean("1234567890123")
            .name("Test Product")
            .brand("Test Brand")
            .orderLinePaidAmount(OrderEntryAmount.builder()
                .grossRetailUnitPrice(new BigDecimal("10.00"))
                .originalGrossDiscountedTotal(originalGrossDiscountedTotal)
                .grossDiscountedUnitPrice(new BigDecimal("9.50")).build())
            .build();
        Order order = Order.builder()
            .orderLines(List.of(orderLine))
            .orderCharges(Collections.emptyList())
            .build();
        // act
        priceService.processAndSetPriceFieldsForOrderLineAndTotalPaidAmount(order);

        // assert
        assertEquals(new BigDecimal("19.00"), order.getTotalPaidPrice().getGrossSubTotal());//without shipping

        assertEquals(new BigDecimal("19.00"), order.getTotalPaidPrice().getGrossDiscountedTotal());
        assertEquals(originalGrossDiscountedTotal, order.getTotalPaidPrice().getOriginalGrossDiscountedTotal());

        assertEquals(new BigDecimal("10.00"), order.getOrderLines().get(0).getStandardRetailPrice());
        assertEquals(new BigDecimal("10.00"), order.getOrderLines().get(0).getOrderLinePaidAmount().getGrossRetailUnitPrice());
        assertEquals(new BigDecimal("9.50"),
            order.getOrderLines().get(0).getOrderLinePaidAmount().getGrossDiscountedUnitPrice());
        assertEquals(originalGrossDiscountedTotal,
            order.getOrderLines().get(0).getOrderLinePaidAmount().getOriginalGrossDiscountedTotal());
        assertEquals(new BigDecimal("19.00"),
            order.getOrderLines().get(0).getOrderLinePaidAmount().getGrossDiscountedTotal());
        assertEquals(new BigDecimal("1.65"), order.getOrderLines().get(0).getOrderLinePaidAmount().getUnitVAT());
        assertEquals(new BigDecimal("3.30"), order.getOrderLines().get(0).getOrderLinePaidAmount().getLineVAT());
        assertEquals(new BigDecimal("00.50"), order.getOrderLines().get(0).getOrderLinePaidAmount().getUnitDiscount());

        assertEquals(1, order.getOrderLines().size());
        assertEquals(2, order.getOrderLines().get(0).getOpenQty());
        assertEquals(2, order.getOrderLines().get(0).getOriginalQty());
    }

    @Test
    void testCalculateAndSetOrderLinePaidAmount() {
        com.bestseller.payment.core.domain.OrderLine orderLine = com.bestseller.payment.core.domain.OrderLine.builder()
            .standardRetailPrice(new BigDecimal("10.00"))
            .openQty(2)
            .originalQty(2)
            .taxRate(new BigDecimal("0.21"))
            .ean("1234567890123")
            .name("Test Product")
            .brand("Test Brand")
            .orderLinePaidAmount(OrderEntryAmount.builder()
                .grossRetailUnitPrice(new BigDecimal("10.00"))
                .originalGrossDiscountedTotal(new BigDecimal("20.00"))
                .grossDiscountedUnitPrice(new BigDecimal("10.00")).build())
            .build();

        Order order = Order.builder()
            .orderLines(List.of(orderLine))
            .orderCharges(Collections.emptyList())
            .build();

        // act
        priceService.processAndSetPriceFieldsForOrderLineAndTotalPaidAmount(order);

        // assert
        assertEquals(new BigDecimal("10.00"), order.getOrderLines().get(0).getStandardRetailPrice());
        assertEquals(new BigDecimal("10.00"), order.getOrderLines().get(0).getOrderLinePaidAmount().getGrossRetailUnitPrice());
        assertEquals(new BigDecimal("10.00"), order.getOrderLines().get(0).getOrderLinePaidAmount().getGrossDiscountedUnitPrice());
        assertEquals(new BigDecimal("20.00"),
            order.getOrderLines().get(0).getOrderLinePaidAmount().getOriginalGrossDiscountedTotal());
        assertEquals(new BigDecimal("20.00"),
            order.getOrderLines().get(0).getOrderLinePaidAmount().getGrossDiscountedTotal());
        assertEquals(new BigDecimal("1.74"), order.getOrderLines().get(0).getOrderLinePaidAmount().getUnitVAT());
        assertEquals(new BigDecimal("3.48"), order.getOrderLines().get(0).getOrderLinePaidAmount().getLineVAT());
        assertEquals(new BigDecimal("00.00"), order.getOrderLines().get(0).getOrderLinePaidAmount().getUnitDiscount());

        assertEquals(1, order.getOrderLines().size());
        assertEquals(2, order.getOrderLines().get(0).getOpenQty());
        assertEquals(2, order.getOrderLines().get(0).getOriginalQty());
    }

    @Test
    void testCalculateAndSetTotalPaidAmount() {
        // arrange
        com.bestseller.payment.core.domain.OrderLine orderLine = com.bestseller.payment.core.domain.OrderLine.builder()
            .standardRetailPrice(new BigDecimal("10.00"))
            .openQty(2)
            .originalQty(2)
            .taxRate(new BigDecimal("0.21"))
            .ean("1234567890123")
            .name("Test Product")
            .brand("Test Brand")
            .orderLinePaidAmount(OrderEntryAmount.builder()
                .grossRetailUnitPrice(new BigDecimal("10.00"))
                .originalGrossDiscountedTotal(new BigDecimal("19.00"))
                .grossDiscountedUnitPrice(new BigDecimal("9.50")).build())
            .build();

        OrderCharge orderCharge = OrderCharge.builder()
            .ean("STANDARD_SHIPPING")
            .name("Shipping")
            .type(EntryType.SHIPMENT_FEE)
            .taxRate(new BigDecimal("0.10"))
            .openQty(1)
            .originalQty(1)
            .costPrice(new BigDecimal("3.0"))
            .standardRetailPrice(new BigDecimal("5.0"))
            .chargeTotal(OrderEntryAmount.builder()
                .grossRetailUnitPrice(new BigDecimal("5.0"))
                .grossDiscountedUnitPrice(new BigDecimal("3.0"))
                .grossDiscountedTotal(new BigDecimal("3.0"))
                .originalGrossDiscountedTotal(new BigDecimal("3.0"))
                .lineVAT(new BigDecimal("0.27"))
                .unitVAT(new BigDecimal("0.27"))
                .unitDiscount(new BigDecimal("2.0"))

                .build())
            .cancelled(false)
            .refunded(false)
            .build();

        Order order = Order.builder()
            .orderLines(List.of(orderLine))
            .orderCharges(List.of(orderCharge))
            .build();


        // act
        priceService.processAndSetPriceFieldsForOrderLineAndTotalPaidAmount(order);

        // assert
        assertEquals(new BigDecimal("19.00"), order.getTotalPaidPrice().getGrossSubTotal());//without shipping

        assertEquals(new BigDecimal("22.00"), order.getTotalPaidPrice().getGrossDiscountedTotal());
        assertEquals(new BigDecimal("22.00"), order.getTotalPaidPrice().getOriginalGrossDiscountedTotal());
        assertEquals(new BigDecimal("3.57"), (order.getTotalPaidPrice().getVat()));

        assertEquals(new BigDecimal("10.00"), order.getOrderLines().get(0).getStandardRetailPrice());
        assertEquals(new BigDecimal("10.00"), order.getOrderLines().get(0).getOrderLinePaidAmount().getGrossRetailUnitPrice());

        // order line
        assertEquals(1, order.getOrderLines().size());
        assertEquals(2, order.getOrderLines().get(0).getOpenQty());
        assertEquals(2, order.getOrderLines().get(0).getOriginalQty());
        // shipping
        OrderCharge shippingCharge =
            order.getOrderCharges().stream().filter(charge -> charge.getType() == EntryType.SHIPMENT_FEE).findFirst().get();
        OrderEntryAmount shippingChargeTotal = shippingCharge.getChargeTotal();
        assertEquals(1, order.getOrderCharges().size());
        assertEquals(BigDecimal.valueOf(3.0), shippingCharge.getCostPrice());
        assertEquals(BigDecimal.valueOf(5.0), shippingCharge.getStandardRetailPrice());
        assertEquals(1, shippingCharge.getOriginalQty());
        assertEquals(1, shippingCharge.getOpenQty());
        assertEquals(false, shippingCharge.getCancelled());
        assertEquals(false, shippingCharge.getRefunded());

        assertEquals(BigDecimal.valueOf(5.0), shippingChargeTotal.getGrossRetailUnitPrice());
        assertEquals(BigDecimal.valueOf(3.0), shippingChargeTotal.getGrossDiscountedUnitPrice());
        assertEquals(BigDecimal.valueOf(3.0), shippingChargeTotal.getGrossDiscountedTotal());
        assertEquals(BigDecimal.valueOf(3.0), shippingChargeTotal.getOriginalGrossDiscountedTotal());
        assertEquals(BigDecimal.valueOf(0.27), shippingChargeTotal.getLineVAT());
        assertEquals(BigDecimal.valueOf(0.27), shippingChargeTotal.getUnitVAT());
        assertEquals(BigDecimal.valueOf(2.0), shippingChargeTotal.getUnitDiscount());
    }

    @Test
    void testCalculateAndSetTotalPaidAmount_Has_OrderCharge_Cancelled() {
        // arrange
        com.bestseller.payment.core.domain.OrderLine orderLine = com.bestseller.payment.core.domain.OrderLine.builder()
            .standardRetailPrice(new BigDecimal("10.00"))
            .openQty(2)
            .originalQty(2)
            .taxRate(new BigDecimal("0.21"))
            .ean("1234567890123")
            .name("Test Product")
            .brand("Test Brand")
            .orderLinePaidAmount(OrderEntryAmount.builder()
                .grossRetailUnitPrice(new BigDecimal("10.00"))
                .originalGrossDiscountedTotal(new BigDecimal("20.00"))
                .grossDiscountedUnitPrice(new BigDecimal("9.50")).build())
            .build();

        OrderCharge orderCharge = OrderCharge.builder()
            .ean("STANDARD_SHIPPING")
            .name("Shipping")
            .type(EntryType.SHIPMENT_FEE)
            .taxRate(new BigDecimal("0.10"))
            .openQty(1)
            .originalQty(1)
            .costPrice(new BigDecimal("3.0"))
            .standardRetailPrice(new BigDecimal("5.0"))
            .chargeTotal(OrderEntryAmount.builder()
                .grossRetailUnitPrice(new BigDecimal("5.0"))
                .grossDiscountedUnitPrice(new BigDecimal("3.0"))
                .grossDiscountedTotal(new BigDecimal("3.0"))
                .lineVAT(new BigDecimal("0.27"))
                .unitVAT(new BigDecimal("0.27"))
                .originalGrossDiscountedTotal(new BigDecimal("3.0"))
                .unitDiscount(new BigDecimal("2.0"))

                .build())
            .cancelled(true)
            .refunded(false)
            .build();

        Order order = Order.builder()
            .orderLines(List.of(orderLine))
            .orderCharges(List.of(orderCharge))
            .build();


        // act
        priceService.processAndSetPriceFieldsForOrderLineAndTotalPaidAmount(order);

        // assert
        assertEquals(new BigDecimal("19.00"), order.getTotalPaidPrice().getGrossSubTotal());//shipping will be deducted

        assertEquals(new BigDecimal("19.00"), order.getTotalPaidPrice().getGrossDiscountedTotal());
        assertEquals(new BigDecimal("20.00"), order.getTotalPaidPrice().getOriginalGrossDiscountedTotal());
        assertEquals(new BigDecimal("3.30"), (order.getTotalPaidPrice().getVat()));

        assertEquals(new BigDecimal("10.00"), order.getOrderLines().get(0).getStandardRetailPrice());
        assertEquals(new BigDecimal("10.00"), order.getOrderLines().get(0).getOrderLinePaidAmount().getGrossRetailUnitPrice());


        assertEquals(1, order.getOrderLines().size());
        assertEquals(2, order.getOrderLines().get(0).getOpenQty());
        assertEquals(2, order.getOrderLines().get(0).getOriginalQty());

        //shipping
        OrderCharge shippingCharge =
            order.getOrderCharges().stream().filter(charge -> charge.getType() == EntryType.SHIPMENT_FEE).findFirst().get();
        OrderEntryAmount shippingChargeTotal = shippingCharge.getChargeTotal();
        assertEquals(1, order.getOrderCharges().size());
        assertEquals(BigDecimal.valueOf(3.0), shippingCharge.getCostPrice());
        assertEquals(BigDecimal.valueOf(5.0), shippingCharge.getStandardRetailPrice());
        assertEquals(1, shippingCharge.getOriginalQty());
        assertEquals(1, shippingCharge.getOpenQty());
        assertEquals(true, shippingCharge.getCancelled());
        assertEquals(false, shippingCharge.getRefunded());

        assertEquals(BigDecimal.valueOf(5.0), shippingChargeTotal.getGrossRetailUnitPrice());
        assertEquals(BigDecimal.valueOf(3.0), shippingChargeTotal.getGrossDiscountedUnitPrice());
        assertEquals(BigDecimal.valueOf(3.0), shippingChargeTotal.getGrossDiscountedTotal());
        assertEquals(BigDecimal.valueOf(3.0), shippingChargeTotal.getOriginalGrossDiscountedTotal());
        assertEquals(BigDecimal.valueOf(0.27), shippingChargeTotal.getLineVAT());
        assertEquals(BigDecimal.valueOf(0.27), shippingChargeTotal.getUnitVAT());
        assertEquals(BigDecimal.valueOf(2.0), shippingChargeTotal.getUnitDiscount());
    }

    @Test
    void testProcessAndSetPriceFields_allOrderLinesCancelled() throws JsonProcessingException {
        // arrange
        final var order = OrderGenerator.createOrder();
        order.setPayments(new ArrayList<>());
        final var initialOrder = JsonUtils.deepCopy(order);
        order.getOrderLines().forEach(orderLine -> orderLine.setOpenQty(0));
        order.getOrderCharges().forEach(orderCharge -> orderCharge.setCancelled(true));

        final BigDecimal expectedOriginalGrossDiscountedTotal_paid = order.getTotalPaidPrice().getOriginalGrossDiscountedTotal()
            .subtract(order.getOrderCharges()
                .stream()
                .filter(OrderCharge::getCancelled)
                .map(OrderCharge::getChargeTotal)
                .map(OrderEntryAmount::getOriginalGrossDiscountedTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        final BigDecimal expectedGrossDiscountedTotal_paid = BigDecimal.ZERO;
        final BigDecimal expectedGrossDiscountedTotal_cancelled = order.getTotalPaidPrice().getOriginalGrossDiscountedTotal();

        // act
        priceService.processAndSetPriceFieldsForOrderLineAndTotalPaidAmount(order);

        // assert
        // Assert
        Assertions.assertEquals(expectedOriginalGrossDiscountedTotal_paid, order.getTotalPaidPrice().getOriginalGrossDiscountedTotal());
        assertThat(order.getTotalPaidPrice().getGrossDiscountedTotal()).isEqualByComparingTo(expectedGrossDiscountedTotal_paid);
        assertThat(expectedGrossDiscountedTotal_paid).isEqualByComparingTo(order.getTotalPaidPrice().getGrossDiscountedTotal());
        Assertions.assertEquals(expectedGrossDiscountedTotal_cancelled, order.getTotalCancelledPrice().getGrossDiscountedTotal());

        order.getOrderLines().forEach(orderLine -> {
            Assertions.assertEquals(orderLine.getOpenQty(), 0);
        });
        initialOrder.getOrderLines().forEach(orderLine -> {
            order.getOrderLines().forEach(ol -> {
                if (ol.getEan().equals(orderLine.getEan())) {
                    assertEntryAmountsDeepEquals(orderLine.getOrderLinePaidAmount(), ol.getOrderLinePaidAmount(), "grossDiscountedTotal", "lineVAT");
                    assertThat(ol.getOrderLinePaidAmount().getGrossDiscountedTotal()).isEqualByComparingTo(BigDecimal.ZERO);
                }
            });
        });
    }

    @Test
    void testProcessAndSetPriceFields_allOrderLinesDispatched() throws JsonProcessingException {
        // arrange
        final var order = OrderGenerator.createOrder();
        order.setPayments(new ArrayList<>());
        final var initialOrder = JsonUtils.deepCopy(order);

        final BigDecimal expectedOriginalGrossDiscountedTotal_paid = order.getTotalPaidPrice().getOriginalGrossDiscountedTotal();
        final BigDecimal expectedGrossDiscountedTotal_paid = order.getTotalPaidPrice().getOriginalGrossDiscountedTotal();


        // act
        priceService.processAndSetPriceFieldsForOrderLineAndTotalPaidAmount(order);

        // assert
        Assertions.assertNull(order.getTotalCancelledPrice());
        assertEntryAmountsDeepEquals(initialOrder.getTotalPaidPrice(), order.getTotalPaidPrice());
        Assertions.assertEquals(expectedGrossDiscountedTotal_paid, order.getTotalPaidPrice().getGrossDiscountedTotal());
        Assertions.assertEquals(expectedOriginalGrossDiscountedTotal_paid, order.getTotalPaidPrice().getOriginalGrossDiscountedTotal());
        order.getOrderLines().forEach(orderLine -> {
            Assertions.assertEquals(orderLine.getOpenQty(), orderLine.getOriginalQty());
        });
        initialOrder.getOrderLines().forEach(orderLine -> {
            order.getOrderLines().forEach(ol -> {
                if (ol.getEan().equals(orderLine.getEan())) {
                    assertEntryAmountsDeepEquals(orderLine.getOrderLinePaidAmount(), ol.getOrderLinePaidAmount());
                }
            });
        });
    }

    @Test
    void testProcessAndSetPriceFields_oneOrderLineCancelled() {
        // arrange
        final var order = OrderGenerator.createOrder();
        order.setPayments(new ArrayList<>());
        final var orderLineToCancel = order.getOrderLines().get(0);
        orderLineToCancel.setOpenQty(0);
        final BigDecimal expectedOriginalGrossDiscountedTotal_paid = order.getTotalPaidPrice().getOriginalGrossDiscountedTotal();
        final BigDecimal expectedGrossDiscountedTotal_cancelled = orderLineToCancel.getOrderLinePaidAmount().getOriginalGrossDiscountedTotal();
        final BigDecimal expectedGrossDiscountedTotal_paid = order.getTotalPaidPrice().getOriginalGrossDiscountedTotal().subtract(expectedGrossDiscountedTotal_cancelled);

        // act
        priceService.processAndSetPriceFieldsForOrderLineAndTotalPaidAmount(order);

        // assert
        Assertions.assertEquals(expectedOriginalGrossDiscountedTotal_paid, order.getTotalPaidPrice().getOriginalGrossDiscountedTotal());
        Assertions.assertEquals(expectedGrossDiscountedTotal_paid, order.getTotalPaidPrice().getGrossDiscountedTotal());
        Assertions.assertEquals(expectedGrossDiscountedTotal_cancelled, order.getTotalCancelledPrice().getGrossDiscountedTotal());
        order.getOrderLines()
            .stream()
            .filter(ol -> ol.equals(orderLineToCancel))
            .findFirst()
            .ifPresentOrElse(orderLine -> {
                Assertions.assertEquals(0, orderLine.getOpenQty());
                Assertions.assertNotNull(orderLine.getOrderLineCancelledAmount());
                Assertions.assertNull(orderLine.getOrderLineCancelledAmount().getOriginalGrossDiscountedTotal());
                Assertions.assertTrue(
                    new ReflectionEquals(orderLineToCancel.getOrderLineCancelledAmount(), "id", "version", "createdTS", "lastModifiedTS", "originalGrossDiscountedTotal")
                        .matches(orderLine.getOrderLineCancelledAmount()));
            }, Assertions::fail);
    }

    @Test
    void testProcessAndSetPriceFields_oneItemWithQuantityMoreThanOneCancelled() {
        // arrange
        final var order = OrderGenerator.createOrder();
        order.setPayments(new ArrayList<>());
        final var orderLineToCancel = order.getOrderLines().get(0);
        orderLineToCancel.setOpenQty(1);

        final OrderEntryAmount cancelledOrderEntryAmount = orderLineToCancel.getOrderLinePaidAmount();
        final BigDecimal cancelledAmount = cancelledOrderEntryAmount.getGrossDiscountedUnitPrice();

        final BigDecimal expectedOriginalGrossDiscountedTotal_paid = order.getTotalPaidPrice().getOriginalGrossDiscountedTotal();
        final BigDecimal expectedGrossDiscountedTotal_cancelled = orderLineToCancel.getOrderLinePaidAmount().getGrossDiscountedUnitPrice();
        final BigDecimal expectedGrossDiscountedTotal_paid = order.getTotalPaidPrice().getOriginalGrossDiscountedTotal().subtract(expectedGrossDiscountedTotal_cancelled);

        // act
        priceService.processAndSetPriceFieldsForOrderLineAndTotalPaidAmount(order);

        // assert
        Assertions.assertEquals(expectedOriginalGrossDiscountedTotal_paid, order.getTotalPaidPrice().getOriginalGrossDiscountedTotal());
        Assertions.assertEquals(expectedGrossDiscountedTotal_paid, order.getTotalPaidPrice().getGrossDiscountedTotal());
        Assertions.assertEquals(expectedGrossDiscountedTotal_cancelled, order.getTotalCancelledPrice().getGrossDiscountedTotal());
        order.getOrderLines()
            .stream()
            .filter(ol -> ol.equals(orderLineToCancel))
            .findFirst()
            .ifPresentOrElse(orderLine -> {
                Assertions.assertEquals(1, orderLine.getOpenQty());
                Assertions.assertNotNull(orderLine.getOrderLineCancelledAmount());
                Assertions.assertNull(orderLine.getOrderLineCancelledAmount().getOriginalGrossDiscountedTotal());
                Assertions.assertEquals(cancelledAmount, orderLine.getOrderLineCancelledAmount().getGrossDiscountedTotal());
                Assertions.assertEquals(cancelledOrderEntryAmount.getUnitDiscount(), orderLine.getOrderLineCancelledAmount().getUnitDiscount());
                Assertions.assertEquals(cancelledOrderEntryAmount.getGrossRetailUnitPrice(), orderLine.getOrderLineCancelledAmount().getGrossRetailUnitPrice());
                Assertions.assertEquals(cancelledOrderEntryAmount.getLineVAT(), orderLine.getOrderLineCancelledAmount().getLineVAT());
                Assertions.assertEquals(cancelledOrderEntryAmount.getUnitVAT(), orderLine.getOrderLineCancelledAmount().getUnitVAT());
            }, Assertions::fail);
    }

    @Test
    void tesCalculateRefundTotal() {
        // arrange
        Refund refund = RefundGenerator.createRefund();
        refund.setRefundTotal(null);
        BigDecimal expectedRefundTotal = REFUND_LINE_GROSS_DISCOUNTED_UNIT_PRICE.add(RETURN_FEE);
        BigDecimal expectedVat = UNIT_VAT.add(REFUND_LINE_UNIT_VAT);

        // act
        priceService.calculateRefundTotal(refund);

        // assert
        assertEquals(expectedRefundTotal, refund.getRefundTotal().getGrossDiscountedTotal());
        assertNull(refund.getRefundTotal().getOriginalGrossDiscountedTotal());
        assertEquals(REFUND_LINE_GROSS_DISCOUNTED_UNIT_PRICE, refund.getRefundTotal().getGrossSubTotal());
        assertEquals(expectedVat, refund.getRefundTotal().getVat());
    }

    @Test
    void testCalculateRefundTotal_withRefundTotal() {
        // arrange
        Refund refund = RefundGenerator.createRefund();
        refund.setRefundTotal(OverallTotal.builder().build());
        BigDecimal expectedRefundTotal = REFUND_LINE_GROSS_DISCOUNTED_UNIT_PRICE.add(RETURN_FEE);
        BigDecimal expectedVat = UNIT_VAT.add(REFUND_LINE_UNIT_VAT);

        // act
        priceService.calculateRefundTotal(refund);

        // assert
        assertEquals(expectedRefundTotal, refund.getRefundTotal().getGrossDiscountedTotal());
        assertNull(refund.getRefundTotal().getOriginalGrossDiscountedTotal());
        assertEquals(REFUND_LINE_GROSS_DISCOUNTED_UNIT_PRICE, refund.getRefundTotal().getGrossSubTotal());
        assertEquals(expectedVat, refund.getRefundTotal().getVat());
    }

    @Test
    void testCalculateRefundLineTotal() {
        // arrange
        Refund refund = RefundGenerator.createRefund();
        doNothing().when(priceService).calculateAndSetUnitDiscount(any());
        doNothing().when(priceService).calculateAndSetGrossAndOriginalDiscountedTotal(any(), anyInt());
        doNothing().when(priceService).calculateAndSetUnitVat(any(), any());
        doNothing().when(priceService).calculateAndSetLineVat(any(), anyInt());

        // act
        priceService.calculateRefundLineTotal(refund);

        // assert
        refund.getRefundLines()
            .forEach(refundLine -> {
                verify(priceService).calculateAndSetUnitDiscount(refundLine.getRefundLineTotal());
                verify(priceService).calculateAndSetGrossAndOriginalDiscountedTotal(refundLine.getRefundLineTotal(), refundLine.getQuantity());
                verify(priceService).calculateAndSetUnitVat(refundLine.getRefundLineTotal(), refundLine.getOrderLine()
                    .getTaxRate());
                verify(priceService).calculateAndSetLineVat(refundLine.getRefundLineTotal(), refundLine.getQuantity());
            });
    }

    @Test
    void testCalculateRefundLineTotal_nullRefundLineTotal() {
        // arrange
        Refund refund = RefundGenerator.createRefund();
        refund.getRefundLines().forEach(refundLine -> refundLine.setRefundLineTotal(null));


        // act
        priceService.calculateRefundLineTotal(refund);

        // assert
        verify(priceService, never()).calculateAndSetUnitDiscount(any());
        verify(priceService, never()).calculateAndSetGrossAndOriginalDiscountedTotal(any(), anyInt());
        verify(priceService, never()).calculateAndSetUnitVat(any(), any());
        verify(priceService, never()).calculateAndSetLineVat(any(), anyInt());
    }

    @Test
    void testCalculateRefundChargeTotal() {
        // arrange
        Refund refund = RefundGenerator.createRefund();
        doNothing().when(priceService).setOrderCharge(refund.getOrder());

        // act
        priceService.calculateRefundChargeTotal(refund);

        // assert
        verify(priceService).setOrderCharge(refund.getOrder());
    }

    @Test
    void testSetOrderCharge() {
        // arrange
        Order order = OrderGenerator.createOrder();
        doNothing().when(priceService).calculateAndSetUnitDiscount(any());
        doNothing().when(priceService).calculateAndSetGrossAndOriginalDiscountedTotal(any(), anyInt());
        doNothing().when(priceService).calculateAndSetUnitVat(any(), any());
        doNothing().when(priceService).calculateAndSetLineVat(any(), anyInt());

        // act
        priceService.setOrderCharge(order);

        // assert
        order.getOrderCharges().forEach(orderCharge -> {
            verify(priceService).calculateAndSetUnitDiscount(orderCharge.getChargeTotal());
            verify(priceService).calculateAndSetGrossAndOriginalDiscountedTotal(orderCharge.getChargeTotal(), orderCharge.getOpenQty());
            verify(priceService).calculateAndSetUnitVat(orderCharge.getChargeTotal(), orderCharge.getTaxRate());
            verify(priceService).calculateAndSetLineVat(orderCharge.getChargeTotal(), orderCharge.getOpenQty());
        });
    }

    @Test
    void testSetOrderCharge_withNullChargeTotal() {
        // arrange
        Order order = OrderGenerator.createOrder();
        order.getOrderCharges().forEach(orderCharge -> orderCharge.setChargeTotal(null));

        // act
        priceService.setOrderCharge(order);

        // assert
        verify(priceService, never()).calculateAndSetUnitDiscount(any());
        verify(priceService, never()).calculateAndSetGrossAndOriginalDiscountedTotal(any(), anyInt());
        verify(priceService, never()).calculateAndSetUnitVat(any(), any());
        verify(priceService, never()).calculateAndSetLineVat(any(), anyInt());
    }

    @Test
    void testCalculateAndSetLineVat() {
        // arrange
        BigDecimal unitVat = new BigDecimal("0.868");
        int qty = 2;
        OrderEntryAmount orderEntryAmount = OrderEntryAmount.builder()
            .unitVAT(unitVat)
            .build();

        // act
        priceService.calculateAndSetLineVat(orderEntryAmount, qty);

        // assert
        assertEquals(unitVat.multiply(new BigDecimal(qty)), orderEntryAmount.getLineVAT());
    }

    @Test
    void testCalculateAndSetGrossAndOriginalDiscountedTotal() {
        // arrange
        int qty = 2;
        BigDecimal grossDiscountedUnitPrice = new BigDecimal("10.00");
        OrderEntryAmount orderEntryAmount = OrderEntryAmount.builder()
            .grossDiscountedUnitPrice(grossDiscountedUnitPrice)
            .build();

        // act
        priceService.calculateAndSetGrossAndOriginalDiscountedTotal(orderEntryAmount, qty);

        // assert
        assertEquals(grossDiscountedUnitPrice.multiply(new BigDecimal(qty)), orderEntryAmount.getGrossDiscountedTotal());
    }

    @Test
    void testCalculateAndSetUnitDiscount() {
        // arrange
        BigDecimal grossRetailUnitPrice = new BigDecimal("10.00");
        BigDecimal grossDiscountedUnitPrice = new BigDecimal("9.50");
        OrderEntryAmount orderEntryAmount = OrderEntryAmount.builder()
            .grossRetailUnitPrice(grossRetailUnitPrice)
            .grossDiscountedUnitPrice(grossDiscountedUnitPrice)
            .build();

        // act
        priceService.calculateAndSetUnitDiscount(orderEntryAmount);

        // assert
        assertEquals(grossRetailUnitPrice.subtract(grossDiscountedUnitPrice), orderEntryAmount.getUnitDiscount());
    }

    private void assertEntryAmountsDeepEquals(Object expected, Object actual, String... excludeFields) {
        var excludeFieldsList = new ArrayList<>(Arrays.asList(excludeFields));
        excludeFieldsList.addAll(List.of("id", "version", "createdTS", "lastModifiedTS"));
        Assertions.assertTrue(
            new ReflectionEquals(expected, excludeFieldsList.toArray(new String[0]))
                .matches(actual)
        );
    }
}
