package com.bestseller.payment.core.domain.enumeration;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

 class PaymentStateTest {

    @Test
    public void testFromIdentifier_ValidIdentifier() {
        // Arrange
        int validIdentifier = 2000; // AUTHORIZED

        // Act
        PaymentState result = PaymentState.fromIdentifier(validIdentifier);

        // Assert
        assertEquals(PaymentState.AUTHORISED, result);
    }

    @Test
    public void testFromIdentifier_NullIdentifier() {
        // Arrange
        Integer nullIdentifier = null;

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> PaymentState.fromIdentifier(nullIdentifier));
    }

    @Test
    public void testFromIdentifier_InvalidIdentifier() {
        // Arrange
        int invalidIdentifier = 9999; // An identifier not in the enum

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> PaymentState.fromIdentifier(invalidIdentifier));

    }

    @Test
    public void testFromDwDescription_ValidDwDescription() {
        // Arrange
        String validDwDescription = "AUTH";

        // Act
        PaymentState result = PaymentState.fromDwDescription(validDwDescription);

        // Assert
        assertEquals(PaymentState.AUTHORISED, result);
    }

    @Test
    public void testFromDwDescription_NullDwDescription() {
        // Arrange
        String nullDwDescription = null;

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> PaymentState.fromDwDescription(nullDwDescription));
    }

    @Test
    public void testFromDwDescription_InvalidDwDescription() {
        // Arrange
        String invalidDwDescription = "INVALID_DESCRIPTION"; // Not a valid description

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> PaymentState.fromDwDescription(invalidDwDescription));
    }

    @Test
    public void testGetIdentifier() {
        // Arrange
        PaymentState state = PaymentState.REVIEW;

        // Act
        Integer identifier = state.getIdentifier();

        // Assert
        assertEquals(1000, identifier);
    }

    @Test
    public void testGetDescription() {
        // Arrange
        PaymentState state = PaymentState.SETTLED;

        // Act
        String description = state.getDescription();

        // Assert
        assertEquals("SETTLED", description);
    }

    @Test
    public void testGetDwDescription() {
        // Arrange
        PaymentState state = PaymentState.AUTHORISED;

        // Act
        String dwDescription = state.getDwDescription();

        // Assert
        assertEquals("AUTH", dwDescription);
    }
}