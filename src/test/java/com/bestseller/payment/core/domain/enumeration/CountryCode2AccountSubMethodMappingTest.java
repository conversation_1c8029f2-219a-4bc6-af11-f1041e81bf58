package com.bestseller.payment.core.domain.enumeration;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

 class CountryCode2AccountSubMethodMappingTest {

    @Test
    public void testGetCodeByCountry_ValidCountry() {
        // Arrange
        String validCountry = "DE"; // Germany

        // Act
        String result = CountryCode2AccountSubMethodMapping.getCodeByCountry(validCountry);

        // Assert
        assertEquals("1922", result);
    }

    @Test
    public void testGetCodeByCountry_InvalidCountry() {
        // Arrange
        String invalidCountry = "XX"; // Invalid country code

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> CountryCode2AccountSubMethodMapping.getCodeByCountry(invalidCountry));
    }

    @Test
    public void testGetCode() {
        // Arrange
        CountryCode2AccountSubMethodMapping mapping = CountryCode2AccountSubMethodMapping.NL_3028;

        // Act
        String code = mapping.getCode();

        // Assert
        assertEquals("3028", code);
    }

    @Test
    public void testGetCountry() {
        // Arrange
        CountryCode2AccountSubMethodMapping mapping = CountryCode2AccountSubMethodMapping.SE_2947;

        // Act
        String country = mapping.getCountry();

        // Assert
        assertEquals("SE", country);
    }
}