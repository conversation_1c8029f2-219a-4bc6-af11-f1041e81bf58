package com.bestseller.payment.core.domain.enumeration;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class PaymentStateTransitionTest {

    @Test
    public void testIsValidTransition_ValidTransition() {
        // Arrange
        PaymentState fromState = PaymentState.START;
        PaymentType paymentType = PaymentType.KLARNA;
        PaymentState toState = PaymentState.REVIEW;

        // Act
        boolean isValid = PaymentStateTransition.isValidTransition(fromState, paymentType, toState);

        // Assert
        assertTrue(isValid);
    }

    @Test
    public void testIsValidTransition_InvalidTransition_KLARNA() {
        // Arrange
        PaymentState fromState = PaymentState.AUTHORISED;
        PaymentType paymentType = PaymentType.KLARNA;
        PaymentState toState = PaymentState.REFUNDED;

        // Act
        boolean isValid = PaymentStateTransition.isValidTransition(fromState, paymentType, toState);

        // Assert
        assertFalse(isValid);
    }

    @Test
    public void testIsValidTransition_InvalidTransition_GIFT_CARD() {
        // Arrange
        PaymentState fromState = PaymentState.REVIEW;
        PaymentType paymentType = PaymentType.GIFTCARD;
        PaymentState toState = PaymentState.SETTLED;

        // Act
        boolean isValid = PaymentStateTransition.isValidTransition(fromState, paymentType, toState);

        // Assert
        assertFalse(isValid);
    }

    @Test
    public void testIsValidTransition_InvalidTransition_NONE() {
        // Arrange
        PaymentState fromState = PaymentState.AUTHORISED;
        PaymentType paymentType = PaymentType.NONE;
        PaymentState toState = PaymentState.OFFLINE;

        // Act
        boolean isValid = PaymentStateTransition.isValidTransition(fromState, paymentType, toState);

        // Assert
        assertFalse(isValid);
    }

    @Test
    public void testIsValidTransition_InvalidTransition_OFFLINE() {
        // Arrange
        PaymentState fromState = PaymentState.AUTHORISED;
        PaymentType paymentType = PaymentType.OFFLINE;
        PaymentState toState = PaymentState.OFFLINE;

        // Act
        boolean isValid = PaymentStateTransition.isValidTransition(fromState, paymentType, toState);

        // Assert
        assertFalse(isValid);
    }

    @Test
    public void testIsValidTransition_validTransition_CANCELLED() {
        // Arrange
        PaymentState fromState = PaymentState.AUTHORISED;
        PaymentType paymentType = PaymentType.ADYEN_BANK;
        PaymentState toState = PaymentState.CANCELLED;

        // Act
        boolean isValid = PaymentStateTransition.isValidTransition(fromState, paymentType, toState);

        // Assert
        assertTrue(isValid);
    }
}