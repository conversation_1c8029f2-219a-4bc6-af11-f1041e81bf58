package com.bestseller.payment.core.dto;

import org.junit.jupiter.api.Test;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class RefundOptionsTest {

    @Test
    void testCsrInitials() {
        // Arrange
        var refundOptions = RefundOptions.builder()
            .chargeReturnFee(true)
            .refundShippingFee(true)
            .refundReason(RefundReason.RETURN)
            .csrInitials("csrInitials")
            .build();

        // Act
        var actual = refundOptions.csrInitials();

        // Assert
        assertThat(actual).isEqualTo("csrInitials");
    }

    @Test
    void testCsrInitials_defaultCsrInitials() {
        // Arrange
        var refundOptions = RefundOptions.builder()
            .chargeReturnFee(true)
            .refundShippingFee(true)
            .refundReason(RefundReason.RETURN)
            .build();

        // Act
        var actual = refundOptions.csrInitials();

        // Assert
        assertThat(actual).isEqualTo("SYSTEM");
    }

    @Test
    void testOrderChargeRefundRequests() {
        // Arrange
        var refundOptions = RefundOptions.builder()
            .chargeReturnFee(true)
            .refundShippingFee(true)
            .refundReason(RefundReason.RETURN)
            .orderChargeRefundRequests(List.of(OrderChargeRefund.builder().build()))
            .build();

        // Act
        var actual = refundOptions.orderChargeRefundRequests();

        // Assert
        assertThat(actual).hasSize(1);
    }

    @Test
    void testOrderChargeRefundRequests_nullOrderChargeRefundRequests() {
        // Arrange
        var refundOptions = RefundOptions.builder()
            .chargeReturnFee(true)
            .refundShippingFee(true)
            .refundReason(RefundReason.RETURN)
            .orderChargeRefundRequests(null)
            .build();

        // Act
        var actual = refundOptions.orderChargeRefundRequests();

        // Assert
        assertThat(actual).isEmpty();
    }
}
