package com.bestseller.payment.core.dto;

import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;


 class ReturnFeeCalculationResultTest {

    @Test
    void testChargeable() {
        // Arrange
        var returnFeeCalculationResult = ReturnFeeCalculationResult.builder()
                .returnFee(BigDecimal.ONE)
                .taxRate(BigDecimal.ZERO)
                .build();

        // Act
        var result = returnFeeCalculationResult.chargeable();

        // Assert
        assertTrue(result);
    }

    @Test
    void testNotChargeable() {
        // Arrange
        var returnFeeCalculationResult = ReturnFeeCalculationResult.builder()
                .returnFee(BigDecimal.ZERO)
                .taxRate(BigDecimal.ZERO)
                .build();

        // Act
        var result = returnFeeCalculationResult.chargeable();

        // Assert
        assertFalse(result);
    }
}
