package com.bestseller.payment.core.validation;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRejected.PaymentRejected;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.core.exception.OrderNotFoundException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertThrows;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PaymentRejectedValidatorTest {

    private static final String ORDER_ID = "ORDER_ID";
    private static final String PSP_REFERENCE = "psp-reference";

    @InjectMocks
    PaymentAuthorizationValidator paymentAuthorizationValidator;

    @Mock
    OrderRepository orderRepository;


    @Test
    public void testValidation_true() {
        PaymentRejected message = new PaymentRejected();
        message.setOrderId(ORDER_ID);
        message.setProvider("KLARNA_PAYMENTS");
        message.setPspReference(PSP_REFERENCE);

        when(orderRepository.existsByOrderIdAndPaymentsPaymentReference(ORDER_ID, PSP_REFERENCE)).thenReturn(true);
        assertTrue(paymentAuthorizationValidator.validate(message.getOrderId(),
                message.getProvider(),
                message.getPspReference()));
    }

    @Test
    public void validate_givenUnknownProvider_throwException() {
        PaymentRejected message = new PaymentRejected();
        message.setOrderId(ORDER_ID);

        // Arrange for unknown provider
        message.setProvider("UNKNOWN");
        message.setPspReference("hsadjhasjdhjas-adjsahdkjsa");

        // Act and assert
        assertThrows(IllegalArgumentException.class, () -> paymentAuthorizationValidator.validate(message.getOrderId(),
                message.getProvider(),
                message.getPspReference()));

        // Arrange for empty pspReference
        message.setProvider("OPTICARD");
        message.setPspReference("");

        // Act and Assert
        assertFalse(paymentAuthorizationValidator.validate(message.getOrderId(),
                message.getProvider(),
                message.getPspReference()));
    }

    @Test
    public void testValidation_throwException_order_not_found() {
        PaymentRejected message = new PaymentRejected();
        message.setOrderId(ORDER_ID);
        message.setProvider("KLARNA_PAYMENTS");
        message.setPspReference(PSP_REFERENCE);

        when(orderRepository.existsByOrderIdAndPaymentsPaymentReference(ORDER_ID, PSP_REFERENCE)).thenReturn(false);
        assertThrows(OrderNotFoundException.class, () -> paymentAuthorizationValidator.validate(message.getOrderId(),
                message.getProvider(),
                message.getPspReference()));
    }
}
