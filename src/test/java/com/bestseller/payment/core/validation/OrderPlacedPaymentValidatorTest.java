package com.bestseller.payment.core.validation;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.OrderDetails;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.OrderPlaced;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.Payment;
import com.bestseller.payment.adapter.api.dto.PaymentValidationResponse;
import com.bestseller.payment.core.exception.PaymentValidationException;
import com.bestseller.payment.core.service.payment.factory.PaymentFactory;
import com.bestseller.payment.utils.OrderPlacedBuilder;
import org.junit.Assert;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static com.bestseller.payment.core.domain.enumeration.Platform.DEMANDWARE;
import static org.mockito.Mockito.doThrow;

@ExtendWith(MockitoExtension.class)
class OrderPlacedPaymentValidatorTest {

    @InjectMocks
    OrderPlacedPaymentValidator orderPlacedPaymentValidator;

    @Mock
    PaymentFactory paymentFactory;

    private OrderPlacedBuilder orderPlacedBuilder;

    @BeforeEach
    public void init() {
        orderPlacedBuilder = new OrderPlacedBuilder();
    }

    @Test
    void testValidateOrderPlaced_tradebyte_success() {
        OrderPlaced tbOrder = orderPlacedBuilder
            .withDefaultOrderLines()
            .withStore("TRADEBYTE")
            .withShippingFee(new BigDecimal(0))
            .withOrderValue(new BigDecimal(6.5))
            .build();
        PaymentValidationResponse result = orderPlacedPaymentValidator.validate(tbOrder);
        assertSuccessfulValidation(result);
    }


    @Test
    void testValidateOrderPlaced_demandwareWithOfflinePayment_success() {
        OrderPlaced tbOrder = orderPlacedBuilder
            .withDefaultOrderLines()
            .withStore(DEMANDWARE.name())
            .withShippingFee(new BigDecimal(0))
            .withOrderValue(new BigDecimal(6.5))
            .withOfflinePayment(true)
            .build();
        PaymentValidationResponse result = orderPlacedPaymentValidator.validate(tbOrder);
        assertSuccessfulValidation(result);
    }

    @Test
    void testValidateOrderPlaced_demandware_klarna_success() {
        OrderPlaced klarnaOrder = orderPlacedBuilder
            .withDefaultValuesPaidWithKlarna()
            .build();
        PaymentValidationResponse result = orderPlacedPaymentValidator.validate(klarnaOrder);
        assertSuccessfulValidation(result);
    }

    @Test
    void testValidateOrderPlaced_demandware_klarna_withNoCurrency_shouldReturnInvalidPayload() {
        OrderPlaced klarnaOrder = orderPlacedBuilder
            .withDefaultValuesPaidWithKlarna()
            .build();
        klarnaOrder.getOrderDetails().setCurrency(null);
        PaymentValidationResponse result = orderPlacedPaymentValidator.validate(klarnaOrder);
        String expectedErrorMessage = "Currency is missing at `OrderDetails.currency`.";
        assertValidationError(result, expectedErrorMessage);
    }

    @Test
    void testValidateOrderPlaced_demandware_adyen_success() {
        OrderPlaced adyenOrder = orderPlacedBuilder
            .withDefaultValuesPaidWithAdyen()
            .build();
        PaymentValidationResponse result = orderPlacedPaymentValidator.validate(adyenOrder);
        assertSuccessfulValidation(result);
    }

    @Test
    void testValidateOrderPlaced_demandware_giftcard_success() {
        OrderPlaced gcOrder = orderPlacedBuilder
            .withDefaultValuesPaidWithGiftCard()
            .build();
        PaymentValidationResponse result = orderPlacedPaymentValidator.validate(gcOrder);
        assertSuccessfulValidation(result);
    }

    @Test
    void testValidateOrderPlaced_demandware_multiplePayment_success() {
        OrderPlaced multiplePaymentOrder = orderPlacedBuilder
            .withDefaultOrderLines()
            .withKlarnaPayment("KLARNA_ACCOUNT", "-1", new BigDecimal(4.0), "AUTH")
            .withGiftCard(new BigDecimal(4.0))
            .build();
        PaymentValidationResponse result = orderPlacedPaymentValidator.validate(multiplePaymentOrder);
        assertSuccessfulValidation(result);
    }

    @Test
    void testValidateOrderPlaced_platform_fail() {
        OrderPlaced orderPlaced = orderPlacedBuilder.withStore("WRONG_PLATFORM").build();
        PaymentValidationResponse result = orderPlacedPaymentValidator.validate(orderPlaced);
        String expectedErrorMessage = String.format("Platform can't be identified by this value: %s",
            orderPlaced.getStore());
        assertValidationError(result, expectedErrorMessage);
    }

    @Test
    void testValidateOrderPlaced_tb_ordervalue_empty() {
        OrderPlaced orderPlaced = orderPlacedBuilder
            .withStore("TRADEBYTE")
            .withOrderValue(null)
            .build();
        PaymentValidationResponse result = orderPlacedPaymentValidator.validate(orderPlaced);
        String expectedErrorMessage = "Order value must not be empty or null.";
        assertValidationError(result, expectedErrorMessage);
    }

    @Test
    void testValidateOrderPlaced_klarna_billingCountryCode_fail() {
        OrderPlaced orderPlaced = orderPlacedBuilder
            .withBillingCountryCode("UNKNOWN")
            .withDefaultOrderLines()
            .withKlarnaPayment("KLARNA_ACCOUNT", "-1", new BigDecimal(8.0), "AUTH")
            .build();
        String expectedErrorMsg = "Invalid country name: UNKNOWN";
        doThrow(new PaymentValidationException(expectedErrorMsg)).when(paymentFactory).validateProcessor(orderPlaced.getPayments().get(0), "UNKNOWN");
        PaymentValidationResponse result = orderPlacedPaymentValidator.validate(orderPlaced);
        assertValidationError(result, expectedErrorMsg);
    }

    @Test
    void testValidateOrderPlaced_adyen_fail() {
        OrderPlaced orderPlaced = orderPlacedBuilder
            .withBillingCountryCode("UNKNOWN")
            .withDefaultOrderLines()
            .withAdyenPayment("ADYEN_IDEAL", "iDeal", new BigDecimal(9.0), "AUTH")
            .build();
        PaymentValidationResponse result = orderPlacedPaymentValidator.validate(orderPlaced);
        String expectedErrorMsg = "Payment amount is inconsistent with the total order charges.";
        assertValidationError(result, expectedErrorMsg);
    }

    @Test
    void testValidateOrderPlaced_demandware_giftcard_fail() {
        List<Payment> paymentList = new ArrayList<>();
        Payment payment = new Payment();
        payment.setMethod("OC_GIFTCARD");
        payment.setProvider("OPTICARD");
        payment.setState("AUTH");
        paymentList.add(payment);
        OrderPlaced gcOrder = orderPlacedBuilder
            .withDefaultOrderLines()
            .withPayments(paymentList)
            .build()
            .withOrderDetails(new OrderDetails().withCurrency("EUR"));
        String expectedErrorMsg = "Missing gift card details.";
        doThrow(new PaymentValidationException(expectedErrorMsg)).when(paymentFactory).validateProcessor(gcOrder.getPayments().get(0), "NL");
        PaymentValidationResponse result = orderPlacedPaymentValidator.validate(gcOrder);
        assertValidationError(result, expectedErrorMsg);
    }

    @Test
    void testValidateOrderPlaced_adyen_fail_paymentstate() {
        OrderPlaced orderPlaced = orderPlacedBuilder
            .withBillingCountryCode("UNKNOWN")
            .withDefaultOrderLines()
            .withAdyenPayment("ADYEN_IDEAL", "iDeal", new BigDecimal(9.0), "INVALID_PAYMENT_STATE")
            .build();
        PaymentValidationResponse result = orderPlacedPaymentValidator.validate(orderPlaced);
        String expectedErrorMsg = "Payment state can't be identified by this dwDescription: INVALID_PAYMENT_STATE";
        assertValidationError(result, expectedErrorMsg);
    }

    @Test
    void testValidateOrderPlaced_klarna_paymentamount_fail() {
        OrderPlaced orderPlaced = orderPlacedBuilder
            .withBillingCountryCode("NL")
            .withDefaultOrderLines()
            .withKlarnaPayment("KLARNA_ACCOUNT", "-1", new BigDecimal(100.0), "AUTH")
            .build();
        String expectedErrorMsg = "Payment amount is inconsistent with the total order charges.";
        PaymentValidationResponse result = orderPlacedPaymentValidator.validate(orderPlaced);
        assertValidationError(result, expectedErrorMsg);
    }

    @Test
    void testValidateOrderPlaced_klarna_shippingfee_fail() {
        OrderPlaced orderPlaced = orderPlacedBuilder
            .withBillingCountryCode("NL")
            .withDefaultOrderLines()
            .withDefaultValuesPaidWithKlarna()
            .withShippingFee(null)
            .build();
        String expectedErrorMsg = "Shipping fee should not be empty.";
        PaymentValidationResponse result = orderPlacedPaymentValidator.validate(orderPlaced);
        assertValidationError(result, expectedErrorMsg);
    }

    @Test
    void testValidateOrderPlaced_klarna_shippingEan_fail() {
        OrderPlaced orderPlaced = orderPlacedBuilder
            .withBillingCountryCode("NL")
            .withDefaultOrderLines()
            .withDefaultValuesPaidWithKlarna()
            .withShippingFeesEan("")
            .build();
        String expectedErrorMsg = "Shipping fees EAN should not be empty.";
        PaymentValidationResponse result = orderPlacedPaymentValidator.validate(orderPlaced);
        assertValidationError(result, expectedErrorMsg);
    }

    @Test
    void testValidateOrderPlaced_klarna_shippingName_fail() {
        OrderPlaced orderPlaced = orderPlacedBuilder
            .withBillingCountryCode("NL")
            .withDefaultOrderLines()
            .withDefaultValuesPaidWithKlarna()
            .withShippingFeesEan("ean")
            .withShippingName("")
            .build();
        String expectedErrorMsg = "Shipping name should not be empty.";
        PaymentValidationResponse result = orderPlacedPaymentValidator.validate(orderPlaced);
        assertValidationError(result, expectedErrorMsg);
    }

    @Test
    void testValidateOrderPlaced_empty_payment() {
        OrderPlaced orderPlaced = orderPlacedBuilder
            .withBillingCountryCode("NL")
            .withDefaultOrderLines()
            .withPayments(null)
            .build();
        String expectedErrorMsg = "Payment details should not be empty.";
        PaymentValidationResponse result = orderPlacedPaymentValidator.validate(orderPlaced);
        assertValidationError(result, expectedErrorMsg);
    }

    @Test
    void testValidateOrderPlaced_demandware_multiplePayment_fail() {
        OrderPlaced multiplePaymentOrder = orderPlacedBuilder
            .withDefaultOrderLines()
            .withKlarnaPayment("KLARNA_ACCOUNT", "-1", new BigDecimal(4.0), "AUTH")
            .withAdyenPayment("ADYEN_IDEAL", "bank", new BigDecimal(4.0), "AUTH")
            .build();
        PaymentValidationResponse result = orderPlacedPaymentValidator.validate(multiplePaymentOrder);
        String expectedErrorMsg = "Invalid payment combination.";
        assertValidationError(result, expectedErrorMsg);
    }

    private void assertSuccessfulValidation(PaymentValidationResponse response) {
        Assert.assertNull(response.getErrorMessages());
        Assert.assertTrue(response.isSuccess());
    }

    private void assertValidationError(PaymentValidationResponse response, String errorMessage) {
        PaymentValidationResponse errorResponse = new PaymentValidationResponse();
        errorResponse.setSuccess(false);
        errorResponse.setErrorMessages(List.of(errorMessage));

        Assert.assertEquals(errorResponse.getErrorMessages(), response.getErrorMessages());
        Assert.assertFalse(response.isSuccess());
    }

}
