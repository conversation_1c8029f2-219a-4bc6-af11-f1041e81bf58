package com.bestseller.payment.core.validation;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentAuthorized.PaymentAuthorized;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.core.exception.OrderNotFoundException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertThrows;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PaymentAuthorizedValidatorTest {

    private static final String ORDER_ID = "ORDER_ID";
    private static final String PSP_REFERENCE = "psp-reference";

    @InjectMocks
    PaymentAuthorizationValidator paymentAuthorizedValidator;

    @Mock
    OrderRepository orderRepository;

    @Test
    public void testValidation_True() {
        PaymentAuthorized message = new PaymentAuthorized();
        message.setOrderId(ORDER_ID);
        message.setProvider("ADYEN");
        message.setPspReference(PSP_REFERENCE);

        when(orderRepository.existsByOrderIdAndPaymentsPaymentReference(ORDER_ID, PSP_REFERENCE)).thenReturn(true);

        assertTrue(paymentAuthorizedValidator.validate(message.getOrderId(),
                message.getProvider(),
                message.getPspReference()));
    }

    @Test
    public void testValidation_False_Not_Have_Payment_Reference() {
        PaymentAuthorized message = new PaymentAuthorized();
        message.setOrderId(ORDER_ID);

        // Arrange for empty pspReference
        message.setProvider("ADYEN");
        message.setPspReference("");

        //Act and assert
        assertFalse(paymentAuthorizedValidator.validate(message.getOrderId(),
                message.getProvider(),
                message.getPspReference()));
    }

    @Test
    public void validate_givenBlankProvider_throwException() {
        PaymentAuthorized message = new PaymentAuthorized();
        message.setOrderId(ORDER_ID);

        // Arrange for empty pspReference
        message.setProvider("");
        message.setPspReference("213131");

        //Act and assert
        assertThrows(IllegalArgumentException.class,
            () -> paymentAuthorizedValidator.validate(message.getOrderId(),
                message.getProvider(),
                message.getPspReference()));
    }

    @Test
    public void testValidation_Exception_whenProcessorId_IsWrong() {
        PaymentAuthorized message = new PaymentAuthorized();
        message.setOrderId(ORDER_ID);

        // Arrange for unknown provider
        message.setProvider("UNKNOWN");
        message.setPspReference("psp-reference");

        // Act and assert
        assertThrows(IllegalArgumentException.class, () -> paymentAuthorizedValidator.validate(message.getOrderId(),
                message.getProvider(),
                message.getPspReference()));
    }

    @Test
    public void testValidation_throwException_Order_Not_Exists() {
        PaymentAuthorized message = new PaymentAuthorized();
        message.setOrderId(ORDER_ID);
        message.setProvider("ADYEN");
        message.setPspReference(PSP_REFERENCE);

        when(orderRepository.existsByOrderIdAndPaymentsPaymentReference(ORDER_ID, PSP_REFERENCE)).thenReturn(false);

        //Act and assert
        assertThrows(OrderNotFoundException.class, () -> paymentAuthorizedValidator.validate(message.getOrderId(),
                message.getProvider(),
                message.getPspReference()));
    }
}
