package com.bestseller.payment.core.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.ItemsToRefund;
import com.bestseller.payment.core.dto.OrderItemToRefund;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

 class OrderItemToRefundMapperTest {
    OrderItemToRefundMapper orderItemToRefundMapper = Mappers.getMapper(OrderItemToRefundMapper.class);


    @Test
    void testMapSingle() {
        // Arrange
        ItemsToRefund itemsToRefund = new ItemsToRefund()
                .withEan("ean")
                .withQuantity(1);

        OrderItemToRefund expected = OrderItemToRefund.builder()
                .ean("ean")
                .quantity(1)
                .build();

        // Act
        var result = orderItemToRefundMapper.mapToOrderItemToRefund(itemsToRefund);

        // Assert
        assertEquals(result, expected);
    }

    @Test
    void testMapList() {
        // Arrange
        ItemsToRefund itemsToRefund = new ItemsToRefund()
                .withEan("ean")
                .withQuantity(1);

        OrderItemToRefund expected = OrderItemToRefund.builder()
                .ean("ean")
                .quantity(1)
                .build();

        // Act
        var result = orderItemToRefundMapper.mapToOrderItemToRefundList(List.of(itemsToRefund));

        // Assert
        assertEquals(result.get(0), expected);
    }

    @Test
    void testMapList_null() {
        // Act
        var result = orderItemToRefundMapper.mapToOrderItemToRefundList(null);

        // Assert
        assertNull(result);
    }

    @Test
    void testMapSingle_null() {
        // Act
        var result = orderItemToRefundMapper.mapToOrderItemToRefund(null);

        // Assert
        assertNull(result);
    }
}
