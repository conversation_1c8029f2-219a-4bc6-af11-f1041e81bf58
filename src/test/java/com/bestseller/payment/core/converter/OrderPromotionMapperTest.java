package com.bestseller.payment.core.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.OrderPromotion;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mapstruct.factory.Mappers;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
class OrderPromotionMapperTest {

    private final OrderPromotionMapper converter = Mappers.getMapper(OrderPromotionMapper.class);

    private static final String PROMOTION_ID = "PROMO1";
    private static final String CAMPAIGN_ID = "CAMPAIGN1";
    private static final BigDecimal NET_PRICE = BigDecimal.valueOf(10.0);
    private static final BigDecimal GROSS_PRICE = BigDecimal.valueOf(15.0);

    @Test
    void mapOrderPromotion_givenValidOrderPromotion_returnsPromotion() {
        // arrange
        OrderPromotion orderPromotion = new OrderPromotion();
        orderPromotion.withPromotionId(PROMOTION_ID)
            .withCampaignId(CAMPAIGN_ID)
            .withNetPrice(NET_PRICE)
            .withGrossPrice(GROSS_PRICE);

        // act
        var result = converter.mapOrderPromotion(orderPromotion);

        // assert
        assertThat(result)
            .extracting("promotionId", "campaignId", "netPrice", "grossPrice")
            .containsExactly(PROMOTION_ID, CAMPAIGN_ID, NET_PRICE, GROSS_PRICE);
    }

    @Test
    void mapOrderPromotion_givenNullOrderPromotion_returnsNull() {
        // act
        var result = converter.mapOrderPromotion(null);

        // Assert
        assertNull(result);
    }

    @Test
    void mapOrderPromotions_givenValidOrderPromotions_returnsPromotions() {
        // arrange
        List<OrderPromotion> orderPromotions = List.of(
            new OrderPromotion().withPromotionId(PROMOTION_ID)
                .withCampaignId(CAMPAIGN_ID)
                .withNetPrice(NET_PRICE)
                .withGrossPrice(GROSS_PRICE)
        );

        // act
        var result = converter.mapOrderPromotions(orderPromotions);

        // assert
        assertThat(result)
            .extracting("promotionId", "campaignId", "netPrice", "grossPrice")
            .containsExactly(Tuple.tuple(PROMOTION_ID, CAMPAIGN_ID, NET_PRICE, GROSS_PRICE));
    }

    @Test
    void mapOrderPromotions_givenNullOrderPromotions_returnsNull() {
        // act
        var result = converter.mapOrderPromotions(null);

        // assert
        assertNull(result);
    }

    @Test
    void mapOrderPromotions_givenEmptyOrderPromotions_returnsEmpty() {
        // act
        var result = converter.mapOrderPromotions(Collections.emptyList());

        // assert
        assertTrue(result.isEmpty());
    }
}
