package com.bestseller.payment.core.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.OrderLine;
import com.bestseller.payment.core.domain.OrderEntryAmount;
import com.bestseller.payment.core.domain.OrderLineDiscount;
import com.bestseller.payment.core.domain.enumeration.EntryType;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

 class OrderLineMapperTest {
    private final OrderLineMapper orderLineMapper = Mappers.getMapper(OrderLineMapper.class);

    @Test
    void testMapOrderLines_fullScenario() {
        // Given
        OrderLine validOrderLine = new OrderLine();
        validOrderLine.setEan("1234567890123");
        validOrderLine.setProductName("Test Product");
        validOrderLine.setRetailPrice(BigDecimal.valueOf(8));
        validOrderLine.setDiscountedUnitPrice(BigDecimal.valueOf(6));
        validOrderLine.setDiscountValue(BigDecimal.valueOf(4));
        validOrderLine.setQuantity(2);
        validOrderLine.setVat(BigDecimal.valueOf(0.20));
        validOrderLine.setVatClass("STANDARD");
        validOrderLine.setBonusProduct(true);
        // Set values for promotion fields
        validOrderLine.setOrderLinePromotion(new com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.OrderLinePromotion());
        validOrderLine.getOrderLinePromotion().setPromotionId("PROMO123");
        validOrderLine.getOrderLinePromotion().setCampaignId("CAMPAIGN456");
        validOrderLine.getOrderLinePromotion().setCouponId("COUPON789");

        // When
        List<com.bestseller.payment.core.domain.OrderLine> domainOrderLines =
                orderLineMapper.mapOrderLines(List.of(validOrderLine));

        com.bestseller.payment.core.domain.OrderLine domainOrderLine = domainOrderLines.get(0);

        // Then
        assertEquals(domainOrderLine.getSkuId(), validOrderLine.getEan());
        assertEquals(domainOrderLine.getName(), validOrderLine.getProductName());
        // Assuming selectPrice() prefers validOrderLine.getListPrice() if not null
        assertEquals(domainOrderLine.getStandardRetailPrice(), validOrderLine.getRetailPrice());
        assertEquals(domainOrderLine.getOriginalQty(), validOrderLine.getQuantity());
        assertEquals(domainOrderLine.getOpenQty(), validOrderLine.getQuantity());
        assertEquals(domainOrderLine.getType(), EntryType.LINE);
        assertEquals(domainOrderLine.getTaxRate(), validOrderLine.getVat());
        assertEquals(domainOrderLine.getVatClassId(), validOrderLine.getVatClass());
        assertEquals(domainOrderLine.getPromotionId(), validOrderLine.getOrderLinePromotion().getPromotionId());
        assertEquals(domainOrderLine.getCampaignId(), validOrderLine.getOrderLinePromotion().getCampaignId());
        assertEquals(domainOrderLine.getCouponId(), validOrderLine.getOrderLinePromotion().getCouponId());

        assertEquals(domainOrderLine.getOrderLinePaidAmount().getGrossRetailUnitPrice(), validOrderLine.getRetailPrice());
        assertEquals(domainOrderLine.getOrderLinePaidAmount().getGrossDiscountedUnitPrice(), validOrderLine.getDiscountedUnitPrice());

        assertEquals(domainOrderLine.getBonusProduct(), validOrderLine.getBonusProduct());
    }

    @Test
    void testMapOrderLines_nullOrderLine() {
        // When
        List<com.bestseller.payment.core.domain.OrderLine> domainOrderLines =
                orderLineMapper.mapOrderLines(null);
        // Then
        assertNull(domainOrderLines);
    }

    @Test
    void testMapOrderLines_nullBonusProduct() {
        // Given
        OrderLine validOrderLine = new OrderLine();
        validOrderLine.setBonusProduct(null);
        validOrderLine.setQuantity(1);
        validOrderLine.setDiscountedUnitPrice(BigDecimal.valueOf(10));

        // When
        List<com.bestseller.payment.core.domain.OrderLine> domainOrderLines =
                orderLineMapper.mapOrderLines(List.of(validOrderLine));

        // Then
        assertFalse(domainOrderLines.get(0).getBonusProduct());
    }

    @Test
    void testSetStandardRetailPrice_ListPrice_PreferredOverRetailPrice() {
        // Given
        OrderLine validOrderLine = new OrderLine();
        validOrderLine.setListPrice(BigDecimal.valueOf(150));
        validOrderLine.setRetailPrice(BigDecimal.valueOf(130));

        // When
        BigDecimal price = orderLineMapper.setStandardRetailPrice(validOrderLine);

        // Then
        assertEquals(price, validOrderLine.getListPrice());

        // Test case when list price is null
        validOrderLine.setListPrice(null);
        price = orderLineMapper.setStandardRetailPrice(validOrderLine);
        assertEquals(price, validOrderLine.getRetailPrice());
    }

    @Test
    void testSetOrderLinePaidAmount() {
        // Given
        OrderLine validOrderLine = new OrderLine();
        validOrderLine.setRetailPrice(BigDecimal.valueOf(200));
        validOrderLine.setDiscountedUnitPrice(BigDecimal.valueOf(180));
        validOrderLine.setDiscountedTotalPrice(BigDecimal.valueOf(360));
        validOrderLine.setQuantity(2);

        // When
        OrderEntryAmount entryAmount = orderLineMapper.setOrderLinePaidAmount(validOrderLine);

        // Then
        assertEquals(entryAmount.getGrossRetailUnitPrice(), validOrderLine.getRetailPrice());
        assertEquals(entryAmount.getGrossDiscountedUnitPrice(), validOrderLine.getDiscountedUnitPrice());
        assertEquals(entryAmount.getOriginalGrossDiscountedTotal(), validOrderLine.getDiscountedTotalPrice());
    }

    @Test
    void testSetOrderLineDiscount() {
        // Given
        OrderLine validOrderLine = new OrderLine();
        validOrderLine.setDiscountValue(BigDecimal.valueOf(20));

        // When
        OrderLineDiscount discount = orderLineMapper.setOrderLineDiscount(validOrderLine);

        // Then
        assertNotNull(discount);
        assertEquals(discount.getDiscountAmount(), validOrderLine.getDiscountValue());

        // Test case when discount value is null or zero
        validOrderLine.setDiscountValue(null);
        assertNull(orderLineMapper.setOrderLineDiscount(validOrderLine));

        validOrderLine.setDiscountValue(BigDecimal.ZERO);
        assertNull(orderLineMapper.setOrderLineDiscount(validOrderLine));
    }

}
