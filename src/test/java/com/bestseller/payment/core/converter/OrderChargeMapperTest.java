package com.bestseller.payment.core.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ShippingCharge;
import com.bestseller.payment.core.domain.OrderCharge;
import com.bestseller.payment.core.domain.enumeration.EntryType;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

 class OrderChargeMapperTest {
    private OrderChargeMapper orderChargeMapper = Mappers.getMapper(OrderChargeMapper.class);
    @Test
    void testMapToOrderCharges_TB() {
        // Arrange
        ShippingCharge shippingCharge = new ShippingCharge();
        shippingCharge.setShippingName("shipping");
        shippingCharge.setShippingFeesEan("STANDARD_SHIPPING");
        shippingCharge.setShippingTaxRate(BigDecimal.valueOf(0.2));
        shippingCharge.setGrossUnitPriceAfterDiscounts(BigDecimal.valueOf(15));
        shippingCharge.setBaseGrossUnitPrice(BigDecimal.valueOf(15));
        shippingCharge.setGrossPriceAfterDiscounts(BigDecimal.valueOf(15));
        shippingCharge.setPromotionId("PROMO1");
        shippingCharge.setCampaignId("CAMPAIGN1");
        shippingCharge.setCouponId("COUPON1");
        List<ShippingCharge> shippingCharges = Arrays.asList(shippingCharge);


        // Act
        List<OrderCharge> result = orderChargeMapper.mapToOrderCharges( shippingCharges,false);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        OrderCharge orderCharge = result.get(0);
        assertEquals("shipping", orderCharge.getName());
        assertEquals(EntryType.SHIPMENT_FEE, orderCharge.getType());
        assertEquals("STANDARD_SHIPPING", orderCharge.getEan());
        assertEquals(0, BigDecimal.valueOf(0.2).compareTo(orderCharge.getTaxRate()));
        assertEquals(1, orderCharge.getOriginalQty());
        assertEquals(1, orderCharge.getOpenQty());
        assertFalse(orderCharge.getCancelled());
        assertFalse(orderCharge.getRefunded());
        assertEquals("service-fees", orderCharge.getVatClassId());
        assertEquals("PROMO1", orderCharge.getPromotionId());
        assertEquals("CAMPAIGN1", orderCharge.getCampaignId());
        assertEquals("COUPON1", orderCharge.getCouponId());
        assertNotNull(orderCharge.getChargeTotal());
    }

    @Test
    void testMapToOrderCharges_Direct() {
        // Arrange
        ShippingCharge shippingCharge = new ShippingCharge();
        shippingCharge.setShippingName("shipping");
        shippingCharge.setShippingFeesEan("STANDARD_SHIPPING");
        shippingCharge.setShippingTaxRate(BigDecimal.valueOf(0.2));
        shippingCharge.setGrossUnitPriceAfterDiscounts(BigDecimal.valueOf(15));
        shippingCharge.setBaseGrossUnitPrice(BigDecimal.valueOf(15));
        shippingCharge.setGrossPriceAfterDiscounts(BigDecimal.valueOf(15));
        shippingCharge.setPromotionId("PROMO1");
        shippingCharge.setCampaignId("CAMPAIGN1");
        shippingCharge.setCouponId("COUPON1");
        List<ShippingCharge> shippingCharges = Arrays.asList(shippingCharge);


        // Act
        List<OrderCharge> result = orderChargeMapper.mapToOrderCharges( shippingCharges,true);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        OrderCharge orderCharge = result.get(0);
        assertEquals("shipping", orderCharge.getName());
        assertEquals(EntryType.SHIPMENT_FEE, orderCharge.getType());
        assertEquals("STANDARD_SHIPPING", orderCharge.getEan());
        assertEquals(0, BigDecimal.valueOf(0.2).compareTo(orderCharge.getTaxRate()));
        assertEquals(1, orderCharge.getOriginalQty());
        assertEquals(1, orderCharge.getOpenQty());
        assertFalse(orderCharge.getCancelled());
        assertFalse(orderCharge.getRefunded());
        assertNull(orderCharge.getVatClassId());
        assertEquals("PROMO1", orderCharge.getPromotionId());
        assertEquals("CAMPAIGN1", orderCharge.getCampaignId());
        assertEquals("COUPON1", orderCharge.getCouponId());
        assertNotNull(orderCharge.getChargeTotal());


    }
    @Test
    void testMapToOrderCharges_empty() {
        // Act
        List<OrderCharge> result = orderChargeMapper.mapToOrderCharges( Collections.emptyList(),false);
        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    void testMapToOrderCharges_null() {
        // Act
         OrderCharge result = orderChargeMapper.mapToOrderCharge( null,true);
        // Assert
        assertNull(result);
    }
}
