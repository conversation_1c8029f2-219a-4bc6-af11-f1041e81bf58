package com.bestseller.payment.integration.controller;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.payment.adapter.api.dto.PaymentValidationResponse;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.integration.container.BaseTestContainer;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.testcontainers.shaded.org.awaitility.Awaitility;

import java.io.IOException;
import java.util.function.Consumer;

import static com.bestseller.payment.utils.FileUtils.loadSampleKafkaMessage;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
public class PaymentControllerIntegrationTest extends BaseTestContainer {
    private static final Consumer<HttpHeaders> HTTP_HEADERS_CONSUMER =
        httpHeaders -> httpHeaders.setBasicAuth("admin", "admin");

    @Value("${spring.cloud.stream.bindings.validOrderPlacedConsumer-in-0.destination}")
    private String validOrderPlacedTopic;

    @Autowired
    private KafkaProducer<String, Object> kafkaProducer;

    @Autowired
    private OrderRepository orderRepository;

    @BeforeEach
    void reset() {
        orderRepository.deleteAll();
    }

    @Test
    public void test_orderNotFound() {
        var orderId = "invalidOrderId";

        getHttpClient().get()
            .uri("/api/v1/payments/{orderId}", orderId)
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .expectStatus()
            .isNotFound();

    }

    @Test
    public void test_unathorized() {
        var orderId = "validOrderId";

        getHttpClient().get()
            .uri("/api/v1/payments/{orderId}", orderId)
            .exchange()
            .expectStatus()
            .isUnauthorized();
    }

    @Test
    public void test_validateCancellation() throws IOException {
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_klarna.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        var response = getHttpClient().get()
            .uri("/api/v1/payments/{orderId}/validate-cancellation", validOrderPlaced.getOrderId())
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .expectStatus()
            .is2xxSuccessful()
            .returnResult(PaymentValidationResponse.class)
            .getResponseBody()
            .blockFirst();

        assertThat(response).isNotNull();
        assertTrue(response.isSuccess());
    }
}
