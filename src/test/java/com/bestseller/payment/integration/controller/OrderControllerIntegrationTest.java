package com.bestseller.payment.integration.controller;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderFinalized;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequest.PaymentRefundRequest;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementRequest.PaymentSettlementRequest;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementSucceeded.PaymentSettlementSucceeded;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundStatusUpdated.RefundStatusUpdated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.interfacecontractannotator.model.paymentstatusupdated.AuthorizedPayload;
import com.bestseller.interfacecontractannotator.model.paymentstatusupdated.OrderDetails;
import com.bestseller.interfacecontractannotator.model.paymentstatusupdated.OrderLine;
import com.bestseller.interfacecontractannotator.model.paymentstatusupdated.Payment;
import com.bestseller.payment.adapter.api.dto.OrderChargeRefundDto;
import com.bestseller.payment.adapter.api.dto.OrderChargesRefundRequestDto;
import com.bestseller.payment.adapter.api.dto.OrderDto;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.adapter.repository.RefundRepository;
import com.bestseller.payment.core.domain.enumeration.ChargedRefundReason;
import com.bestseller.payment.core.domain.enumeration.PaymentMethod;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.integration.container.BaseTestContainer;
import com.bestseller.payment.messaging.consumer.PaymentRefundRequestTestConsumer;
import com.bestseller.payment.messaging.consumer.PaymentSettlementRequestTestConsumer;
import com.bestseller.payment.messaging.consumer.PaymentStatusUpdatedTestConsumer;
import com.bestseller.payment.messaging.consumer.RefundStatusUpdatedTestConsumer;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.testcontainers.shaded.org.awaitility.Awaitility;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import java.util.function.Consumer;

import static com.bestseller.payment.core.domain.enumeration.ProcessorId.ADYEN;
import static com.bestseller.payment.utils.FileUtils.loadSampleKafkaMessage;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
class OrderControllerIntegrationTest extends BaseTestContainer {
    private static final Consumer<HttpHeaders> HTTP_HEADERS_CONSUMER =
        httpHeaders -> {
            httpHeaders.setBasicAuth("admin", "admin");
            httpHeaders.add("Content-Type", "application/json");
        };

    private static final String KLARNA_PAYMENTS = "KLARNA_PAYMENTS";

    @Value("${spring.cloud.stream.bindings.validOrderPlacedConsumer-in-0.destination}")
    private String validOrderPlacedTopic;

    @Value("${spring.cloud.stream.bindings.orderFinalizedConsumer-in-0.destination}")
    private String orderFinalizedTopic;

    @Autowired
    private KafkaProducer<String, Object> kafkaProducer;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private RefundRepository refundRepository;

    @Value("${spring.cloud.stream.bindings.paymentSettlementSucceededConsumer-in-0.destination}")
    private String paymentSettlementSucceededTopic;

    @Autowired
    private PaymentSettlementRequestTestConsumer paymentSettlementRequestTestConsumer;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private PaymentStatusUpdatedTestConsumer paymentStatusUpdatedTestConsumer;

    @Autowired
    private PaymentRefundRequestTestConsumer paymentRefundRequestTestConsumer;

    @Autowired
    private RefundStatusUpdatedTestConsumer refundStatusUpdatedTestConsumer;

    @BeforeEach
    void reset() {
        orderRepository.deleteAll();
        refundRepository.deleteAll();

        paymentSettlementRequestTestConsumer.clearMessages();
        paymentStatusUpdatedTestConsumer.clearMessages();
        paymentRefundRequestTestConsumer.clearMessages();
        refundStatusUpdatedTestConsumer.clearMessages();
    }

    @Test
    void test_getOrderCharge() throws IOException {
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_klarna.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        var order = orderRepository.findById(validOrderPlaced.getOrderId()).get();

        var orderDto = getHttpClient().get()
            .uri("/api/v1/orders/{orderId}", validOrderPlaced.getOrderId())
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .expectStatus()
            .is2xxSuccessful()
            .returnResult(OrderDto.class)
            .getResponseBody()
            .blockFirst();


        assertThat(orderDto).isNotNull();
        assertThat(orderDto.getOrderId()).isEqualTo(order.getOrderId());
    }

    @Test
    void test_orderNotFound() {
        var orderId = "invalidOrderId";

        getHttpClient().get()
            .uri("/api/v1/orders/{orderId}", orderId)
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .expectStatus()
            .isNotFound();

    }

    @Test
    void test_unathorized() {
        var orderId = "validOrderId";

        getHttpClient().get()
            .uri("/api/v1/orders/{orderId}/order-charges", orderId)
            .exchange()
            .expectStatus()
            .isUnauthorized();
    }

    @Test
    void test_refundOrderCharges_notFinalizedYet() throws IOException {
        final ValidOrderPlaced validOrderPlaced =
            loadSampleKafkaMessage("/messages/validOrderPlaced_adyenBank.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        var order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
        var orderChargeId = order.getOrderCharges().getFirst().getOrderEntryId();
        var reason = ChargedRefundReason.EXPRESS_DELIVERY_NOT_ON_TIME.getDescription();
        var payload = OrderChargesRefundRequestDto.builder()
            .csrInitials("csrInitials")
            .orderChargesRefundRequestList(
                List.of(OrderChargeRefundDto.builder()
                    .orderChargeId(orderChargeId)
                    .reason(reason)
                    .build())
            )
            .build();

        getHttpClient().post()
            .uri("/api/v1/orders/{orderId}/refund-order-charges", validOrderPlaced.getOrderId())
            .headers(HTTP_HEADERS_CONSUMER)
            .bodyValue(objectMapper.writeValueAsString(payload))
            .exchange()
            .expectStatus()
            .isBadRequest();
    }

    @Test
    void test_refundOrderCharges_notSettledYet() throws IOException {
        final ValidOrderPlaced validOrderPlaced =
            loadSampleKafkaMessage("/messages/validOrderPlaced_klarna.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json", OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));
        Awaitility.await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
            final PaymentSettlementRequest paymentSettlementRequest = messages.getFirst();
            assertEquals(orderFinalized.getOrderId(), paymentSettlementRequest.getOrderId());
            assertEquals(KLARNA_PAYMENTS, paymentSettlementRequest.getProvider());
        });

        var order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
        var orderChargeId = order.getOrderCharges().getFirst().getOrderEntryId();
        var reason = ChargedRefundReason.EXPRESS_DELIVERY_NOT_ON_TIME.getDescription();
        var payload = OrderChargesRefundRequestDto.builder()
            .csrInitials("csrInitials")
            .orderChargesRefundRequestList(
                List.of(OrderChargeRefundDto.builder()
                    .orderChargeId(orderChargeId)
                    .reason(reason)
                    .build())
            )
            .build();

        getHttpClient().post()
            .uri("/api/v1/orders/{orderId}/refund-order-charges", validOrderPlaced.getOrderId())
            .headers(HTTP_HEADERS_CONSUMER)
            .bodyValue(objectMapper.writeValueAsString(payload))
            .exchange()
            .expectStatus()
            .isBadRequest();
    }

    @Test
    void test_refundOrderCharges_success() throws IOException {
        final ValidOrderPlaced validOrderPlaced =
            loadSampleKafkaMessage("/messages/validOrderPlaced_klarna.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json", OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));
        Awaitility.await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
            final PaymentSettlementRequest paymentSettlementRequest = messages.getFirst();
            assertEquals(orderFinalized.getOrderId(), paymentSettlementRequest.getOrderId());
            assertEquals(KLARNA_PAYMENTS, paymentSettlementRequest.getProvider());
        });

        final PaymentSettlementSucceeded paymentSettlementSucceeded = loadSampleKafkaMessage(
            "/messages/paymentSettlementSucceeded_klarna.json", PaymentSettlementSucceeded.class);
        kafkaProducer.send(new ProducerRecord<>(paymentSettlementSucceededTopic, paymentSettlementSucceeded));

        Awaitility.await().until(() -> orderRepository
            .findById(validOrderPlaced.getOrderId())
            .orElseThrow()
            .getPaymentStatus().equals(PaymentState.SETTLED));

        var order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
        var orderChargeId = order.getOrderCharges().getFirst().getOrderEntryId();
        var reason = ChargedRefundReason.EXPRESS_DELIVERY_NOT_ON_TIME.getDescription();
        var payload = OrderChargesRefundRequestDto.builder()
            .csrInitials("csrInitials")
            .orderChargesRefundRequestList(
                List.of(OrderChargeRefundDto.builder()
                    .orderChargeId(orderChargeId)
                    .reason(reason)
                    .build())
            )
            .build();

        getHttpClient().post()
            .uri("/api/v1/orders/{orderId}/refund-order-charges", validOrderPlaced.getOrderId())
            .headers(HTTP_HEADERS_CONSUMER)
            .bodyValue(objectMapper.writeValueAsString(payload))
            .exchange()
            .expectStatus()
            .is2xxSuccessful();
    }

    @Test
    void test_refundOrderCharges_duplicateRequest() throws IOException {
        final ValidOrderPlaced validOrderPlaced =
            loadSampleKafkaMessage("/messages/validOrderPlaced_klarna.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json", OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));
        Awaitility.await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
            final PaymentSettlementRequest paymentSettlementRequest = messages.getFirst();
            assertEquals(orderFinalized.getOrderId(), paymentSettlementRequest.getOrderId());
            assertEquals(KLARNA_PAYMENTS, paymentSettlementRequest.getProvider());
        });

        final PaymentSettlementSucceeded paymentSettlementSucceeded = loadSampleKafkaMessage(
            "/messages/paymentSettlementSucceeded_klarna.json", PaymentSettlementSucceeded.class);
        kafkaProducer.send(new ProducerRecord<>(paymentSettlementSucceededTopic, paymentSettlementSucceeded));

        Awaitility.await().until(() -> orderRepository
            .findById(validOrderPlaced.getOrderId())
            .orElseThrow()
            .getPaymentStatus().equals(PaymentState.SETTLED));

        var order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
        var orderChargeId = order.getOrderCharges().getFirst().getOrderEntryId();
        var reason = ChargedRefundReason.EXPRESS_DELIVERY_NOT_ON_TIME.getDescription();
        var payload = OrderChargesRefundRequestDto.builder()
            .csrInitials("csrInitials")
            .orderChargesRefundRequestList(
                List.of(OrderChargeRefundDto.builder()
                    .orderChargeId(orderChargeId)
                    .reason(reason)
                    .build())
            )
            .build();

        getHttpClient().post()
            .uri("/api/v1/orders/{orderId}/refund-order-charges", validOrderPlaced.getOrderId())
            .headers(HTTP_HEADERS_CONSUMER)
            .bodyValue(objectMapper.writeValueAsString(payload))
            .exchange()
            .expectStatus()
            .is2xxSuccessful();

        getHttpClient().post()
            .uri("/api/v1/orders/{orderId}/refund-order-charges", validOrderPlaced.getOrderId())
            .headers(HTTP_HEADERS_CONSUMER)
            .bodyValue(objectMapper.writeValueAsString(payload))
            .exchange()
            .expectStatus()
            .isBadRequest();
    }

    @Test
    void getAllPaymentsByOrderId_directPayment_returnsOrderStatusUpdatedMessage() throws IOException {
        // arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_klarna.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        var order = orderRepository.findById(validOrderPlaced.getOrderId()).get();

        // act
        var paymentStatusUpdated = getHttpClient().get()
            .uri("/api/v1/orders/{orderId}/payment-status-updated-message", validOrderPlaced.getOrderId())
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .expectStatus()
            .is2xxSuccessful()
            .returnResult(PaymentStatusUpdated.class)
            .getResponseBody()
            .collectList()
            .block();

        // assert
        var expectedPaymentStatusUpdated = new PaymentStatusUpdated()
            .withOrderId(order.getOrderId())
            .withPaymentState(PaymentState.AUTHORISED.toPaymentStatusUpdatedPaymentState())
            .withPayload(AuthorizedPayload.builder()
                .payments(List.of(Payment.builder()
                    .amount(new BigDecimal(order.getPayments().getFirst().getAuthorisedAmount()))
                    .subMethod(PaymentMethod.KLARNA_INVOICE.name())
                    .name(order.getPayments().getFirst().getType().name())
                    .build()))
                .orderDetails(OrderDetails.builder()
                    .orderValue(new BigDecimal(order.getPayments().getFirst().getAuthorisedAmount()))
                    .shippingFeesTaxPercentage(order.getOrderCharges().getFirst().getTaxRate())
                    .shippingFees(order.getOrderCharges().getFirst().getChargeTotal().getGrossRetailUnitPrice())
                    .shippingFeesCancelled(false)
                    .build())
                .orderLines(List.of(OrderLine.builder()
                    .ean(order.getOrderLines().getFirst().getEan())
                    .retailPrice(order.getOrderLines().getFirst().getStandardRetailPrice())
                    .taxPercentage(order.getOrderLines().getFirst().getTaxRate())
                    .discountValue(order.getOrderLines().getFirst().getOrderLinePaidAmount().getUnitDiscount())
                    .build(),
                    OrderLine.builder()
                        .ean(order.getOrderLines().get(1).getEan())
                        .retailPrice(order.getOrderLines().get(1).getStandardRetailPrice())
                        .taxPercentage(order.getOrderLines().get(1).getTaxRate())
                        .discountValue(order.getOrderLines().get(1).getOrderLinePaidAmount().getUnitDiscount())
                        .build(),
                    OrderLine.builder()
                        .ean(order.getOrderLines().get(2).getEan())
                        .retailPrice(order.getOrderLines().get(2).getStandardRetailPrice())
                        .taxPercentage(order.getOrderLines().get(2).getTaxRate())
                        .discountValue(order.getOrderLines().get(2).getOrderLinePaidAmount().getUnitDiscount())
                        .build()
                    ))
                .build());

        assertThat(paymentStatusUpdated)
            .usingRecursiveComparison()
            .ignoringFields("timestamp")
            .isEqualTo(List.of(expectedPaymentStatusUpdated));
    }

    @Test
    public void when_OrderFinalized_adyenCardInReview_manuallyCancellation() throws IOException {
        // arrange
        var orderId = UUID.randomUUID().toString();
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenCardInReview.json", ValidOrderPlaced.class);
        validOrderPlaced.setOrderId(orderId);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(orderId));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_cancelled.json", OrderFinalized.class);
        orderFinalized.setOrderId(orderId);

        // act
        callCancelOrder(orderId);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        // assert
        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        await().untilAsserted(() -> {
            final List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();
            assertEquals(1, messages
                .stream()
                .filter(message -> message.getOrderId().equals(orderId))
                .filter(message -> message.getPaymentState().name().equals(PaymentState.CANCELLED.name()))
                .count()
            );
        });

        await().untilAsserted(() -> {
            final List<RefundStatusUpdated> messages = refundStatusUpdatedTestConsumer.getMessages();
            assertEquals(0, messages
                .stream()
                .filter(message -> message.getOrderId().equals(orderId))
                .count()
            );
        });

        assertManuallyCancelledOrder(orderId, 0);
    }

    @Test
    public void when_OrderFinalized_adyenBankInReview_manualCancellation() throws IOException {
        // arrange
        var orderId = UUID.randomUUID().toString();
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenBankInReview.json", ValidOrderPlaced.class);
        validOrderPlaced.setOrderId(orderId);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(orderId));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_cancelled.json", OrderFinalized.class);
        orderFinalized.setOrderId(orderId);

        // act
        callCancelOrder(orderId);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        // assert
        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(0, messages.size());
        });

        await().untilAsserted(() -> {
            final List<RefundStatusUpdated> messages = refundStatusUpdatedTestConsumer.getMessages();
            assertEquals(2, messages
                .stream()
                .filter(message -> message.getOrderId().equals(orderId))
                .count()
            );
        });

        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());

            assertEquals(ADYEN.name(), messages.get(0).getProvider());
        });

        assertManuallyCancelledOrder(orderId, 1);

    }

    private void callCancelOrder(String orderId) {
        getHttpClient().put()
            .uri("/api/v1/orders/{orderId}/cancel", orderId)
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .expectStatus()
            .is2xxSuccessful();
    }

    private void assertManuallyCancelledOrder(String orderId, int numberOfRefunds) {
        var order = orderRepository.findById(orderId).get();
        boolean isAllCancelled = order.getOrderLines().stream()
            .allMatch(orderLine -> orderLine.getOpenQty() == 0);
        assertTrue(isAllCancelled, "All order lines should be cancelled (openQty == 0)");

        assertThat(order.getPrevPaymentStatus()).isEqualTo(PaymentState.REVIEW);
        assertThat(order.getPaymentStatus()).isEqualTo(PaymentState.CANCELLED);
        assertThat(order.getRefunds()).hasSize(numberOfRefunds);
        BigDecimal authorisedAmount = new BigDecimal(order.getPayments().getFirst().getAuthorisedAmount());
        if (numberOfRefunds > 0) {
            assertEquals(authorisedAmount,
                order.getRefunds().getFirst().getRefundTotal().getGrossDiscountedTotal());
        }
    }

}
