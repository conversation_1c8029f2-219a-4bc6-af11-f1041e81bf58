package com.bestseller.payment.integration.controller;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.RefundCreationRequested;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.adapter.repository.RefundRepository;
import com.bestseller.payment.integration.container.BaseTestContainer;
import com.logistics.statetransition.RefundState;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.testcontainers.shaded.org.awaitility.Awaitility;

import java.io.IOException;
import java.util.function.Consumer;

import static com.bestseller.payment.utils.FileUtils.loadSampleKafkaMessage;
import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
public class RefundControllerIntegrationTest extends BaseTestContainer {
    private static final Consumer<HttpHeaders> HTTP_HEADERS_CONSUMER =
        httpHeaders -> {
            httpHeaders.setBasicAuth("admin", "admin");
            httpHeaders.add("Content-Type", "application/json");
        };

    @Value("${spring.cloud.stream.bindings.validOrderPlacedConsumer-in-0.destination}")
    private String validOrderPlacedTopic;

    @Value("${spring.cloud.stream.bindings.refundCreationRequestedConsumer-in-0.destination}")
    private String refundCreationRequestedTopic;

    @Autowired
    private KafkaProducer<String, Object> kafkaProducer;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private RefundRepository refundRepository;

    @BeforeEach
    void reset() {
        refundRepository.deleteAll();
        orderRepository.deleteAll();
    }

    @Test
    void test_unathorized() {
        var orderId = "validOrderId";

        getHttpClient().get()
            .uri("/api/v1/refunds/{orderId}", orderId)
            .exchange()
            .expectStatus()
            .isUnauthorized();
    }

    @Test
    void test_markRefundAsRefundSuccess_success() throws IOException {
        String orderId = createRefundAndReturnOrderId();
        var order = orderRepository.findById(orderId).orElseThrow();
        var refund = order.getRefunds().getFirst();
        refund.setRefundState(RefundState.REFUND_FAILED);

        refundRepository.save(refund);
        getHttpClient().put()
            .uri(uriBuilder -> uriBuilder.path("/api/v1/refunds/{refundId}/refund-success")
                .build(refund.getId()))
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .expectStatus()
            .is2xxSuccessful();
    }

    @Test
    void test_markRefundAsRefundSuccess_failed() throws IOException {
        String orderId = createRefundAndReturnOrderId();
        var order = orderRepository.findById(orderId).orElseThrow();
        var refund = order.getRefunds().getFirst();
        refund.setRefundState(RefundState.REFUND_CANCELLED);

        refundRepository.save(refund);

        getHttpClient().put()
            .uri(uriBuilder -> uriBuilder.path("/api/v1/refunds/{refundId}/refund-success")
                .build(refund.getId()))
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .expectStatus()
            .isBadRequest();
    }

    @Test
    void test_markRefundAsRefundCancelled_success() throws IOException {
        String orderId = createRefundAndReturnOrderId();
        var order = orderRepository.findById(orderId).orElseThrow();
        var refund = order.getRefunds().getFirst();
        refund.setRefundState(RefundState.REFUND_FAILED);

        refundRepository.save(refund);
        getHttpClient().put()
            .uri(uriBuilder -> uriBuilder.path("/api/v1/refunds/{refundId}/refund-cancel")
                .build(refund.getId()))
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .expectStatus()
            .is2xxSuccessful();
    }

    @Test
    void test_markRefundAsRefundCancelled_failed() throws IOException {
        String orderId = createRefundAndReturnOrderId();
        var order = orderRepository.findById(orderId).orElseThrow();
        var refund = order.getRefunds().getFirst();
        refund.setRefundState(RefundState.REFUND_REQUESTING);

        refundRepository.save(refund);

        getHttpClient().put()
            .uri(uriBuilder -> uriBuilder.path("/api/v1/refunds/{refundId}/refund-cancel")
                .build(refund.getId()))
            .headers(HTTP_HEADERS_CONSUMER)
            .exchange()
            .expectStatus()
            .isBadRequest();
    }

    private @NotNull String createRefundAndReturnOrderId() throws IOException {
        final ValidOrderPlaced validOrderPlaced =
            loadSampleKafkaMessage("/messages/validOrderPlaced_adyenBank.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_chargeReturnFee_refundShippingFee.json",
            RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        await().untilAsserted(() -> {
            var order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertThat(order.getRefunds()).isNotEmpty();
        });

        return validOrderPlaced.getOrderId();
    }
}
