package com.bestseller.payment.integration.orderfinalized;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderFinalized;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequest.PaymentRefundRequest;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementRequest.PaymentSettlementRequest;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.RefundCreationRequested;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundStatusUpdated.RefundStatusUpdated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.adapter.repository.RefundRepository;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.integration.container.BaseTestContainer;
import com.bestseller.payment.messaging.consumer.PaymentRefundRequestTestConsumer;
import com.bestseller.payment.messaging.consumer.PaymentSettlementRequestTestConsumer;
import com.bestseller.payment.messaging.consumer.PaymentStatusUpdatedTestConsumer;
import com.bestseller.payment.messaging.consumer.RefundStatusUpdatedTestConsumer;
import com.logistics.statetransition.RefundState;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.testcontainers.shaded.org.awaitility.Awaitility;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

import static com.bestseller.payment.utils.FileUtils.loadSampleKafkaMessage;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
class OrderFinalizedIntegrationTest extends BaseTestContainer {

    private static final String KLARNA_PAYMENTS = "KLARNA_PAYMENTS";
    private static final String ADYEN = "ADYEN";
    private static final String GIFT_CARD = "OPTICARD_PHASE_ONE";
    private static final BigDecimal EAN_1_PRICE = BigDecimal.valueOf(4.99);
    private static final BigDecimal EAN_2_PRICE = BigDecimal.valueOf(4.99);
    private static final BigDecimal EAN_3_PRICE = BigDecimal.valueOf(24.99);
    private static final BigDecimal BANK_PAYMENT = BigDecimal.valueOf(28.90);
    private static final BigDecimal BD_100 = BigDecimal.valueOf(100);
    private static final BigDecimal RETURN_FEE = BigDecimal.valueOf(-1.99);

    @Autowired
    private PaymentSettlementRequestTestConsumer paymentSettlementRequestTestConsumer;

    @Autowired
    private PaymentStatusUpdatedTestConsumer paymentStatusUpdatedTestConsumer;

    @Autowired
    private PaymentRefundRequestTestConsumer paymentRefundRequestTestConsumer;

    @Autowired
    private RefundStatusUpdatedTestConsumer refundStatusUpdatedTestConsumer;

    @Autowired
    private KafkaProducer<String, Object> kafkaProducer;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private RefundRepository refundRepository;

    @Value("${spring.cloud.stream.bindings.validOrderPlacedConsumer-in-0.destination}")
    private String validOrderPlacedTopic;

    @Value("${spring.cloud.stream.bindings.orderFinalizedConsumer-in-0.destination}")
    private String orderFinalizedTopic;

    @Value("${spring.cloud.stream.bindings.refundCreationRequestedConsumer-in-0.destination}")
    private String refundCreationRequestedTopic;

    @Test
    public void when_OrderFinalized_klarna_allDispatched() throws IOException {
        // arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_klarna.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json", OrderFinalized.class);

        // act
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        // assert
        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
            final PaymentSettlementRequest paymentSettlementRequest = messages.get(0);
            assertEquals(orderFinalized.getOrderId(), paymentSettlementRequest.getOrderId());
            assertEquals(KLARNA_PAYMENTS, paymentSettlementRequest.getProvider());
        });

        await().untilAsserted(() -> {
            final List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();
            assertEquals(1, messages
                .stream()
                .filter(message -> message.getOrderId().equals(validOrderPlaced.getOrderId()))
                .filter(message -> message.getPaymentState().name().equals(PaymentState.SETTLEMENT_REQUESTING.name()))
                .count()
            );
        });
    }

    @BeforeEach
    public void cleanup() throws InterruptedException {
        Thread.sleep(500);
        refundRepository.deleteAll();
        orderRepository.deleteAll();
        paymentSettlementRequestTestConsumer.clearMessages();
        paymentStatusUpdatedTestConsumer.clearMessages();
        paymentRefundRequestTestConsumer.clearMessages();
        refundStatusUpdatedTestConsumer.clearMessages();
    }

    @Test
    public void when_OrderFinalized_adyenBank_allDispatched() throws IOException {
        // arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenBank.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json", OrderFinalized.class);

        // act
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        // assert
        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(0, messages.size());
        });
    }

    @Test
    public void when_OrderFinalized_giftCard_allDispatched() throws IOException {
        // arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_giftCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json", OrderFinalized.class);

        // act
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        // assert
        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(0, messages.size());
        });
    }

    @Test
    public void when_OrderFinalized_adyenCard_allDispatched() throws IOException {
        // arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json", OrderFinalized.class);

        // act
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        // assert
        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
            final PaymentSettlementRequest paymentSettlementRequest = messages.get(0);
            assertEquals(orderFinalized.getOrderId(), paymentSettlementRequest.getOrderId());
            assertEquals(ADYEN, paymentSettlementRequest.getProvider());
        });

        await().untilAsserted(() -> {
            final List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();
            assertEquals(1, messages
                .stream()
                .filter(message -> message.getOrderId().equals(validOrderPlaced.getOrderId()))
                .filter(message -> message.getPaymentState().name().equals(PaymentState.SETTLEMENT_REQUESTING.name()))
                .count()
            );
        });
    }

    @Test
    public void when_OrderFinalized_receiveDuplicateMessages() throws IOException {
        // arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json", OrderFinalized.class);

        // act
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().until(() -> paymentSettlementRequestTestConsumer.getMessages().size() == 1);

        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        // assert
        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        await().untilAsserted(() -> {
            final List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();
            assertEquals(1, messages
                .stream()
                .filter(message -> message.getOrderId().equals(validOrderPlaced.getOrderId()))
                .filter(message -> message.getPaymentState().name().equals(PaymentState.SETTLEMENT_REQUESTING.name()))
                .count()
            );
        });
    }

    @Test
    public void when_OrderFinalized_klarna_allCancelled() throws IOException {
        // arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_klarna.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_cancelled.json", OrderFinalized.class);

        // act
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        // assert
        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        await().untilAsserted(() -> {
            final List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();
            assertEquals(1, messages
                .stream()
                .filter(message -> message.getOrderId().equals(validOrderPlaced.getOrderId()))
                .filter(message -> message.getPaymentState().name().equals(PaymentState.CANCELLED.name()))
                .count()
            );
        });

        await().untilAsserted(() -> {
            final List<RefundStatusUpdated> messages = refundStatusUpdatedTestConsumer.getMessages();
            assertEquals(0, messages
                .stream()
                .filter(message -> message.getOrderId().equals(validOrderPlaced.getOrderId()))
                .count()
            );
        });
    }

    @Test
    public void when_OrderFinalized_adyenBank_allCancelled() throws IOException {
        // arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenBank.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_cancelled.json", OrderFinalized.class);

        // act
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        // assert
        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(0, messages.size());
        });

        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());

            assertEquals(ADYEN, messages.get(0).getProvider());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());
    }

    @Test
    public void when_OrderFinalized_adyenBank_someCancelled() throws IOException {
        // arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenBank.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_one_cancelled_with_quantity_one.json", OrderFinalized.class);

        int refundAmount = EAN_3_PRICE
            .multiply(BD_100)
            .intValue();

        // act
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        // assert
        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(0, messages.size());
        });

        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());

            assertEquals(ADYEN, messages.getFirst().getProvider());
            assertEquals(refundAmount, messages.getFirst().getTotalAmount());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());
    }

    @Test
    public void when_OrderFinalized_adyenBank_someReturned() throws IOException {
        // arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenBank.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_one_returned.json", OrderFinalized.class);

        // act
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        // assert
        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(0, messages.size());
        });

        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            assertEquals(0, messages.size());
        });

        await().untilAsserted(() -> {
            final List<RefundStatusUpdated> messages = refundStatusUpdatedTestConsumer.getMessages();
            assertEquals(0, messages
                .stream()
                .filter(message -> message.getOrderId().equals(validOrderPlaced.getOrderId()))
                .count()
            );
        });
    }

    @Test
    public void when_OrderFinalized_mixedPayments_adyenCard_partiallyCancelled_lessThanBankPaymentAuthorisedAmount() throws IOException {
        // arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenCardWithGiftCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_one_cancelled_from_quantity_more_than_one.json", OrderFinalized.class);

        // act
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        // assert
        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages
                .stream()
                .filter(message -> message.getProvider().equals(ADYEN))
                .count());
        });

        await().untilAsserted(() -> {
            final List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();
            assertEquals(1, messages
                .stream()
                .filter(message -> message.getOrderId().equals(validOrderPlaced.getOrderId()))
                .filter(message -> message.getPaymentState().name().equals(PaymentState.SETTLEMENT_REQUESTING.name()))
                .count()
            );
        });

        await().untilAsserted(() -> {
            final List<RefundStatusUpdated> messages = refundStatusUpdatedTestConsumer.getMessages();
            assertEquals(0, messages
                .stream()
                .filter(message -> message.getOrderId().equals(validOrderPlaced.getOrderId()))
                .count()
            );
        });
    }

    @Test
    public void when_OrderFinalized_mixedPayments_adyenCard_allCancelled_moreThanBankPaymentAuthorisedAmount() throws IOException {
        // arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenCardWithGiftCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_cancelled.json", OrderFinalized.class);

        // act
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        // assert
        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());

            assertEquals(GIFT_CARD, messages.get(0).getProvider());
        });

        assertRefundStatusUpdatedMessagesNotProduced();

        await().untilAsserted(() -> {
            final List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();
            assertEquals(1, messages
                .stream()
                .filter(message -> message.getOrderId().equals(validOrderPlaced.getOrderId()))
                .filter(message -> message.getPaymentState().name().equals(PaymentState.CANCELLED.name()))
                .count()
            );
        });
    }

    @Test
    public void when_OrderFinalized_mixedPayments_adyenCard_partiallyCancelled_moreThanBankPaymentAuthorisedAmount() throws IOException {
        // arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenCardWithGiftCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_partially_cancelled_moreThanBankTransaction.json", OrderFinalized.class);

        int totalToRefund = Math.max(0,
            EAN_1_PRICE
                .add(EAN_1_PRICE)
                .add(EAN_3_PRICE)
                .subtract(BANK_PAYMENT)
                .multiply(BD_100)
                .intValue());

        // act
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        // assert
        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();

            assertEquals(1,
                messages.stream()
                    .filter(message -> message.getProvider().equals(ADYEN))
                    .count());
            assertEquals(0, messages.get(0).getTotalAmount());
        });

        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());

            assertEquals(GIFT_CARD, messages.get(0).getProvider());
            assertEquals(totalToRefund, messages.get(0).getTotalAmount());
        });

        await().untilAsserted(() -> {
            final List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();
            assertEquals(1, messages
                .stream()
                .filter(message -> message.getOrderId().equals(validOrderPlaced.getOrderId()))
                .filter(message -> message.getPaymentState().name().equals(PaymentState.CANCELLED.name()))
                .count()
            );
        });

        assertRefundStatusUpdatedMessagesNotProduced();
    }

    @Test
    public void when_OrderFinalized_mixedPayments_adyenBank_return_partiallyCancelled_lessThanBankPaymentAmount() throws IOException {
        // arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenBankWithGiftCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        int returnRefundTotal = EAN_1_PRICE
            .add(RETURN_FEE)
            .multiply(BD_100)
            .intValue();

        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage("/messages/refundCreationRequested_lessThanBankTransaction.json", RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());

            assertEquals(ADYEN, messages.get(0).getProvider());
            assertEquals(returnRefundTotal, messages.get(0).getTotalAmount());
        });

        int cancellationRefundTotal = EAN_2_PRICE
            .add(EAN_2_PRICE)
            .multiply(BD_100)
            .intValue();

        // act
        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_ean1Returned_ean2Cancelled.json", OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        // assert
        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(0, messages.size());
        });

        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            assertEquals(1, messages
                .stream()
                .filter(message -> message.getProvider().equals(ADYEN))
                .filter(message -> message.getTotalAmount() == cancellationRefundTotal)
                .count());

            assertEquals(0, messages
                .stream()
                .filter(message -> message.getProvider().equals(GIFT_CARD))
                .count());
        });
    }

    @Test
    public void when_OrderFinalized_mixedPayments_adyenBank_return_partiallyCancelled_moreThanBankPaymentAmount() throws IOException {
        // arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenBankWithGiftCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        int returnRefundTotal = EAN_1_PRICE
            .add(RETURN_FEE)
            .multiply(BD_100)
            .intValue();

        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage("/messages/refundCreationRequested_lessThanBankTransaction.json", RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());

            assertEquals(ADYEN, messages.get(0).getProvider());
            assertEquals(returnRefundTotal, messages.get(0).getTotalAmount());
        });

        BigDecimal remainingBankPayment = BANK_PAYMENT.subtract(EAN_1_PRICE
            .add(RETURN_FEE));
        int cancellationRefundGiftCardTotal = EAN_2_PRICE
            .add(EAN_2_PRICE)
            .add(EAN_3_PRICE)
            .subtract(remainingBankPayment)
            .multiply(BD_100)
            .intValue();


        // act
        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_ean1Returned_ean2Cancelled_ean3Cancelled.json", OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        // assert
        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(0, messages.size());
        });

        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            assertEquals(1, messages
                .stream()
                .filter(message -> message.getProvider().equals(ADYEN))
                .filter(message -> message.getTotalAmount() == remainingBankPayment.multiply(BD_100).intValue())
                .count());

            assertEquals(1, messages
                .stream()
                .filter(message -> message.getProvider().equals(GIFT_CARD))
                .filter(message -> message.getTotalAmount() == cancellationRefundGiftCardTotal)
                .count());
        });

        await().untilAsserted(() -> {
            final List<RefundStatusUpdated> messages = refundStatusUpdatedTestConsumer.getMessages();
            assertEquals(2, messages
                .stream()
                .filter(message -> message.getOrderId().equals(validOrderPlaced.getOrderId()))
                .filter(message -> message.getStatus().name().equals(RefundState.CREATED.name()))
                .count()
            );
            assertEquals(2, messages
                .stream()
                .filter(message -> message.getOrderId().equals(validOrderPlaced.getOrderId()))
                .filter(message -> message.getStatus().name().equals(RefundState.REFUND_REQUESTING.name()))
                .count()
            );
        });
    }

    @Test
    public void when_OrderFinalized_notAllCancelled_vatNumberIsGenerated() throws IOException {
        // arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_klarna.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_one_returned.json", OrderFinalized.class);

        // act
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        // assert
        await().until(() -> orderRepository.findById(validOrderPlaced.getOrderId()).get().getVatOrderNumber() != null);
    }

    @Test
    public void when_OrderFinalized_allCancelled_vatNumberIsNotGenerated() throws IOException {
        // arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_klarna.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_cancelled.json", OrderFinalized.class);

        // act
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        // assert
        await().until(() -> orderRepository.findById(validOrderPlaced.getOrderId()).get().getVatOrderNumber() == null);
    }

    private void assertRefundStatusUpdatedMessagesProduced(String orderId) {
        await().untilAsserted(() -> {
            final List<RefundStatusUpdated> messages = refundStatusUpdatedTestConsumer.getMessages();
            assertEquals(2, messages.size());
            assertEquals(1,
                messages.stream()
                    .filter(message -> message.getOrderId().equals(orderId))
                    .filter(message -> message.getStatus().name().equals(RefundState.CREATED.name()))
                    .count());

            assertEquals(1,
                messages.stream()
                    .filter(message -> message.getOrderId().equals(orderId))
                    .filter(message -> message.getStatus().name().equals(RefundState.REFUND_REQUESTING.name()))
                    .count());
        });
    }

    private void assertRefundStatusUpdatedMessagesNotProduced() {
        await().untilAsserted(() -> {
            final List<RefundStatusUpdated> messages = refundStatusUpdatedTestConsumer.getMessages();
            assertEquals(0, messages.size());
        });
    }
}
