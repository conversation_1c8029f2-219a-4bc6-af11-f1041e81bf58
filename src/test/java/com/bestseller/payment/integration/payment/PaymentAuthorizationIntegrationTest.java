package com.bestseller.payment.integration.payment;


import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentAuthorized.PaymentAuthorized;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRejected.PaymentRejected;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.enumeration.PaymentMethod;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import com.bestseller.payment.core.domain.payment.AdyenPayment;
import com.bestseller.payment.integration.container.BaseTestContainer;
import com.bestseller.payment.messaging.consumer.PaymentStatusUpdatedTestConsumer;
import com.bestseller.payment.utils.ValidOrderPlacedGenerator;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.assertj.core.api.recursive.comparison.RecursiveComparisonConfiguration;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.testcontainers.shaded.org.awaitility.Awaitility;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
class PaymentAuthorizationIntegrationTest extends BaseTestContainer {

    public static final String ORDER_ID = "OL12345";
    public static final RecursiveComparisonConfiguration PAYMENT_STATUS_UPDATED_RECURSIVE_COMPARISON_CONFIGURATION =
        RecursiveComparisonConfiguration.builder()
            .withComparedFields("orderId", "paymentState")
            .build();

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private PaymentStatusUpdatedTestConsumer paymentStatusUpdatedTestConsumer;

    @Autowired
    private KafkaProducer<String, Object> kafkaProducer;

    @Value("${spring.cloud.stream.bindings.validOrderPlacedConsumer-in-0.destination}")
    private String validOrderPlacedTopic;

    @Value("${spring.cloud.stream.bindings.paymentAuthorizedConsumer-in-0.destination}")
    private String paymentAuthorizedTopic;

    @Value("${spring.cloud.stream.bindings.paymentRejectedConsumer-in-0.destination}")
    private String paymentRejectedTopic;


    @BeforeEach
    public void resetDB() {
        orderRepository.deleteAll();
        paymentStatusUpdatedTestConsumer.clearMessages();
    }


    /**
     * For ADYEN-IDEAL
     * <p>
     * When validorderplaced is sent and payment is authorized
     * Then order and payment is processed and saved in the database
     * and paymentStatusUpdated is produced
     */
    @Test
    public void when_ValidOrderPlaced_AdyenIdeal_Authorized() {
        String method = "ADYEN_IDEAL";
        String subMethod = "ideal";
        String state = "AUTH";
        // Arrange & Act
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createBseOrderPlacedMessage_Adyen(method, subMethod, state);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));

        await().until(
            () -> orderRepository.existsById(validOrderPlacedMessage.getOrderId()));

        // Assert
        await().untilAsserted(() -> {
            // Assert PaymentStatusUpdated message
            List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();
            checkPaymentStatusUpdatedIsConsumed(messages, PaymentStatusUpdated.PaymentState.AUTHORISED);

            orderRepository.findById(validOrderPlacedMessage.getOrderId())
                .ifPresentOrElse(order -> {
                    // Assert Order saved in DB
                    assertThat(order.getOrderId()).isEqualTo(ORDER_ID);
                    assertThat(order.getPaymentStatus()).isEqualTo(PaymentState.AUTHORISED);
                    assertThat(order.getPrevPaymentStatus()).isEqualTo(PaymentState.START);

                    // Assert Payment saved in DB
                    assertThat(order.getPayments().size()).isEqualTo(1);
                    assertThat(order.getPayments().get(0) instanceof AdyenPayment);
                    AdyenPayment adyenPayment = (AdyenPayment) order.getPayments().get(0);
                    assertThat(adyenPayment.getPaymentReference()).isEqualTo("**********");
                    assertThat(adyenPayment.getType()).isEqualTo(PaymentType.ADYEN_BANK);
                    assertThat(adyenPayment.getPaymentReference()).isEqualTo("**********");
                    assertThat(adyenPayment.getAuthorisedAmount()).isEqualTo("24.95");
                }, Assertions::fail);

        });
    }

    /**
     * For ADYEN-CARD-mc
     * <p>
     * When validorderplaced is sent and payment is in review
     * Then order and payment is processed and saved in the database
     * And
     * When a payment authorized message is received
     * then PaymentStatusUpdated is produced and order payment status is updated
     */
    @Test
    public void when_ValidOrderPlaced_AdyenCard_Review_ThenPaymentIsAuthorised() {
        String method = "ADYEN_CREDIT_CARD";
        String subMethod = "mc";
        String state = "REVIEW";
        // Arrange
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createBseOrderPlacedMessage_Adyen(method, subMethod, state);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));

        Awaitility.await().until(
            () -> orderRepository.existsById(validOrderPlacedMessage.getOrderId()));
        orderRepository.findById(validOrderPlacedMessage.getOrderId())
            .ifPresentOrElse(order -> {
                // Assert Order saved in DB
                assertThat(order.getOrderId()).isEqualTo(ORDER_ID);
                assertThat(order.getPaymentStatus()).isEqualTo(PaymentState.REVIEW);
                assertThat(order.getPrevPaymentStatus()).isEqualTo(PaymentState.START);

                // Assert Payment saved in DB
                assertThat(order.getPayments().size()).isEqualTo(1);
                assertThat(order.getPayments().get(0) instanceof AdyenPayment);
                AdyenPayment adyenPayment = (AdyenPayment) order.getPayments().get(0);
                assertThat(adyenPayment.getType()).isEqualTo(PaymentType.ADYEN_CARD);
                assertThat(adyenPayment.getPaymentReference()).isEqualTo("**********");
                assertThat(adyenPayment.getPaymentReference()).isEqualTo("**********");

                assertThat(adyenPayment.getAuthorisedAmount()).isEqualTo("24.95");

            }, Assertions::fail);


        // Send a paymentauthorized message
        PaymentAuthorized paymentAuthorizedMsg = new PaymentAuthorized("ADYEN", "**********",
            ORDER_ID, "mc", new BigDecimal("89.99"), "EUR");
        kafkaProducer.send(new ProducerRecord<>(paymentAuthorizedTopic, paymentAuthorizedMsg));

        await().untilAsserted(() -> {
            List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();
            // Assert PaymentStatusUpdated message
            checkPaymentStatusUpdatedIsConsumed(messages, PaymentStatusUpdated.PaymentState.AUTHORISED);
            orderRepository.findById(validOrderPlacedMessage.getOrderId())
                .ifPresentOrElse(order -> {
                    // Assert Order is updated and saved in DB
                    assertThat(order.getOrderId()).isEqualTo(ORDER_ID);
                    assertThat(order.getPaymentStatus()).isEqualTo(PaymentState.AUTHORISED);
                    assertThat(order.getPrevPaymentStatus()).isEqualTo(PaymentState.REVIEW);

                    // Assert Payment saved in DB
                    assertThat(order.getPayments().size()).isEqualTo(1);
                    assertThat(order.getPayments().get(0) instanceof AdyenPayment);
                    AdyenPayment updatedAdyenPayment = (AdyenPayment) order.getPayments().get(0);
                    assertThat(updatedAdyenPayment.getType()).isEqualTo(PaymentType.ADYEN_CARD);
                    assertThat(updatedAdyenPayment.getPaymentReference()).isEqualTo("**********");
                    assertThat(updatedAdyenPayment.getPaymentReference()).isEqualTo("**********");
                    assertThat(updatedAdyenPayment.getAuthorisedAmount()).isEqualTo("24.95");
                }, Assertions::fail);

        });


    }

    /**
     * For ADYEN-CARD-mc
     * <p>
     * When validorderplaced is sent and payment is in review
     * Then order and payment is processed and saved in the database
     * And
     * When payment rejected message is received
     * then PaymentStatusUpdated is produced and order payment status is updated
     */
    @Test
    public void when_ValidOrderPlaced_AdyenCard_Review_ThenPaymentIsRejected() {
        String method = "ADYEN_CREDIT_CARD";
        String subMethod = "mc";
        String state = "REVIEW";
        // Arrange
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createBseOrderPlacedMessage_Adyen(method, subMethod, state);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));

        await().until(
            () -> orderRepository.existsById(validOrderPlacedMessage.getOrderId()));

        orderRepository.findById(validOrderPlacedMessage.getOrderId())
            .ifPresentOrElse(order -> {
                // Assert Order saved in DB
                assertThat(order.getOrderId()).isEqualTo(ORDER_ID);
                assertThat(order.getPaymentStatus()).isEqualTo(PaymentState.REVIEW);
                assertThat(order.getPrevPaymentStatus()).isEqualTo(PaymentState.START);

                // Assert Payment saved in DB
                assertThat(order.getPayments().size()).isEqualTo(1);
                assertThat(order.getPayments().get(0) instanceof AdyenPayment);
                AdyenPayment adyenPayment = (AdyenPayment) order.getPayments().get(0);
                assertThat(adyenPayment.getType()).isEqualTo(PaymentType.ADYEN_CARD);
                assertThat(adyenPayment.getPaymentReference()).isEqualTo("**********");
                assertThat(adyenPayment.getPaymentReference()).isEqualTo("**********");

                assertThat(adyenPayment.getAuthorisedAmount()).isEqualTo("24.95");

            }, Assertions::fail);


        // Send a paymentrejectedmessage
        PaymentRejected paymentRejectedMsg = new PaymentRejected("ADYEN", "**********",
            ORDER_ID);
        kafkaProducer.send(new ProducerRecord<>(paymentRejectedTopic, paymentRejectedMsg));

        await().untilAsserted(() -> {
            List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();
            // Assert PaymentStatusUpdated message
            checkPaymentStatusUpdatedIsConsumed(messages, PaymentStatusUpdated.PaymentState.CANCELLED);
            orderRepository.findById(validOrderPlacedMessage.getOrderId())
                .ifPresentOrElse(order -> {
                    // Assert Order is updated and saved in DB
                    assertThat(order.getOrderId()).isEqualTo(ORDER_ID);
                    assertThat(order.getPaymentStatus()).isEqualTo(PaymentState.CANCELLED);
                    assertThat(order.getPrevPaymentStatus()).isEqualTo(PaymentState.REVIEW);
                }, Assertions::fail);

        });
    }

    /**
     * For ADYEN-CARD-mc
     * <p>
     * When validorderplaced is sent and payment has no new status updates
     * Then order and payment is not processed and not updated in the database,
     * and PaymentStatusUpdated is NOT produced
     */
    @Test
    public void when_ValidOrderPlaced_AdyenCard_NoChange() {
        String method = "ADYEN_CREDIT_CARD";
        String subMethod = "mc";
        String state = "AUTH";
        // Arrange
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createBseOrderPlacedMessage_Adyen(method, subMethod, state);

        // Act
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));

        await().until(() -> orderRepository.existsById(validOrderPlacedMessage.getOrderId()));

        orderRepository.findById(validOrderPlacedMessage.getOrderId())
            .ifPresentOrElse(order -> {
                // Assert Order saved in DB
                assertThat(order.getOrderId()).isEqualTo(ORDER_ID);
                assertThat(order.getPaymentStatus()).isEqualTo(PaymentState.AUTHORISED);
                assertThat(order.getPrevPaymentStatus()).isEqualTo(PaymentState.START);
                // Assert Payment saved in DB
                assertThat(order.getPayments().size()).isEqualTo(1);
                assertThat(order.getPayments().get(0) instanceof AdyenPayment);
                AdyenPayment adyenPayment = (AdyenPayment) order.getPayments().get(0);
                assertThat(adyenPayment.getType()).isEqualTo(PaymentType.ADYEN_CARD);
                assertThat(adyenPayment.getPaymentReference()).isEqualTo("**********");
                assertThat(adyenPayment.getAuthorisedAmount()).isEqualTo("24.95");
            }, Assertions::fail);


        // Send a paymentauthorized message
        PaymentAuthorized paymentAuthorizedMsg = new PaymentAuthorized("ADYEN", "**********",
            ORDER_ID, "mc", new BigDecimal("89.99"), "EUR");
        kafkaProducer.send(new ProducerRecord<>(paymentAuthorizedTopic, paymentAuthorizedMsg));


        await().untilAsserted(() -> {
            List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();
            // Assert PaymentStatusUpdated message
            checkPaymentStatusUpdatedIsConsumed(messages, PaymentStatusUpdated.PaymentState.AUTHORISED);
            orderRepository.findById(validOrderPlacedMessage.getOrderId())
                .ifPresentOrElse(order -> {
                    // Assert Order status is not updated in DB, prevPaymentStatus is the same
                    assertThat(order.getOrderId()).isEqualTo(ORDER_ID);
                    assertThat(order.getPaymentStatus()).isEqualTo(PaymentState.AUTHORISED);
                    assertThat(order.getPrevPaymentStatus()).isEqualTo(PaymentState.START);
                }, Assertions::fail);
        });
    }

    /**
     * For ADYEN-CARD-mc
     * <p>
     * When validorderplaced is sent and payment has invalid transition
     * Then PaymentStatusUpdated is NOT produced and there is no change in order payment status
     */
    @Test
    public void when_ValidOrderPlaced_AdyenCard_InvalidTransition() {
        String method = "ADYEN_CREDIT_CARD";
        String subMethod = "mc";
        String state = "SETTLED";
        // Arrange
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createBseOrderPlacedMessage_Adyen(method, subMethod, state);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));

        // Act
        //poll for PaymentStatusUpdatedResponse
        await().untilAsserted(() -> {
            List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();
            Optional<Order> order = orderRepository.findById(validOrderPlacedMessage.getOrderId());
            // Assert PaymentStatusUpdatedResponse is not produced
            assertThat(messages)
                .usingRecursiveFieldByFieldElementComparator(
                    RecursiveComparisonConfiguration.builder()
                        .withComparedFields("orderId")
                        .build()
                )
                .doesNotContain(
                    new PaymentStatusUpdated()
                        .withOrderId(ORDER_ID)
                );
            // Assert Order is empty
            assertThat(order).isEmpty();
        });
    }

    /**
     * For ADYEN-CARD-mc
     * <p>
     * When validorderplaced is sent and payment is authorised
     * and a paymentrejectedmessage arrives
     * then PaymentStatusUpdated is not produced and order payment status is not updated
     */
    @Test
    public void when_ValidOrderPlaced_AdyenCard_Authorised_ThenPaymentIsRejected() {
        String method = "ADYEN_CREDIT_CARD";
        String subMethod = "mc";
        String state = "AUTH";
        // Arrange
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createBseOrderPlacedMessage_Adyen(method, subMethod, state);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));

        await().until(
            () -> orderRepository.existsById(validOrderPlacedMessage.getOrderId()));

        // Send a paymentrejectedmessage
        PaymentRejected paymentRejectedMsg = new PaymentRejected("ADYEN", "**********",
            ORDER_ID);
        kafkaProducer.send(new ProducerRecord<>(paymentRejectedTopic, paymentRejectedMsg));

        //poll for PaymentStatusUpdatedResponse
        await().untilAsserted(() -> {
            List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();
            // Assert only one PaymentStatusUpdated message
            checkPaymentStatusUpdatedIsConsumed(messages, PaymentStatusUpdated.PaymentState.AUTHORISED);
            orderRepository.findById(validOrderPlacedMessage.getOrderId())
                .ifPresentOrElse(order -> {
                    assertThat(order.getOrderId()).isEqualTo(ORDER_ID);
                    assertThat(order.getPaymentStatus()).isEqualTo(PaymentState.AUTHORISED);
                    assertThat(order.getPrevPaymentStatus()).isEqualTo(PaymentState.START);
                }, Assertions::fail);
        });
    }

    /**
     * When an order with an adyen and giftcard payments is received
     * And a paymentauthorization message arrives
     * Then order payment status  is authorised and an PaymentStatusUpdated is sent
     */
    @Test
    public void when_ValidOrderPlaced_WithMultiplePayment_Review_To_Authorised() {

        // Arrange
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createBseOrderPlacedMessage_Multi_Payment("REVIEW");
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));

        await().until(
            () -> orderRepository.existsById(validOrderPlacedMessage.getOrderId()));


        orderRepository.findById(validOrderPlacedMessage.getOrderId())
            .ifPresentOrElse(order -> {
                assertThat(order.getOrderId()).isEqualTo(ORDER_ID);
                assertThat(order.getPaymentStatus()).isEqualTo(PaymentState.REVIEW);
                assertThat(order.getPrevPaymentStatus()).isEqualTo(PaymentState.START);
            }, Assertions::fail);


        // Send a paymentauthorized message
        PaymentAuthorized paymentAuthorizedMsg = new PaymentAuthorized("ADYEN", "**********",
            ORDER_ID, "ideal", new BigDecimal("24.95"), "EUR");
        kafkaProducer.send(new ProducerRecord<>(paymentAuthorizedTopic, paymentAuthorizedMsg));

        // Act
        await().untilAsserted(() -> {
            List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();
            // Assert
            checkPaymentStatusUpdatedIsConsumed(messages, PaymentStatusUpdated.PaymentState.AUTHORISED);
            orderRepository.findById(validOrderPlacedMessage.getOrderId())
                .ifPresentOrElse(order -> {

                    assertThat(order.getOrderId()).isEqualTo(ORDER_ID);
                    assertThat(order.getPaymentStatus()).isEqualTo(PaymentState.AUTHORISED);
                    assertThat(order.getPrevPaymentStatus()).isEqualTo(PaymentState.REVIEW);

                    assertThat(order.getPayments().size()).isEqualTo(2);

                    assertThat(order.getPayments().get(0).getType()).isEqualTo(PaymentType.ADYEN_BANK);
                    assertThat(order.getPayments().get(0).getAuthorisedAmount()).isEqualTo("24.95");
                    assertThat(order.getPayments().get(0).getProcessorId()).isEqualTo(ProcessorId.ADYEN);
                    assertThat(order.getPayments().get(0).getSubMethodName()).isEqualTo(PaymentMethod.ADYEN_IDEAL);
                    assertThat(order.getPayments().get(0).getSubMethod()).isEqualTo("ideal");
                    assertThat(order.getPayments().get(0).getPaymentReference()).isEqualTo("**********");

                    assertThat(order.getPayments().get(1).getType()).isEqualTo(PaymentType.GIFTCARD);
                    assertThat(order.getPayments().get(1).getAuthorisedAmount()).isEqualTo("50.0");
                    assertThat(order.getPayments().get(1).getProcessorId()).isEqualTo(ProcessorId.OPTICARD);
                    assertThat(order.getPayments().get(1).getSubMethodName()).isEqualTo(PaymentMethod.OC_GIFTCARD);
                    assertThat(order.getPayments().get(1).getSubMethod()).isNull();
                    assertThat(order.getPayments().get(1).getPaymentReference()).isEqualTo("6299201155483449386");
                }, Assertions::fail);
        });


    }


    /**
     * When an order with an adyen and giftcard payments is received
     * And a paymentrejected message arrives
     * Then order payment status  is cancelled and an PaymentStatusUpdated is sent
     */
    @Test
    public void when_ValidOrderPlaced_WithMultiplePayment_Review_To_Rejected() {

        // Arrange
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createBseOrderPlacedMessage_Multi_Payment("REVIEW");
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));

        await().until(() -> orderRepository.existsById(validOrderPlacedMessage.getOrderId()));

        orderRepository.findById(validOrderPlacedMessage.getOrderId())
            .ifPresentOrElse(order -> {
                assertThat(order.getOrderId()).isEqualTo(ORDER_ID);
                assertThat(order.getPaymentStatus()).isEqualTo(PaymentState.REVIEW);
                assertThat(order.getPrevPaymentStatus()).isEqualTo(PaymentState.START);
            }, Assertions::fail);


        // Send a paymentrejected message
        // Act
        PaymentRejected paymentRejectedMsg = new PaymentRejected("ADYEN", "**********", ORDER_ID);
        kafkaProducer.send(new ProducerRecord<>(paymentRejectedTopic, paymentRejectedMsg));

        await().untilAsserted(() -> {
            List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();
            // Assert
            checkPaymentStatusUpdatedIsConsumed(messages, PaymentStatusUpdated.PaymentState.CANCELLED);
            orderRepository.findById(validOrderPlacedMessage.getOrderId())
                .ifPresentOrElse(order -> {
                    assertThat(order.getOrderId()).isEqualTo(ORDER_ID);
                    assertThat(order.getPaymentStatus()).isEqualTo(PaymentState.CANCELLED);
                    assertThat(order.getPrevPaymentStatus()).isEqualTo(PaymentState.REVIEW);

                    assertThat(order.getPayments().size()).isEqualTo(2);

                    assertThat(order.getPayments().get(0).getType()).isEqualTo(PaymentType.ADYEN_BANK);
                    assertThat(order.getPayments().get(0).getAuthorisedAmount()).isEqualTo("24.95");
                    assertThat(order.getPayments().get(0).getProcessorId()).isEqualTo(ProcessorId.ADYEN);
                    assertThat(order.getPayments().get(0).getSubMethodName()).isEqualTo(PaymentMethod.ADYEN_IDEAL);
                    assertThat(order.getPayments().get(0).getSubMethod()).isEqualTo("ideal");
                    assertThat(order.getPayments().get(0).getPaymentReference()).isEqualTo("**********");

                    assertThat(order.getPayments().get(1).getType()).isEqualTo(PaymentType.GIFTCARD);
                    assertThat(order.getPayments().get(1).getAuthorisedAmount()).isEqualTo("50.0");
                    assertThat(order.getPayments().get(1).getProcessorId()).isEqualTo(ProcessorId.OPTICARD);
                    assertThat(order.getPayments().get(1).getSubMethodName()).isEqualTo(PaymentMethod.OC_GIFTCARD);
                    assertThat(order.getPayments().get(1).getSubMethod()).isNull();
                    assertThat(order.getPayments().get(1).getPaymentReference()).isEqualTo("6299201155483449386");
                }, Assertions::fail);
        });

    }

    private static void checkPaymentStatusUpdatedIsConsumed(
        List<PaymentStatusUpdated> messages,
        PaymentStatusUpdated.PaymentState paymentState
    ) {
        assertThat(messages)
            .usingRecursiveFieldByFieldElementComparator(PAYMENT_STATUS_UPDATED_RECURSIVE_COMPARISON_CONFIGURATION)
            .contains(
                new PaymentStatusUpdated()
                    .withOrderId(ORDER_ID)
                    .withPaymentState(paymentState)
            );
    }
}
