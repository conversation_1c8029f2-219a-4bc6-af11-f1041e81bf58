package com.bestseller.payment.integration.payment;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderFinalized;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementSucceeded.PaymentSettlementSucceeded;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.integration.container.BaseTestContainer;
import com.bestseller.payment.messaging.consumer.PaymentStatusUpdatedTestConsumer;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

import static com.bestseller.payment.utils.FileUtils.loadSampleKafkaMessage;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
 class PaymentSettlementSucceededIntegrationTest extends BaseTestContainer {
    @Autowired
    private KafkaProducer<String, Object> kafkaProducer;

    @Autowired
    private PaymentStatusUpdatedTestConsumer paymentStatusUpdatedTestConsumer;

    @Autowired
    private OrderRepository orderRepository;

    @Value("${spring.cloud.stream.bindings.validOrderPlacedConsumer-in-0.destination}")
    private String validOrderPlacedTopic;

    @Value("${spring.cloud.stream.bindings.orderFinalizedConsumer-in-0.destination}")
    private String orderFinalizedTopic;

    @Value("${spring.cloud.stream.bindings.paymentSettlementSucceededConsumer-in-0.destination}")
    private String paymentSettlementSucceededTopic;

    @BeforeEach
    void cleanup() {
        orderRepository.deleteAll();
        paymentStatusUpdatedTestConsumer.clearMessages();
    }

    @Test
    void when_paymentSettlementSucceeded_klarna() throws IOException {
        // arrange
        final String orderId = prepareOrder("/messages/validOrderPlaced_klarna.json");

        // act
        final PaymentSettlementSucceeded paymentSettlementSucceeded = loadSampleKafkaMessage("/messages/paymentSettlementSucceeded_klarna.json", PaymentSettlementSucceeded.class);
        kafkaProducer.send(new ProducerRecord<>(paymentSettlementSucceededTopic, paymentSettlementSucceeded));

        // assert
        await().untilAsserted(
                () -> {
                    final List<PaymentStatusUpdated> paymentStatusUpdatedMessages = paymentStatusUpdatedTestConsumer.getMessages();
                    assertEquals(1,
                            paymentStatusUpdatedMessages
                                    .stream()
                                    .filter(message -> message.getOrderId().equals(orderId))
                                    .filter(message -> message.getPaymentState().name().equals(PaymentState.SETTLED.name()))
                                    .count()
                    );
                });
    }

    @Test
    void when_paymentSettlementSucceeded_adyen() throws IOException {
        // arrange
        final String orderId = prepareOrder("/messages/validOrderPlaced_adyenCard.json");

        // act
        final PaymentSettlementSucceeded paymentSettlementSucceeded = loadSampleKafkaMessage("/messages/paymentSettlementSucceeded_adyen.json", PaymentSettlementSucceeded.class);
        kafkaProducer.send(new ProducerRecord<>(paymentSettlementSucceededTopic, paymentSettlementSucceeded));

        // assert
        await().untilAsserted(
                () -> {
                    final List<PaymentStatusUpdated> paymentStatusUpdatedMessages = paymentStatusUpdatedTestConsumer.getMessages();
                    assertEquals(1,
                            paymentStatusUpdatedMessages
                                    .stream()
                                    .filter(message -> message.getOrderId().equals(orderId))
                                    .filter(message -> message.getPaymentState().name().equals(PaymentState.SETTLED.name()))
                                    .count()
                    );
                });
    }

    @Test
    void when_paymentSettlementSucceeded_receiveDuplicateMessages() throws IOException {
        // arrange
        final String orderId = prepareOrder("/messages/validOrderPlaced_adyenCard.json");

        // act
        PaymentSettlementSucceeded paymentSettlementSucceeded = loadSampleKafkaMessage("/messages/paymentSettlementSucceeded_adyen.json", PaymentSettlementSucceeded.class);
        kafkaProducer.send(new ProducerRecord<>(paymentSettlementSucceededTopic, paymentSettlementSucceeded));

        await().until(() -> paymentStatusUpdatedTestConsumer
                .getMessages()
                .stream()
                .filter(message -> message.getOrderId().equals(orderId))
                .filter(message -> message.getPaymentState().name().equals(PaymentState.SETTLED.name()))
                .count() == 1);

        kafkaProducer.send(new ProducerRecord<>(paymentSettlementSucceededTopic, paymentSettlementSucceeded));

        // assert
        await().untilAsserted(() -> {
            final List<PaymentStatusUpdated> paymentStatusUpdatedMessages = paymentStatusUpdatedTestConsumer.getMessages();
            assertEquals(1, paymentStatusUpdatedMessages
                    .stream()
                    .filter(message -> message.getOrderId().equals(orderId))
                    .filter(message -> message.getPaymentState().name().equals(PaymentState.SETTLED.name()))
                    .count());
        });
    }

    private String prepareOrder(String validOrderPlacedFilename) throws IOException {
        String orderFinalizedFilename = "/messages/orderFinalized_all_dispatched.json";
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(validOrderPlacedFilename, ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
                () -> orderRepository.existsById(validOrderPlaced.getOrderId()));


        final OrderFinalized orderFinalized = loadSampleKafkaMessage(orderFinalizedFilename, OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().until(
                () -> {
                    Optional<Order> order = orderRepository.findById(orderFinalized.getOrderId());
                    return order.isPresent() && order.get().getPaymentStatus().equals(PaymentState.SETTLEMENT_REQUESTING);
                });

        return validOrderPlaced.getOrderId();
    }
}
