package com.bestseller.payment.integration.orderplaced;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.OrderPromotion;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.core.domain.OrderCharge;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.enumeration.EntryType;
import com.bestseller.payment.core.domain.enumeration.PaymentMethod;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.enumeration.Platform;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import com.bestseller.payment.integration.container.BaseTestContainer;
import com.bestseller.payment.messaging.consumer.PaymentStatusUpdatedTestConsumer;
import com.bestseller.payment.utils.ValidOrderPlacedGenerator;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.assertj.core.api.recursive.comparison.RecursiveComparisonConfiguration;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

import static com.bestseller.payment.core.domain.enumeration.Platform.DEMANDWARE;
import static com.bestseller.payment.core.domain.enumeration.Platform.TRADEBYTE;
import static com.bestseller.payment.utils.FileUtils.loadSampleKafkaMessage;
import static org.assertj.core.api.Assertions.assertThat;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
class ValidOrderPlacedIntegrationTest extends BaseTestContainer {

    private static final RecursiveComparisonConfiguration PAYMENT_STATUS_UPDATED_RECURSIVE_COMPARISON_CONFIGURATION =
        RecursiveComparisonConfiguration.builder()
            .withComparedFields("orderId", "paymentState")
            .build();

    @Autowired
    private PaymentStatusUpdatedTestConsumer paymentStatusUpdatedTestConsumer;

    @Autowired
    private KafkaProducer<String, Object> kafkaProducer;

    @Autowired
    private OrderRepository orderRepository;

    @Value("${spring.cloud.stream.bindings.validOrderPlacedConsumer-in-0.destination}")
    private String validOrderPlacedTopic;

    @BeforeEach
    public void resetDB() {
        orderRepository.deleteAll();
        paymentStatusUpdatedTestConsumer.clearMessages();
    }

    /**
     * this is a test for the case when the order is placed as tradebyte order and does not
     * have any payment method
     */
    @Test
    void when_OrderPlaced_IsFrom_Tradebyte() {
        // arrange
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createTradebyteOrderPlacedMessageWithShippingInformation();

        // act
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));

        await().untilAsserted(() -> {
            List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();
            // Assert
            checkPaymentStatusUpdatedIsConsumed(messages, "TB12345");

            orderRepository.findById(validOrderPlacedMessage.getOrderId())
                .ifPresentOrElse(order -> {
                        assertThat(order.getOrderId()).isEqualTo("TB12345");
                        assertThat(order.getPaymentStatus()).isEqualTo(PaymentState.OFFLINE);
                        assertThat(order.getPrevPaymentStatus()).isEqualTo(PaymentState.START);
                        assertThat(order.getPlatform()).isEqualTo(Platform.TRADEBYTE);

                        assertThat(order.getPayments().size()).isEqualTo(1);
                        assertThat(order.getPayments().getFirst().getType()).isEqualTo(PaymentType.NONE);
                        assertThat(order.getPayments().getFirst().getAuthorisedAmount()).isEqualTo("24.90");
                    },
                    Assertions::fail
                );
        });

    }

    /**
     * This is a test for the case when the order is placed with a gift card
     */
    @Test
    void when_OrderPlaced_Has_GiftCard_As_Payment_Method() {
        // arrange
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createBseOrderPlacedMessage_One_GiftCard();
        validOrderPlacedMessage.setOrderId("OL1201825216");
        validOrderPlacedMessage.setStore("DEMANDWARE");

        // act
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));

        await().untilAsserted(() -> {
            List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();

            // assert
            checkPaymentStatusUpdatedIsConsumed(messages, "OL1201825216");

            orderRepository.findById(validOrderPlacedMessage.getOrderId())
                .ifPresentOrElse(order -> {
                        assertThat(order.getPlatform()).isEqualTo(DEMANDWARE);
                        assertThat(order.getPayments().size()).isEqualTo(1);
                        assertThat(order.getPayments().getFirst().getType()).isEqualTo(PaymentType.GIFTCARD);
                        assertThat(order.getPayments().getFirst().getAuthorisedAmount()).isEqualTo("5.01");
                        assertThat(order.getPayments().getFirst().getProcessorId()).isEqualTo(ProcessorId.OPTICARD);
                        assertThat(order.getPayments().getFirst().getSubMethodName()).isEqualTo(PaymentMethod.OC_GIFTCARD);
                        assertThat(order.getPayments().getFirst().getSubMethod()).isNull();
                        assertThat(order.getPayments().getFirst().getPaymentReference()).isEqualTo("6299201243102467987");
                    },
                    Assertions::fail
                );
        });
    }

    /**
     * This is a test for the case when the order is placed with multiple gift cards
     * then we combine the gift cards into one payment
     */
    @Test
    void when_OrderPlaced_Has_Multiple_GiftCards_As_Payment_Method() {
        // arrange
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createBseOrderPlacedMessage_Multi_GiftCard();
        validOrderPlacedMessage.setOrderId("OL1201825217");

        // act
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));

        await().untilAsserted(() -> {
            List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();

            // assert
            checkPaymentStatusUpdatedIsConsumed(messages, "OL1201825217");

            orderRepository.findById(validOrderPlacedMessage.getOrderId())
                .ifPresentOrElse(order -> {
                        assertThat(order.getPayments().size()).isEqualTo(1);
                        assertThat(order.getPayments().getFirst().getType()).isEqualTo(PaymentType.GIFTCARD);
                        assertThat(order.getPayments().getFirst().getAuthorisedAmount()).isEqualTo("119.99");
                        assertThat(order.getPayments().getFirst().getProcessorId()).isEqualTo(ProcessorId.OPTICARD);
                        assertThat(order.getPayments().getFirst().getSubMethodName()).isEqualTo(PaymentMethod.OC_GIFTCARD);
                        assertThat(order.getPayments().getFirst().getSubMethod()).isNull();
                        assertThat(order.getPayments().getFirst().getPaymentReference()).isEqualTo("6299201243102467987");//because of combination of gift card the first transaction id is taken
                    },
                    Assertions::fail
                );
        });


    }

    /**
     * This is a test for the case when the order is placed with GiftCard along with other payment method
     * like Adyen,Klarna etc
     */
    @Test
    void when_OrderPlaced_Has_Both_GiftCard_And_Normal_Payment() {
        // arrange
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createBseOrderPlacedMessage_Multi_Payment();
        validOrderPlacedMessage.setOrderId("OL1201825210");

        // act
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));
        await().untilAsserted(() -> {
            List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();

            // assert
            checkPaymentStatusUpdatedIsConsumed(messages, "OL1201825210");

            orderRepository.findById(validOrderPlacedMessage.getOrderId())
                .ifPresentOrElse(order -> {
                        assertThat(order.getOrderId()).isEqualTo("OL1201825210");
                        assertThat(order.getPaymentStatus()).isEqualTo(PaymentState.AUTHORISED);
                        assertThat(order.getPrevPaymentStatus()).isEqualTo(PaymentState.START);

                        assertThat(order.getPayments().size()).isEqualTo(2);

                        assertThat(order.getPayments().getFirst().getType()).isEqualTo(PaymentType.ADYEN_BANK);
                        assertThat(order.getPayments().getFirst().getAuthorisedAmount()).isEqualTo("24.95");
                        assertThat(order.getPayments().getFirst().getProcessorId()).isEqualTo(ProcessorId.ADYEN);
                        assertThat(order.getPayments().getFirst().getSubMethodName()).isEqualTo(PaymentMethod.ADYEN_IDEAL);
                        assertThat(order.getPayments().getFirst().getSubMethod()).isEqualTo("ideal");
                        assertThat(order.getPayments().getFirst().getPaymentReference()).isEqualTo("**********");

                        assertThat(order.getPayments().get(1).getType()).isEqualTo(PaymentType.GIFTCARD);
                        assertThat(order.getPayments().get(1).getAuthorisedAmount()).isEqualTo("50.0");
                        assertThat(order.getPayments().get(1).getProcessorId()).isEqualTo(ProcessorId.OPTICARD);
                        assertThat(order.getPayments().get(1).getSubMethodName()).isEqualTo(PaymentMethod.OC_GIFTCARD);
                        assertThat(order.getPayments().get(1).getSubMethod()).isNull();
                        assertThat(order.getPayments().get(1).getPaymentReference()).isEqualTo("6299201155483449386");//because of combination of gift card the first transaction id is taken
                    },
                    Assertions::fail
                );

        });
    }

    /**
     * This is a test for the case when the order is placed with Adyen_Ideal payment method
     */
    @Test
    void when_OrderPlaced_Has_Adyen_Ideal_Payment() {
        // arrange
        String method = "ADYEN_IDEAL";
        String subMethod = "ideal";
        String state = "AUTH";

        // arrange
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createBseOrderPlacedMessage_Adyen(method, subMethod, state);
        validOrderPlacedMessage.setOrderId("OL1201825201");

        // act
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));
        await().untilAsserted(() -> {
            List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();
            // Assert
            checkPaymentStatusUpdatedIsConsumed(messages, "OL1201825201");

            orderRepository.findById(validOrderPlacedMessage.getOrderId())
                .ifPresentOrElse(order -> {
                        assertThat(order.getOrderId()).isEqualTo("OL1201825201");
                        assertThat(order.getPaymentStatus()).isEqualTo(PaymentState.AUTHORISED);
                        assertThat(order.getPrevPaymentStatus()).isEqualTo(PaymentState.START);

                        assertThat(order.getPayments().size()).isEqualTo(1);

                        assertThat(order.getPayments().getFirst().getType()).isEqualTo(PaymentType.ADYEN_BANK);
                        assertThat(order.getPayments().getFirst().getAuthorisedAmount()).isEqualTo("24.95");
                        assertThat(order.getPayments().getFirst().getProcessorId()).isEqualTo(ProcessorId.ADYEN);
                        assertThat(order.getPayments().getFirst().getSubMethodName()).isEqualTo(PaymentMethod.ADYEN_IDEAL);
                        assertThat(order.getPayments().getFirst().getSubMethod()).isEqualTo(subMethod);
                        assertThat(order.getPayments().getFirst().getPaymentReference()).isEqualTo("**********");
                    },
                    Assertions::fail
                );
        });
    }

    /**
     * This is a test for the case when the order is placed with Adyen_Credit_Card payment method and bcmc is false
     */
    @Test
    void when_OrderPlaced_Has_Adyen_Credit_Card_Payment() {
        String method = "ADYEN_CREDIT_CARD";
        String subMethod = "mc";
        String state = "AUTH";

        // arrange
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createBseOrderPlacedMessage_Adyen(method, subMethod, state);
        validOrderPlacedMessage.setOrderId("OL1201825202");

        // act
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));
        await().untilAsserted(() -> {
            List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();

            // assert
            checkPaymentStatusUpdatedIsConsumed(messages, "OL1201825202");

            orderRepository.findById(validOrderPlacedMessage.getOrderId())
                .ifPresentOrElse(order -> {
                        assertThat(order.getOrderId()).isEqualTo("OL1201825202");
                        assertThat(order.getPaymentStatus()).isEqualTo(PaymentState.AUTHORISED);
                        assertThat(order.getPrevPaymentStatus()).isEqualTo(PaymentState.START);

                        assertThat(order.getPayments().size()).isEqualTo(1);

                        assertThat(order.getPayments().getFirst().getType()).isEqualTo(PaymentType.ADYEN_CARD);
                        assertThat(order.getPayments().getFirst().getAuthorisedAmount()).isEqualTo("24.95");
                        assertThat(order.getPayments().getFirst().getProcessorId()).isEqualTo(ProcessorId.ADYEN);
                        assertThat(order.getPayments().getFirst().getSubMethodName()).isEqualTo(PaymentMethod.ADYEN_CREDIT_CARD);
                        assertThat(order.getPayments().getFirst().getSubMethod()).isEqualTo(subMethod);
                        assertThat(order.getPayments().getFirst().getPaymentReference()).isEqualTo("**********");
                    },
                    Assertions::fail
                );
        });
    }

    /**
     * This is a test for the case when the order is placed with Adyen_Credit_Card payment method and bcmc is true
     */
    @Test
    void when_OrderPlaced_Has_Adyen_Credit_Card_With_BCMC_Payment() {
        String method = "ADYEN_CREDIT_CARD";
        String subMethod = "bcmc";
        String state = "AUTH";

        // arrange
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createBseOrderPlacedMessage_Adyen(method, subMethod, state);
        validOrderPlacedMessage.setOrderId("OL1201825203");

        // act
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));
        await().untilAsserted(() -> {
            List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();

            // assert
            checkPaymentStatusUpdatedIsConsumed(messages, "OL1201825203");

            orderRepository.findById(validOrderPlacedMessage.getOrderId())
                .ifPresentOrElse(order -> {
                        assertThat(order.getOrderId()).isEqualTo("OL1201825203");
                        assertThat(order.getPaymentStatus()).isEqualTo(PaymentState.AUTHORISED);
                        assertThat(order.getPrevPaymentStatus()).isEqualTo(PaymentState.START);

                        assertThat(order.getPayments().size()).isEqualTo(1);

                        assertThat(order.getPayments().getFirst().getType()).isEqualTo(PaymentType.ADYEN_BANK);
                        assertThat(order.getPayments().getFirst().getAuthorisedAmount()).isEqualTo("24.95");
                        assertThat(order.getPayments().getFirst().getProcessorId()).isEqualTo(ProcessorId.ADYEN);
                        assertThat(order.getPayments().getFirst().getSubMethodName()).isEqualTo(PaymentMethod.ADYEN_CREDIT_CARD);
                        assertThat(order.getPayments().getFirst().getSubMethod()).isEqualTo(subMethod);
                        assertThat(order.getPayments().getFirst().getPaymentReference()).isEqualTo("**********");
                    },
                    Assertions::fail
                );
        });
    }

    /**
     * This is a test for the case when the order is placed with Adyen_BCMC payment method no matter what bcmc is true or false
     */

    @Test
    void when_OrderPlaced_Has_Adyen_BCMC_Payment() {
        String method = "ADYEN_BCMC";
        String state = "AUTH";

        // arrange
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createBseOrderPlacedMessage_Adyen(method, null, state);
        validOrderPlacedMessage.setOrderId("OL1201825204");
        validOrderPlacedMessage.getPayments().getFirst().setMethod(PaymentMethod.ADYEN_BCMC.name());

        // act
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));
        await().untilAsserted(() -> {
            List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();

            // assert
            checkPaymentStatusUpdatedIsConsumed(messages, "OL1201825204");

            orderRepository.findById(validOrderPlacedMessage.getOrderId())
                .ifPresentOrElse(order -> {
                        assertThat(order.getOrderId()).isEqualTo("OL1201825204");
                        assertThat(order.getPaymentStatus()).isEqualTo(PaymentState.AUTHORISED);
                        assertThat(order.getPrevPaymentStatus()).isEqualTo(PaymentState.START);

                        assertThat(order.getPayments().size()).isEqualTo(1);

                        assertThat(order.getPayments().getFirst().getType()).isEqualTo(PaymentType.ADYEN_BANK);
                        assertThat(order.getPayments().getFirst().getAuthorisedAmount()).isEqualTo("24.95");
                        assertThat(order.getPayments().getFirst().getProcessorId()).isEqualTo(ProcessorId.ADYEN);
                        assertThat(order.getPayments().getFirst().getSubMethodName()).isEqualTo(PaymentMethod.ADYEN_BCMC);
                        assertThat(order.getPayments().getFirst().getSubMethod()).isNull();
                        assertThat(order.getPayments().getFirst().getPaymentReference()).isEqualTo("**********");
                    },
                    Assertions::fail
                );
        });
    }

    /**
     * this is a test for the case when the order is placed with Adyen_Klarna payment method
     */
    @Test
    void when_OrderPlaced_Has_Adyen_Klarna_Payment() {
        String method = "ADYEN_KLARNA";
        String state = "AUTH";

        // arrange
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createBseOrderPlacedMessage_Adyen(method, null, state);
        validOrderPlacedMessage.setOrderId("OL1201825205");

        // act
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));
        await().untilAsserted(() -> {
            List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();

            // assert
            checkPaymentStatusUpdatedIsConsumed(messages, "OL1201825205");

            orderRepository.findById(validOrderPlacedMessage.getOrderId())
                .ifPresentOrElse(order -> {
                        assertThat(order.getOrderId()).isEqualTo("OL1201825205");
                        assertThat(order.getPaymentStatus()).isEqualTo(PaymentState.AUTHORISED);
                        assertThat(order.getPrevPaymentStatus()).isEqualTo(PaymentState.START);

                        assertThat(order.getPayments().size()).isEqualTo(1);

                        assertThat(order.getPayments().getFirst().getType()).isEqualTo(PaymentType.ADYEN_CARD);
                        assertThat(order.getPayments().getFirst().getAuthorisedAmount()).isEqualTo("24.95");
                        assertThat(order.getPayments().getFirst().getProcessorId()).isEqualTo(ProcessorId.ADYEN);
                        assertThat(order.getPayments().getFirst().getSubMethodName()).isEqualTo(PaymentMethod.ADYEN_KLARNA);
                        assertThat(order.getPayments().getFirst().getSubMethod()).isNull();
                        assertThat(order.getPayments().getFirst().getPaymentReference()).isEqualTo("**********");
                    },
                    Assertions::fail
                );
        });
    }

    /**
     * This is a test for the case when the order is placed with Klarna with Klarna_Account payment method
     */
    @Test
    void when_OrderPlaced_Has_Klarna_Account_Payment() {
        // arrange
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createOrderWithKlarnaPayment();
        validOrderPlacedMessage.getPayments().getFirst().setProvider("KLARNA_PAYMENTS");
        validOrderPlacedMessage.getPayments().getFirst().setMethod(PaymentMethod.KLARNA_ACCOUNT.name());
        validOrderPlacedMessage.setOrderId("OL1201825206");

        // act
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));
        await().untilAsserted(() -> {
            List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();

            // assert
            checkPaymentStatusUpdatedIsConsumed(messages, "OL1201825206");

            orderRepository.findById(validOrderPlacedMessage.getOrderId())
                .ifPresentOrElse(order -> {
                        assertThat(order.getOrderId()).isEqualTo("OL1201825206");
                        assertThat(order.getPaymentStatus()).isEqualTo(PaymentState.AUTHORISED);
                        assertThat(order.getPrevPaymentStatus()).isEqualTo(PaymentState.START);

                        assertThat(order.getPayments().size()).isEqualTo(1);

                        assertThat(order.getPayments().getFirst().getType()).isEqualTo(PaymentType.KLARNA);
                        assertThat(order.getPayments().getFirst().getAuthorisedAmount()).isEqualTo("301.89");
                        assertThat(order.getPayments().getFirst().getProcessorId()).isEqualTo(ProcessorId.KLARNA_PAYMENTS);
                        assertThat(order.getPayments().getFirst().getSubMethodName()).isEqualTo(PaymentMethod.KLARNA_ACCOUNT);
                        assertThat(order.getPayments().getFirst().getSubMethod()).isEqualTo("2982");
                        assertThat(order.getPayments().getFirst().getPaymentReference()).isEqualTo("31e364b5-f197-426a-b322-4c7096311588");
                    },
                    Assertions::fail
                );
        });
    }

    /**
     * This is a test for the case when the order is placed with Klarna with Klarna_Invoice payment method
     */
    @Test
    void when_OrderPlaced_Has_Klarna_Invoice_Payment() {
        // arrange
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createOrderWithKlarnaPayment();
        validOrderPlacedMessage.getPayments().getFirst().setProvider("KLARNA_PAYMENTS");
        validOrderPlacedMessage.getPayments().getFirst().setMethod(PaymentMethod.KLARNA_INVOICE.name());
        validOrderPlacedMessage.getPayments().getFirst().setSubMethod("-1");
        validOrderPlacedMessage.setOrderId("OL1201825207");

        // act
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));
        await().untilAsserted(() -> {
            List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();

            // assert
            checkPaymentStatusUpdatedIsConsumed(messages, "OL1201825207");

            orderRepository.findById(validOrderPlacedMessage.getOrderId())
                .ifPresentOrElse(order -> {
                        assertThat(order.getOrderId()).isEqualTo("OL1201825207");
                        assertThat(order.getPaymentStatus()).isEqualTo(PaymentState.AUTHORISED);
                        assertThat(order.getPrevPaymentStatus()).isEqualTo(PaymentState.START);

                        assertThat(order.getPayments().size()).isEqualTo(1);

                        assertThat(order.getPayments().getFirst().getType()).isEqualTo(PaymentType.KLARNA);
                        assertThat(order.getPayments().getFirst().getAuthorisedAmount()).isEqualTo("301.89");
                        assertThat(order.getPayments().getFirst().getProcessorId()).isEqualTo(ProcessorId.KLARNA_PAYMENTS);
                        assertThat(order.getPayments().getFirst().getSubMethodName()).isEqualTo(PaymentMethod.KLARNA_INVOICE);
                        assertThat(order.getPayments().getFirst().getSubMethod()).isEqualTo("-1");
                        assertThat(order.getPayments().getFirst().getPaymentReference()).isEqualTo("31e364b5-f197-426a-b322-4c7096311588");
                    },
                    Assertions::fail
                );
        });
    }


    /**
     * This is a test when order is from tradebyte and has one order line
     */
    @Test
    void when_OrderPlaced_IsFrom_Tradebyte_Has_One_OrderLine() {
        // arrange
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createTradebyteOrderPlacedMessageWithShippingInformation();
        validOrderPlacedMessage.setOrderId("TB12346");

        // act
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));
        await().until(() -> orderRepository.existsById(validOrderPlacedMessage.getOrderId()));

        orderRepository.findById(validOrderPlacedMessage.getOrderId())
            .ifPresentOrElse(order -> {
                    // assert
                    assertThat(order.getOrderId()).isEqualTo("TB12346");

                    assertThat(order.getOrderLines().size()).isEqualTo(1);

                    assertThat(order.getOrderLines().getFirst().getOrderLinePaidAmount()).isNotNull();

                    //overallTotal
                    assertThat(order.getTotalPaidPrice().getOriginalGrossDiscountedTotal().compareTo(BigDecimal.valueOf(24.90))).isEqualTo(0);
                    assertThat(order.getTotalPaidPrice().getGrossDiscountedTotal().compareTo(BigDecimal.valueOf(24.90))).isEqualTo(0);
                    assertThat(order.getTotalPaidPrice().getGrossSubTotal()).isEqualTo(BigDecimal.valueOf(20.95));
                    assertThat(order.getTotalPaidPrice().getVat().compareTo(BigDecimal.valueOf(4.27))).isEqualTo(0);

                    //orderEntryAmount
                    assertThat(order.getOrderLines().getFirst().getOrderLinePaidAmount().getLineVAT()).isEqualTo(BigDecimal.valueOf(3.64));
                    assertThat(order.getOrderLines().getFirst().getOrderLinePaidAmount().getUnitVAT()).isEqualTo(BigDecimal.valueOf(3.64));
                    assertThat(order.getOrderLines().getFirst().getOrderLinePaidAmount().getGrossDiscountedTotal()).isEqualTo(BigDecimal.valueOf(20.95));
                    assertThat(order.getOrderLines().getFirst().getOrderLinePaidAmount().getGrossDiscountedUnitPrice()).isEqualTo(BigDecimal.valueOf(20.95));
                    assertThat(order.getOrderLines().getFirst().getOrderLinePaidAmount().getGrossRetailUnitPrice()).isEqualTo(BigDecimal.valueOf(20.95));
                    assertThat(order.getOrderLines().getFirst().getOrderLinePaidAmount().getOriginalGrossDiscountedTotal()).isEqualTo(BigDecimal.valueOf(20.95));
                    assertThat(order.getOrderLines().getFirst().getOrderLinePaidAmount().getUnitDiscount().compareTo(BigDecimal.valueOf(0.00)) == 0).isTrue();
                },
                Assertions::fail
            );
    }

    @Test
    void when_OrderPlaced_Is_DM_Has_One_OrderLine_And_Discount() {
        String method = "ADYEN_IDEAL";
        String subMethod = "ideal";
        String state = "AUTH";

        // arrange
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createBseOrderPlacedMessage_Adyen(method, subMethod, state);
        validOrderPlacedMessage.setOrderId("OL12018251000");
        validOrderPlacedMessage.setOrderLines(List.of(ValidOrderPlacedGenerator.createOrderLine(BigDecimal.valueOf(36)
            , BigDecimal.valueOf(18), BigDecimal.valueOf(20), BigDecimal.valueOf(0.21), 2,
            BigDecimal.valueOf(3.124), null)));

        // act
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));
        await().until(() -> orderRepository.existsById(validOrderPlacedMessage.getOrderId()));
        orderRepository.findById(validOrderPlacedMessage.getOrderId())
            .ifPresentOrElse(order -> {
                    // Assert
                    assertThat(order.getOrderId()).isEqualTo("OL12018251000");


                    assertThat(order.getOrderLines().size()).isEqualTo(1);

                    assertThat(order.getOrderLines().getFirst().getOrderLinePaidAmount()).isNotNull();

                    //overallTotal
                    assertThat(order.getTotalPaidPrice().getOriginalGrossDiscountedTotal().compareTo(BigDecimal.valueOf(40)) == 0);
                    assertThat(order.getTotalPaidPrice().getGrossDiscountedTotal().compareTo(BigDecimal.valueOf(40)) == 0);
                    assertThat(order.getTotalPaidPrice().getGrossSubTotal().compareTo(BigDecimal.valueOf(36)) == 0);
                    assertThat(order.getTotalPaidPrice().getVat()).isEqualTo(BigDecimal.valueOf(6.93));

                    //orderEntryAmount
                    assertThat(order.getOrderLines().getFirst().getOrderLinePaidAmount().getLineVAT()).isEqualTo(BigDecimal.valueOf(6.24));
                    assertThat(order.getOrderLines().getFirst().getOrderLinePaidAmount().getUnitVAT()).isEqualTo(BigDecimal.valueOf(3.12));

                    assertThat(order.getOrderLines().getFirst().getOrderLinePaidAmount().getGrossDiscountedTotal().compareTo(BigDecimal.valueOf(36)) == 0);
                    assertThat(order.getOrderLines().getFirst().getOrderLinePaidAmount().getGrossDiscountedUnitPrice().compareTo(BigDecimal.valueOf(18.0)) == 0);
                    assertThat(order.getOrderLines().getFirst().getOrderLinePaidAmount().getGrossRetailUnitPrice().compareTo(BigDecimal.valueOf(20.0)) == 0);
                    assertThat(order.getOrderLines().getFirst().getOrderLinePaidAmount().getOriginalGrossDiscountedTotal().compareTo(BigDecimal.valueOf(36)) == 0);
                    assertThat(order.getOrderLines().getFirst().getOrderLinePaidAmount().getUnitDiscount().compareTo(BigDecimal.valueOf(2.0)) == 0);
                },
                Assertions::fail
            );
    }

    @Test
    void when_OrderPlaced_Is_DM_Has_Two_Different_OrderLine_And_Discount() {
        String method = "ADYEN_IDEAL";
        String subMethod = "ideal";
        String state = "AUTH";

        // arrange
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createBseOrderPlacedMessage_Adyen(method, subMethod, state);
        validOrderPlacedMessage.setOrderId("OL12018251001");
        validOrderPlacedMessage.setOrderLines(List.of(
            ValidOrderPlacedGenerator.createOrderLine(BigDecimal.valueOf(36)
                , BigDecimal.valueOf(18), BigDecimal.valueOf(20), BigDecimal.valueOf(0.21), 2,
                BigDecimal.valueOf(3.124), "123"),
            ValidOrderPlacedGenerator.createOrderLine(BigDecimal.valueOf(40)
                , BigDecimal.valueOf(20), BigDecimal.valueOf(20), BigDecimal.valueOf(0.21), 2,
                BigDecimal.valueOf(3.471), "124")));

        // act
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));
        await().until(() -> orderRepository.existsById(validOrderPlacedMessage.getOrderId()));
        orderRepository.findById(validOrderPlacedMessage.getOrderId())
            .ifPresentOrElse(order -> {
                    // assert
                    assertThat(order.getOrderId()).isEqualTo("OL12018251001");

                    assertThat(order.getOrderLines().size()).isEqualTo(2);

                    assertThat(order.getOrderLines().getFirst().getOrderLinePaidAmount()).isNotNull();
                    assertThat(order.getOrderLines().get(1).getOrderLinePaidAmount()).isNotNull();

                    //overallTotal
                    assertThat(order.getTotalPaidPrice().getOriginalGrossDiscountedTotal().compareTo(BigDecimal.valueOf(80)) == 0);
                    assertThat(order.getTotalPaidPrice().getGrossDiscountedTotal().compareTo(BigDecimal.valueOf(80)) == 0);
                    assertThat(order.getTotalPaidPrice().getGrossSubTotal().compareTo(BigDecimal.valueOf(76)) == 0);
                    assertThat(order.getTotalPaidPrice().getVat()).isEqualTo(BigDecimal.valueOf(13.87));


                    //orderEntryAmount

                    assertThat(order.getOrderLines().getFirst().getOrderLinePaidAmount().getUnitVAT()).isEqualTo(BigDecimal.valueOf(3.12)); // it was 3.12 but it is rounded to 2 decimals by mysql
                    assertThat(order.getOrderLines().get(1).getOrderLinePaidAmount().getUnitVAT()).isEqualTo(BigDecimal.valueOf(3.47)); // it was 3.47 but it is rounded to 2 decimals by mysql

                    assertThat(order.getOrderLines().getFirst().getOrderLinePaidAmount().getLineVAT()).isEqualTo(BigDecimal.valueOf(6.24)); // 3.12*2 = 6.24 then it is rounded to 2 decimals by mysql to  6.24
                    assertThat(order.getOrderLines().get(1).getOrderLinePaidAmount().getLineVAT()).isEqualTo(BigDecimal.valueOf(6.94)); // 3.47*2 = 6.94 then it is rounded to 2 decimals by mysql to  6.94

                    assertThat(order.getOrderLines().getFirst().getOrderLinePaidAmount().getGrossDiscountedTotal().compareTo(BigDecimal.valueOf(36)) == 0);
                    assertThat(order.getOrderLines().getFirst().getOrderLinePaidAmount().getGrossDiscountedUnitPrice().compareTo(BigDecimal.valueOf(18.0)) == 0);
                    assertThat(order.getOrderLines().getFirst().getOrderLinePaidAmount().getGrossRetailUnitPrice().compareTo(BigDecimal.valueOf(20.0)) == 0);
                    assertThat(order.getOrderLines().getFirst().getOrderLinePaidAmount().getOriginalGrossDiscountedTotal().compareTo(BigDecimal.valueOf(36)) == 0);
                    assertThat(order.getOrderLines().getFirst().getOrderLinePaidAmount().getUnitDiscount().compareTo(BigDecimal.valueOf(2.0)) == 0);

                    assertThat(order.getOrderLines().get(1).getOrderLinePaidAmount().getGrossDiscountedTotal().compareTo(BigDecimal.valueOf(40)) == 0);
                    assertThat(order.getOrderLines().get(1).getOrderLinePaidAmount().getGrossDiscountedUnitPrice().compareTo(BigDecimal.valueOf(20.0)) == 0);
                    assertThat(order.getOrderLines().get(1).getOrderLinePaidAmount().getGrossRetailUnitPrice().compareTo(BigDecimal.valueOf(20.0)) == 0);
                    assertThat(order.getOrderLines().get(1).getOrderLinePaidAmount().getOriginalGrossDiscountedTotal().compareTo(BigDecimal.valueOf(40)) == 0);
                    assertThat(order.getOrderLines().get(1).getOrderLinePaidAmount().getUnitDiscount().compareTo(BigDecimal.valueOf(0.0)) == 0);
                },
                Assertions::fail
            );
    }


    /**
     * Creating a separate test to test order charges when orders are coming from tradebyte
     */
    @Test
    void when_OrderPlaced_IsFrom_Tradebyte_Has_One_OrderLine_And_ShippingCharges() {
        // arrange
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createTradebyteOrderPlacedMessageWithShippingInformation();

        // act
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));

        await().untilAsserted(() -> {
            List<PaymentStatusUpdated> messages = paymentStatusUpdatedTestConsumer.getMessages();
            // assert
            checkPaymentStatusUpdatedIsConsumed(messages, "TB12345");

            orderRepository.findById(validOrderPlacedMessage.getOrderId())
                .ifPresentOrElse(order -> {
                        assertThat(order.getOrderId()).isEqualTo("TB12345");
                        assertThat(order.getPaymentStatus()).isEqualTo(PaymentState.OFFLINE);
                        assertThat(order.getPrevPaymentStatus()).isEqualTo(PaymentState.START);

                        assertThat(order.getPayments().size()).isEqualTo(1);
                        assertThat(order.getPayments().getFirst().getType()).isEqualTo(PaymentType.NONE);
                        assertThat(order.getPayments().getFirst().getAuthorisedAmount()).isEqualTo("24.90");

                        assertThat(order.getOrderLines().size()).isEqualTo(1);

                        assertThat(order.getOrderLines().getFirst().getOrderLinePaidAmount()).isNotNull();
                        // orderCharge
                        assertThat(order.getOrderCharges().size()).isEqualTo(1);
                        assertThat(order.getOrderCharges().getFirst().getName()).isEqualTo("Shipping");
                        assertThat(order.getOrderCharges().getFirst().getType()).isEqualTo(EntryType.SHIPMENT_FEE);
                        assertThat(order.getOrderCharges().getFirst().getOpenQty()).isEqualTo(1);
                        assertThat(order.getOrderCharges().getFirst().getOriginalQty()).isEqualTo(1);
                        assertThat(order.getOrderCharges().getFirst().getEan()).isEqualTo("STANDARD_SHIPPING");
                        assertThat(order.getOrderCharges().getFirst().getCancelled()).isEqualTo(false);
                        assertThat(order.getOrderCharges().getFirst().getRefunded()).isEqualTo(false);
                        assertThat(order.getOrderCharges().getFirst().getTaxRate()).isEqualTo(new BigDecimal("0.1900"));

                        //orderEntryAmount
                        assertThat(order.getOrderCharges().getFirst().getChargeTotal()).isNotNull();
                        assertThat(order.getOrderCharges().getFirst().getChargeTotal().getGrossDiscountedUnitPrice()).isEqualTo(new BigDecimal("3.95"));
                        assertThat(order.getOrderCharges().getFirst().getChargeTotal().getGrossRetailUnitPrice()).isEqualTo(new BigDecimal("3.95"));
                        assertThat(order.getOrderCharges().getFirst().getChargeTotal().getUnitDiscount()).isEqualTo(new BigDecimal("0.00"));
                        assertThat(order.getOrderCharges().getFirst().getChargeTotal().getGrossDiscountedTotal()).isEqualTo(new BigDecimal("3.95"));
                        assertThat(order.getOrderCharges().getFirst().getChargeTotal().getOriginalGrossDiscountedTotal()).isEqualTo(new BigDecimal("3.95"));
                        assertThat(order.getOrderCharges().getFirst().getChargeTotal().getUnitVAT()).isEqualTo(new BigDecimal("0.63"));
                        assertThat(order.getOrderCharges().getFirst().getChargeTotal().getLineVAT()).isEqualTo(new BigDecimal("0.63"));

                        //overallTotal
                        assertThat(order.getTotalPaidPrice().getOriginalGrossDiscountedTotal().compareTo(BigDecimal.valueOf(24.90))).isEqualTo(0);
                        assertThat(order.getTotalPaidPrice().getGrossDiscountedTotal().compareTo(BigDecimal.valueOf(24.90))).isEqualTo(0);
                        assertThat(order.getTotalPaidPrice().getGrossSubTotal()).isEqualTo(BigDecimal.valueOf(20.95));
                        assertThat(order.getTotalPaidPrice().getVat().compareTo(BigDecimal.valueOf(4.27))).isEqualTo(0);
                    },
                    Assertions::fail
                );
        });

    }

    /**
     * Creating a separate test to test order charges when orders are coming from storefront.
     */
    @Test
    void when_DmwvalidOrderPlaced_Has_One_OrderLine_And_ShippingCharges() {
        String method = "ADYEN_IDEAL";
        String subMethod = "ideal";
        String state = "AUTH";

        // arrange
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createBseOrderWithShippingCharge(method, subMethod, state);
        validOrderPlacedMessage.setOrderId("OL12018251001");
        validOrderPlacedMessage.setOrderLines(List.of(ValidOrderPlacedGenerator.createOrderLine(BigDecimal.valueOf(36)
            , BigDecimal.valueOf(18), BigDecimal.valueOf(20), BigDecimal.valueOf(0.21), 2,
            BigDecimal.valueOf(3.124), null)));

        // act
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));
        await().until(() -> orderRepository.existsById(validOrderPlacedMessage.getOrderId()));
        orderRepository.findById(validOrderPlacedMessage.getOrderId())
            .ifPresentOrElse(order -> {
                    // Assert
                    // Assert
                    assertThat(order.getPayments().size()).isEqualTo(1);

                    assertThat(order.getPayments().getFirst().getType()).isEqualTo(PaymentType.ADYEN_BANK);
                    assertThat(order.getPayments().getFirst().getProcessorId()).isEqualTo(ProcessorId.ADYEN);
                    assertThat(order.getPayments().getFirst().getSubMethodName()).isEqualTo(PaymentMethod.ADYEN_IDEAL);
                    assertThat(order.getPayments().getFirst().getSubMethod()).isEqualTo(subMethod);
                    assertThat(order.getPayments().getFirst().getPaymentReference()).isEqualTo("**********");

                    // order charge
                    assertThat(order.getOrderCharges().size()).isEqualTo(1);
                    assertThat(order.getOrderCharges().getFirst().getName()).isEqualTo("Shipping");
                    assertThat(order.getOrderCharges().getFirst().getType()).isEqualTo(EntryType.SHIPMENT_FEE);
                    assertThat(order.getOrderCharges().getFirst().getOpenQty()).isEqualTo(1);
                    assertThat(order.getOrderCharges().getFirst().getOriginalQty()).isEqualTo(1);
                    assertThat(order.getOrderCharges().getFirst().getEan()).isEqualTo("STANDARD_SHIPPING");
                    assertThat(order.getOrderCharges().getFirst().getCancelled()).isEqualTo(false);
                    assertThat(order.getOrderCharges().getFirst().getRefunded()).isEqualTo(false);
                    assertThat(order.getOrderCharges().getFirst().getTaxRate()).isEqualTo(new BigDecimal("0.1900"));

                    //orderEntryAmount
                    assertThat(order.getOrderCharges().getFirst().getChargeTotal()).isNotNull();
                    assertThat(order.getOrderCharges().getFirst().getChargeTotal().getGrossDiscountedUnitPrice()).isEqualTo(new BigDecimal("3.95"));
                    assertThat(order.getOrderCharges().getFirst().getChargeTotal().getGrossRetailUnitPrice()).isEqualTo(new BigDecimal("3.95"));
                    assertThat(order.getOrderCharges().getFirst().getChargeTotal().getUnitDiscount()).isEqualTo(new BigDecimal("0.00"));
                    assertThat(order.getOrderCharges().getFirst().getChargeTotal().getGrossDiscountedTotal()).isEqualTo(new BigDecimal("3.95"));
                    assertThat(order.getOrderCharges().getFirst().getChargeTotal().getOriginalGrossDiscountedTotal()).isEqualTo(new BigDecimal("3.95"));
                    assertThat(order.getOrderCharges().getFirst().getChargeTotal().getUnitVAT()).isEqualTo(new BigDecimal("0.63"));
                    assertThat(order.getOrderCharges().getFirst().getChargeTotal().getLineVAT()).isEqualTo(new BigDecimal("0.63"));

                    //overallTotal
                    assertThat(order.getTotalPaidPrice().getOriginalGrossDiscountedTotal()).isEqualByComparingTo(BigDecimal.valueOf(39.95));
                    assertThat(order.getTotalPaidPrice().getGrossDiscountedTotal()).isEqualByComparingTo(BigDecimal.valueOf(39.95));
                    assertThat(order.getTotalPaidPrice().getGrossSubTotal()).isEqualByComparingTo(BigDecimal.valueOf(36.00));
                    assertThat(order.getTotalPaidPrice().getVat()).isEqualByComparingTo(BigDecimal.valueOf(6.87));
                },
                Assertions::fail
            );
    }

    /**
     * Creating a separate test to test order charges when orders are coming from storefront.
     */
    @Test
    void orderPlacedConsumption_givenTradebyteOrderWithOfflinePayment_orderShouldBeSaved() {
        // arrange
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createTradebyteOrderPlacedMessageWithoutPaymentInformation();
        validOrderPlacedMessage.setOrderId("OL12018251002");
        validOrderPlacedMessage.setOrderLines(List.of(ValidOrderPlacedGenerator.createOrderLine(BigDecimal.valueOf(36)
            , BigDecimal.valueOf(18), BigDecimal.valueOf(20), BigDecimal.valueOf(0.21), 2,
            BigDecimal.valueOf(3.12), null)));
        validOrderPlacedMessage.withOfflinePayment(true);
        validOrderPlacedMessage.withStore(TRADEBYTE.name());

        // act
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));
        await().until(() -> orderRepository.existsById(validOrderPlacedMessage.getOrderId()));
        orderRepository.findById(validOrderPlacedMessage.getOrderId())
            .ifPresentOrElse(order -> {
                    // assert
                    assertThat(order.getPayments().size()).isOne();

                    PaymentInfo paymentInfo = order.getPayments().getFirst();
                    assertThat(paymentInfo.getType()).isEqualTo(PaymentType.NONE);
                    assertThat(paymentInfo.getProcessorId()).isEqualTo(ProcessorId.OFFLINE);
                    assertThat(paymentInfo.getSubMethodName()).isNull();
                    assertThat(paymentInfo.getPaymentReference()).isNull();

                    // order charge
                    assertThat(order.getOrderCharges()).hasSize(1);
                    OrderCharge orderCharge = order.getOrderCharges().getFirst();

                    assertThat(orderCharge.getName()).isEqualTo("Shipping");
                    assertThat(orderCharge.getType()).isEqualTo(EntryType.SHIPMENT_FEE);
                    assertThat(orderCharge.getOpenQty()).isOne();
                    assertThat(orderCharge.getOriginalQty()).isOne();
                    assertThat(orderCharge.getEan()).isEqualTo("STANDARD_SHIPPING");
                    assertThat(orderCharge.getCancelled()).isFalse();
                    assertThat(orderCharge.getRefunded()).isFalse();
                    assertThat(orderCharge.getTaxRate()).isEqualTo(new BigDecimal("0.1900"));

                    //orderEntryAmount
                    assertThat(orderCharge.getChargeTotal()).isNotNull();
                    assertThat(orderCharge.getChargeTotal().getGrossDiscountedUnitPrice()).isEqualTo(new BigDecimal("3.95"));
                    assertThat(orderCharge.getChargeTotal().getGrossRetailUnitPrice()).isEqualTo(new BigDecimal("3.95"));
                    assertThat(orderCharge.getChargeTotal().getUnitDiscount()).isEqualTo(new BigDecimal("0.00"));
                    assertThat(orderCharge.getChargeTotal().getGrossDiscountedTotal()).isEqualTo(new BigDecimal("3.95"));
                    assertThat(orderCharge.getChargeTotal().getOriginalGrossDiscountedTotal()).isEqualTo(new BigDecimal("3.95"));
                    assertThat(orderCharge.getChargeTotal().getUnitVAT()).isEqualTo(new BigDecimal("0.63"));
                    assertThat(orderCharge.getChargeTotal().getLineVAT()).isEqualTo(new BigDecimal("0.63"));

                    //overallTotal
                    assertThat(order.getTotalPaidPrice().getOriginalGrossDiscountedTotal()).isEqualByComparingTo(BigDecimal.valueOf(39.95));
                    assertThat(order.getTotalPaidPrice().getGrossDiscountedTotal()).isEqualByComparingTo(BigDecimal.valueOf(39.95));
                    assertThat(order.getTotalPaidPrice().getGrossSubTotal()).isEqualByComparingTo(BigDecimal.valueOf(36.00));
                    assertThat(order.getTotalPaidPrice().getVat()).isEqualByComparingTo(BigDecimal.valueOf(6.87));
                },
                Assertions::fail
            );


    }

    @Test
    void test_OrderPlacedConsumption_givenMixedPaymentsMultipleGiftCard_orderShouldBeSaved() throws IOException {
        // arrange
        final var validOrderPlaced = loadSampleKafkaMessage(
            "/messages/validOrderPlaced_mixPaymentsMultipleGiftCards.json", ValidOrderPlaced.class);

        // act
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        // assert
        await().untilAsserted(
            () -> {
                var order = orderRepository.findById(validOrderPlaced.getOrderId())
                    .orElseThrow(() -> new AssertionError("Order not found"));

                assertThat(order.getOrderId()).isEqualTo(validOrderPlaced.getOrderId());
                assertThat(order.getPayments()
                    .stream()
                    .filter(Objects::nonNull)
                    .count()).isEqualTo(2);
            });
    }

    @Test
    void test_OrderPlacedConsumption_givenVirtualOrder_orderShouldBeSaved() throws IOException {
        // arrange
        final var validOrderPlaced = loadSampleKafkaMessage(
            "/messages/validOrderPlaced_virtualOrder.json", ValidOrderPlaced.class);

        //act
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        // assert
        await().untilAsserted(
            () -> {
                var order = orderRepository.findById(validOrderPlaced.getOrderId())
                    .orElseThrow(() -> new AssertionError("Order not found"));

                assertThat(order.getOrderId()).isEqualTo(validOrderPlaced.getOrderId());
            });
    }

    @Test
    void process_orderPlacedWithPromotions_promotionsSaved() {
        final String promotionId = "PROMO1";
        final String campaignId = "CAMPAIGN1";
        final BigDecimal netPrice = BigDecimal.valueOf(10.00).setScale(2, RoundingMode.HALF_UP);
        final BigDecimal grossPrice = BigDecimal.valueOf(15.00).setScale(2, RoundingMode.HALF_UP);

        final String method = "ADYEN_IDEAL";
        final String subMethod = "ideal";
        final String state = "AUTH";

        // arrange
        ValidOrderPlaced validOrderPlacedMessage = ValidOrderPlacedGenerator.createBseOrderPlacedMessage_Adyen(method, subMethod, state);
        validOrderPlacedMessage.setOrderId("OL1201825217");
        validOrderPlacedMessage.setOrderPromotions(List.of(
            new OrderPromotion(promotionId, campaignId, grossPrice, netPrice))
        );

        // act
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlacedMessage));

        // assert
        await().untilAsserted(() -> {
            orderRepository.findById(validOrderPlacedMessage.getOrderId())
                .ifPresentOrElse(order -> {
                        assertThat(order.getPromotions())
                            .extracting("promotionId", "campaignId", "netPrice", "grossPrice")
                            .containsExactlyInAnyOrder(Tuple.tuple(promotionId, campaignId, netPrice, grossPrice));
                    },
                    Assertions::fail
                );
        });
    }

    private static void checkPaymentStatusUpdatedIsConsumed(List<PaymentStatusUpdated> messages, String orderId) {
        assertThat(messages)
            .usingRecursiveFieldByFieldElementComparator(PAYMENT_STATUS_UPDATED_RECURSIVE_COMPARISON_CONFIGURATION)
            .contains(
                new PaymentStatusUpdated()
                    .withOrderId(orderId)
                    .withPaymentState(PaymentStatusUpdated.PaymentState.AUTHORISED)
            );
    }
}