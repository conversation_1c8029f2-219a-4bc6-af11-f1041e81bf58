package com.bestseller.payment.integration.container;

import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.kafka.core.KafkaAdmin.NewTopics;

@TestConfiguration
public class KafkaTestConfiguration {

    private static final short REPLICATION_FACTOR = 1;
    private static final int NUM_PARTITIONS = 2;

    @Bean
    public NewTopics partitionedKafkaTopics() {
        return new NewTopics(
            new NewTopic("RefundStatusUpdated", NUM_PARTITIONS, REPLICATION_FACTOR)
        );
    }

}
