package com.bestseller.payment.integration.container;

import com.bestseller.payment.config.SecurityConfig;
import com.github.tomakehurst.wiremock.junit5.WireMockExtension;
import lombok.Getter;
import org.junit.jupiter.api.extension.RegisterExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.web.reactive.server.WebTestClient;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.containers.MySQLContainer;
import org.testcontainers.kafka.KafkaContainer;

import java.util.stream.Stream;

import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.wireMockConfig;
import static org.testcontainers.utility.DockerImageName.parse;

@Getter
@ActiveProfiles("test")
@Import({SecurityConfig.class, KafkaTestConfiguration.class})
public abstract class BaseTestContainer {

    @Autowired
    private WebTestClient httpClient;

    @SuppressWarnings("resource")
    private static final MySQLContainer<?> MYSQL_TEST_CONTAINER = new MySQLContainer<>(parse("mysql:8.0-debian"))
        .withReuse(true);

    private static final KafkaContainer KAFKA_TEST_CONTAINER = new KafkaContainer(parse("apache/kafka:latest"))
        .withReuse(true);

    @RegisterExtension
    protected static WireMockExtension wireMock = WireMockExtension
        .newInstance()
        .options(wireMockConfig().port(8085))  // Use fixed port 8085 otherwise wiremock does not work
        .build();


    static {
        Stream.of(MYSQL_TEST_CONTAINER, KAFKA_TEST_CONTAINER)
            .parallel()
            .forEach(GenericContainer::start);
    }

    @DynamicPropertySource
    static void setApplicationProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", MYSQL_TEST_CONTAINER::getJdbcUrl);
        registry.add("spring.datasource.password", MYSQL_TEST_CONTAINER::getPassword);
        registry.add("spring.datasource.username", MYSQL_TEST_CONTAINER::getUsername);

        registry.add("spring.flyway.url", MYSQL_TEST_CONTAINER::getJdbcUrl);
        registry.add("spring.flyway.password", MYSQL_TEST_CONTAINER::getPassword);
        registry.add("spring.flyway.user", MYSQL_TEST_CONTAINER::getUsername);

        registry.add("spring.kafka.bootstrap-servers", KAFKA_TEST_CONTAINER::getBootstrapServers);
        registry.add("spring.cloud.stream.kafka.binder.brokers", KAFKA_TEST_CONTAINER::getBootstrapServers);
        registry.add("gateway.services.fulfilment-core-service.url", wireMock::baseUrl);
    }

}