package com.bestseller.payment.integration.giftcardrefundresponse;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.giftCardRefundResponse.GiftCardRefundResponse;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequest.PaymentRefundRequest;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.RefundCreationRequested;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundStatusUpdated.RefundStatusUpdated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.adapter.repository.RefundRepository;
import com.bestseller.payment.core.domain.Refund;
import com.bestseller.payment.core.domain.enumeration.RefundType;
import com.bestseller.payment.integration.container.BaseTestContainer;
import com.bestseller.payment.messaging.consumer.PaymentRefundRequestTestConsumer;
import com.bestseller.payment.messaging.consumer.RefundStatusUpdatedTestConsumer;
import com.bestseller.payment.utils.GeneralUtils;
import com.logistics.statetransition.RefundState;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.testcontainers.shaded.org.awaitility.Awaitility;

import java.io.IOException;
import java.util.List;
import java.util.UUID;

import static com.bestseller.payment.utils.FileUtils.loadSampleKafkaMessage;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
class GiftCardRefundResponseIntegrationTest extends BaseTestContainer {
    private static final String ADYEN = "ADYEN";
    private static final String GIFT_CARD = "OPTICARD_PHASE_ONE";

    @Autowired
    private PaymentRefundRequestTestConsumer paymentRefundRequestTestConsumer;

    @Autowired
    private RefundStatusUpdatedTestConsumer refundStatusUpdatedTestConsumer;

    @Autowired
    private KafkaProducer<String, Object> kafkaProducer;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private RefundRepository refundRepository;

    @Value("${spring.cloud.stream.bindings.validOrderPlacedConsumer-in-0.destination}")
    private String validOrderPlacedTopic;

    @Value("${spring.cloud.stream.bindings.refundCreationRequestedConsumer-in-0.destination}")
    private String refundCreationRequestedTopic;

    @Value("${spring.cloud.stream.bindings.giftCardRefundResponseConsumer-in-0.destination}")
    private String giftCardRefundResponseTopic;

    @Test
    void refund_paidOnlyWithGiftCard_getSuccessMessage() throws IOException {
        String orderId = UUID.randomUUID().toString();
        prepareOrder(orderId, RefundType.GIFT_CARD);

        assertPaymentRefundRequestPublishedAndRefundIsInCorrectState(orderId, RefundType.GIFT_CARD);

        assertNoRefundStatusUpdatedPublished(orderId);

        // Act
        final Refund refund = orderRepository.findById(orderId).get().getRefunds().getFirst();
        final GiftCardRefundResponse giftCardRefundResponse = loadSampleKafkaMessage(
            "/messages/giftCardRefundResponse_success.json", GiftCardRefundResponse.class);
        giftCardRefundResponse.withCorrelationId(refund.getGiftCardCorrelationId().toString())
            .withOrderId(orderId)
            .withStatus(true);

        kafkaProducer.send(new ProducerRecord<>(giftCardRefundResponseTopic, giftCardRefundResponse));

        // Assert
        await().untilAsserted(() -> {
            Refund updatedRefund = refundRepository.findById(refund.getId()).get();
            assertNotNull(updatedRefund.getRefundId());
            assertEquals(RefundState.REFUND_SUCCESS, updatedRefund.getRefundState());
        });

        assertNoRefundStatusUpdatedPublished(orderId);
    }

    @Test
    void refund_paidOnlyWithGiftCard_getFailedMessage() throws IOException {
        String orderId = UUID.randomUUID().toString();
        prepareOrder(orderId, RefundType.GIFT_CARD);

        assertPaymentRefundRequestPublishedAndRefundIsInCorrectState(orderId, RefundType.GIFT_CARD);

        assertNoRefundStatusUpdatedPublished(orderId);

        // Act
        final Refund refund = orderRepository.findById(orderId).get().getRefunds().getFirst();
        final GiftCardRefundResponse giftCardRefundResponse = loadSampleKafkaMessage(
            "/messages/giftCardRefundResponse_success.json", GiftCardRefundResponse.class);
        giftCardRefundResponse.withCorrelationId(refund.getGiftCardCorrelationId().toString())
            .withOrderId(orderId)
            .withStatus(false);

        kafkaProducer.send(new ProducerRecord<>(giftCardRefundResponseTopic, giftCardRefundResponse));

        // Assert
        await().untilAsserted(() -> {
            Refund updatedRefund = refundRepository.findById(refund.getId()).get();
            assertNull(updatedRefund.getRefundId());
            assertEquals(RefundState.REFUND_FAILED, updatedRefund.getRefundState());
        });

        assertNoRefundStatusUpdatedPublished(orderId);
    }

    @Test
    void refund_paidWithGiftCardAndBankCard_getSuccessMessage() throws IOException {
        String orderId = UUID.randomUUID().toString();
        prepareOrder(orderId, RefundType.MIXED);

        assertPaymentRefundRequestPublishedAndRefundIsInCorrectState(orderId, RefundType.MIXED);

        assertRefundStatusUpdatedPublished(orderId);
        refundStatusUpdatedTestConsumer.clearMessages();

        // Act
        final Refund refund = orderRepository.findById(orderId).get().getRefunds().getFirst();
        final GiftCardRefundResponse giftCardRefundResponse = loadSampleKafkaMessage(
            "/messages/giftCardRefundResponse_success.json", GiftCardRefundResponse.class);
        giftCardRefundResponse.withCorrelationId(refund.getGiftCardCorrelationId().toString())
            .withOrderId(orderId)
            .withStatus(true);

        kafkaProducer.send(new ProducerRecord<>(giftCardRefundResponseTopic, giftCardRefundResponse));

        // Assert
        assertNoRefundStatusUpdatedPublished(orderId);

        await().untilAsserted(() -> {
            Refund updatedRefund = refundRepository.findById(refund.getId()).get();
            assertNull(updatedRefund.getRefundId());
            assertEquals(RefundState.REFUND_REQUESTING, updatedRefund.getRefundState());
        });
    }

    @Test
    void refund_paidWithGiftCardAndBankCard_getFailedMessage() throws IOException {
        String orderId = UUID.randomUUID().toString();
        prepareOrder(orderId, RefundType.MIXED);

        assertPaymentRefundRequestPublishedAndRefundIsInCorrectState(orderId, RefundType.MIXED);

        assertRefundStatusUpdatedPublished(orderId);
        refundStatusUpdatedTestConsumer.clearMessages();

        // Act
        final Refund refund = orderRepository.findById(orderId).get().getRefunds().getFirst();
        final GiftCardRefundResponse giftCardRefundResponse = loadSampleKafkaMessage(
            "/messages/giftCardRefundResponse_success.json", GiftCardRefundResponse.class);
        giftCardRefundResponse.withCorrelationId(refund.getGiftCardCorrelationId().toString())
            .withOrderId(orderId)
            .withStatus(false);

        kafkaProducer.send(new ProducerRecord<>(giftCardRefundResponseTopic, giftCardRefundResponse));

        // Assert
        assertNoRefundStatusUpdatedPublished(orderId);

        await().untilAsserted(() -> {
            Refund updatedRefund = refundRepository.findById(refund.getId()).get();
            assertNull(updatedRefund.getRefundId());
            assertEquals(RefundState.REFUND_REQUESTING, updatedRefund.getRefundState());
        });
    }

    private void prepareOrder(String orderId, RefundType refundType) throws IOException {
        String validOrderPlacedFilename = switch (refundType) {
            case GIFT_CARD -> "/messages/validOrderPlaced_giftCard.json";
            case MIXED -> "/messages/validOrderPlaced_adyenBankWithGiftCard.json";
            case BANK_TRANSACTION ->
                throw new IllegalArgumentException("Refund type BANK_TRANSACTION is not supported");
            case NONE -> throw new IllegalArgumentException("Refund type NONE is not supported");
        };
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(validOrderPlacedFilename, ValidOrderPlaced.class);
        validOrderPlaced.setOrderId(orderId);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));

        Awaitility.await().until(() -> orderRepository.existsById(orderId));

        // Act
        String refundCreationRequestFilename = switch (refundType) {
            case GIFT_CARD -> "/messages/refundCreationRequested_chargeReturnFee_refundShippingFee.json";
            case MIXED -> "/messages/refundCreationRequested_moreThanBankTransaction.json";
            case BANK_TRANSACTION ->
                throw new IllegalArgumentException("Refund type BANK_TRANSACTION is not supported");
            case NONE -> throw new IllegalArgumentException("Refund type NONE is not supported");
        };
        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            refundCreationRequestFilename,
            RefundCreationRequested.class);
        refundCreationRequested.setOrderId(orderId);
        refundCreationRequested.setRefundCreationRequestedId(UUID.randomUUID());
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        Awaitility.await().until(
            () -> !orderRepository.findById(orderId).get().getRefunds().isEmpty());
    }

    private void assertPaymentRefundRequestPublishedAndRefundIsInCorrectState(String orderId, RefundType refundType) {
        final Refund refund = orderRepository.findById(orderId).get().getRefunds().getFirst();
        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();

            if (refundType == RefundType.GIFT_CARD) {
                assertGiftCardRefund(orderId, messages, refund);
            } else if (refundType == RefundType.BANK_TRANSACTION) {
                assertBankTransactionRefund(orderId, messages, refund);
            } else if (refundType == RefundType.MIXED) {
                assertGiftCardRefund(orderId, messages, refund);
                assertBankTransactionRefund(orderId, messages, refund);
            }
        });

    }

    private static void assertBankTransactionRefund(String orderId, List<PaymentRefundRequest> messages, Refund refund) {
        messages.stream()
            .filter(msg -> msg.getOrderId().equals(orderId))
            .filter(msg -> msg.getProvider().equals(ADYEN))
            .findFirst()
            .ifPresentOrElse((message) -> {
                    assertEquals(refund.getId().toString(), message.getCorrelationId());
                    assertNull(refund.getRefundId());
                    assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
                },
                Assertions::fail
            );
    }

    private static void assertGiftCardRefund(String orderId, List<PaymentRefundRequest> messages, Refund refund) {
        messages.stream()
            .filter(msg -> msg.getOrderId().equals(orderId))
            .filter(msg -> msg.getProvider().equals(GIFT_CARD))
            .findFirst()
            .ifPresentOrElse((message) -> {
                    assertTrue(GeneralUtils.isUUID(message.getCorrelationId()));
                    assertEquals(refund.getGiftCardCorrelationId().toString(), message.getCorrelationId());
                    assertNull(refund.getRefundId());
                    assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
                },
                Assertions::fail
            );
    }

    private void assertNoRefundStatusUpdatedPublished(String orderId) {
        await().untilAsserted(() -> {
            final List<RefundStatusUpdated> messages = refundStatusUpdatedTestConsumer.getMessages();
            assertFalse(messages.stream().anyMatch(msg -> msg.getOrderId().equals(orderId)));
        });
    }

    private void assertRefundStatusUpdatedPublished(String orderId) {
        await().untilAsserted(() -> {
            final List<RefundStatusUpdated> messages = refundStatusUpdatedTestConsumer.getMessages();
            assertTrue(messages.stream().anyMatch(msg -> msg.getOrderId().equals(orderId)));
        });
    }
}
