package com.bestseller.payment.integration.config.queue;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import com.bestseller.payment.integration.container.BaseTestContainer;
import com.fasterxml.jackson.databind.DeserializationFeature;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
class PaymentStatusUpdatedProducerQueueConfigurationTest extends BaseTestContainer {

    @Autowired
    private TaskPayloadTransformer<PaymentStatusUpdated> paymentStatusUpdatedTaskPayloadTransformer;

    /**
     * Test that the {@link TaskPayloadTransformer} can transform a JSON payload into a {@link PaymentStatusUpdated} object
     * when the JSON payload does not contain a payload field, and the Jackson property
     * {@link DeserializationFeature#FAIL_ON_MISSING_EXTERNAL_TYPE_ID_PROPERTY} is set to false.
     */
    @Test
    void toObject_givenJsonWithoutPayload_objectIsCreated() {
        // arrange
        var jsonPayload = """
            {"orderId":"OL1204763336","paymentState":"CANCELLED"}
            """;

        // act
        var paymentStatusUpdated = paymentStatusUpdatedTaskPayloadTransformer.toObject(jsonPayload);

        // assert
        assertThat(paymentStatusUpdated)
            .usingRecursiveComparison()
            .isEqualTo(new PaymentStatusUpdated("OL1204763336", null,
                PaymentStatusUpdated.PaymentState.CANCELLED, null));
    }
}
