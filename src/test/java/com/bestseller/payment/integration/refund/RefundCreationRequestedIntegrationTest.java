package com.bestseller.payment.integration.refund;

import com.bestseller.dbqueue.core.api.Task;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderFinalized;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequest.PaymentRefundRequest;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementRequest.PaymentSettlementRequest;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementSucceeded.PaymentSettlementSucceeded;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.postPurchaseEventReceived.PostPurchaseEventReceived;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.ItemsToRefund;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.RefundCreationRequested;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundStatusUpdated.RefundStatusUpdated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.adapter.repository.RefundRepository;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.Refund;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.queue.RefundCreationRequestedQueueConsumer;
import com.bestseller.payment.integration.container.BaseTestContainer;
import com.bestseller.payment.messaging.consumer.PaymentRefundRequestTestConsumer;
import com.bestseller.payment.messaging.consumer.PaymentSettlementRequestTestConsumer;
import com.bestseller.payment.messaging.consumer.RefundStatusUpdatedTestConsumer;
import com.bestseller.springtest.DatabaseQueueTaskDao;
import com.logistics.statetransition.RefundState;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.assertj.core.api.AssertionsForInterfaceTypes;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static com.bestseller.payment.utils.FileUtils.loadSampleKafkaMessage;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
class RefundCreationRequestedIntegrationTest extends BaseTestContainer {
    private static final String KLARNA_PAYMENTS = "KLARNA_PAYMENTS";
    private static final String ADYEN = "ADYEN";
    private static final String GIFT_CARD = "OPTICARD_PHASE_ONE";
    private static final BigDecimal RETURN_FEE = BigDecimal.valueOf(-1.99);
    private static final BigDecimal SHIPPING_FEE = BigDecimal.valueOf(3.95);
    private static final BigDecimal BD_100 = BigDecimal.valueOf(100);
    private static final BigDecimal EAN_1_PRICE = BigDecimal.valueOf(4.99);
    private static final BigDecimal EAN_2_PRICE = BigDecimal.valueOf(4.99);
    private static final BigDecimal EAN_3_PRICE = BigDecimal.valueOf(24.99);
    private static final BigDecimal BANK_PAYMENT = BigDecimal.valueOf(28.90);
    private static final BigDecimal GIFTCARD_PAYMENT = BigDecimal.valueOf(20.00);

    @Autowired
    private PaymentRefundRequestTestConsumer paymentRefundRequestTestConsumer;

    @Autowired
    private PaymentSettlementRequestTestConsumer paymentSettlementRequestTestConsumer;

    @Autowired
    private RefundStatusUpdatedTestConsumer refundStatusUpdatedTestConsumer;

    @Autowired
    private KafkaProducer<String, Object> kafkaProducer;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private RefundRepository refundRepository;

    @Value("${spring.cloud.stream.bindings.validOrderPlacedConsumer-in-0.destination}")
    private String validOrderPlacedTopic;

    @Value("${spring.cloud.stream.bindings.orderFinalizedConsumer-in-0.destination}")
    private String orderFinalizedTopic;

    @Value("${spring.cloud.stream.bindings.refundCreationRequestedConsumer-in-0.destination}")
    private String refundCreationRequestedTopic;

    @Value("${spring.cloud.stream.bindings.paymentSettlementSucceededConsumer-in-0.destination}")
    private String paymentSettlementSucceededTopic;

    @Value("${spring.cloud.stream.bindings.postPurchaseEventReceivedConsumer-in-0.destination}")
    private String postPurchaseEventReceivedTopic;

    @Autowired
    private DatabaseQueueTaskDao databaseQueueTaskDao;

    @Autowired
    private RefundCreationRequestedQueueConsumer refundCreationRequestedQueueConsumer;

    @BeforeEach
    public void reset() {
        refundRepository.deleteAll();
        orderRepository.deleteAll();
        paymentRefundRequestTestConsumer.clearMessages();
        paymentSettlementRequestTestConsumer.clearMessages();
        refundStatusUpdatedTestConsumer.clearMessages();
    }

    @Test
    void testRefundCreationRequested_onlyAdyenBankPayment() throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenBank.json",
            ValidOrderPlaced.class);
        var orderId = UUID.randomUUID().toString();
        validOrderPlaced.setOrderId(orderId);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        int expectedRefundAmount = RETURN_FEE
            .add(SHIPPING_FEE)
            .add(EAN_1_PRICE)
            .multiply(BD_100)
            .intValue();

        // Act
        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_chargeReturnFee_refundShippingFee.json",
            RefundCreationRequested.class);
        refundCreationRequested.setOrderId(orderId);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            final PaymentRefundRequest message = messages.get(0);
            assertEquals(refundCreationRequested.getOrderId(), message.getOrderId());
            assertEquals(ADYEN, message.getProvider());
            assertEquals(expectedRefundAmount, message.getTotalAmount());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());

        await().until(() -> orderRepository.findById(validOrderPlaced.getOrderId())
            .orElseThrow()
            .getRefunds()
            .getFirst()
            .getRefundId() == null);
    }

    @Test
    void testRefundCreationRequested_onlyAdyenBankPayment_belowCountryThreshold_withReturnFee() throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenBank_countryMT_belowThreshold.json",
            ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));
        final BigDecimal MT_RETURN_FEE = BigDecimal.valueOf(-2.99);
        int expectedRefundAmount = MT_RETURN_FEE
            .add(SHIPPING_FEE)
            .add(EAN_1_PRICE)
            .multiply(BD_100)
            .intValue();

        // Act
        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_chargeReturnFee_refundShippingFee.json",
            RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            final PaymentRefundRequest message = messages.get(0);
            assertEquals(refundCreationRequested.getOrderId(), message.getOrderId());
            assertEquals(ADYEN, message.getProvider());
            assertEquals(expectedRefundAmount, message.getTotalAmount());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());

        await().until(() -> orderRepository.findById(validOrderPlaced.getOrderId())
            .orElseThrow()
            .getRefunds()
            .getFirst()
            .getRefundId() == null);
    }

    @Test
    void testRefundCreationRequested_onlyAdyenCardPayment_paymentSettled_allDispatched() throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenCard.json",
            ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json",
            OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        final PaymentSettlementSucceeded paymentSettlementSucceeded = loadSampleKafkaMessage(
            "/messages/paymentSettlementSucceeded_adyen.json", PaymentSettlementSucceeded.class);
        kafkaProducer.send(new ProducerRecord<>(paymentSettlementSucceededTopic, paymentSettlementSucceeded));

        await().until(() -> orderRepository
            .findById(validOrderPlaced.getOrderId())
            .orElseThrow()
            .getPaymentStatus().equals(PaymentState.SETTLED));

        int expectedRefundAmount = RETURN_FEE
            .add(SHIPPING_FEE)
            .add(EAN_1_PRICE)
            .multiply(BD_100)
            .intValue();
        // Act
        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_chargeReturnFee_refundShippingFee.json",
            RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            final PaymentRefundRequest message = messages.get(0);
            assertEquals(refundCreationRequested.getOrderId(), message.getOrderId());
            assertEquals(ADYEN, message.getProvider());
            assertEquals(expectedRefundAmount, message.getTotalAmount());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());
    }

    @Test
    void testRefundCreationRequested_onlyAdyenCardPayment_paymentNotSettledYet_allDispatched() throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenCard.json",
            ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json",
            OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        // Act
        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_chargeReturnFee_refundShippingFee.json",
            RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        assertNoMessageIsProducedAndIncomingMessageIsEnqueued(refundCreationRequested);
    }

    @Test
    void testRefundCreationRequested_onlyKlarnaPayment_paymentSettled_allDispatched() throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_klarna.json",
            ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json",
            OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        final PaymentSettlementSucceeded paymentSettlementSucceeded = loadSampleKafkaMessage(
            "/messages/paymentSettlementSucceeded_klarna.json", PaymentSettlementSucceeded.class);
        kafkaProducer.send(new ProducerRecord<>(paymentSettlementSucceededTopic, paymentSettlementSucceeded));

        await().until(() -> orderRepository
            .findById(validOrderPlaced.getOrderId())
            .orElseThrow()
            .getPaymentStatus().equals(PaymentState.SETTLED));

        int expectedRefundAmount = RETURN_FEE
            .add(SHIPPING_FEE)
            .add(EAN_1_PRICE)
            .multiply(BD_100)
            .intValue();

        // Act
        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_chargeReturnFee_refundShippingFee.json",
            RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            final PaymentRefundRequest message = messages.get(0);
            assertEquals(refundCreationRequested.getOrderId(), message.getOrderId());
            assertEquals(KLARNA_PAYMENTS, message.getProvider());
            assertEquals(expectedRefundAmount, message.getTotalAmount());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());
    }

    @Test
    void testRefundCreationRequested_onlyKlarnaPayment_paymentNotSettledYet_allDispatched() throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_klarna.json",
            ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json",
            OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        // Act
        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_chargeReturnFee_refundShippingFee.json",
            RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        assertNoMessageIsProducedAndIncomingMessageIsEnqueued(refundCreationRequested);
    }

    @Test
    void testRefundCreationRequested_onlyGiftCard() throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_giftCard.json",
            ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        int expectedRefundAmount = RETURN_FEE
            .add(SHIPPING_FEE)
            .add(EAN_1_PRICE)
            .multiply(BD_100)
            .intValue();
        // Act
        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_chargeReturnFee_refundShippingFee.json",
            RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            final PaymentRefundRequest message = messages.get(0);
            assertEquals(refundCreationRequested.getOrderId(), message.getOrderId());
            assertEquals(GIFT_CARD, message.getProvider());
            assertEquals(expectedRefundAmount, message.getTotalAmount());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());

        });

        assertRefundStatusUpdatedMessagesNotProduced();
    }

    @Test
    void testRefundCreationRequested_adyenBankWithGiftCardPayment_moreThanBankTransaction() throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages/validOrderPlaced_adyenBankWithGiftCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        // Act
        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_moreThanBankTransaction.json", RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(2, messages.size());

            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(ADYEN))
                    .filter(m -> m.getTotalAmount() == BANK_PAYMENT.multiply(BD_100).intValue())
                    .count());
            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .filter(m -> m.getTotalAmount() == GIFTCARD_PAYMENT.multiply(BD_100).intValue())
                    .count());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());
    }

    @Test
    void testRefundCreationRequested_adyenBankWithGiftCardPayment_lessThanBankTransaction() throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages/validOrderPlaced_adyenBankWithGiftCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        int expectedRefundAmountViaBankInCents = RETURN_FEE
            .add(EAN_1_PRICE)
            .multiply(BD_100)
            .intValue();

        // Act
        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_lessThanBankTransaction.json", RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(ADYEN))
                    .filter(m -> m.getTotalAmount() == expectedRefundAmountViaBankInCents)
                    .count());
            assertEquals(
                0,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .count());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());
    }

    @Test
    void testRefundCreationRequested_adyenCardWithGiftCardPayment_paymentNotSettledYet_moreThanBankTransaction_allDispatched()
        throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages/validOrderPlaced_adyenCardWithGiftCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json",
            OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        // Act
        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_moreThanBankTransaction.json", RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        assertNoMessageIsProducedAndIncomingMessageIsEnqueued(refundCreationRequested);
    }

    @Test
    void testRefundCreationRequested_adyenCardWithGiftCardPayment_paymentSettled_moreThanBankTransaction_allDispatched()
        throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages/validOrderPlaced_adyenCardWithGiftCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json",
            OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        final PaymentSettlementSucceeded paymentSettlementSucceeded = loadSampleKafkaMessage(
            "/messages/paymentSettlementSucceeded_adyen.json", PaymentSettlementSucceeded.class);
        kafkaProducer.send(new ProducerRecord<>(paymentSettlementSucceededTopic, paymentSettlementSucceeded));

        await().until(() -> orderRepository
            .findById(validOrderPlaced.getOrderId())
            .orElseThrow()
            .getPaymentStatus().equals(PaymentState.SETTLED));

        // Act
        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_moreThanBankTransaction.json", RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(2, messages.size());

            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(ADYEN))
                    .filter(m -> m.getTotalAmount() == BANK_PAYMENT.multiply(BD_100).intValue())
                    .count());
            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .filter(m -> m.getTotalAmount() == GIFTCARD_PAYMENT.multiply(BD_100).intValue())
                    .count());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());
    }

    @Test
    void testRefundCreationRequested_adyenCardWithGiftCardPayment_someCancelled_lessThanBankAuthorisedAmount_paymentSettled_someReturned_moreThanBankAuthorisedAmount()
        throws IOException {
        // Arrange
        String OrderId = UUID.randomUUID().toString();
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages/validOrderPlaced_adyenCardWithGiftCard.json", ValidOrderPlaced.class);

        validOrderPlaced.setOrderId(OrderId);

        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage(
            "/messages/orderFinalized_one_cancelled_with_quantity_one.json", OrderFinalized.class);

        orderFinalized.setOrderId(OrderId);

        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        final PaymentSettlementSucceeded paymentSettlementSucceeded = loadSampleKafkaMessage(
            "/messages/paymentSettlementSucceeded_adyen.json", PaymentSettlementSucceeded.class);

        paymentSettlementSucceeded.setCorrelationId(OrderId);

        kafkaProducer.send(new ProducerRecord<>(paymentSettlementSucceededTopic, paymentSettlementSucceeded));

        await().until(() -> orderRepository
            .findById(validOrderPlaced.getOrderId())
            .orElseThrow()
            .getPaymentStatus().equals(PaymentState.SETTLED));

        BigDecimal cancelledAmount = EAN_3_PRICE
            .multiply(BD_100);
        BigDecimal totalRefundAmount = EAN_1_PRICE
            .multiply(BD_100);

        BigDecimal remainingBankAmount = BANK_PAYMENT
            .multiply(BD_100)
            .subtract(cancelledAmount);

        // Act
        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_lessThanBankTransaction_noReturnFee.json",
            RefundCreationRequested.class);

        refundCreationRequested.setOrderId(OrderId);

        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(2, messages.size());

            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(ADYEN))
                    .filter(m -> m.getTotalAmount() == remainingBankAmount.intValue())
                    .count());
            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .filter(m -> m
                        .getTotalAmount() == (totalRefundAmount.subtract(remainingBankAmount).intValue()))
                    .count());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());
    }

    @Test
    void testRefundCreationRequested_adyenCardWithGiftCardPayment_paymentNotSettledYet_lessThanBankTransaction_allDispatched()
        throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages/validOrderPlaced_adyenCardWithGiftCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json",
            OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        // Act
        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_lessThanBankTransaction.json", RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        assertNoMessageIsProducedAndIncomingMessageIsEnqueued(refundCreationRequested);
    }

    @Test
    void testRefundCreationRequested_adyenCardWithGiftCardPayment_paymentSettled_lessThanBankTransaction_allDispatched()
        throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages/validOrderPlaced_adyenCardWithGiftCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json",
            OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        final PaymentSettlementSucceeded paymentSettlementSucceeded = loadSampleKafkaMessage(
            "/messages/paymentSettlementSucceeded_adyen.json", PaymentSettlementSucceeded.class);
        kafkaProducer.send(new ProducerRecord<>(paymentSettlementSucceededTopic, paymentSettlementSucceeded));

        await().until(() -> orderRepository
            .findById(validOrderPlaced.getOrderId())
            .orElseThrow()
            .getPaymentStatus().equals(PaymentState.SETTLED));

        int expectedRefundAmountViaBankInCents = RETURN_FEE
            .add(EAN_1_PRICE)
            .multiply(BD_100)
            .intValue();
        // Act
        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_lessThanBankTransaction.json", RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(ADYEN))
                    .filter(m -> m.getTotalAmount() == expectedRefundAmountViaBankInCents)
                    .count());
            assertEquals(
                0,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .count());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());
    }

    @Test
    void testRefundCreationRequested_adyenCardWithGiftCardPayment_someCancelled_multipleRefunds() throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages/validOrderPlaced_adyenCardWithGiftCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        // Act
        final OrderFinalized orderFinalized = loadSampleKafkaMessage(
            "/messages/orderFinalized_partially_cancelled_moreThanBankTransaction.json", OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        await().untilAsserted(() -> {
            BigDecimal totalRefundAmount = EAN_1_PRICE
                .add(EAN_1_PRICE)
                .add(EAN_3_PRICE)
                .subtract(BANK_PAYMENT)
                .multiply(BD_100);

            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());

            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(validOrderPlaced.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .filter(m -> m.getTotalAmount() == totalRefundAmount.intValue())
                    .count());

            paymentRefundRequestTestConsumer.clearMessages();
        });

        assertRefundStatusUpdatedMessagesNotProduced();

        // clean up
        refundStatusUpdatedTestConsumer.clearMessages();

        // Act
        RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_chargeReturnFee_notRefundShippingFee.json",
            RefundCreationRequested.class);
        refundCreationRequested.setItemsToRefund(List.of(
            new ItemsToRefund("ean2", 1)));
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            BigDecimal totalRefundAmount = EAN_2_PRICE
                .add(RETURN_FEE)
                .multiply(BD_100);

            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            assertEquals(
                0,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(ADYEN))
                    .count());
            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .filter(m -> m.getTotalAmount() == totalRefundAmount.intValue())
                    .count());

            assertEquals(2, order.getRefunds().size());

            Refund refund = order.getRefunds().getFirst();
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesNotProduced();

        // Clean up
        paymentRefundRequestTestConsumer.clearMessages();
        refundStatusUpdatedTestConsumer.clearMessages();

        // Act
        refundCreationRequested.setRefundCreationRequestedId(UUID.randomUUID());
        refundCreationRequested.setChargeReturnFee(false);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            BigDecimal totalRefundAmount = EAN_2_PRICE
                .multiply(BD_100);

            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            assertEquals(
                0,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(ADYEN))
                    .count());
            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .filter(m -> m.getTotalAmount() == totalRefundAmount.intValue())
                    .count());

            assertEquals(3, order.getRefunds().size());

            Refund refund = order.getRefunds()
                .stream()
                .sorted((r1, r2) -> r2.getId() - r1.getId())
                .toList()
                .getFirst();
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesNotProduced();
    }

    @Test
    void testRefundCreationRequested_klarnaWithGiftCardPayment_paymentNotSettledYet_moreThanBankTransaction_allDispatched()
        throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages/validOrderPlaced_klarnaWithGiftCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json",
            OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        // Act
        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_moreThanBankTransaction.json", RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        assertNoMessageIsProducedAndIncomingMessageIsEnqueued(refundCreationRequested);
    }

    @Test
    void testRefundCreationRequested_klarnaWithGiftCardPayment_paymentSettled_moreThanBankTransaction_allDispatched()
        throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages/validOrderPlaced_klarnaWithGiftCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json",
            OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        final PaymentSettlementSucceeded paymentSettlementSucceeded = loadSampleKafkaMessage(
            "/messages/paymentSettlementSucceeded_adyen.json", PaymentSettlementSucceeded.class);
        kafkaProducer.send(new ProducerRecord<>(paymentSettlementSucceededTopic, paymentSettlementSucceeded));

        await().until(() -> orderRepository
            .findById(validOrderPlaced.getOrderId())
            .orElseThrow()
            .getPaymentStatus().equals(PaymentState.SETTLED));

        // Act
        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_moreThanBankTransaction.json", RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(2, messages.size());

            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(KLARNA_PAYMENTS))
                    .filter(m -> m.getTotalAmount() == BANK_PAYMENT.multiply(BD_100).intValue())
                    .count());
            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .filter(m -> m.getTotalAmount() == GIFTCARD_PAYMENT.multiply(BD_100).intValue())
                    .count());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());
    }

    @Test
    void testRefundCreationRequested_klarnaWithGiftCardPayment_paymentNotSettledYet_lessThanBankTransaction_allDispatched()
        throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages/validOrderPlaced_klarnaWithGiftCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json",
            OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        // Act
        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_lessThanBankTransaction.json", RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        assertNoMessageIsProducedAndIncomingMessageIsEnqueued(refundCreationRequested);
    }

    @Test
    void testRefundCreationRequested_klarnaWithGiftCardPayment_paymentSettled_lessThanBankTransaction_allDispatched()
        throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages/validOrderPlaced_klarnaWithGiftCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json",
            OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        final PaymentSettlementSucceeded paymentSettlementSucceeded = loadSampleKafkaMessage(
            "/messages/paymentSettlementSucceeded_adyen.json", PaymentSettlementSucceeded.class);
        kafkaProducer.send(new ProducerRecord<>(paymentSettlementSucceededTopic, paymentSettlementSucceeded));

        await().until(() -> orderRepository
            .findById(validOrderPlaced.getOrderId())
            .orElseThrow()
            .getPaymentStatus().equals(PaymentState.SETTLED));

        int expectedRefundAmountViaBankInCents = RETURN_FEE
            .add(EAN_1_PRICE)
            .multiply(BD_100)
            .intValue();
        // Act
        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_lessThanBankTransaction.json", RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(KLARNA_PAYMENTS))
                    .filter(m -> m.getTotalAmount() == expectedRefundAmountViaBankInCents)
                    .count());
            assertEquals(
                0,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .count());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());
    }

    @Test
    void testRefundCreationRequested_klarnaWithGiftCardPayment_someCancelled_moreThanBankTransaction_someReturned()
        throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages/validOrderPlaced_klarnaWithGiftCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage(
            "/messages/orderFinalized_cancelled_moreThanBankTransaction.json", OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        await().until(() -> orderRepository
            .findById(validOrderPlaced.getOrderId())
            .orElseThrow()
            .getPaymentStatus().equals(PaymentState.CANCELLED));

        final int expectedCancellationRefundAmountViaGiftCardInCents = EAN_2_PRICE
            .add(EAN_2_PRICE)
            .add(EAN_3_PRICE)
            .subtract(BANK_PAYMENT)
            .multiply(BD_100)
            .intValue();

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();

            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(validOrderPlaced.getOrderId()))
                    .filter(m -> m.getProvider().equals(KLARNA_PAYMENTS))
                    .filter(m -> m.getTotalAmount() == 0)
                    .count());
        });

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();

            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(validOrderPlaced.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .filter(m -> m.getTotalAmount() == expectedCancellationRefundAmountViaGiftCardInCents)
                    .count());

            paymentRefundRequestTestConsumer.clearMessages();
        });

        final int expectedRefundAmountViaGiftCardInCents = RETURN_FEE
            .add(EAN_1_PRICE)
            .multiply(BD_100)
            .intValue();
        // Act
        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_lessThanBankTransaction.json", RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .filter(m -> m.getTotalAmount() == expectedRefundAmountViaGiftCardInCents)
                    .count());

            assertEquals(2, order.getRefunds().size());
        });

        assertRefundStatusUpdatedMessagesNotProduced();
    }

    @Test
    void testRefundCreationRequested_100PercentDiscount_chargeReturnFee_notRefundShippingFee() throws IOException {
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages/validOrderPlaced_100_percent_discount.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_chargeReturnFee_notRefundShippingFee.json",
            RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(0, messages.size());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_SUCCESS, refund.getRefundState());
        });
    }

    @Test
    void testRefundCreationRequested_100PercentDiscount_chargeReturnFee_refundShippingFee() throws IOException {
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages/validOrderPlaced_100_percent_discount.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_chargeReturnFee_refundShippingFee.json",
            RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        var amountToRefund = RETURN_FEE
            .add(SHIPPING_FEE)
            .multiply(BD_100)
            .intValue();

        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            assertEquals(1, order.getRefunds().size());
            assertEquals(amountToRefund, messages.get(0).getTotalAmount());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });
    }

    @Test
    void testRefundCreationRequested_adyenCardWithGiftCardPayment_multipleRefunds() throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages/validOrderPlaced_adyenCardWithGiftCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json",
            OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        final PaymentSettlementSucceeded paymentSettlementSucceeded = loadSampleKafkaMessage(
            "/messages/paymentSettlementSucceeded_adyen.json", PaymentSettlementSucceeded.class);
        kafkaProducer.send(new ProducerRecord<>(paymentSettlementSucceededTopic, paymentSettlementSucceeded));

        await().until(() -> orderRepository
            .findById(validOrderPlaced.getOrderId())
            .orElseThrow()
            .getPaymentStatus().equals(PaymentState.SETTLED));

        // Act
        RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_chargeReturnFee_notRefundShippingFee.json",
            RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            BigDecimal totalRefundAmount = EAN_1_PRICE
                .add(RETURN_FEE)
                .multiply(BD_100);

            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(ADYEN))
                    .filter(m -> m.getTotalAmount() == totalRefundAmount.intValue())
                    .count());
            assertEquals(
                0,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .count());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().getFirst();
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());

        // Clean up
        paymentRefundRequestTestConsumer.clearMessages();
        refundStatusUpdatedTestConsumer.clearMessages();

        // Act
        refundCreationRequested.setRefundCreationRequestedId(UUID.randomUUID());
        refundCreationRequested.setChargeReturnFee(false);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            BigDecimal totalRefundAmount = EAN_1_PRICE
                .multiply(BD_100);

            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(ADYEN))
                    .filter(m -> m.getTotalAmount() == totalRefundAmount.intValue())
                    .count());
            assertEquals(
                0,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .count());

            assertEquals(2, order.getRefunds().size());

            Refund refund = order.getRefunds()
                .stream()
                .sorted((r1, r2) -> r2.getId() - r1.getId())
                .toList()
                .getFirst();
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());

        // Clean up
        paymentRefundRequestTestConsumer.clearMessages();
        refundStatusUpdatedTestConsumer.clearMessages();

        // Act
        refundCreationRequested.setRefundCreationRequestedId(UUID.randomUUID());
        refundCreationRequested.setChargeReturnFee(false);
        refundCreationRequested.setItemsToRefund(List.of(
            new ItemsToRefund("ean3", 1)));
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            BigDecimal alreadyRefundedAmount = EAN_1_PRICE
                .add(EAN_1_PRICE)
                .add(RETURN_FEE)
                .multiply(BD_100);
            BigDecimal totalRefundAmount = EAN_3_PRICE
                .multiply(BD_100);

            BigDecimal bankTotalRefund = BANK_PAYMENT
                .multiply(BD_100)
                .subtract(alreadyRefundedAmount);

            BigDecimal giftCardTotalRefund = totalRefundAmount
                .subtract(bankTotalRefund);

            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(2, messages.size());

            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(ADYEN))
                    .filter(m -> m.getTotalAmount() == bankTotalRefund.intValue())
                    .count());
            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .filter(m -> m.getTotalAmount() == giftCardTotalRefund.intValue())
                    .count());

            assertEquals(3, order.getRefunds().size());

            Refund refund = order.getRefunds()
                .stream()
                .sorted((r1, r2) -> r2.getId() - r1.getId())
                .toList()
                .getFirst();
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });
    }

    @Test
    void testRefundCreationRequested_klarnaWithGiftCardPayment_multipleRefunds() throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages/validOrderPlaced_klarnaWithGiftCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json",
            OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        final PaymentSettlementSucceeded paymentSettlementSucceeded = loadSampleKafkaMessage(
            "/messages/paymentSettlementSucceeded_adyen.json", PaymentSettlementSucceeded.class);
        kafkaProducer.send(new ProducerRecord<>(paymentSettlementSucceededTopic, paymentSettlementSucceeded));

        await().until(() -> orderRepository
            .findById(validOrderPlaced.getOrderId())
            .orElseThrow()
            .getPaymentStatus().equals(PaymentState.SETTLED));

        // Act
        RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_chargeReturnFee_notRefundShippingFee.json",
            RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            BigDecimal totalRefundAmount = EAN_1_PRICE
                .add(RETURN_FEE)
                .multiply(BD_100);

            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(KLARNA_PAYMENTS))
                    .filter(m -> m.getTotalAmount() == totalRefundAmount.intValue())
                    .count());
            assertEquals(
                0,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .count());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().getFirst();
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());

        // Clean up
        paymentRefundRequestTestConsumer.clearMessages();
        refundStatusUpdatedTestConsumer.clearMessages();

        // Act
        refundCreationRequested.setRefundCreationRequestedId(UUID.randomUUID());
        refundCreationRequested.setChargeReturnFee(false);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            BigDecimal totalRefundAmount = EAN_1_PRICE
                .multiply(BD_100);

            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(KLARNA_PAYMENTS))
                    .filter(m -> m.getTotalAmount() == totalRefundAmount.intValue())
                    .count());
            assertEquals(
                0,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .count());

            assertEquals(2, order.getRefunds().size());

            Refund refund = order.getRefunds()
                .stream()
                .sorted((r1, r2) -> r2.getId() - r1.getId())
                .toList()
                .getFirst();
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());

        // Clean up
        paymentRefundRequestTestConsumer.clearMessages();
        refundStatusUpdatedTestConsumer.clearMessages();

        // Act
        refundCreationRequested.setRefundCreationRequestedId(UUID.randomUUID());
        refundCreationRequested.setChargeReturnFee(false);
        refundCreationRequested.setItemsToRefund(List.of(
            new ItemsToRefund("ean3", 1)));
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            BigDecimal alreadyRefundedAmount = EAN_1_PRICE
                .add(EAN_1_PRICE)
                .add(RETURN_FEE)
                .multiply(BD_100);
            BigDecimal totalRefundAmount = EAN_3_PRICE
                .multiply(BD_100);

            BigDecimal bankTotalRefund = BANK_PAYMENT
                .multiply(BD_100)
                .subtract(alreadyRefundedAmount);

            BigDecimal giftCardTotalRefund = totalRefundAmount
                .subtract(bankTotalRefund);

            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(2, messages.size());

            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(KLARNA_PAYMENTS))
                    .filter(m -> m.getTotalAmount() == bankTotalRefund.intValue())
                    .count());
            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .filter(m -> m.getTotalAmount() == giftCardTotalRefund.intValue())
                    .count());

            assertEquals(3, order.getRefunds().size());

            Refund refund = order.getRefunds()
                .stream()
                .sorted((r1, r2) -> r2.getId() - r1.getId())
                .toList()
                .getFirst();
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });
    }

    @Test
    void testRefundCreationRequested_adyenBankWithGiftCardPayment_multipleRefunds()
        throws IOException, InterruptedException {
        // Arrange
        String orderId = UUID.randomUUID().toString();
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages/validOrderPlaced_adyenBankWithGiftCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json",
            OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        // There is no other way to wait for the message to be processed. If we add
        // isFinalized to the order, we need to change the code.
        Thread.sleep(500);

        // Act
        RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_chargeReturnFee_notRefundShippingFee.json",
            RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            BigDecimal totalRefundAmount = EAN_1_PRICE
                .add(RETURN_FEE)
                .multiply(BD_100);

            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(ADYEN))
                    .filter(m -> m.getTotalAmount() == totalRefundAmount.intValue())
                    .count());
            assertEquals(
                0,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .count());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().getFirst();
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());

        // Clean up
        paymentRefundRequestTestConsumer.clearMessages();
        refundStatusUpdatedTestConsumer.clearMessages();

        // Act
        refundCreationRequested.setRefundCreationRequestedId(UUID.randomUUID());
        refundCreationRequested.setChargeReturnFee(false);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            BigDecimal totalRefundAmount = EAN_1_PRICE
                .multiply(BD_100);

            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(ADYEN))
                    .filter(m -> m.getTotalAmount() == totalRefundAmount.intValue())
                    .count());
            assertEquals(
                0,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .count());

            assertEquals(2, order.getRefunds().size());

            Refund refund = order.getRefunds()
                .stream()
                .sorted((r1, r2) -> r2.getId() - r1.getId())
                .toList()
                .getFirst();
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());

        // Clean up
        paymentRefundRequestTestConsumer.clearMessages();
        refundStatusUpdatedTestConsumer.clearMessages();

        // Act
        refundCreationRequested.setRefundCreationRequestedId(UUID.randomUUID());
        refundCreationRequested.setChargeReturnFee(false);
        refundCreationRequested.setItemsToRefund(List.of(
            new ItemsToRefund("ean3", 1)));
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            BigDecimal alreadyRefundedAmount = EAN_1_PRICE
                .add(EAN_1_PRICE)
                .add(RETURN_FEE)
                .multiply(BD_100);
            BigDecimal totalRefundAmount = EAN_3_PRICE
                .multiply(BD_100);

            BigDecimal bankTotalRefund = BANK_PAYMENT
                .multiply(BD_100)
                .subtract(alreadyRefundedAmount);

            BigDecimal giftCardTotalRefund = totalRefundAmount
                .subtract(bankTotalRefund);

            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(2, messages.size());

            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(ADYEN))
                    .filter(m -> m.getTotalAmount() == bankTotalRefund.intValue())
                    .count());
            assertEquals(
                1,
                messages
                    .stream()
                    .filter(m -> m.getOrderId().equals(refundCreationRequested.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .filter(m -> m.getTotalAmount() == giftCardTotalRefund.intValue())
                    .count());

            assertEquals(3, order.getRefunds().size());

            Refund refund = order.getRefunds()
                .stream()
                .sorted((r1, r2) -> r2.getId() - r1.getId())
                .toList()
                .getFirst();
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });
    }

    @Test
    void testRefundCreationRequested_vatIdIsNull() throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenBank.json",
            ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        int expectedRefundAmount = RETURN_FEE
            .add(SHIPPING_FEE)
            .add(EAN_1_PRICE)
            .multiply(BD_100)
            .intValue();

        // Act
        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_chargeReturnFee_refundShippingFee.json",
            RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            final PaymentRefundRequest message = messages.get(0);
            assertEquals(refundCreationRequested.getOrderId(), message.getOrderId());
            assertEquals(ADYEN, message.getProvider());
            assertEquals(expectedRefundAmount, message.getTotalAmount());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
            assertNull(refund.getRefundId());
        });
    }

    @Test
    void testRefundCreationRequested_givenRefundChoiceLessThanBankPayment_RefundWithGiftCardAndBank() throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenBank.json",
            ValidOrderPlaced.class);
        var orderId = UUID.randomUUID().toString();
        validOrderPlaced.setOrderId(orderId);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        int expectedGiftCardRefundAmount = EAN_1_PRICE
            .multiply(BD_100)
            .intValue();

        int expectedBankRefundAmount = RETURN_FEE
            .add(SHIPPING_FEE)
            .add(EAN_1_PRICE)
            .multiply(BD_100)
            .intValue() - expectedGiftCardRefundAmount;


        final PostPurchaseEventReceived postPurchaseEventReceived = loadSampleKafkaMessage(
            "/messages/postPurchase/postPurchaseEventReceived.json", PostPurchaseEventReceived.class);
        postPurchaseEventReceived.setOrderId(orderId);
        kafkaProducer.send(new ProducerRecord<>(postPurchaseEventReceivedTopic, postPurchaseEventReceived));

        await().until(() -> orderRepository.findById(validOrderPlaced.getOrderId())
            .orElseThrow()
            .getOrderLines()
            .getFirst()
            .getCustomerRefundChoices().size() == 2);

        // Act
        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_chargeReturnFee_refundShippingFee.json",
            RefundCreationRequested.class);
        refundCreationRequested.setOrderId(orderId);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(2, messages.size());

            assertEquals(expectedGiftCardRefundAmount, messages
                .stream()
                .filter(m -> m.getProvider().equals(GIFT_CARD))
                .findFirst()
                .orElseThrow()
                .getTotalAmount());


            assertEquals(expectedBankRefundAmount, messages
                .stream()
                .filter(m -> m.getProvider().equals(ADYEN))
                .findFirst()
                .orElseThrow()
                .getTotalAmount());


            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());

        await().until(() -> orderRepository.findById(validOrderPlaced.getOrderId())
            .orElseThrow()
            .getRefunds()
            .getFirst()
            .getRefundId() == null);
    }

    @Test
    void testRefundCreationRequested_givenRefundChoiceMoreThanBankPayment_RefundWithGiftCardAndBank() throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenBankWithGiftCard.json",
            ValidOrderPlaced.class);
        var orderId = UUID.randomUUID().toString();
        validOrderPlaced.setOrderId(orderId);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        int expectedBankFirstRefundAmount = RETURN_FEE
            .add(EAN_1_PRICE.multiply(BigDecimal.valueOf(2)))
            .add(EAN_2_PRICE.multiply(BigDecimal.valueOf(2)))
            .multiply(BD_100)
            .intValue();


        // Act
        final RefundCreationRequested refundCreationRequestedFirtPart = loadSampleKafkaMessage(
            "/messages/postPurchase/refundCreationRequested_firstPart.json",
            RefundCreationRequested.class);
        refundCreationRequestedFirtPart.setOrderId(orderId);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequestedFirtPart));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            final PaymentRefundRequest message = messages.get(0);
            assertEquals(refundCreationRequestedFirtPart.getOrderId(), message.getOrderId());
            assertEquals(ADYEN, message.getProvider());
            assertEquals(expectedBankFirstRefundAmount, message.getTotalAmount());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());

        await().until(() -> orderRepository.findById(validOrderPlaced.getOrderId())
            .orElseThrow()
            .getRefunds()
            .getFirst()
            .getRefundId() == null);


        final PostPurchaseEventReceived postPurchaseEventReceived = loadSampleKafkaMessage(
            "/messages/postPurchase/postPurchaseEventReceivedMoreThanBank.json", PostPurchaseEventReceived.class);
        postPurchaseEventReceived.setOrderId(orderId);
        kafkaProducer.send(new ProducerRecord<>(postPurchaseEventReceivedTopic, postPurchaseEventReceived));

        await().until(() -> orderRepository.findById(validOrderPlaced.getOrderId())
            .orElseThrow()
            .getOrderLines()
            .stream().filter(ol -> ol.getEan().equals("ean3"))
            .findFirst()
            .orElseThrow()
            .getCustomerRefundChoices().size() == 1);


        paymentRefundRequestTestConsumer.clearMessages();


        final RefundCreationRequested refundCreationRequestedSecondPart = loadSampleKafkaMessage(
            "/messages/postPurchase/refundCreationRequested_secondPart.json",
            RefundCreationRequested.class);
        refundCreationRequestedSecondPart.setOrderId(orderId);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequestedSecondPart));


        int expectedGiftCardRefundAmount = EAN_3_PRICE
            .multiply(BD_100)
            .intValue();

        int expectedBankSecondRefundAmount = BigDecimal.ZERO
            .multiply(BD_100)
            .intValue();

        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            assertEquals(expectedGiftCardRefundAmount, messages
                .stream()
                .filter(m -> m.getProvider().equals(GIFT_CARD))
                .findFirst()
                .orElseThrow()
                .getTotalAmount());


            assertEquals(2, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

    }

    private void assertRefundStatusUpdatedMessagesNotProduced() {
        await().untilAsserted(() -> {
            final List<RefundStatusUpdated> messages = refundStatusUpdatedTestConsumer.getMessages();
            assertEquals(0, messages.size());
        });
    }

    private void assertRefundStatusUpdatedMessagesProduced(String orderId) {
        await().untilAsserted(() -> {
            final List<RefundStatusUpdated> messages = refundStatusUpdatedTestConsumer.getMessages();
            assertEquals(1,
                messages.stream()
                    .filter(message -> message.getOrderId().equals(orderId))
                    .filter(message -> message.getStatus().name().equals(RefundState.CREATED.name()))
                    .count());

            assertEquals(1,
                messages.stream()
                    .filter(message -> message.getOrderId().equals(orderId))
                    .filter(message -> message.getStatus().name().equals(RefundState.REFUND_REQUESTING.name()))
                    .count());
        });
    }

    private void assertNoMessageIsProducedAndIncomingMessageIsEnqueued(RefundCreationRequested refundCreationRequested) {
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            assertEquals(0, messages.size());

            var tasks = databaseQueueTaskDao.getTasks(
                refundCreationRequestedQueueConsumer.getQueueConfig().getLocation().getQueueId().asString(),
                RefundCreationRequested.class);

            List<RefundCreationRequested> taskPayloads = tasks.stream()
                .map(Task::getPayload)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .toList();

            AssertionsForInterfaceTypes.assertThat(taskPayloads)
                .usingRecursiveFieldByFieldElementComparator()
                .contains(refundCreationRequested);
        });
    }

    @Test
    void testRefundCreationRequested_sameProduct_oneCancelled_createRefundForReturn() throws IOException {
        // arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenBank.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_one_cancelled_with_quantity_one.json", OrderFinalized.class);

        int refundAmount = EAN_3_PRICE
            .multiply(BD_100)
            .intValue();

        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(0, messages.size());
        });

        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());

            assertEquals(ADYEN, messages.getFirst().getProvider());
            assertEquals(refundAmount, messages.getFirst().getTotalAmount());

            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, order.getRefunds().size());
        });

        paymentRefundRequestTestConsumer.clearMessages();

        int expectedRefundAmount = RETURN_FEE
            .add(SHIPPING_FEE)
            .add(EAN_1_PRICE)
            .multiply(BD_100)
            .intValue();

        // Act
        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_chargeReturnFee_refundShippingFee.json",
            RefundCreationRequested.class);
        refundCreationRequested.setOrderId(validOrderPlaced.getOrderId());
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            final PaymentRefundRequest message = messages.get(0);
            assertEquals(refundCreationRequested.getOrderId(), message.getOrderId());
            assertEquals(ADYEN, message.getProvider());
            assertEquals(expectedRefundAmount, message.getTotalAmount());

            assertEquals(2, order.getRefunds().size());

            for (Refund refund : order.getRefunds()) {
                assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
            }
        });

    }
}
