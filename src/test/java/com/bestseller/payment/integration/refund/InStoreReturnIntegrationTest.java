package com.bestseller.payment.integration.refund;

import com.bestseller.dbqueue.core.api.Task;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inStoreReturnSettlement.InStoreReturnSettlement;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderFinalized;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequest.PaymentRefundRequest;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementRequest.PaymentSettlementRequest;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementSucceeded.PaymentSettlementSucceeded;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundStatusUpdated.RefundStatusUpdated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderLine;
import com.bestseller.payment.core.domain.Refund;
import com.bestseller.payment.core.domain.enumeration.ChargedRefundReason;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.queue.InStoreReturnSettlementQueueConsumer;
import com.bestseller.payment.integration.container.BaseTestContainer;
import com.bestseller.payment.messaging.consumer.PaymentRefundRequestTestConsumer;
import com.bestseller.payment.messaging.consumer.PaymentSettlementRequestTestConsumer;
import com.bestseller.payment.messaging.consumer.RefundStatusUpdatedTestConsumer;
import com.bestseller.springtest.DatabaseQueueTaskDao;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.stubbing.Scenario;
import com.logistics.statetransition.RefundState;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.assertj.core.api.AssertionsForInterfaceTypes;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static com.bestseller.payment.utils.FileUtils.loadSampleKafkaMessage;
import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
class InStoreReturnIntegrationTest extends BaseTestContainer {
    private static final String ADYEN = "ADYEN";
    private static final String KLARNA_PAYMENTS = "KLARNA_PAYMENTS";
    private static final String GIFT_CARD = "OPTICARD_PHASE_ONE";
    private static final BigDecimal RETURN_FEE = BigDecimal.valueOf(-1.99);
    private static final BigDecimal SHIPPING_FEE = BigDecimal.valueOf(3.95);
    private static final BigDecimal BD_100 = BigDecimal.valueOf(100);
    private static final BigDecimal EAN_1_PRICE = BigDecimal.valueOf(4.99);
    private static final BigDecimal EAN_3_PRICE = BigDecimal.valueOf(24.99);
    private static final BigDecimal BANK_PAYMENT = BigDecimal.valueOf(28.90);
    private static final BigDecimal GIFTCARD_PAYMENT = BigDecimal.valueOf(20.00);

    @Value("${spring.cloud.stream.bindings.validOrderPlacedConsumer-in-0.destination}")
    private String validOrderPlacedTopic;

    @Value("${spring.cloud.stream.bindings.inStoreReturnSettlementConsumer-in-0.destination}")
    private String inStoreReturnSettlementTopic;

    @Value("${spring.cloud.stream.bindings.orderFinalizedConsumer-in-0.destination}")
    private String orderFinalizedTopic;

    @Value("${spring.cloud.stream.bindings.paymentSettlementSucceededConsumer-in-0.destination}")
    private String paymentSettlementSucceededTopic;

    @Autowired
    private PaymentSettlementRequestTestConsumer paymentSettlementRequestTestConsumer;

    @Autowired
    private PaymentRefundRequestTestConsumer paymentRefundRequestTestConsumer;

    @Autowired
    private RefundStatusUpdatedTestConsumer refundStatusUpdatedTestConsumer;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private KafkaProducer<String, Object> kafkaProducer;

    @Autowired
    private DatabaseQueueTaskDao databaseQueueTaskDao;

    @Autowired
    private InStoreReturnSettlementQueueConsumer inStoreReturnSettlementQueueConsumer;

    @BeforeEach
    void reset() {
        orderRepository.deleteAll();
        paymentRefundRequestTestConsumer.clearMessages();
        refundStatusUpdatedTestConsumer.clearMessages();
        paymentSettlementRequestTestConsumer.clearMessages();
    }

    @Test
    void testReturnInStore_onlyAdyenBankPayment() throws IOException {
        // Arrange
        ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenBank.json",
            ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        int expectedRefundAmount = RETURN_FEE.add(SHIPPING_FEE).add(EAN_1_PRICE).multiply(BD_100).intValue();

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options"))
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":true,\"refundShippingFee\":true}")));

        // Act

        final Order savedOrder = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
        OrderLine orderLine = savedOrder.getOrderLines().stream()
            .filter(orderLine1 -> orderLine1.getEan().equals("ean1")).findFirst().get();

        InStoreReturnSettlement inStoreReturnSettlement = loadSampleKafkaMessage(
            "/messages/inStoreReturn/inStore_ReturnSettlement.json", InStoreReturnSettlement.class,
            List.of(orderLine.getEan()));

        kafkaProducer.send(new ProducerRecord<>(inStoreReturnSettlementTopic, inStoreReturnSettlement));

        // Assert
        await().atMost(5, TimeUnit.MINUTES).untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());

            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();

            final PaymentRefundRequest message = messages.get(0);
            assertEquals(inStoreReturnSettlement.getOrderId(), message.getOrderId());
            assertEquals(ADYEN, message.getProvider());
            assertEquals(expectedRefundAmount, message.getTotalAmount());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());
    }

    @Test
    void testReturnInStore_onlyAdyenCardPayment_paymentSettled_allDispatched() throws IOException {
        // Arrange
        var orderId = UUID.randomUUID().toString();
        ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenCard.json",
            ValidOrderPlaced.class);
        validOrderPlaced.setOrderId(orderId);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json",
            OrderFinalized.class);
        orderFinalized.setOrderId(orderId);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        final PaymentSettlementSucceeded paymentSettlementSucceeded = loadSampleKafkaMessage(
            "/messages" + "/paymentSettlementSucceeded_adyen.json", PaymentSettlementSucceeded.class);
        paymentSettlementSucceeded.setCorrelationId(orderId);
        kafkaProducer.send(new ProducerRecord<>(paymentSettlementSucceededTopic, paymentSettlementSucceeded));

        await().until(() -> orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow().getPaymentStatus()
            .equals(PaymentState.SETTLED));

        int expectedRefundAmount = RETURN_FEE.add(SHIPPING_FEE).add(EAN_1_PRICE).multiply(BD_100).intValue();

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options"))
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":true,\"refundShippingFee\":true}")));

        // Act
        final Order savedOrder = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
        OrderLine orderLine = savedOrder.getOrderLines().stream()
            .filter(orderLine1 -> orderLine1.getEan().equals("ean1")).findFirst().get();

        InStoreReturnSettlement inStoreReturnSettlement = loadSampleKafkaMessage(
            "/messages/inStoreReturn/inStore_ReturnSettlement.json", InStoreReturnSettlement.class,
            List.of(orderLine.getEan()));
        inStoreReturnSettlement.setOrderId(orderId);

        kafkaProducer.send(new ProducerRecord<>(inStoreReturnSettlementTopic, inStoreReturnSettlement));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            final PaymentRefundRequest message = messages.get(0);
            assertEquals(inStoreReturnSettlement.getOrderId(), message.getOrderId());
            assertEquals(ADYEN, message.getProvider());
            assertEquals(expectedRefundAmount, message.getTotalAmount());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());
    }

    @Test
    void testReturnInStore_onlyKlarnaPayment_paymentSettled_allDispatched() throws IOException {
        // Arrange
        ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_klarna.json",
            ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json",
            OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        final PaymentSettlementSucceeded paymentSettlementSucceeded = loadSampleKafkaMessage(
            "/messages" + "/paymentSettlementSucceeded_adyen.json", PaymentSettlementSucceeded.class);
        kafkaProducer.send(new ProducerRecord<>(paymentSettlementSucceededTopic, paymentSettlementSucceeded));

        await().until(() -> orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow().getPaymentStatus()
            .equals(PaymentState.SETTLED));

        int expectedRefundAmount = RETURN_FEE.add(SHIPPING_FEE).add(EAN_1_PRICE).multiply(BD_100).intValue();

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options"))
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":true,\"refundShippingFee\":true}")));

        // Act
        final Order savedOrder = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
        OrderLine orderLine = savedOrder.getOrderLines().stream()
            .filter(orderLine1 -> orderLine1.getEan().equals("ean1")).findFirst().get();

        InStoreReturnSettlement inStoreReturnSettlement = loadSampleKafkaMessage(
            "/messages/inStoreReturn/inStore_ReturnSettlement.json", InStoreReturnSettlement.class,
            List.of(orderLine.getEan()));

        kafkaProducer.send(new ProducerRecord<>(inStoreReturnSettlementTopic, inStoreReturnSettlement));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            final PaymentRefundRequest message = messages.get(0);
            assertEquals(inStoreReturnSettlement.getOrderId(), message.getOrderId());
            assertEquals(KLARNA_PAYMENTS, message.getProvider());
            assertEquals(expectedRefundAmount, message.getTotalAmount());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());
    }

    @Test
    void testReturnInStore_onlyGiftCard() throws IOException {
        // Arrange
        ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_giftCard.json",
            ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        int expectedRefundAmount = RETURN_FEE.add(SHIPPING_FEE).add(EAN_1_PRICE).multiply(BD_100).intValue();

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options"))
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":true,\"refundShippingFee\":true}")));

        // Act
        final Order savedOrder = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
        OrderLine orderLine = savedOrder.getOrderLines().stream()
            .filter(orderLine1 -> orderLine1.getEan().equals("ean1")).findFirst().get();

        InStoreReturnSettlement inStoreReturnSettlement = loadSampleKafkaMessage(
            "/messages/inStoreReturn/inStore_ReturnSettlement.json", InStoreReturnSettlement.class,
            List.of(orderLine.getEan()));

        kafkaProducer.send(new ProducerRecord<>(inStoreReturnSettlementTopic, inStoreReturnSettlement));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            final PaymentRefundRequest message = messages.get(0);
            assertEquals(inStoreReturnSettlement.getOrderId(), message.getOrderId());
            assertEquals(GIFT_CARD, message.getProvider());
            assertEquals(expectedRefundAmount, message.getTotalAmount());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        await().untilAsserted(() -> {
            final List<RefundStatusUpdated> messages = refundStatusUpdatedTestConsumer.getMessages();
            assertEquals(0, messages.size());
        });
    }

    @Test
    void testReturnInStore_adyenBankWithGiftCardPayment_lessThanBankTransaction() throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages" + "/validOrderPlaced_adyenBankWithGiftCard.json", ValidOrderPlaced.class);

        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options"))
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":true,\"refundShippingFee\":false}")));

        int expectedRefundAmountViaBankInCents = RETURN_FEE.add(EAN_1_PRICE).multiply(BD_100).intValue();

        // Act
        final Order savedOrder = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
        OrderLine orderLine = savedOrder.getOrderLines().stream()
            .filter(orderLine1 -> orderLine1.getEan().equals("ean1")).findFirst().get();

        InStoreReturnSettlement inStoreReturnSettlement = loadSampleKafkaMessage(
            "/messages/inStoreReturn/inStore_ReturnSettlement.json", InStoreReturnSettlement.class,
            List.of(orderLine.getEan()));

        kafkaProducer.send(new ProducerRecord<>(inStoreReturnSettlementTopic, inStoreReturnSettlement));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            assertEquals(1,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(ADYEN))
                    .filter(m -> m.getTotalAmount() == expectedRefundAmountViaBankInCents).count());
            assertEquals(0, messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                .filter(m -> m.getProvider().equals(GIFT_CARD)).count());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());
    }

    @Test
    void testReturnInStore_adyenCardWithGiftCardPayment_paymentSettled_lessThanBankTransaction_allDispatched()
        throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages" + "/validOrderPlaced_adyenCardWithGiftCard.json", ValidOrderPlaced.class);

        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));

        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json",
            OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        final PaymentSettlementSucceeded paymentSettlementSucceeded = loadSampleKafkaMessage(
            "/messages/paymentSettlementSucceeded_adyen.json", PaymentSettlementSucceeded.class);

        kafkaProducer.send(new ProducerRecord<>(paymentSettlementSucceededTopic, paymentSettlementSucceeded));

        await().until(() -> orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow().getPaymentStatus()
            .equals(PaymentState.SETTLED));

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options"))
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":true,\"refundShippingFee\":false}")));

        int expectedRefundAmountViaBankInCents = RETURN_FEE.add(EAN_1_PRICE).multiply(BD_100).intValue();

        // Act
        final Order savedOrder = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
        OrderLine orderLine = savedOrder.getOrderLines().stream()
            .filter(orderLine1 -> orderLine1.getEan().equals("ean1")).findFirst().get();

        InStoreReturnSettlement inStoreReturnSettlement = loadSampleKafkaMessage(
            "/messages/inStoreReturn/inStore_ReturnSettlement.json", InStoreReturnSettlement.class,
            List.of(orderLine.getEan()));

        kafkaProducer.send(new ProducerRecord<>(inStoreReturnSettlementTopic, inStoreReturnSettlement));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            assertEquals(1,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(ADYEN))
                    .filter(m -> m.getTotalAmount() == expectedRefundAmountViaBankInCents).count());
            assertEquals(0, messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                .filter(m -> m.getProvider().equals(GIFT_CARD)).count());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());
    }

    @Test
    void testReturnInStore_adyenCardWithGiftCardPayment_someCancelled_lessThanBankAuthorisedAmount_paymentSettled_someReturned_moreThanBankAuthorisedAmount()
        throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages" + "/validOrderPlaced_adyenCardWithGiftCard.json", ValidOrderPlaced.class);

        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage(
            "/messages" + "/orderFinalized_one_cancelled_with_quantity_one.json", OrderFinalized.class);

        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        final PaymentSettlementSucceeded paymentSettlementSucceeded = loadSampleKafkaMessage(
            "/messages" + "/paymentSettlementSucceeded_adyen.json", PaymentSettlementSucceeded.class);

        kafkaProducer.send(new ProducerRecord<>(paymentSettlementSucceededTopic, paymentSettlementSucceeded));

        await().until(() -> orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow().getPaymentStatus()
            .equals(PaymentState.SETTLED));

        BigDecimal cancelledAmount = EAN_3_PRICE.multiply(BD_100);
        BigDecimal totalRefundAmount = EAN_1_PRICE.multiply(BD_100);

        BigDecimal remainingBankAmount = BANK_PAYMENT.multiply(BD_100).subtract(cancelledAmount);

        BigDecimal remainingGiftCardAmount = totalRefundAmount.subtract(remainingBankAmount);
        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options"))
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":false,\"refundShippingFee\":false}")));

        // Act
        final Order savedOrder = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
        OrderLine orderLine = savedOrder.getOrderLines().stream()
            .filter(orderLine1 -> orderLine1.getEan().equals("ean1")).findFirst().get();

        InStoreReturnSettlement inStoreReturnSettlement = loadSampleKafkaMessage(
            "/messages/inStoreReturn/inStore_ReturnSettlement.json", InStoreReturnSettlement.class,
            List.of(orderLine.getEan()));

        kafkaProducer.send(new ProducerRecord<>(inStoreReturnSettlementTopic, inStoreReturnSettlement));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(2, messages.size());

            assertEquals(1,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(ADYEN))
                    .filter(m -> m.getTotalAmount() == remainingBankAmount.intValue()).count());
            assertEquals(1,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .filter(m -> m.getTotalAmount() == (remainingGiftCardAmount.intValue())).count());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());
    }

    @Test
    void testReturnInStore_klarnaWithGiftCardPayment_paymentSettled_lessThanBankTransaction_allDispatched()
        throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages" + "/validOrderPlaced_klarnaWithGiftCard.json", ValidOrderPlaced.class);

        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));

        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json",
            OrderFinalized.class);

        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        final PaymentSettlementSucceeded paymentSettlementSucceeded = loadSampleKafkaMessage(
            "/messages/paymentSettlementSucceeded_adyen.json", PaymentSettlementSucceeded.class);

        kafkaProducer.send(new ProducerRecord<>(paymentSettlementSucceededTopic, paymentSettlementSucceeded));

        await().until(() -> orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow().getPaymentStatus()
            .equals(PaymentState.SETTLED));

        int expectedRefundAmountViaBankInCents = RETURN_FEE.add(EAN_1_PRICE).multiply(BD_100).intValue();

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options"))
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":true,\"refundShippingFee\":false}")));

        // Act
        final Order savedOrder = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
        OrderLine orderLine = savedOrder.getOrderLines().stream()
            .filter(orderLine1 -> orderLine1.getEan().equals("ean1")).findFirst().get();

        InStoreReturnSettlement inStoreReturnSettlement = loadSampleKafkaMessage(
            "/messages/inStoreReturn/inStore_ReturnSettlement.json", InStoreReturnSettlement.class,
            List.of(orderLine.getEan()));

        kafkaProducer.send(new ProducerRecord<>(inStoreReturnSettlementTopic, inStoreReturnSettlement));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            assertEquals(1,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(KLARNA_PAYMENTS))
                    .filter(m -> m.getTotalAmount() == expectedRefundAmountViaBankInCents).count());
            assertEquals(0, messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                .filter(m -> m.getProvider().equals(GIFT_CARD)).count());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());
    }

    @Test
    void testReturnInStore_refundReasonPersisted() throws IOException {
        // Arrange
        ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenBank.json",
            ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        int expectedRefundAmount = RETURN_FEE.add(SHIPPING_FEE).add(EAN_1_PRICE).multiply(BD_100).intValue();

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options"))
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":true,\"refundShippingFee\":true}")));

        // Act

        final Order savedOrder = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
        OrderLine orderLine = savedOrder.getOrderLines().stream()
            .filter(orderLine1 -> orderLine1.getEan().equals("ean1")).findFirst().get();

        InStoreReturnSettlement inStoreReturnSettlement = loadSampleKafkaMessage(
            "/messages/inStoreReturn/inStore_ReturnSettlement.json", InStoreReturnSettlement.class,
            List.of(orderLine.getEan()));

        kafkaProducer.send(new ProducerRecord<>(inStoreReturnSettlementTopic, inStoreReturnSettlement));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());

            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();

            final PaymentRefundRequest message = messages.get(0);
            assertEquals(inStoreReturnSettlement.getOrderId(), message.getOrderId());
            assertEquals(ADYEN, message.getProvider());
            assertEquals(expectedRefundAmount, message.getTotalAmount());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
            assertEquals(order.getOrderCharges().get(0).getRefundReason(), ChargedRefundReason.RETURNED_ITEMS_IN_STORE);
            assertEquals(order.getOrderCharges().get(1).getRefundReason(), ChargedRefundReason.RETURNED_ITEMS_IN_STORE);
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());
    }

    @Test
    void testReturnInStore_100PercentDiscount_chargeReturnFee_notRefundShippingFee() throws IOException {
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages" + "/validOrderPlaced_100_percent_discount.json", ValidOrderPlaced.class);

        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options"))
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":true,\"refundShippingFee\":false}")));

        final Order savedOrder = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
        OrderLine orderLine = savedOrder.getOrderLines().stream()
            .filter(orderLine1 -> orderLine1.getEan().equals("ean1")).findFirst().get();

        InStoreReturnSettlement inStoreReturnSettlement = loadSampleKafkaMessage(
            "/messages/inStoreReturn" + "/inStore_returnSettlement_100_percent_discount.json",
            InStoreReturnSettlement.class, List.of(orderLine.getEan()));

        kafkaProducer.send(new ProducerRecord<>(inStoreReturnSettlementTopic, inStoreReturnSettlement));

        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(0, messages.size());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_SUCCESS, refund.getRefundState());
        });
    }

    @Test
    void testReturnInStore_100PercentDiscount_chargeReturnFee_RefundShippingFee() throws IOException {
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages" + "/validOrderPlaced_100_percent_discount.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options"))
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":true,\"refundShippingFee\":true}")));

        var amountToRefund = RETURN_FEE.add(SHIPPING_FEE).multiply(BD_100).intValue();

        final Order savedOrder = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
        OrderLine orderLine = savedOrder.getOrderLines().stream()
            .filter(orderLine1 -> orderLine1.getEan().equals("ean1")).findFirst().get();

        InStoreReturnSettlement inStoreReturnSettlement = loadSampleKafkaMessage(
            "/messages/inStoreReturn" + "/inStore_returnSettlement_100_percent_discount.json",
            InStoreReturnSettlement.class, List.of(orderLine.getEan()));

        kafkaProducer.send(new ProducerRecord<>(inStoreReturnSettlementTopic, inStoreReturnSettlement));

        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            assertEquals(1, order.getRefunds().size());
            assertEquals(amountToRefund, messages.get(0).getTotalAmount());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });
    }

    @Test
    void testReturnInStore_adyenCardWithGiftCardPayment_multipleRefunds() throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages" + "/validOrderPlaced_adyenCardWithGiftCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json",
            OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        final PaymentSettlementSucceeded paymentSettlementSucceeded = loadSampleKafkaMessage(
            "/messages/paymentSettlementSucceeded_adyen.json", PaymentSettlementSucceeded.class);
        kafkaProducer.send(new ProducerRecord<>(paymentSettlementSucceededTopic, paymentSettlementSucceeded));

        await().until(() -> orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow().getPaymentStatus()
            .equals(PaymentState.SETTLED));

        String scenario = "Refund Options Scenario";

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options")).inScenario(scenario)
            .whenScenarioStateIs(Scenario.STARTED)
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":true,\"refundShippingFee\":false}"))
            .willSetStateTo("Second Call"));

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options")).inScenario(scenario)
            .whenScenarioStateIs("Second Call")
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":true,\"refundShippingFee\":false}"))
            .willSetStateTo("Third Call"));

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options")).inScenario(scenario)
            .whenScenarioStateIs("Third Call")
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":false,\"refundShippingFee\":false}"))
            .willSetStateTo("Forth Call"));

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options")).inScenario(scenario)
            .whenScenarioStateIs("Forth Call")
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":false,\"refundShippingFee\":false}")));

        // Act
        final Order savedOrder = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
        OrderLine orderLine = savedOrder.getOrderLines().stream()
            .filter(orderLine1 -> orderLine1.getEan().equals("ean1")).findFirst().get();

        InStoreReturnSettlement inStoreReturnSettlement = loadSampleKafkaMessage(
            "/messages/inStoreReturn/inStore_ReturnSettlement.json", InStoreReturnSettlement.class,
            List.of(orderLine.getEan()));

        kafkaProducer.send(new ProducerRecord<>(inStoreReturnSettlementTopic, inStoreReturnSettlement));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            BigDecimal totalRefundAmount = EAN_1_PRICE.add(RETURN_FEE).multiply(BD_100);

            assertEquals(1,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(ADYEN))
                    .filter(m -> m.getTotalAmount() == totalRefundAmount.intValue()).count());
            assertEquals(0,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .filter(m -> m.getTotalAmount() == GIFTCARD_PAYMENT.multiply(BD_100).intValue()).count());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());

        // Clean up
        paymentRefundRequestTestConsumer.clearMessages();
        refundStatusUpdatedTestConsumer.clearMessages();

        // Act

        inStoreReturnSettlement.setRefundId(inStoreReturnSettlement.getRefundId() + 1);
        kafkaProducer.send(new ProducerRecord<>(inStoreReturnSettlementTopic, inStoreReturnSettlement));

        // Assert
        await().untilAsserted(() -> {
            BigDecimal totalRefundAmount = EAN_1_PRICE.multiply(BD_100);

            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            assertEquals(1,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(ADYEN))
                    .filter(m -> m.getTotalAmount() == totalRefundAmount.intValue()).count());
            assertEquals(0, messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                .filter(m -> m.getProvider().equals(GIFT_CARD)).count());

            assertEquals(2, order.getRefunds().size());

            Refund refund = order.getRefunds().stream().sorted((r1, r2) -> r2.getId() - r1.getId()).toList().getFirst();
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());

        // Clean up
        paymentRefundRequestTestConsumer.clearMessages();
        refundStatusUpdatedTestConsumer.clearMessages();

        // Act
        inStoreReturnSettlement.setRefundId(inStoreReturnSettlement.getRefundId() + 1);
        final OrderLine newOrderLine = savedOrder.getOrderLines().stream().filter(ol -> ol.getEan().equals("ean3"))
            .findFirst().orElseThrow();
        inStoreReturnSettlement.setTotalRefundAmount(EAN_3_PRICE);
        inStoreReturnSettlement.setOrderLines(List.of(
            new com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inStoreReturnSettlement.OrderLine()
                .withEan(newOrderLine.getEan()).withQuantity(1)));
        kafkaProducer.send(new ProducerRecord<>(inStoreReturnSettlementTopic, inStoreReturnSettlement));

        // Assert
        await().untilAsserted(() -> {
            BigDecimal alreadyRefundedAmount = EAN_1_PRICE.add(EAN_1_PRICE).add(RETURN_FEE).multiply(BD_100);
            BigDecimal totalRefundAmount = EAN_3_PRICE.multiply(BD_100);

            BigDecimal bankTotalRefund = BANK_PAYMENT.multiply(BD_100).subtract(alreadyRefundedAmount);

            BigDecimal giftCardTotalRefund = totalRefundAmount.subtract(bankTotalRefund);

            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(2, messages.size());

            assertEquals(1,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(ADYEN))
                    .filter(m -> m.getTotalAmount() == bankTotalRefund.intValue()).count());
            assertEquals(1,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .filter(m -> m.getTotalAmount() == giftCardTotalRefund.intValue()).count());

            assertEquals(3, order.getRefunds().size());

            Refund refund = order.getRefunds().stream().sorted((r1, r2) -> r2.getId() - r1.getId()).toList().getFirst();
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });
    }

    @Test
    void testReturnInStore_klarnaWithGiftCardPayment_multipleRefunds() throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages" + "/validOrderPlaced_klarnaWithGiftCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json",
            OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        final PaymentSettlementSucceeded paymentSettlementSucceeded = loadSampleKafkaMessage(
            "/messages/paymentSettlementSucceeded_adyen.json", PaymentSettlementSucceeded.class);
        kafkaProducer.send(new ProducerRecord<>(paymentSettlementSucceededTopic, paymentSettlementSucceeded));

        await().until(() -> orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow().getPaymentStatus()
            .equals(PaymentState.SETTLED));

        String scenario = "Refund Options Scenario";

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options")).inScenario(scenario)
            .whenScenarioStateIs(Scenario.STARTED)
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":true,\"refundShippingFee\":false}"))
            .willSetStateTo("Second Call"));

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options")).inScenario(scenario)
            .whenScenarioStateIs("Second Call")
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":true,\"refundShippingFee\":false}"))
            .willSetStateTo("Third Call"));

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options")).inScenario(scenario)
            .whenScenarioStateIs("Third Call")
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":false,\"refundShippingFee\":false}"))
            .willSetStateTo("Forth Call"));

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options")).inScenario(scenario)
            .whenScenarioStateIs("Forth Call")
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":false,\"refundShippingFee\":false}")));

        // Act
        final Order savedOrder = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
        OrderLine orderLine = savedOrder.getOrderLines().stream()
            .filter(orderLine1 -> orderLine1.getEan().equals("ean1")).findFirst().get();

        InStoreReturnSettlement inStoreReturnSettlement = loadSampleKafkaMessage(
            "/messages/inStoreReturn/inStore_ReturnSettlement.json", InStoreReturnSettlement.class,
            List.of(orderLine.getEan()));

        kafkaProducer.send(new ProducerRecord<>(inStoreReturnSettlementTopic, inStoreReturnSettlement));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            BigDecimal totalRefundAmount = EAN_1_PRICE.add(RETURN_FEE).multiply(BD_100);

            assertEquals(1,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(KLARNA_PAYMENTS))
                    .filter(m -> m.getTotalAmount() == totalRefundAmount.intValue()).count());
            assertEquals(0,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .filter(m -> m.getTotalAmount() == GIFTCARD_PAYMENT.multiply(BD_100).intValue()).count());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());

        // Clean up
        paymentRefundRequestTestConsumer.clearMessages();
        refundStatusUpdatedTestConsumer.clearMessages();

        // Act

        inStoreReturnSettlement.setRefundId(inStoreReturnSettlement.getRefundId() + 1);
        kafkaProducer.send(new ProducerRecord<>(inStoreReturnSettlementTopic, inStoreReturnSettlement));

        // Assert
        await().untilAsserted(() -> {
            BigDecimal totalRefundAmount = EAN_1_PRICE.multiply(BD_100);

            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            assertEquals(1,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(KLARNA_PAYMENTS))
                    .filter(m -> m.getTotalAmount() == totalRefundAmount.intValue()).count());
            assertEquals(0, messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                .filter(m -> m.getProvider().equals(GIFT_CARD)).count());

            assertEquals(2, order.getRefunds().size());

            Refund refund = order.getRefunds().stream().sorted((r1, r2) -> r2.getId() - r1.getId()).toList().getFirst();
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());

        // Clean up
        paymentRefundRequestTestConsumer.clearMessages();
        refundStatusUpdatedTestConsumer.clearMessages();

        // Act
        inStoreReturnSettlement.setRefundId(inStoreReturnSettlement.getRefundId() + 2);
        final OrderLine newOrderLine = savedOrder.getOrderLines().stream().filter(ol -> ol.getEan().equals("ean3"))
            .findFirst().orElseThrow();
        inStoreReturnSettlement.setTotalRefundAmount(EAN_3_PRICE);
        inStoreReturnSettlement.setOrderLines(List.of(
            new com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inStoreReturnSettlement.OrderLine()
                .withEan(newOrderLine.getEan()).withQuantity(1)));
        kafkaProducer.send(new ProducerRecord<>(inStoreReturnSettlementTopic, inStoreReturnSettlement));

        // Assert
        await().untilAsserted(() -> {
            BigDecimal alreadyRefundedAmount = EAN_1_PRICE.add(EAN_1_PRICE).add(RETURN_FEE).multiply(BD_100);
            BigDecimal totalRefundAmount = EAN_3_PRICE.multiply(BD_100);

            BigDecimal bankTotalRefund = BANK_PAYMENT.multiply(BD_100).subtract(alreadyRefundedAmount);

            BigDecimal giftCardTotalRefund = totalRefundAmount.subtract(bankTotalRefund);

            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(2, messages.size());

            assertEquals(1,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(KLARNA_PAYMENTS))
                    .filter(m -> m.getTotalAmount() == bankTotalRefund.intValue()).count());
            assertEquals(1,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .filter(m -> m.getTotalAmount() == giftCardTotalRefund.intValue()).count());

            assertEquals(3, order.getRefunds().size());

            Refund refund = order.getRefunds().stream().sorted((r1, r2) -> r2.getId() - r1.getId()).toList().getFirst();
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });
    }

    @Test
    void testReturnInStore_adyenBankWithGiftCardPayment_multipleRefunds() throws IOException, InterruptedException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages" + "/validOrderPlaced_adyenBankWithGiftCard.json", ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json",
            OrderFinalized.class);
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        Thread.sleep(500);

        String scenario = "Refund Options Scenario";

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options")).inScenario(scenario)
            .whenScenarioStateIs(Scenario.STARTED)
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":true,\"refundShippingFee\":false}"))
            .willSetStateTo("Second Call"));

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options")).inScenario(scenario)
            .whenScenarioStateIs("Second Call")
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":true,\"refundShippingFee\":false}"))
            .willSetStateTo("Third Call"));

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options")).inScenario(scenario)
            .whenScenarioStateIs("Third Call")
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":false,\"refundShippingFee\":false}"))
            .willSetStateTo("Forth Call"));

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options")).inScenario(scenario)
            .whenScenarioStateIs("Forth Call")
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":false,\"refundShippingFee\":false}")));

        // Act
        final Order savedOrder = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
        OrderLine orderLine = savedOrder.getOrderLines().stream()
            .filter(orderLine1 -> orderLine1.getEan().equals("ean1")).findFirst().get();

        InStoreReturnSettlement inStoreReturnSettlement = loadSampleKafkaMessage(
            "/messages/inStoreReturn/inStore_ReturnSettlement.json", InStoreReturnSettlement.class,
            List.of(orderLine.getEan()));

        kafkaProducer.send(new ProducerRecord<>(inStoreReturnSettlementTopic, inStoreReturnSettlement));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            BigDecimal totalRefundAmount = EAN_1_PRICE.add(RETURN_FEE).multiply(BD_100);

            assertEquals(1,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(ADYEN))
                    .filter(m -> m.getTotalAmount() == totalRefundAmount.intValue()).count());
            assertEquals(0,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .filter(m -> m.getTotalAmount() == GIFTCARD_PAYMENT.multiply(BD_100).intValue()).count());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());

        // Clean up
        paymentRefundRequestTestConsumer.clearMessages();
        refundStatusUpdatedTestConsumer.clearMessages();

        // Act

        inStoreReturnSettlement.setRefundId(inStoreReturnSettlement.getRefundId() + 1);
        kafkaProducer.send(new ProducerRecord<>(inStoreReturnSettlementTopic, inStoreReturnSettlement));

        // Assert
        await().untilAsserted(() -> {
            BigDecimal totalRefundAmount = EAN_1_PRICE.multiply(BD_100);

            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(1, messages.size());

            assertEquals(1,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(ADYEN))
                    .filter(m -> m.getTotalAmount() == totalRefundAmount.intValue()).count());
            assertEquals(0, messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                .filter(m -> m.getProvider().equals(GIFT_CARD)).count());

            assertEquals(2, order.getRefunds().size());

            Refund refund = order.getRefunds().stream().sorted((r1, r2) -> r2.getId() - r1.getId()).toList().getFirst();
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());

        // Clean up
        paymentRefundRequestTestConsumer.clearMessages();
        refundStatusUpdatedTestConsumer.clearMessages();

        // Act
        inStoreReturnSettlement.setRefundId(inStoreReturnSettlement.getRefundId() + 2);
        final OrderLine newOrderLine = savedOrder.getOrderLines().stream().filter(ol -> ol.getEan().equals("ean3"))
            .findFirst().orElseThrow();
        inStoreReturnSettlement.setTotalRefundAmount(EAN_3_PRICE);
        inStoreReturnSettlement.setOrderLines(List.of(
            new com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inStoreReturnSettlement.OrderLine()
                .withEan(newOrderLine.getEan()).withQuantity(1)));
        kafkaProducer.send(new ProducerRecord<>(inStoreReturnSettlementTopic, inStoreReturnSettlement));

        // Assert
        await().untilAsserted(() -> {
            BigDecimal alreadyRefundedAmount = EAN_1_PRICE.add(EAN_1_PRICE).add(RETURN_FEE).multiply(BD_100);
            BigDecimal totalRefundAmount = EAN_3_PRICE.multiply(BD_100);

            BigDecimal bankTotalRefund = BANK_PAYMENT.multiply(BD_100).subtract(alreadyRefundedAmount);

            BigDecimal giftCardTotalRefund = totalRefundAmount.subtract(bankTotalRefund);

            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(2, messages.size());

            assertEquals(1,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(ADYEN))
                    .filter(m -> m.getTotalAmount() == bankTotalRefund.intValue()).count());
            assertEquals(1,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .filter(m -> m.getTotalAmount() == giftCardTotalRefund.intValue()).count());

            assertEquals(3, order.getRefunds().size());

            Refund refund = order.getRefunds().stream().sorted((r1, r2) -> r2.getId() - r1.getId()).toList().getFirst();
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });
    }

    @Test
    void testReturnInStore_klarnaWithGiftCardPayment_paymentSettled_moreThanBankTransaction_allDispatched()
        throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages" + "/validOrderPlaced_klarnaWithGiftCard.json", ValidOrderPlaced.class);

        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));

        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json",
            OrderFinalized.class);

        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        final PaymentSettlementSucceeded paymentSettlementSucceeded = loadSampleKafkaMessage(
            "/messages/paymentSettlementSucceeded_adyen.json", PaymentSettlementSucceeded.class);

        kafkaProducer.send(new ProducerRecord<>(paymentSettlementSucceededTopic, paymentSettlementSucceeded));

        await().until(() -> orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow().getPaymentStatus()
            .equals(PaymentState.SETTLED));

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options"))
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":false,\"refundShippingFee\":true}")));

        // Act
        final Order savedOrder = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();

        List<String> eans = savedOrder.getOrderLines().stream().map(OrderLine::getEan).toList();

        InStoreReturnSettlement inStoreReturnSettlement = loadSampleKafkaMessage(
            "/messages/inStoreReturn/inStore_returnSettlement_moreThanBankTransaction.json",
            InStoreReturnSettlement.class, eans);

        kafkaProducer.send(new ProducerRecord<>(inStoreReturnSettlementTopic, inStoreReturnSettlement));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(2, messages.size());

            assertEquals(1,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(KLARNA_PAYMENTS))
                    .filter(m -> m.getTotalAmount() == BANK_PAYMENT.multiply(BD_100).intValue()).count());
            assertEquals(1,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .filter(m -> m.getTotalAmount() == GIFTCARD_PAYMENT.multiply(BD_100).intValue()).count());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());
    }

    @Test
    void testReturnInStore_adyenCardWithGiftCardPayment_paymentSettled_moreThanBankTransaction_allDispatched()
        throws IOException {
        // Arrange
        var orderId = UUID.randomUUID().toString();
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages" + "/validOrderPlaced_adyenCardWithGiftCard.json", ValidOrderPlaced.class);
        validOrderPlaced.setOrderId(orderId);

        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        final OrderFinalized orderFinalized = loadSampleKafkaMessage("/messages/orderFinalized_all_dispatched.json",
            OrderFinalized.class);
        orderFinalized.setOrderId(orderId);

        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().untilAsserted(() -> {
            final List<PaymentSettlementRequest> messages = paymentSettlementRequestTestConsumer.getMessages();
            assertEquals(1, messages.size());
        });

        final PaymentSettlementSucceeded paymentSettlementSucceeded = loadSampleKafkaMessage(
            "/messages/paymentSettlementSucceeded_adyen.json", PaymentSettlementSucceeded.class);
        paymentSettlementSucceeded.setCorrelationId(orderId);

        kafkaProducer.send(new ProducerRecord<>(paymentSettlementSucceededTopic, paymentSettlementSucceeded));

        await().until(() -> orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow().getPaymentStatus()
            .equals(PaymentState.SETTLED));

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options"))
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":false,\"refundShippingFee\":true}")));

        // Act
        final Order savedOrder = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();

        List<String> eans = savedOrder.getOrderLines().stream().map(OrderLine::getEan).toList();

        InStoreReturnSettlement inStoreReturnSettlement = loadSampleKafkaMessage(
            "/messages/inStoreReturn/inStore_returnSettlement_moreThanBankTransaction.json",
            InStoreReturnSettlement.class, eans);
        inStoreReturnSettlement.setOrderId(orderId);

        kafkaProducer.send(new ProducerRecord<>(inStoreReturnSettlementTopic, inStoreReturnSettlement));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(2, messages.size());

            assertEquals(1,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(ADYEN))
                    .filter(m -> m.getTotalAmount() == BANK_PAYMENT.multiply(BD_100).intValue()).count());
            assertEquals(1,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .filter(m -> m.getTotalAmount() == GIFTCARD_PAYMENT.multiply(BD_100).intValue()).count());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());
    }

    @Test
    void testReturnInStore_adyenBankWithGiftCardPayment_moreThanBankTransaction() throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage(
            "/messages" + "/validOrderPlaced_adyenBankWithGiftCard.json", ValidOrderPlaced.class);

        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options"))
            .willReturn(aResponse().withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                .withBody("{\"chargeReturnFee\":false,\"refundShippingFee\":true}")));

        // Act
        final Order savedOrder = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();

        InStoreReturnSettlement inStoreReturnSettlement = loadSampleKafkaMessage(
            "/messages/inStoreReturn/inStore_returnSettlement_moreThanBankTransaction.json",
            InStoreReturnSettlement.class,
            savedOrder.getOrderLines().stream().map(OrderLine::getEan).toList());

        kafkaProducer.send(new ProducerRecord<>(inStoreReturnSettlementTopic, inStoreReturnSettlement));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            assertEquals(2, messages.size());

            assertEquals(1,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(ADYEN))
                    .filter(m -> m.getTotalAmount() == BANK_PAYMENT.multiply(BD_100).intValue()).count());
            assertEquals(1,
                messages.stream().filter(m -> m.getOrderId().equals(inStoreReturnSettlement.getOrderId()))
                    .filter(m -> m.getProvider().equals(GIFT_CARD))
                    .filter(m -> m.getTotalAmount() == GIFTCARD_PAYMENT.multiply(BD_100).intValue()).count());

            assertEquals(1, order.getRefunds().size());

            Refund refund = order.getRefunds().get(0);
            assertEquals(RefundState.REFUND_REQUESTING, refund.getRefundState());
        });

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());
    }

    @Test
    void testReturnInStore_refundOptionsProviderNotAccessible() throws IOException {
        // Arrange
        ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenBank.json",
            ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        wireMock.stubFor(WireMock.get(urlMatching("/orders/.*/refund-options"))
            .willReturn(aResponse().withStatus(500)));

        // Act
        final Order savedOrder = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
        OrderLine orderLine = savedOrder.getOrderLines().stream()
            .filter(orderLine1 -> orderLine1.getEan().equals("ean1")).findFirst().get();

        InStoreReturnSettlement inStoreReturnSettlement = loadSampleKafkaMessage(
            "/messages/inStoreReturn/inStore_ReturnSettlement.json", InStoreReturnSettlement.class,
            List.of(orderLine.getEan()));

        kafkaProducer.send(new ProducerRecord<>(inStoreReturnSettlementTopic, inStoreReturnSettlement));

        // Assert
        await().untilAsserted(() -> {
            final List<PaymentRefundRequest> messages = paymentRefundRequestTestConsumer.getMessages();
            assertEquals(0, messages.size());

            var tasks = databaseQueueTaskDao.getTasks(
                inStoreReturnSettlementQueueConsumer.getQueueConfig().getLocation().getQueueId().asString(),
                InStoreReturnSettlement.class);

            List<InStoreReturnSettlement> taskPayloads = tasks.stream()
                .map(Task::getPayload)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .toList();

            AssertionsForInterfaceTypes.assertThat(taskPayloads)
                .usingRecursiveFieldByFieldElementComparator()
                .contains(inStoreReturnSettlement);
        });
    }

    private void assertRefundStatusUpdatedMessagesProduced(String orderId) {
        await().untilAsserted(() -> {
            final List<RefundStatusUpdated> messages = refundStatusUpdatedTestConsumer.getMessages();
            assertEquals(1,
                messages.stream()
                    .filter(message -> message.getOrderId().equals(orderId))
                    .filter(message -> message.getStatus().name().equals(RefundState.CREATED.name()))
                    .count());

            assertEquals(1,
                messages.stream()
                    .filter(message -> message.getOrderId().equals(orderId))
                    .filter(message -> message.getStatus().name().equals(RefundState.REFUND_REQUESTING.name()))
                    .count());
        });
    }

}
