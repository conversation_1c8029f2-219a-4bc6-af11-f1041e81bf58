package com.bestseller.payment.integration.refund;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundFailed.PaymentRefundFailed;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequested.PaymentRefundRequested;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundSucceeded.PaymentRefundSucceeded;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.RefundCreationRequested;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundStatusUpdated.RefundStatusUpdated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.adapter.repository.RefundRepository;
import com.bestseller.payment.core.domain.Refund;
import com.bestseller.payment.integration.container.BaseTestContainer;
import com.bestseller.payment.messaging.consumer.PaymentRefundRequestTestConsumer;
import com.bestseller.payment.messaging.consumer.PaymentRefundRequestedTestConsumer;
import com.bestseller.payment.messaging.consumer.PaymentRefundSucceededTestConsumer;
import com.bestseller.payment.messaging.consumer.PaymentSettlementRequestTestConsumer;
import com.bestseller.payment.messaging.consumer.RefundStatusUpdatedTestConsumer;
import com.logistics.statetransition.RefundState;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.io.IOException;
import java.util.List;

import static com.bestseller.payment.utils.FileUtils.loadSampleKafkaMessage;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
public class RefundStatusUpdatesIntegrationTest extends BaseTestContainer {

    @Autowired
    private PaymentRefundRequestTestConsumer paymentRefundRequestTestConsumer;

    @Autowired
    private PaymentSettlementRequestTestConsumer paymentSettlementRequestTestConsumer;

    @Autowired
    private RefundStatusUpdatedTestConsumer refundStatusUpdatedTestConsumer;

    @Autowired
    private PaymentRefundRequestedTestConsumer paymentRefundRequestedTestConsumer;

    @Autowired
    private PaymentRefundSucceededTestConsumer paymentRefundSucceededTestConsumer;

    @Autowired
    private KafkaProducer<String, Object> kafkaProducer;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private RefundRepository refundRepository;

    @Value("${spring.cloud.stream.bindings.validOrderPlacedConsumer-in-0.destination}")
    private String validOrderPlacedTopic;

    @Value("${spring.cloud.stream.bindings.refundCreationRequestedConsumer-in-0.destination}")
    private String refundCreationRequestedTopic;

    @Value("${spring.cloud.stream.bindings.paymentRefundRequestedConsumer-in-0.destination}")
    private String paymentRefundRequestedTopic;

    @Value("${spring.cloud.stream.bindings.paymentRefundFailedConsumer-in-0.destination}")
    private String paymentRefundFailedTopic;

    @Value("${spring.cloud.stream.bindings.paymentRefundSucceededConsumer-in-0.destination}")
    private String paymentRefundSucceededTopic;

    @BeforeEach
    public void reset() {
        refundRepository.deleteAll();
        orderRepository.deleteAll();
        paymentRefundRequestTestConsumer.clearMessages();
        paymentSettlementRequestTestConsumer.clearMessages();
        refundStatusUpdatedTestConsumer.clearMessages();
        paymentRefundRequestedTestConsumer.clearMessages();
        paymentRefundSucceededTestConsumer.clearMessages();
    }

    @Test
    void testSuccessfulRefund() throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenBank.json",
            ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        // Act
        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_chargeReturnFee_refundShippingFee.json",
            RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().until(() -> paymentRefundRequestTestConsumer.getMessages().size() == 1);

        await().until(() ->
            orderRepository.findById(validOrderPlaced.getOrderId())
                .orElseThrow()
                .getRefunds()
                .getFirst()
                .getRefundState()
                .equals(RefundState.REFUND_REQUESTING)
        );

        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());

        // Act
        Refund refund = orderRepository.findById(validOrderPlaced.getOrderId())
            .orElseThrow()
            .getRefunds()
            .getFirst();

        final PaymentRefundRequested paymentRefundRequested = loadSampleKafkaMessage(
            "/messages/paymentRefundRequested.json",
            PaymentRefundRequested.class);
        paymentRefundRequested.setCorrelationId(refund.getId().toString());

        kafkaProducer.send(new ProducerRecord<>(paymentRefundRequestedTopic, paymentRefundRequested));
        await().until(() ->
            RefundState.REFUND_REQUESTED.equals(
                orderRepository.findById(validOrderPlaced.getOrderId())
                    .orElseThrow()
                    .getRefunds()
                    .getFirst()
                    .getRefundState())
        );

        await().untilAsserted(() -> {
            final List<RefundStatusUpdated> messages = refundStatusUpdatedTestConsumer.getMessages();
            assertEquals(1, messages.size());
            assertEquals(RefundStatusUpdated.Status.REFUND_REQUESTED, messages.getFirst().getStatus());
        });

        refundStatusUpdatedTestConsumer.clearMessages();

        final PaymentRefundSucceeded paymentRefundSucceeded = loadSampleKafkaMessage(
            "/messages/paymentRefundSucceeded.json",
            PaymentRefundSucceeded.class);
        paymentRefundSucceeded.setCorrelationId(refund.getId().toString());
        kafkaProducer.send(new ProducerRecord<>(paymentRefundSucceededTopic, paymentRefundSucceeded));

        await().until(() ->
            RefundState.REFUND_SUCCESS.equals(
                orderRepository.findById(validOrderPlaced.getOrderId())
                    .orElseThrow()
                    .getRefunds()
                    .getFirst()
                    .getRefundState())
        );

        await().untilAsserted(() -> {
            final List<RefundStatusUpdated> messages = refundStatusUpdatedTestConsumer.getMessages();
            assertEquals(1, messages.size());
            assertEquals(RefundStatusUpdated.Status.REFUND_SUCCESS, messages.getFirst().getStatus());
        });
    }

    @Test
    void testFailedRefund() throws IOException {
        // Arrange
        final ValidOrderPlaced validOrderPlaced = loadSampleKafkaMessage("/messages/validOrderPlaced_adyenBank.json",
            ValidOrderPlaced.class);
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(
            () -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        // Act
        final RefundCreationRequested refundCreationRequested = loadSampleKafkaMessage(
            "/messages/refundCreationRequested_chargeReturnFee_refundShippingFee.json",
            RefundCreationRequested.class);
        kafkaProducer.send(new ProducerRecord<>(refundCreationRequestedTopic, refundCreationRequested));

        // Assert
        await().until(() -> paymentRefundRequestTestConsumer.getMessages().size() == 1);

        await().until(() ->
            orderRepository.findById(validOrderPlaced.getOrderId())
                .orElseThrow()
                .getRefunds()
                .getFirst()
                .getRefundState()
                .equals(RefundState.REFUND_REQUESTING)
        );


        assertRefundStatusUpdatedMessagesProduced(validOrderPlaced.getOrderId());

        // Act
        Refund refund = orderRepository.findById(validOrderPlaced.getOrderId())
            .orElseThrow()
            .getRefunds()
            .getFirst();

        final PaymentRefundRequested paymentRefundRequested = loadSampleKafkaMessage(
            "/messages/paymentRefundRequested.json",
            PaymentRefundRequested.class);
        paymentRefundRequested.setCorrelationId(refund.getId().toString());

        kafkaProducer.send(new ProducerRecord<>(paymentRefundRequestedTopic, paymentRefundRequested));
        await().until(() ->
            RefundState.REFUND_REQUESTED.equals(
                orderRepository.findById(validOrderPlaced.getOrderId())
                    .orElseThrow()
                    .getRefunds()
                    .getFirst()
                    .getRefundState())
        );

        await().untilAsserted(() -> {
            final List<RefundStatusUpdated> messages = refundStatusUpdatedTestConsumer.getMessages();
            assertEquals(1, messages.size());
            assertEquals(RefundStatusUpdated.Status.REFUND_REQUESTED, messages.getFirst().getStatus());
        });

        refundStatusUpdatedTestConsumer.clearMessages();

        final PaymentRefundFailed paymentRefundFailed = loadSampleKafkaMessage(
            "/messages/paymentRefundFailed.json",
            PaymentRefundFailed.class);
        paymentRefundFailed.setCorrelationId(refund.getId().toString());
        kafkaProducer.send(new ProducerRecord<>(paymentRefundFailedTopic, paymentRefundFailed));

        await().until(() ->
            RefundState.REFUND_FAILED.equals(
                orderRepository.findById(validOrderPlaced.getOrderId())
                    .orElseThrow()
                    .getRefunds()
                    .getFirst()
                    .getRefundState())
        );

        await().untilAsserted(() -> {
            final List<RefundStatusUpdated> messages = refundStatusUpdatedTestConsumer.getMessages();
            assertEquals(1, messages.size());
            assertEquals(RefundStatusUpdated.Status.REFUND_FAILED, messages.getFirst().getStatus());
        });
    }

    private void assertRefundStatusUpdatedMessagesProduced(String orderId) {
        await().untilAsserted(() -> {
            final List<RefundStatusUpdated> messages = refundStatusUpdatedTestConsumer.getMessages();
            assertEquals(2, messages.size());
            assertEquals(1,
                messages.stream()
                    .filter(message -> message.getOrderId().equals(orderId))
                    .filter(message -> message.getStatus().name().equals(RefundState.CREATED.name()))
                    .count());

            assertEquals(1,
                messages.stream()
                    .filter(message -> message.getOrderId().equals(orderId))
                    .filter(message -> message.getStatus().name().equals(RefundState.REFUND_REQUESTING.name()))
                    .count());
        });

        refundStatusUpdatedTestConsumer.clearMessages();
    }
}
