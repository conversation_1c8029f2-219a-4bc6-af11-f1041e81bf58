package com.bestseller.payment.integration.postpurchaseevent;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderFinalized;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.postPurchaseEventReceived.PostPurchaseEventReceived;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.interfacecontractannotator.model.postpurchaseeventreceived.ReturnCreatedPayload;
import com.bestseller.payment.adapter.repository.CustomerRefundChoiceRepository;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.core.domain.CustomerRefundChoice;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.integration.container.BaseTestContainer;
import com.bestseller.payment.utils.FileUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.io.IOException;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

@SpringJUnitConfig
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
class PostPurchaseEventReceivedIntegrationTest extends BaseTestContainer {

    @Autowired
    private KafkaProducer<String, Object> kafkaProducer;

    @Autowired
    private OrderRepository orderRepository;

    @Autowired
    private CustomerRefundChoiceRepository customerRefundChoiceRepository;

    @Autowired
    private QueueProducer<PostPurchaseEventReceived> postPurchaseEventReceivedQueueProducer;

    @Value("${spring.cloud.stream.bindings.validOrderPlacedConsumer-in-0.destination}")
    private String validOrderPlacedTopic;

    @Value("${spring.cloud.stream.bindings.postPurchaseEventReceivedConsumer-in-0.destination}")
    private String postPurchaseEventReceivedTopic;

    @Value("${spring.cloud.stream.bindings.orderFinalizedConsumer-in-0.destination}")
    private String orderFinalizedTopic;

    @BeforeEach
    public void resetDB() {
        customerRefundChoiceRepository.deleteAll();
        orderRepository.deleteAll();
    }

    @Test
    void when_PostPurchaseEventReceived_IsConsumed_CustomerChoiceShouldBeCreated() throws IOException, InterruptedException {
        // arrange
        ValidOrderPlaced validOrderPlaced = FileUtils.loadSampleKafkaMessage(
            "/messages/postPurchase/validOrderPlaced_forPostPurchaseEvent.json", ValidOrderPlaced.class);
        validOrderPlaced.setOrderId("ORDER-123-" + UUID.randomUUID().toString().substring(0, 8));

        PostPurchaseEventReceived postPurchaseEventReceived = FileUtils.loadSampleKafkaMessage(
            "/messages/postPurchase/postPurchaseEventReceived.json", PostPurchaseEventReceived.class);
        postPurchaseEventReceived.setOrderId(validOrderPlaced.getOrderId());

        // act - First place the order
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        // act - Then send the post purchase event
        kafkaProducer.send(new ProducerRecord<>(postPurchaseEventReceivedTopic, postPurchaseEventReceived));


        // assert - Verify customer choice was created
        await().untilAsserted(() -> {
            // Get the order and check customer choices through order lines
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            List<CustomerRefundChoice> customerRefundChoices = order.getOrderLines().get(0).getCustomerRefundChoices();
            assertThat(customerRefundChoices).hasSize(2);

            CustomerRefundChoice customerRefundChoice = customerRefundChoices.get(0);
            assertThat(customerRefundChoice.getReturnId()).isEqualTo("RETURN-456");
            assertThat(customerRefundChoice.getCustomerRefundMethod().name()).isEqualTo("GIFT_CARD");
        });
    }

    @Test
    void when_PostPurchaseEventReceived_WithMultipleItems_IsConsumed_MultipleCustomerChoicesShouldBeCreated() throws IOException {
        // arrange
        ValidOrderPlaced validOrderPlaced = FileUtils.loadSampleKafkaMessage(
            "/messages/postPurchase/validOrderPlaced_forPostPurchaseEvent_multipleItems.json", ValidOrderPlaced.class);
        validOrderPlaced.setOrderId("ORDER-456-" + UUID.randomUUID().toString().substring(0, 8));

        PostPurchaseEventReceived postPurchaseEventReceived = FileUtils.loadSampleKafkaMessage(
            "/messages/postPurchase/postPurchaseEventReceived_multipleItems.json", PostPurchaseEventReceived.class);
        postPurchaseEventReceived.setOrderId(validOrderPlaced.getOrderId());

        // act - First place the order
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        // act - Then send the post purchase event
        kafkaProducer.send(new ProducerRecord<>(postPurchaseEventReceivedTopic, postPurchaseEventReceived));

        // assert - Verify multiple customer choices were created
        await().untilAsserted(() -> {
            // Get the order and check customer choices through order lines
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();

            // Find customer choices for first product
            List<CustomerRefundChoice> firstProductChoices = order.getOrderLines().stream()
                .filter(ol -> ol.getEan().equals("ean1"))
                .flatMap(ol -> ol.getCustomerRefundChoices().stream())
                .toList();
            assertThat(firstProductChoices).hasSize(2);

            // Find customer choices for second product
            List<CustomerRefundChoice> secondProductChoices = order.getOrderLines().stream()
                .filter(ol -> ol.getEan().equals("ean2"))
                .flatMap(ol -> ol.getCustomerRefundChoices().stream())
                .toList();
            assertThat(secondProductChoices).hasSize(1);
        });
    }

    @Test
    void when_PostPurchaseEventReceived_WithZeroQuantity_IsConsumed_NoCustomerChoiceShouldBeCreated() throws IOException {
        // arrange
        ValidOrderPlaced validOrderPlaced = FileUtils.loadSampleKafkaMessage(
            "/messages/postPurchase/validOrderPlaced_forPostPurchaseEvent_zeroQuantity.json", ValidOrderPlaced.class);
        validOrderPlaced.setOrderId("ORDER-999-" + UUID.randomUUID().toString().substring(0, 8));

        PostPurchaseEventReceived postPurchaseEventReceived = FileUtils.loadSampleKafkaMessage(
            "/messages/postPurchase/postPurchaseEventReceived_zeroQuantity.json", PostPurchaseEventReceived.class);
        postPurchaseEventReceived.setOrderId(validOrderPlaced.getOrderId());

        // act - First place the order
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        // act - Then send the post purchase event
        kafkaProducer.send(new ProducerRecord<>(postPurchaseEventReceivedTopic, postPurchaseEventReceived));

        // assert - Verify no customer choices were created
        await().untilAsserted(() -> {
            // Get the order and check that no customer choices were created
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            List<CustomerRefundChoice> customerRefundChoices = order.getOrderLines().get(0).getCustomerRefundChoices();
            assertThat(customerRefundChoices).isEmpty();
        });
    }

    @Test
    void when_PostPurchaseEventReceived_IsEnqueuedViaDatabaseQueue_ShouldBeProcessedWithoutKafkaConsumption() throws IOException {
        // arrange
        ValidOrderPlaced validOrderPlaced = FileUtils.loadSampleKafkaMessage(
            "/messages/postPurchase/validOrderPlaced_forPostPurchaseEvent.json", ValidOrderPlaced.class);
        validOrderPlaced.setOrderId("ORDER-DB-" + UUID.randomUUID().toString().substring(0, 8));

        PostPurchaseEventReceived postPurchaseEventReceived = FileUtils.loadSampleKafkaMessage(
            "/messages/postPurchase/postPurchaseEventReceived.json", PostPurchaseEventReceived.class);
        postPurchaseEventReceived.setOrderId(validOrderPlaced.getOrderId());

        // act - First place the order
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        // act - Enqueue the post purchase event via database queue
        postPurchaseEventReceivedQueueProducer.enqueue(EnqueueParams.create(postPurchaseEventReceived));

        // assert - Verify that customer choices are created through database queue processing
        await().untilAsserted(() -> {
            // Get the order and check customer choices through order lines
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            List<CustomerRefundChoice> customerRefundChoices = order.getOrderLines().get(0).getCustomerRefundChoices();
            assertThat(customerRefundChoices).hasSize(2);

            CustomerRefundChoice customerRefundChoice = customerRefundChoices.get(0);
            assertThat(customerRefundChoice.getReturnId()).isEqualTo("RETURN-456");
            assertThat(customerRefundChoice.getCustomerRefundMethod().name()).isEqualTo("GIFT_CARD");
        });
    }

    @Test
    void when_PostPurchaseEventReceived_IsSentTwiceWithSameOrderIdAndReturnId_SecondMessageShouldBeRejectedByIdempotency() throws IOException {
        // arrange
        ValidOrderPlaced validOrderPlaced = FileUtils.loadSampleKafkaMessage(
            "/messages/postPurchase/validOrderPlaced_forPostPurchaseEvent.json", ValidOrderPlaced.class);
        validOrderPlaced.setOrderId("ORDER-IDEMPOTENCY-" + UUID.randomUUID().toString().substring(0, 8));

        PostPurchaseEventReceived firstMessage = FileUtils.loadSampleKafkaMessage(
            "/messages/postPurchase/postPurchaseEventReceived.json", PostPurchaseEventReceived.class);
        firstMessage.setOrderId(validOrderPlaced.getOrderId());

        PostPurchaseEventReceived secondMessage = FileUtils.loadSampleKafkaMessage(
            "/messages/postPurchase/postPurchaseEventReceived.json", PostPurchaseEventReceived.class);
        secondMessage.setOrderId(validOrderPlaced.getOrderId());
        // Same orderId and returnId as first message

        // act - First place the order
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        // act - Send the first post purchase event
        kafkaProducer.send(new ProducerRecord<>(postPurchaseEventReceivedTopic, firstMessage));

        // wait for first message to be processed
        await().untilAsserted(() -> {
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            List<CustomerRefundChoice> customerRefundChoices = order.getOrderLines().get(0).getCustomerRefundChoices();
            assertThat(customerRefundChoices).hasSize(2);
        });

        // Get the count of customer choices after first message
        Order orderAfterFirstMessage = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
        int customerChoicesAfterFirstMessage = orderAfterFirstMessage.getOrderLines().get(0).getCustomerRefundChoices().size();

        // act - Send the second post purchase event with same orderId and returnId
        kafkaProducer.send(new ProducerRecord<>(postPurchaseEventReceivedTopic, secondMessage));

        // assert - Verify that no additional customer choices were created (idempotency check worked)
        await().untilAsserted(() -> {
            Order orderAfterSecondMessage = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            List<CustomerRefundChoice> customerRefundChoices = orderAfterSecondMessage.getOrderLines().get(0).getCustomerRefundChoices();

            // Should have the same number of customer choices as after the first message
            assertThat(customerRefundChoices).hasSize(customerChoicesAfterFirstMessage);

            // Verify the existing customer choice is still there with correct data
            CustomerRefundChoice customerRefundChoice = customerRefundChoices.get(0);
            assertThat(customerRefundChoice.getReturnId()).isEqualTo("RETURN-456");
            assertThat(customerRefundChoice.getCustomerRefundMethod().name()).isEqualTo("GIFT_CARD");
        });
    }

    @Test
    void when_RefundQuantityExceedsOriginalQty_MoreQuantityRequestedToRefundExceptionIsThrown() throws IOException, InterruptedException {
        // arrange: create and place an order with one order line (originalQty = 2)
        ValidOrderPlaced validOrderPlaced = FileUtils.loadSampleKafkaMessage(
            "/messages/postPurchase/validOrderPlaced_forPostPurchaseEvent.json", ValidOrderPlaced.class);
        validOrderPlaced.setOrderId("ORDER-EXCEED-" + UUID.randomUUID().toString().substring(0, 8));
        // Set the order line's quantity to 2
        validOrderPlaced.getOrderLines().forEach(ol -> ol.setQuantity(2));

        // Place the order
        kafkaProducer.send(new ProducerRecord<>(validOrderPlacedTopic, validOrderPlaced));
        await().until(() -> orderRepository.existsById(validOrderPlaced.getOrderId()));

        // cancel on quantity by warehouse
        OrderFinalized orderFinalized = FileUtils.loadSampleKafkaMessage(
            "/messages/postPurchase/orderFinalized_cancel_ean1_one_time.json", OrderFinalized.class);
        orderFinalized.setOrderId(validOrderPlaced.getOrderId());
        kafkaProducer.send(new ProducerRecord<>(orderFinalizedTopic, orderFinalized));

        await().until(() -> {
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            return order.getOrderLines().stream()
                .anyMatch(orderLine -> orderLine.getEan().equals("ean1") && orderLine.getOpenQty() == 1);
        });

        // First refund: quantity 1 (should succeed)
        PostPurchaseEventReceived firstRefund = FileUtils.loadSampleKafkaMessage(
            "/messages/postPurchase/postPurchaseEventReceived.json", PostPurchaseEventReceived.class);
        firstRefund.setOrderId(validOrderPlaced.getOrderId());
        ((com.bestseller.interfacecontractannotator.model.postpurchaseeventreceived.ReturnCreatedPayload) firstRefund.getData())
            .getReturnRequest().forEach(r -> r.setQuantity(1));

        kafkaProducer.send(new ProducerRecord<>(postPurchaseEventReceivedTopic, firstRefund));
        await().untilAsserted(() -> {
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            List<CustomerRefundChoice> choices = order.getOrderLines().get(0).getCustomerRefundChoices();
            assertThat(choices).hasSize(1);
        });

        // Second refund: quantity 2 (should throw)
        PostPurchaseEventReceived secondRefund = FileUtils.loadSampleKafkaMessage(
            "/messages/postPurchase/postPurchaseEventReceived.json", PostPurchaseEventReceived.class);
        secondRefund.setOrderId(validOrderPlaced.getOrderId());
        ReturnCreatedPayload secondPayload = (ReturnCreatedPayload) secondRefund.getData();
        secondPayload.getReturnRequest().forEach(r -> r.setQuantity(2));
        secondPayload.setReturnId("RETURN-789"); // Different returnId to avoid idempotency issues
        secondRefund.setData(secondPayload);

        // act & assert
        kafkaProducer.send(new ProducerRecord<>(postPurchaseEventReceivedTopic, secondRefund));
        await().untilAsserted(() -> {
            Order order = orderRepository.findById(validOrderPlaced.getOrderId()).orElseThrow();
            // The number of refund choices should still be 1 (no new ones added)
            List<CustomerRefundChoice> choices = order.getOrderLines().get(0).getCustomerRefundChoices();
            assertThat(choices).hasSize(1);
        });
    }
}