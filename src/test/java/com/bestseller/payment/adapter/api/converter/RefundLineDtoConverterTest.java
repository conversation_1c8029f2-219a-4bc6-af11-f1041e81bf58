package com.bestseller.payment.adapter.api.converter;

import com.bestseller.payment.adapter.api.dto.RefundLineDto;
import com.bestseller.payment.core.domain.Refund;
import com.bestseller.payment.core.domain.RefundLine;
import com.bestseller.payment.utils.RefundGenerator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

public class RefundLineDtoConverterTest {
    RefundLineDtoConverter refundLineDtoConverter;

    @BeforeEach
    void setUp() {
        refundLineDtoConverter = Mappers.getMapper(RefundLineDtoConverter.class);
    }

    @Test
    void toRefundLineDto_nullInput() {
        // Arrange
        RefundLine refundLine = null;
        RefundLineDto expected = null;

        // Act
        var result = refundLineDtoConverter.toRefundLineDto(refundLine);

        // Assert
        assertEquals(expected, result);
    }

    @Test
    void toRefundLineDto_nullLineNumber() {
        // Arrange
        Refund refund = RefundGenerator.createRefund();
        refund.getRefundLines()
            .getFirst()
            .setLineNumber(null);

        RefundLine refundLine = refund.getRefundLines().getFirst();

        // Act
        var result = refundLineDtoConverter.toRefundLineDto(refundLine);

        // Assert
        assertNull(result.getLineNumber());
    }
}
