package com.bestseller.payment.adapter.api.controller;

import com.bestseller.payment.config.SecurityConfig;
import com.bestseller.payment.core.exception.InvalidRefundStateTransitionException;
import com.bestseller.payment.core.exception.RefundNotFoundException;
import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(RefundController.class)
@ActiveProfiles("test")
@Import(SecurityConfig.class)
class RefundControllerTest {

    @InjectMocks
    private RefundController refundController;

    @MockitoBean
    private RefundService refundService;

    @Autowired
    private MockMvc mockMvc;

    private static final String API_PATH = "/api/v1/refunds";
    private static final Integer REFUND_ID = 1;

    @Test
    @WithMockUser
    void testMarkRefundAsRefundSuccess_shouldReturnSuccessful() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders
                .put(API_PATH + "/" + REFUND_ID + "/refund-success")
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk());
        verify(refundService).updateRefundStatus(1, RefundState.REFUND_SUCCESS);
    }

    @Test
    @WithMockUser
    void testMarkRefundAsRefundSuccess_shouldReturnBadRequest() throws Exception {
        doThrow(InvalidRefundStateTransitionException.class)
            .when(refundService).updateRefundStatus(1, RefundState.REFUND_SUCCESS);


        mockMvc.perform(MockMvcRequestBuilders
                .put(API_PATH + "/" + REFUND_ID + "/refund-success")
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser
    void testMarkRefundAsRefundSuccess_shouldReturnNotFound() throws Exception {
        doThrow(RefundNotFoundException.class)
            .when(refundService).updateRefundStatus(1, RefundState.REFUND_SUCCESS);

        mockMvc.perform(MockMvcRequestBuilders
                .put(API_PATH + "/" + REFUND_ID + "/refund-success")
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNotFound());
    }

    @Test
    @WithMockUser
    void testMarkRefundAsRefundCancelled_shouldReturnSuccessful() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders
                .put(API_PATH + "/" + REFUND_ID + "/refund-cancel")
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk());
        verify(refundService).updateRefundStatus(1, RefundState.REFUND_CANCELLED);
    }

    @Test
    @WithMockUser
    void testMarkRefundAsRefundCancelled_shouldReturnBadRequest() throws Exception {
        doThrow(InvalidRefundStateTransitionException.class)
            .when(refundService).updateRefundStatus(1, RefundState.REFUND_CANCELLED);

        mockMvc.perform(MockMvcRequestBuilders
                .put(API_PATH + "/" + REFUND_ID + "/refund-cancel")
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser
    void testMarkRefundAsRefundCancelled_shouldReturnNotFound() throws Exception {
        doThrow(RefundNotFoundException.class)
            .when(refundService).updateRefundStatus(1, RefundState.REFUND_CANCELLED);

        mockMvc.perform(MockMvcRequestBuilders
                .put(API_PATH + "/" + REFUND_ID + "/refund-cancel")
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNotFound());
    }


}
