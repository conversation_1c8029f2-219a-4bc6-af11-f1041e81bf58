package com.bestseller.payment.adapter.api.converter;

import com.bestseller.payment.adapter.api.dto.OrderChargeDto;
import com.bestseller.payment.core.domain.OrderCharge;
import com.bestseller.payment.core.domain.enumeration.ChargedRefundReason;
import com.bestseller.payment.core.domain.enumeration.EntryType;
import com.bestseller.payment.utils.OrderGenerator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;

public class OrderChargeDtoConverterTest {

    OrderChargeDtoConverter orderChargeDtoConverter;

    @BeforeEach
    void setUp() {
        orderChargeDtoConverter = Mappers.getMapper(OrderChargeDtoConverter.class);
    }

    @Test
    void toOrderChargeDtoList() {
        // Arrange
        var order = OrderGenerator.createOrder();
        var expected = order.getOrderCharges()
            .stream()
            .map(orderCharge -> OrderChargeDto.builder()
                .chargeTotal(orderCharge.getChargeTotal().getGrossDiscountedTotal())
                .name(orderCharge.getName())
                .id(orderCharge.getOrderEntryId())
                .ean(orderCharge.getEan())
                .cancelled(orderCharge.getCancelled())
                .refunded(orderCharge.getRefunded())
                .type(orderCharge.getType().name())
                .allowedRefundReasons(Stream.of(
                    ChargedRefundReason.FREE_SHIPPING_CODE_DID_NOT_WORK,
                    ChargedRefundReason.CUSTOMER_FORGOT_TO_USE_FREE_SHIPPING_CODE,
                    ChargedRefundReason.SHIPPING_FEE_REFUND_TEST_BUY,
                    ChargedRefundReason.REFUND_DUE_TO_WRONG_RETURNCODE,
                    ChargedRefundReason.CUSTOMER_REFUSED_PACKAGE_BUT_PAID_RETURN_FEE,
                    ChargedRefundReason.EXPRESS_DELIVERY_NOT_ON_TIME,
                    ChargedRefundReason.CUSTOMER_NEVER_RECEIVED_PARCEL,
                    ChargedRefundReason.CUSTOMER_PAID_FOR_RETURN_SHIPMENT,
                    ChargedRefundReason.CUSTOMER_HAS_PAID_INVOICE_SHIPMENT,
                    ChargedRefundReason.RETURNED_ITEMS_VIA_POSTOFFICE_SHIPMENT,
                    ChargedRefundReason.GENERAL_RETURN_PROBLEMS_SHIPMENT
                ).map(ChargedRefundReason::getDescription).toList())
                .build())
            .toList();

        // Act
        var result = orderChargeDtoConverter.toOrderChargeDtoList(order.getOrderCharges());

        // Assert
        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(expected);
    }

    @Test
    void toOrderChargeDtoList_nullInput() {
        // Arrange
        List<OrderCharge> expected = null;

        // Act
        var actual = orderChargeDtoConverter.toOrderChargeDtoList(null);

        // Assert
        assertThat(actual)
            .isEqualTo(expected);
    }

    @Test
    void testGetOrderChargeRefundReasons_invoicePaymentFee() {
        // Arrange
        var entryType = EntryType.INVOICE_PAYMENT_FEE;
        var expected = List.of(
            ChargedRefundReason.CUSTOMER_HAS_PAID_INVOICE_PAYMENT.getDescription(),
            ChargedRefundReason.RETURNED_ITEMS_VIA_POSTOFFICE_PAYMENT.getDescription(),
            ChargedRefundReason.GENERAL_RETURN_PROBLEMS_PAYMENT.getDescription(),
            ChargedRefundReason.RETURNED_ITEMS_IN_STORE.getDescription()
        );

        // Act
        var result = orderChargeDtoConverter.getOrderChargeRefundReasons(entryType);

        // Assert
        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(expected);
    }

    @Test
    void testGetOrderChargeRefundReasons_shipmentFees() {
        // Arrange
        var entryType = EntryType.SHIPMENT_FEE;
        var expected = List.of(
            ChargedRefundReason.FREE_SHIPPING_CODE_DID_NOT_WORK.getDescription(),
            ChargedRefundReason.CUSTOMER_FORGOT_TO_USE_FREE_SHIPPING_CODE.getDescription(),
            ChargedRefundReason.SHIPPING_FEE_REFUND_TEST_BUY.getDescription(),
            ChargedRefundReason.REFUND_DUE_TO_WRONG_RETURNCODE.getDescription(),
            ChargedRefundReason.CUSTOMER_REFUSED_PACKAGE_BUT_PAID_RETURN_FEE.getDescription(),
            ChargedRefundReason.EXPRESS_DELIVERY_NOT_ON_TIME.getDescription(),
            ChargedRefundReason.CUSTOMER_NEVER_RECEIVED_PARCEL.getDescription(),
            ChargedRefundReason.CUSTOMER_PAID_FOR_RETURN_SHIPMENT.getDescription(),
            ChargedRefundReason.CUSTOMER_HAS_PAID_INVOICE_SHIPMENT.getDescription(),
            ChargedRefundReason.RETURNED_ITEMS_VIA_POSTOFFICE_SHIPMENT.getDescription(),
            ChargedRefundReason.GENERAL_RETURN_PROBLEMS_SHIPMENT.getDescription()
        );

        // Act
        var result = orderChargeDtoConverter.getOrderChargeRefundReasons(entryType);

        // Assert
        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(expected);
    }
}
