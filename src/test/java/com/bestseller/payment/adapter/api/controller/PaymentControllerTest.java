package com.bestseller.payment.adapter.api.controller;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.OrderPlaced;
import com.bestseller.payment.adapter.api.dto.PaymentValidationResponse;
import com.bestseller.payment.config.SecurityConfig;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.exception.InvalidRefundStateTransitionException;
import com.bestseller.payment.core.exception.OrderNotFoundException;
import com.bestseller.payment.core.service.order.OrderService;
import com.bestseller.payment.core.service.payment.PaymentService;
import com.bestseller.payment.utils.OrderPlacedBuilder;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(PaymentController.class)
@ActiveProfiles("test")
@Import({SecurityConfig.class, ObjectMapper.class})
class PaymentControllerTest {

    @InjectMocks
    private PaymentController paymentController;

    @MockitoBean
    private PaymentService paymentService;

    @MockitoBean
    private OrderService orderService;

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    private static final String API_PATH = "/api/v1/payments";
    private static final String ORDER_ID = "ORDER_ID";

    private OrderPlacedBuilder orderPlacedBuilder;

    @BeforeEach
    void init() {
        orderPlacedBuilder = new OrderPlacedBuilder();
    }

    @Test
    @WithMockUser
    void testValidateOrder_shouldInternalServerError() throws Exception {
        OrderPlaced orderPlaced = new OrderPlaced();
        orderPlaced.setOrderId("ORDER_ID");
        String requestBody = objectMapper.writeValueAsString(orderPlaced);
        when(paymentService.validateOrderPlaced(any(OrderPlaced.class))).thenThrow(NullPointerException.class);
        mockMvc.perform(MockMvcRequestBuilders
                .post(API_PATH + "/validate-order")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isInternalServerError());
    }

    @Test
    @WithMockUser
    void testValidateOrder_shouldBadRequest() throws Exception {
        OrderPlaced orderPlaced = new OrderPlaced();
        orderPlaced.setOrderId("ORDER_ID");
        String requestBody = objectMapper.writeValueAsString(orderPlaced);
        when(paymentService.validateOrderPlaced(any(OrderPlaced.class)))
            .thenThrow(InvalidRefundStateTransitionException.class);
        mockMvc.perform(MockMvcRequestBuilders
                .post(API_PATH + "/validate-order")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser
    void testValidateOrder_shouldReturnSuccessful() throws Exception {
        OrderPlaced orderPlaced = orderPlacedBuilder
            .withStore("TRADEBYTE")
            .build();
        String requestBody = objectMapper.writeValueAsString(orderPlaced);
        when(paymentService.validateOrderPlaced(any(OrderPlaced.class))).thenReturn(getSuccessfulResponse());
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                .post(API_PATH + "/validate-order")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andReturn();

        PaymentValidationResponse response = objectMapper.readValue(
            mvcResult.getResponse().getContentAsByteArray(), PaymentValidationResponse.class);

        assertSuccessfulValidation(response);
    }

    @Test
    @WithMockUser
    void testValidateOrder_shouldReturnValidationError() throws Exception {
        OrderPlaced orderPlaced = orderPlacedBuilder
            .withStore(null)
            .build();
        String requestBody = objectMapper.writeValueAsString(orderPlaced);
        when(paymentService.validateOrderPlaced(any(OrderPlaced.class))).thenReturn(getValidationWithErrorResponse());
        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                .post(API_PATH + "/validate-order")
                .content(requestBody)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andReturn();

        PaymentValidationResponse response = objectMapper.readValue(
            mvcResult.getResponse().getContentAsByteArray(), PaymentValidationResponse.class);

        assertValidationError(response);
    }

    @Test
    @WithMockUser
    void testIsValidToCancelPayment_shouldReturnSuccessful() throws Exception {
        when(paymentService.validateStateTransition(ORDER_ID, PaymentState.CANCELLED))
            .thenReturn(getSuccessfulResponse());
        mockMvc.perform(MockMvcRequestBuilders
                .get(API_PATH + "/" + ORDER_ID + "/validate-cancellation")
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk());
    }

    @Test
    @WithMockUser
    void testIsValidToCancelPayment_shouldReturnNotFound() throws Exception {
        when(paymentService.validateStateTransition(ORDER_ID, PaymentState.CANCELLED))
            .thenThrow(OrderNotFoundException.class);
        mockMvc.perform(MockMvcRequestBuilders
                .get(API_PATH + "/" + ORDER_ID + "/validate-cancellation")
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNotFound());
    }

    @Test
    @WithMockUser
    void testIsValidToCancelPayment_shouldReturnTransitionNotAllowed() throws Exception {
        when(paymentService.validateStateTransition(ORDER_ID, PaymentState.CANCELLED))
            .thenReturn(getValidationWithErrorResponse());
        mockMvc.perform(MockMvcRequestBuilders
                .get(API_PATH + "/" + ORDER_ID + "/validate-cancellation")
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk());
    }

    private void assertSuccessfulValidation(PaymentValidationResponse response) {
        assertNull(response.getErrorMessages());
        assertTrue(response.isSuccess());
    }

    private void assertValidationError(PaymentValidationResponse response) {
        PaymentValidationResponse errorResponse = getValidationWithErrorResponse();
        assertEquals(errorResponse.getErrorMessages(), response.getErrorMessages());
        assertFalse(response.isSuccess());
    }

    private PaymentValidationResponse getValidationWithErrorResponse() {
        PaymentValidationResponse errorResponse = new PaymentValidationResponse();
        errorResponse.setSuccess(false);
        errorResponse.setErrorMessages(List.of(String.format("Unable to determine platform from order id %s", ORDER_ID)));
        return errorResponse;
    }

    private PaymentValidationResponse getSuccessfulResponse() {
        PaymentValidationResponse successfulResponse = new PaymentValidationResponse();
        successfulResponse.setSuccess(true);
        return successfulResponse;
    }

}
