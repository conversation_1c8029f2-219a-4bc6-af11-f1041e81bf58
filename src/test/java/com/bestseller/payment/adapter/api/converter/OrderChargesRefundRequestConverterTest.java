package com.bestseller.payment.adapter.api.converter;

import com.bestseller.payment.adapter.api.dto.OrderChargeRefundDto;
import com.bestseller.payment.adapter.api.dto.OrderChargesRefundRequestDto;
import com.bestseller.payment.core.domain.enumeration.ChargedRefundReason;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

public class OrderChargesRefundRequestConverterTest {
    OrderChargesRefundRequestConverter orderChargesRefundRequestConverter;

    @BeforeEach
    void setUp() {
        orderChargesRefundRequestConverter = Mappers.getMapper(OrderChargesRefundRequestConverter.class);
    }

    @Test
    void testToOrderChargeRefund() {
        // Arrange
        OrderChargeRefundDto orderChargeRefundDto = OrderChargeRefundDto.builder()
            .orderChargeId(1)
            .reason(ChargedRefundReason.CUSTOMER_HAS_PAID_INVOICE_PAYMENT.getDescription())
            .build();

        // Act
        var actual = orderChargesRefundRequestConverter.toOrderChargeRefund(orderChargeRefundDto);

        // Assert
        assertEquals(actual.reason(), ChargedRefundReason.CUSTOMER_HAS_PAID_INVOICE_PAYMENT.getDescription());
        assertEquals(actual.orderChargeId(), 1);
    }

    @Test
    void testToOrderChargeRefund_null() {
        // Act
        var actual = orderChargesRefundRequestConverter.toOrderChargeRefund(null);

        // Assert
        assertNull(actual);
    }

    @Test
    void testToOrderChargeRefundRequest() {
        // Arrange
        var orderId = "1";
        var orderChargesRefundRequestDto = OrderChargesRefundRequestDto.builder()
            .orderChargesRefundRequestList(List.of(OrderChargeRefundDto.builder()
                .orderChargeId(1)
                .reason(ChargedRefundReason.CUSTOMER_HAS_PAID_INVOICE_PAYMENT.getDescription())
                .build()))
            .build();

        // Act
        var actual = orderChargesRefundRequestConverter.toOrderChargeRefundRequest(orderId, orderChargesRefundRequestDto);

        // Assert
        assertEquals(actual.orderId(), orderId);
        assertEquals(actual.orderChargesRefundRequestList().getFirst().reason(),
            ChargedRefundReason.CUSTOMER_HAS_PAID_INVOICE_PAYMENT.getDescription());
        assertEquals(actual.orderChargesRefundRequestList().getFirst().orderChargeId(), 1);
    }

    @Test
    void testToOrderChargeRefundRequest_nullInput() {
        // Act
        var actual = orderChargesRefundRequestConverter.toOrderChargeRefundRequest(null, null);

        // Assert
        assertNull(actual);
    }
}
