package com.bestseller.payment.adapter.api.converter;

import com.bestseller.payment.adapter.api.dto.OrderLineDto;
import com.bestseller.payment.core.domain.OrderEntryAmount;
import com.bestseller.payment.core.domain.OrderLine;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.UUID;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;

class OrderLineDtoConverterTest {

    private OrderLineDtoConverter orderLineDtoConverter;

    @BeforeEach
    void setUp() {
        orderLineDtoConverter = Mappers.getMapper(OrderLineDtoConverter.class);
    }

    @ParameterizedTest
    @MethodSource("data")
    void toOrderLineDto_givenDifferentParameters(OrderLine orderLine, OrderLineDto expected) {
        // act
        var actual = orderLineDtoConverter.toOrderLineDto(orderLine);

        // assert
        assertThat(actual)
            .usingRecursiveComparison()
            .isEqualTo(expected);
    }

    private static Stream<Arguments> data() {
        var ean = UUID.randomUUID().toString();
        return Stream.of(
            Arguments.of(null, null),
            Arguments.of(
                OrderLine.builder()
                    .ean(ean)
                    .standardRetailPrice(BigDecimal.ONE)
                    .build(),
                OrderLineDto.builder()
                    .ean(ean)
                    .standardRetailPrice(BigDecimal.ONE)
                    .build()
            ),
            Arguments.of(
                OrderLine.builder()
                    .ean(ean)
                    .standardRetailPrice(BigDecimal.ONE)
                    .orderLinePaidAmount(
                        OrderEntryAmount.builder()
                            .grossDiscountedUnitPrice(BigDecimal.TEN)
                            .unitDiscount(BigDecimal.TWO)
                            .build()
                    )
                    .build(),
                OrderLineDto.builder()
                    .ean(ean)
                    .standardRetailPrice(BigDecimal.ONE)
                    .pricePaid(BigDecimal.TEN)
                    .discount(BigDecimal.TWO)
                    .build()
            )
        );
    }

}
