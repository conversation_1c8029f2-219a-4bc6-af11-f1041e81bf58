package com.bestseller.payment.adapter.api.converter;

import com.bestseller.payment.adapter.api.dto.PaymentDto;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.payment.GiftcardPayment;
import com.bestseller.payment.utils.OrderGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderDtoConverterTest {

    @InjectMocks
    private OrderDtoConverterImpl orderDtoConverter;

    @Mock
    private OrderLineDtoConverter orderLineDtoConverter;

    @Test
    void testToPaymentDto_bankPayment() {
        // Arrange
        var order = OrderGenerator.createAdyenOrder();
        var nonGiftCardPayment = order.getPayments().getFirst();
        var expected = PaymentDto.builder()
            .subMethodType(nonGiftCardPayment.getSubMethod())
            .subMethodName(nonGiftCardPayment.getSubMethodName().getDescription())
            .subMethodId(nonGiftCardPayment.getSubMethodName().name())
            .processorId(nonGiftCardPayment.getProcessorId().name())
            .paymentTypeName(nonGiftCardPayment.getType().name())
            .transactionId(nonGiftCardPayment.getPaymentReference())
            .paymentType(nonGiftCardPayment.getType().getDescription())
            .nonGiftCardAuthorisedAmount(new BigDecimal(nonGiftCardPayment.getAuthorisedAmount()))
            .paymentStatus(order.getPaymentStatus().name())
            .giftCardAuthorisedAmount(BigDecimal.ZERO)
            .originalGrossDiscountedTotal(order.getTotalPaidPrice().getOriginalGrossDiscountedTotal())
            .grossDiscountedTotal(order.getTotalPaidPrice().getGrossDiscountedTotal())
            .build();

        // Act
        var actual = orderDtoConverter.toPaymentDto(order);

        // Assert
        assertThat(actual)
            .usingRecursiveComparison()
            .isEqualTo(expected);
    }

    @Test
    void testToPaymentDto_giftCardPayment() {
        // Arrange
        var order = OrderGenerator.createOrderPaidOnlyByGiftCard();
        var expected = PaymentDto.builder()
            .subMethodType(null)
            .subMethodName(null)
            .transactionId(null)
            .paymentType(null)
            .subMethodId(null)
            .processorId(null)
            .paymentTypeName(null)
            .nonGiftCardAuthorisedAmount(null)
            .paymentStatus(order.getPaymentStatus().name())
            .giftCardAuthorisedAmount(new BigDecimal(order.getPayments().getFirst().getAuthorisedAmount()))
            .originalGrossDiscountedTotal(order.getTotalPaidPrice().getOriginalGrossDiscountedTotal())
            .grossDiscountedTotal(order.getTotalPaidPrice().getGrossDiscountedTotal())
            .build();

        // Act
        var actual = orderDtoConverter.toPaymentDto(order);

        // Assert
        assertThat(actual)
            .usingRecursiveComparison()
            .isEqualTo(expected);
    }

    @Test
    void testToPaymentDto_mixedPayment() {
        // Arrange
        var order = OrderGenerator.createOrder(true);

        var giftCardPayment = order.getPayments().stream()
            .filter(payment -> payment instanceof GiftcardPayment)
            .findFirst()
            .orElse(null);

        var nonGiftCardPayment = order.getPayments().getFirst();
        var expected = PaymentDto.builder()
            .subMethodType(nonGiftCardPayment.getSubMethod())
            .subMethodName(nonGiftCardPayment.getSubMethodName().getDescription())
            .subMethodId(nonGiftCardPayment.getSubMethodName().name())
            .processorId(nonGiftCardPayment.getProcessorId().name())
            .paymentTypeName(nonGiftCardPayment.getType().name())
            .transactionId(nonGiftCardPayment.getPaymentReference())
            .paymentType(nonGiftCardPayment.getType().getDescription())
            .nonGiftCardAuthorisedAmount(new BigDecimal(nonGiftCardPayment.getAuthorisedAmount()))
            .paymentStatus(order.getPaymentStatus().name())
            .giftCardAuthorisedAmount(new BigDecimal(giftCardPayment.getAuthorisedAmount()))
            .originalGrossDiscountedTotal(order.getTotalPaidPrice().getOriginalGrossDiscountedTotal())
            .grossDiscountedTotal(order.getTotalPaidPrice().getGrossDiscountedTotal())
            .build();

        // Act
        var actual = orderDtoConverter.toPaymentDto(order);

        // Assert
        assertThat(actual)
            .usingRecursiveComparison()
            .isEqualTo(expected);
    }

    @Test
    void testToOrderDto() {
        // Arrange
        var order = OrderGenerator.createOrder(true);
        var expected = OrderGenerator.createOrderDto(order);
        expected.setRefundable(true);
        for (int i = 0; i < expected.getOrderLines().size(); i++) {
            when(orderLineDtoConverter.toOrderLineDto(order.getOrderLines().get(i)))
                .thenReturn(expected.getOrderLines().get(i));
        }

        // Act
        var actual = orderDtoConverter.toOrderDto(order, true);

        // Assert
        assertThat(actual)
            .usingRecursiveComparison()
            .isEqualTo(expected);
    }

    @Test
    void testToOrderDto_null() {
        // Arrange
        Order order = null;

        // Act
        var actual = orderDtoConverter.toOrderDto(order, false);

        // Assert
        assertThat(actual).isNull();
    }
}
