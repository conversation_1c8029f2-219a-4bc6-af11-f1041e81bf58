package com.bestseller.payment.adapter.api.converter;

import com.bestseller.payment.adapter.api.dto.PaymentInfoDto;
import com.bestseller.payment.core.domain.enumeration.PaymentMethod;
import com.bestseller.payment.utils.PaymentInfoGenerator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;

import static org.assertj.core.api.Assertions.assertThat;

public class PaymentInfoDtoConverterTest {

    PaymentInfoDtoConverter paymentInfoDtoConverter;

    @BeforeEach
    void setUp() {
        paymentInfoDtoConverter = Mappers.getMapper(PaymentInfoDtoConverter.class);
    }

    @Test
    void testToPaymentInfoDto_adyenCard() {
        // Arrange
        var adyenCardPayment = PaymentInfoGenerator.generateAdyenCardPayment(PaymentMethod.ADYEN_IDEAL);
        var expected = PaymentInfoDto.builder()
            .transactionId(adyenCardPayment.getPaymentReference())
            .paymentType(adyenCardPayment.getType().getDescription())
            .authorisedAmount(new BigDecimal(adyenCardPayment.getAuthorisedAmount()))
            .subMethodName(PaymentMethod.valueOf(adyenCardPayment.getSubMethodName().name()).getDescription())
            .subMethodType(adyenCardPayment.getSubMethod())
            .processorId(adyenCardPayment.getProcessorId().name())
            .subMethodId(adyenCardPayment.getSubMethodName().name())
            .type(adyenCardPayment.getType().name())
            .build();

        // Act
        var actual = paymentInfoDtoConverter.toPaymentInfoDto(adyenCardPayment);

        // Assert
        assertThat(actual)
            .usingRecursiveComparison()
            .isEqualTo(expected);
    }

    @Test
    void testToPaymentInfoDto_klarna() {
        // Arrange
        var klarnaPayment = PaymentInfoGenerator.generateKlarnaPayment(PaymentMethod.KLARNA_INVOICE);
        var expected = PaymentInfoDto.builder()
            .transactionId(klarnaPayment.getPaymentReference())
            .paymentType(klarnaPayment.getType().getDescription())
            .authorisedAmount(new BigDecimal(klarnaPayment.getAuthorisedAmount()))
            .subMethodName(PaymentMethod.valueOf(klarnaPayment.getSubMethodName().name()).getDescription())
            .subMethodType(klarnaPayment.getSubMethod())
            .processorId(klarnaPayment.getProcessorId().name())
            .subMethodId(klarnaPayment.getSubMethodName().name())
            .type(klarnaPayment.getType().name())
            .build();

        // Act
        var actual = paymentInfoDtoConverter.toPaymentInfoDto(klarnaPayment);

        // Assert
        assertThat(actual)
            .usingRecursiveComparison()
            .isEqualTo(expected);
    }

    @Test
    void testToPaymentInfoDto_offlinePayment() {
        // Arrange
        var offlinePayment = PaymentInfoGenerator.generateNonePayment();
        var expected = PaymentInfoDto.builder()
            .transactionId(offlinePayment.getPaymentReference())
            .paymentType(offlinePayment.getType().getDescription())
            .authorisedAmount(new BigDecimal(offlinePayment.getAuthorisedAmount()))
            .subMethodName("OFFLINE")
            .processorId(offlinePayment.getProcessorId().name())
            .subMethodId(null)
            .type(offlinePayment.getType().name())
            .build();

        // Act
        var actual = paymentInfoDtoConverter.toPaymentInfoDto(offlinePayment);

        // Assert
        assertThat(actual)
            .usingRecursiveComparison()
            .isEqualTo(expected);
    }
}
