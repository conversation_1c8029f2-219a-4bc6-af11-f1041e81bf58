package com.bestseller.payment.adapter.api.controller;

import com.bestseller.payment.adapter.api.converter.OrderChargesRefundRequestConverter;
import com.bestseller.payment.adapter.api.converter.OrderDtoConverter;
import com.bestseller.payment.adapter.stream.messagegenerator.PaymentStatusUpdatedGenerator;
import com.bestseller.payment.adapter.stream.validator.PaymentValidation;
import com.bestseller.payment.config.SecurityConfig;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.dto.OrderChargeRefund;
import com.bestseller.payment.core.dto.OrderChargesRefundRequest;
import com.bestseller.payment.core.exception.OrderNotFoundException;
import com.bestseller.payment.core.exception.RefundNotFoundException;
import com.bestseller.payment.core.service.order.OrderService;
import com.bestseller.payment.core.service.refund.OrderChargesRefundService;
import com.bestseller.payment.utils.OrderGenerator;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(OrderController.class)
@ActiveProfiles("test")
@Import(SecurityConfig.class)
class OrderControllerTest {

    @InjectMocks
    private OrderController orderController;

    @MockitoBean
    private OrderService orderService;

    @MockitoBean
    private OrderDtoConverter orderDtoConverter;

    @MockitoBean
    private PaymentValidation paymentValidation;

    @MockitoBean
    private OrderChargesRefundService orderChargesRefundService;

    @MockitoBean
    private OrderChargesRefundRequestConverter orderChargesRefundRequestConverter;

    @MockitoBean
    private PaymentStatusUpdatedGenerator paymentStatusUpdatedGenerator;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private MockMvc mockMvc;

    private static final String API_PATH = "/api/v1/orders";
    private static final String ORDER_ID = "ORDER_ID";

    @Test
    @WithMockUser
    void testGetOrder_shouldReturnSuccessful() throws Exception {
        var order = OrderGenerator.createOrder();
        when(orderService.getOrderById(ORDER_ID)).thenReturn(order);
        when(paymentValidation.isRefundable(order)).thenReturn(true);

        mockMvc.perform(MockMvcRequestBuilders
                .get(API_PATH + "/" + ORDER_ID)
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk());
        verify(orderDtoConverter).toOrderDto(order, true);
    }

    @Test
    @WithMockUser
    void testGetOrder_shouldReturnNotFound() throws Exception {
        when(orderService.getOrderById(ORDER_ID)).thenThrow(OrderNotFoundException.class);

        mockMvc.perform(MockMvcRequestBuilders
                .get(API_PATH + "/" + ORDER_ID)
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNotFound());
    }

    @Test
    @WithMockUser
    void testRefundOrderCharges_shouldReturnSuccessful() throws Exception {
        var body = OrderChargesRefundRequest.builder()
            .csrInitials("csrInitials")
            .orderChargesRefundRequestList(List.of(OrderChargeRefund.builder()
                .orderChargeId(1)
                .reason("reason")
                .build()))
            .build();
        mockMvc.perform(MockMvcRequestBuilders
                .post(API_PATH + "/" + ORDER_ID + "/refund-order-charges")
                .content(objectMapper.writeValueAsString(body))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk());
    }

    @Test
    @WithMockUser
    void testRefundOrderCharges_shouldReturnBadRequest() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders
                .post(API_PATH + "/" + ORDER_ID + "/refund-order-charges")
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser
    void testRefundOrderCharges_shouldReturnNotFound() throws Exception {
        var body = OrderChargesRefundRequest.builder()
            .csrInitials("csrInitials")
            .orderChargesRefundRequestList(List.of(OrderChargeRefund.builder()
                .orderChargeId(1)
                .reason("reason")
                .build()))
            .build();
        doThrow(RefundNotFoundException.class)
            .when(orderChargesRefundService).refundOrderCharges(any());
        mockMvc.perform(MockMvcRequestBuilders
                .post(API_PATH + "/" + ORDER_ID + "/refund-order-charges")
                .content(objectMapper.writeValueAsString(body))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNotFound());
    }

    @Test
    @WithMockUser
    void getPaymentStatusUpdatedMessageByOrderId_existingOrder_shouldReturnMessage() throws Exception {
        var order = OrderGenerator.createOrder(true);
        when(orderService.getOrderById(ORDER_ID)).thenReturn(order);

        mockMvc.perform(MockMvcRequestBuilders
                .get(API_PATH + "/" + ORDER_ID + "/payment-status-updated-message")
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk());
        verify(paymentStatusUpdatedGenerator).generate(order);
    }

    @Test
    @WithMockUser
    void getPaymentStatusUpdatedMessageByOrderId_nonExistingOrder_shouldReturnNotFound() throws Exception {
        when(orderService.getOrderById(ORDER_ID)).thenThrow(OrderNotFoundException.class);

        mockMvc.perform(MockMvcRequestBuilders
                .get(API_PATH + "/" + ORDER_ID + "/payment-status-updated-message")
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNotFound());
    }

    @Test
    @WithMockUser
    void getPaymentStatusUpdatedMessageByOrderId_existingOrderWithReviewStatus_shouldReturnNull() throws Exception {
        var order = OrderGenerator.createOrder(true);
        order.setPaymentStatus(PaymentState.REVIEW);
        when(orderService.getOrderById(ORDER_ID)).thenReturn(order);

        mockMvc.perform(MockMvcRequestBuilders
                .get(API_PATH + "/" + ORDER_ID + "/payment-status-updated-message")
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk());
        verify(paymentStatusUpdatedGenerator, never()).generate(order);
    }

    @Test
    @WithMockUser
    void cancelOrder_shouldReturnSuccessful() throws Exception {
        var order = OrderGenerator.createOrder();
        order.setPaymentStatus(PaymentState.REVIEW);
        when(orderService.getOrderById(ORDER_ID)).thenReturn(order);
        mockMvc.perform(MockMvcRequestBuilders
                .put(API_PATH + "/" + ORDER_ID + "/cancel")
                .accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk());
        verify(orderService).cancelOrder(ORDER_ID);
    }
}
