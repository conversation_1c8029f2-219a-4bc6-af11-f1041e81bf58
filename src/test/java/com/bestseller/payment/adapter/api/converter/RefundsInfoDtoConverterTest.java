package com.bestseller.payment.adapter.api.converter;

import com.bestseller.payment.adapter.api.dto.RefundChargeDto;
import com.bestseller.payment.adapter.api.dto.RefundDto;
import com.bestseller.payment.adapter.api.dto.RefundLineDto;
import com.bestseller.payment.adapter.api.dto.RefundLinesInfoDto;
import com.bestseller.payment.adapter.api.dto.RefundsInfoDto;
import com.bestseller.payment.core.domain.enumeration.ChargedRefundReason;
import com.bestseller.payment.utils.RefundGenerator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;

import static com.bestseller.payment.utils.OrderGenerator.ORDER_LINE_EAN;
import static com.bestseller.payment.utils.OrderGenerator.ORDER_LINE_NAME;
import static com.bestseller.payment.utils.RefundGenerator.CSR_INITIALS;
import static com.bestseller.payment.utils.RefundGenerator.REFUND_LINE_GROSS_DISCOUNTED_TOTAL_PRICE;
import static com.bestseller.payment.utils.RefundGenerator.REFUND_LINE_GROSS_DISCOUNTED_UNIT_PRICE;
import static com.bestseller.payment.utils.RefundGenerator.REQUEST_ID;
import static com.bestseller.payment.utils.RefundGenerator.RETURN_FEE;
import static org.assertj.core.api.Assertions.assertThat;

public class RefundsInfoDtoConverterTest {
    RefundsInfoDtoConverter refundsInfoDtoConverter;

    @BeforeEach
    void setUp() {
        refundsInfoDtoConverter = Mappers.getMapper(RefundsInfoDtoConverter.class);
    }

    @Test
    void testToRefundsInfoDto() {
        // Arrange
        var refunds = RefundGenerator.generateRefunds(1);

        var expected = RefundsInfoDto.builder()
            .refunds(List.of(
                RefundDto.builder().vatId("REFUND_ID0")
                    .id(0)
                    .refundStatus("REFUND_REQUESTED")
                    .refundDate(ZonedDateTime.ofInstant(refunds.get(0).getCreatedTS(), ZoneId.systemDefault()))
                    .lastUpdate(ZonedDateTime.ofInstant(refunds.get(0).getLastModifiedTS(), ZoneId.systemDefault()))
                    .refundLinesInfo(
                        RefundLinesInfoDto.builder()
                            .refundLines(
                                List.of(
                                    RefundLineDto.builder()
                                        .lineNumber(1)
                                        .quantity(1)
                                        .ean(ORDER_LINE_EAN + 1)
                                        .description(ORDER_LINE_NAME + 1)
                                        .unitDiscountedPrice(REFUND_LINE_GROSS_DISCOUNTED_UNIT_PRICE)
                                        .pricePaid(REFUND_LINE_GROSS_DISCOUNTED_TOTAL_PRICE)
                                        .build()
                                ))
                            .totalAmount(REFUND_LINE_GROSS_DISCOUNTED_TOTAL_PRICE)
                            .build()
                    )
                    .refundCharges(
                        List.of(
                            RefundChargeDto.builder()
                                .title("Return fee")
                                .description(ChargedRefundReason.SYSTEM.getDescription())
                                .amount(RETURN_FEE)
                                .isReturnFee(true)
                                .refunded(false)
                                .csrInitials(CSR_INITIALS)
                                .build()
                        )
                    )
                    .totalAmount(new BigDecimal("100.00"))
                    .canBeMarkedAsRefunded(true)
                    .canBeMarkedAsCancelled(false)
                    .refundRequestId(REQUEST_ID.toString())
                    .build()))
            .totalAmount(new BigDecimal("100.00"))
            .build();


        // Act
        var result = refundsInfoDtoConverter.toRefundsInfoDto(refunds);

        // Assert
        assertThat(result)
            .usingRecursiveComparison()
            .isEqualTo(expected);

    }
}
