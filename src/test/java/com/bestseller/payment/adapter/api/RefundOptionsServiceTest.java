package com.bestseller.payment.adapter.api;

import com.bestseller.payment.config.ServiceCredential;
import com.bestseller.payment.config.ServiceCredentialConfig;
import com.bestseller.payment.config.WebClientConfig;
import com.bestseller.payment.core.dto.RefundOptionsResponse;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.http.Fault;
import com.github.tomakehurst.wiremock.junit5.WireMockRuntimeInfo;
import com.github.tomakehurst.wiremock.junit5.WireMockTest;
import com.github.tomakehurst.wiremock.stubbing.Scenario;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.Duration;
import java.util.Map;
import java.util.Optional;

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse;
import static com.github.tomakehurst.wiremock.client.WireMock.getRequestedFor;
import static com.github.tomakehurst.wiremock.client.WireMock.stubFor;
import static com.github.tomakehurst.wiremock.client.WireMock.urlMatching;
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo;
import static com.github.tomakehurst.wiremock.client.WireMock.verify;

@ExtendWith(MockitoExtension.class)
@WireMockTest
@Slf4j
class RefundOptionsServiceTest {
    private static final String PASSWORD = "admin";
    private static final String USERNAME = "admin";
    @Mock
    WebClient fcsClient;

    @InjectMocks
    RefundOptionsImpl refundOptionsImp;

    @BeforeEach
    void setup(WireMockRuntimeInfo wmRuntimeInfo) {
        var credentials = Map.of("fulfilment-core-service",
                ServiceCredential.builder()
                        .auth(true)
                        .url(wmRuntimeInfo.getHttpBaseUrl())
                        .password(PASSWORD)
                        .username(USERNAME)
                        .build());
        var webClient = new WebClientConfig()
                .fulfilmentCoreServiceClient(new ServiceCredentialConfig(credentials));
        refundOptionsImp = new RefundOptionsImpl(webClient);
        final int maxAttempts = 3;
        final Duration backoff = Duration.ofSeconds(2);
        ReflectionTestUtils.setField(refundOptionsImp, "maxAttempts", maxAttempts);
        ReflectionTestUtils.setField(refundOptionsImp, "backoffDuration", backoff);

    }

    @Test
    void test_getRefundOptionsByOrderId() {
        // Arrange
        String orderId = "orderId";
        stubFor(
                WireMock
                    .get(urlMatching("/orders/orderId/refund-options"))
                        .willReturn(
                                aResponse()
                                        .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                                        .withBody("{\"chargeReturnFee\":true,\"refundShippingFee\":true}")
                        )
        );

        // Act
        Optional<RefundOptionsResponse> response = refundOptionsImp.getRefundOptionsByOrderId(orderId);

        // Assert
        assert response.isPresent();
    }

    @Test
    void test_return_empty() {
        // Arrange
        String orderId = "orderId";
        stubFor(
                WireMock
                    .get(urlMatching("/orders/orderId/refund-options"))
                        .willReturn(
                                aResponse()
                                        .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                                        .withBody("")
                        )
        );
        // Act
        Optional<RefundOptionsResponse> response = refundOptionsImp.getRefundOptionsByOrderId(orderId);

        // Assert
        assert response.isEmpty();
    }

    @Test
    void test_return_empty_due_server_error() {
        // Arrange
        String orderId = "orderId";
        stubFor(
                WireMock
                    .get(urlMatching("/orders/orderId/refund-options"))
                        .willReturn(
                                aResponse()
                                        .withStatus(500)
                        )
        );
        // Act
        Optional<RefundOptionsResponse> response = refundOptionsImp.getRefundOptionsByOrderId(orderId);

        // Assert
        assert response.isEmpty();
    }

    @Test
    void test_return_empty_due_url_not_found_error() {
        // Arrange
        String orderId = "orderId";
        stubFor(
                WireMock
                    .get(urlMatching("/orders/orderId/refund-options"))
                        .willReturn(
                                aResponse()
                                        .withStatus(404)
                        )
        );
        // Act
        Optional<RefundOptionsResponse> response = refundOptionsImp.getRefundOptionsByOrderId(orderId);

        // Assert
        assert response.isEmpty();
    }

    @Test
    void test_retry_mechanism() {
        // Arrange
        String orderId = "orderId";
        stubFor(
            WireMock
                .get(urlMatching("/orders/orderId/refund-options"))
                .inScenario("Retry Scenario")
                .whenScenarioStateIs(Scenario.STARTED)
                .willReturn(
                    aResponse()
                        .withFault(Fault.CONNECTION_RESET_BY_PEER)
                )
                .willSetStateTo("First Retry")
        );

        stubFor(
            WireMock
                .get(urlMatching("/orders/orderId/refund-options"))
                .inScenario("Retry Scenario")
                .whenScenarioStateIs("First Retry")
                .willReturn(
                    aResponse()
                        .withFault(Fault.CONNECTION_RESET_BY_PEER)
                ).willSetStateTo("Second Retry")
        );

        stubFor(
            WireMock
                .get(urlMatching("/orders/orderId/refund-options"))
                .inScenario("Retry Scenario")
                .whenScenarioStateIs("Second Retry")
                .willReturn(
                    aResponse()
                        .withHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
                        .withBody("{\"chargeReturnFee\":true,\"refundShippingFee\":true}")
                )
        );
        // Act
        Optional<RefundOptionsResponse> response = refundOptionsImp.getRefundOptionsByOrderId(orderId);

        // Assert
        assert response.isPresent();
        verify(3, getRequestedFor(urlPathEqualTo("/orders/orderId/refund-options"))); // Verify 3 calls
    }
}
