package com.bestseller.payment.adapter.api.converter;

import com.bestseller.payment.adapter.api.dto.OrderChargeDto;
import com.bestseller.payment.adapter.api.dto.RefundChargeDto;
import com.bestseller.payment.core.domain.enumeration.EntryType;
import com.bestseller.payment.utils.RefundGenerator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class RefundChargeDtoConverterTest {
    RefundChargeDtoConverter refundChargeDtoConverter;

    @BeforeEach
    void setUp() {
        refundChargeDtoConverter = Mappers.getMapper(RefundChargeDtoConverter.class);
    }

    @Test
    void testToRefundChargeDto() {
        // Arrange
        var csrInitials = "CSR";
        var refundCharge = RefundGenerator.createRefund().getRefundCharges().getFirst();
        var expected = RefundChargeDto.builder()
            .isReturnFee(EntryType.RETURN_FEE.equals(refundCharge.getType()))
            .description(refundCharge.getRefundReason().getDescription())
            .amount(refundCharge.getChargeTotal().getGrossDiscountedTotal())
            .title(refundCharge.getName())
            .refunded(refundCharge.getRefunded())
            .csrInitials(csrInitials)
            .build();

        // Act
        var actual = refundChargeDtoConverter.toRefundChargeDto(refundCharge, csrInitials);

        // Assert
        assertThat(actual)
            .usingRecursiveComparison()
            .isEqualTo(expected);
    }

    @Test
    void testToRefundChargeDto_nullInput() {
        // Arrange
        String csrInitials = "csrInitials";
        OrderChargeDto expected = null;

        // Act
        var actual = refundChargeDtoConverter.toRefundChargeDto(null, csrInitials);

        // Assert
        assertThat(actual)
            .isEqualTo(expected);
    }

    @Test
    void testToRefundChargeDto_nullList() {
        // Arrange
        String csrInitials = "csrInitials";
        List<OrderChargeDto> expected = null;

        // Act
        var actual = refundChargeDtoConverter.toRefundChargeDtoList(null, csrInitials);

        // Assert
        assertThat(actual)
            .isEqualTo(expected);
    }

    @Test
    void testToRefundChargeDto_nullRefundReason() {
        // Arrange
        String csrInitials = "csrInitials";
        var refundCharge = RefundGenerator.createRefund().getRefundCharges().getFirst();
        refundCharge.setRefundReason(null);
        var expected = RefundChargeDto.builder()
            .isReturnFee(EntryType.RETURN_FEE.equals(refundCharge.getType()))
            .description(null)
            .amount(refundCharge.getChargeTotal().getGrossDiscountedTotal())
            .title(refundCharge.getName())
            .refunded(refundCharge.getRefunded())
            .csrInitials(csrInitials)
            .build();

        // Act
        var actual = refundChargeDtoConverter.toRefundChargeDto(refundCharge, csrInitials);

        // Assert
        assertThat(actual)
            .usingRecursiveComparison()
            .isEqualTo(expected);
    }
}
