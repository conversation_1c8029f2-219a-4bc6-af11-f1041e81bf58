package com.bestseller.payment.adapter.stream.validator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundSucceeded.PaymentRefundSucceeded;
import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.isNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PaymentRefundSucceededMessageValidatorTest {

    @InjectMocks
    PaymentRefundSucceededMessageValidator paymentRefundSucceededMessageValidator;

    @Mock
    RefundService refundService;

    @Mock
    Validator  validator;

    @Test
    void test_getCorrelationId() {
        // Arrange
        PaymentRefundSucceeded message = new PaymentRefundSucceeded()
            .withCorrelationId("correlationId");

        // Act
        String correlationId = paymentRefundSucceededMessageValidator.getCorrelationId(message);

        // Assert
        assertEquals("correlationId", correlationId);
    }

    @Test
    void passesCustomValidation_givenValidRefundSucceeded_returnsTrue() {
        // Arrange
        PaymentRefundSucceeded message = new PaymentRefundSucceeded()
            .withCorrelationId("12345");

        when(refundService.isExists(12345)).thenReturn(true);
        when(refundService.isStateTransitionAllowed(12345, RefundState.REFUND_SUCCESS)).thenReturn(true);

        // Act
        boolean actual = paymentRefundSucceededMessageValidator.passesCustomValidation(message);

        // Assert
        assertTrue(actual);
        verify(refundService).isStateTransitionAllowed(12345, RefundState.REFUND_SUCCESS);
    }

    @Test
    void passesCustomValidation_refundNotFound_returnsFalse() {
        // Arrange
        PaymentRefundSucceeded message = new PaymentRefundSucceeded()
            .withCorrelationId("12345");

        when(refundService.isExists(12345)).thenReturn(false);

        // Act
        boolean actual = paymentRefundSucceededMessageValidator.passesCustomValidation(message);

        // Assert
        assertFalse(actual);
    }

    @Test
    void passesCustomValidation_invalidCorrelationId_returnsFalse() {
        // Arrange
        PaymentRefundSucceeded message = new PaymentRefundSucceeded()
            .withCorrelationId("invalid");

        // Act
        boolean actual = paymentRefundSucceededMessageValidator.passesCustomValidation(message);

        // Assert
        assertFalse(actual);
    }

    @Test
    void passesCustomValidation_nullCorrelationId_returnsFalse() {
        // Arrange
        PaymentRefundSucceeded message = new PaymentRefundSucceeded();

        // Act
        boolean actual = paymentRefundSucceededMessageValidator.passesCustomValidation(message);

        // Assert
        assertFalse(actual);
    }

    @Test
    void test_validate_nullMessage() {
        // arrange
        when(validator.validate(isNull())).thenReturn(Set.of(mock(ConstraintViolation.class)));

        // act
        boolean actual = paymentRefundSucceededMessageValidator.isValid(null);

        // Assert
        assertFalse(actual);
    }

    @Test
    void test_getTargetRefundState() {
        // Act
        var refundState = paymentRefundSucceededMessageValidator.getTargetRefundState();

        // Assert
        assertEquals(RefundState.REFUND_SUCCESS, refundState);
    }
}
