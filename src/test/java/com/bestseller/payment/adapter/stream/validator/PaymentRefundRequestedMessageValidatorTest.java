package com.bestseller.payment.adapter.stream.validator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequested.PaymentRefundRequested;
import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PaymentRefundRequestedMessageValidatorTest {

    @InjectMocks
    PaymentRefundRequestedMessageValidator paymentRefundRequestedMessageValidator;

    @Mock
    RefundService refundService;

    @Test
    void test_getCorrelationId() {
        // Arrange
        PaymentRefundRequested message = new PaymentRefundRequested()
            .withCorrelationId("correlationId");

        // Act
        String correlationId = paymentRefundRequestedMessageValidator.getCorrelationId(message);

        // Assert
        assertEquals("correlationId", correlationId);
    }

    @Test
    void passesCustomValidation_givenValidPaymentRefundRequested_shouldReturnTrue() {
        // Arrange
        PaymentRefundRequested message = new PaymentRefundRequested()
            .withCorrelationId("12345");

        when(refundService.isExists(12345)).thenReturn(true);
        when(refundService.isStateTransitionAllowed(12345, RefundState.REFUND_REQUESTED)).thenReturn(true);

        // Act
        boolean actual = paymentRefundRequestedMessageValidator.passesCustomValidation(message);

        // Assert
        assertTrue(actual);
        verify(refundService).isStateTransitionAllowed(12345, RefundState.REFUND_REQUESTED);
    }

    @Test
    void passesCustomValidation_givenRefundNotFound_shouldReturnFalse() {
        // Arrange
        PaymentRefundRequested message = new PaymentRefundRequested()
            .withCorrelationId("12345");

        when(refundService.isExists(12345)).thenReturn(false);

        // Act
        boolean actual = paymentRefundRequestedMessageValidator.passesCustomValidation(message);

        // Assert
        assertFalse(actual);
    }

    @Test
    void passesCustomValidation_givenInvalidCorrelationId_shouldReturnFalse() {
        // Arrange
        PaymentRefundRequested message = new PaymentRefundRequested()
            .withCorrelationId("invalid");

        // Act
        boolean actual = paymentRefundRequestedMessageValidator.passesCustomValidation(message);

        // Assert
        assertFalse(actual);
    }

    @Test
    void passesCustomValidation_givenNullCorrelationId_shouldReturnFalse() {
        // Arrange
        PaymentRefundRequested message = new PaymentRefundRequested()
            .withCorrelationId(null);

        // Act
        boolean actual = paymentRefundRequestedMessageValidator.passesCustomValidation(message);

        // Assert
        assertFalse(actual);
    }

    @Test
    void test_getTargetRefundState() {
        // Act
        var refundState = paymentRefundRequestedMessageValidator.getTargetRefundState();

        // Assert
        assertEquals(RefundState.REFUND_REQUESTED, refundState);
    }
}
