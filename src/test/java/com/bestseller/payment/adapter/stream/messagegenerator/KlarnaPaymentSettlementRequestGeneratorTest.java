package com.bestseller.payment.adapter.stream.messagegenerator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementRequest.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementRequest.PaymentSettlementRequest;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import com.bestseller.payment.core.exception.PaymentNotFoundException;
import com.bestseller.payment.core.utils.PaymentUtils;
import com.bestseller.payment.utils.OrderGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.stream.IntStream;

import static com.bestseller.payment.utils.OrderGenerator.ORDER_CHARGE_AMOUNT;
import static com.bestseller.payment.utils.OrderGenerator.ORDER_CHARGE_LINE_UNIT;
import static com.bestseller.payment.utils.OrderGenerator.ORDER_ID;
import static com.bestseller.payment.utils.OrderGenerator.ORDER_LINE_EAN;
import static com.bestseller.payment.utils.OrderGenerator.ORDER_LINE_GROSS_DISCOUNTED_TOTAL;
import static com.bestseller.payment.utils.OrderGenerator.ORDER_LINE_NAME;
import static com.bestseller.payment.utils.OrderGenerator.ORDER_LINE_QTY;
import static com.bestseller.payment.utils.OrderGenerator.SHIPPING;
import static com.bestseller.payment.utils.OrderGenerator.TAX_RATE;
import static com.bestseller.payment.utils.OrderGenerator.TOTAL_ORIGINAL_GROSS_DISCOUNTED_TOTAL;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertThrows;

@ExtendWith(MockitoExtension.class)
class KlarnaPaymentSettlementRequestGeneratorTest {
    private static final BigDecimal BD_100 = new BigDecimal("100.00");
    @InjectMocks
    private KlarnaPaymentSettlementRequestGenerator klarnaPaymentSettlementRequestGenerator;

    @Test
    void testGenerate() {
        // Arrange
        final var order = OrderGenerator.createOrder();
        changePaymentToKlarna(order);

        final var expectedOrderLines = new ArrayList<>(
                IntStream.range(0, 3)
                        .mapToObj(i -> new OrderLine()
                                .withEan(ORDER_LINE_EAN + (i + 1))
                                .withQuantity(ORDER_LINE_QTY.get(i))
                                .withPaidAmount(PaymentUtils.toIntCents(ORDER_LINE_GROSS_DISCOUNTED_TOTAL.get(i)))
                                .withDescription(ORDER_LINE_NAME + (i + 1))
                        ).toList()
        );
        expectedOrderLines.add(new OrderLine()
                .withEan(SHIPPING)
                .withQuantity(1)
                .withPaidAmount(PaymentUtils.toIntCents(ORDER_CHARGE_AMOUNT))
                .withTaxRate(PaymentUtils.toIntCents(TAX_RATE.multiply(BD_100)))
                .withTaxAmount(PaymentUtils.toIntCents(ORDER_CHARGE_LINE_UNIT))
                .withDescription(SHIPPING));

        final var expectedPaymentSettlementRequest = new PaymentSettlementRequest()
                .withOrderId(ORDER_ID)
                .withOrderId(order.getOrderId())
                .withCorrelationId(order.getOrderId())
                .withCurrency(order.getCurrency())
                .withTotalAmount(PaymentUtils.toIntCents(TOTAL_ORIGINAL_GROSS_DISCOUNTED_TOTAL))
                .withProvider(ProcessorId.KLARNA_PAYMENTS.name())
                .withOrderLines(expectedOrderLines);
        // Act
        final var actualPaymentSettlementRequest = klarnaPaymentSettlementRequestGenerator.generate(order);

        // Assert
        assert actualPaymentSettlementRequest != null;
        assertThat(actualPaymentSettlementRequest)
                .usingRecursiveComparison()
                .isEqualTo(expectedPaymentSettlementRequest);
    }

    @Test
    void testGenerate_standardShipping() {
        final var order = OrderGenerator.createOrder();
        changePaymentToKlarna(order);
        order.getOrderCharges().get(0).setEan("STANDARD_SHIPPING");

        final var actualPaymentSettlementRequest = klarnaPaymentSettlementRequestGenerator.generate(order);
        assert actualPaymentSettlementRequest != null;
        assert actualPaymentSettlementRequest.getOrderLines().size() == 4;
        assert actualPaymentSettlementRequest.getOrderLines()
                .get(actualPaymentSettlementRequest.getOrderLines().size() - 1)
                .getEan()
                .equals("Shipping");
    }

    @Test
    void testGenerate_giftCardPayment() {
        final var order = OrderGenerator.createOrder();
        changePaymentToGiftCard(order);
        // Act & Assert
        assertThrows(PaymentNotFoundException.class, () -> klarnaPaymentSettlementRequestGenerator.generate(order));
    }

    private static void changePaymentToKlarna(Order order) {
        order.getPayments().get(0).setType(PaymentType.KLARNA);
        order.getPayments().get(0).setProcessorId(ProcessorId.KLARNA_PAYMENTS);
    }

    private static void changePaymentToGiftCard(Order order) {
        order.getPayments().get(0).setType(PaymentType.GIFTCARD);
        order.getPayments().get(0).setProcessorId(ProcessorId.OPTICARD);
    }
}
