package com.bestseller.payment.adapter.stream.consumer;

import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inStoreReturnSettlement.InStoreReturnSettlement;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundFailed.PaymentRefundFailed;
import com.bestseller.payment.adapter.stream.validator.PaymentRefundFailedMessageValidator;
import com.bestseller.payment.adapter.stream.validator.idempotency.PaymentRefundFailedIdempotencyCheckService;
import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PaymentRefundFailedConsumerTest {

    private static final String CORRELATION_ID = "1234";

    @InjectMocks
    PaymentRefundFailedConsumer paymentRefundFailedConsumer;

    @Mock
    PaymentRefundFailedIdempotencyCheckService idempotencyCheckService;

    @Mock
    PaymentRefundFailedMessageValidator messageValidator;

    @Mock
    RefundService refundService;

    @Mock
    MessageFilter<PaymentRefundFailed> messageFilter;

    @Test
    void test_getMessageKey() {
        // Arrange
        PaymentRefundFailed message = new PaymentRefundFailed()
            .withCorrelationId(CORRELATION_ID);

        // Act
        var correlationId = paymentRefundFailedConsumer.getMessageDetails(message);

        // Assert
        assertThat(correlationId).isEqualTo("correlationId=%s", CORRELATION_ID);
    }

    @Test
    void test_process() {
        // Arrange
        PaymentRefundFailed message = new PaymentRefundFailed()
            .withCorrelationId(CORRELATION_ID);

        when(messageValidator.isValid(message)).thenReturn(true);
        when(idempotencyCheckService.isDuplicate(message)).thenReturn(false);
        when(messageValidator.passesCustomValidation(message)).thenReturn(true);

        // Act
        paymentRefundFailedConsumer.accept(message);

        // Assert
        verify(refundService).updateRefundStatus(1234, RefundState.REFUND_FAILED);
    }

}
