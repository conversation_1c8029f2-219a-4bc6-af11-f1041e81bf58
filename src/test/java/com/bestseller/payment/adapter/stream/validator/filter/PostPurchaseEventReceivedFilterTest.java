package com.bestseller.payment.adapter.stream.validator.filter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.postPurchaseEventReceived.PostPurchaseEventReceived;
import com.bestseller.interfacecontractannotator.model.paymentstatusupdated.AuthorizedPayload;
import com.bestseller.interfacecontractannotator.model.postpurchaseeventreceived.ReturnCreatedPayload;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

class PostPurchaseEventReceivedFilterTest {

    private final PostPurchaseEventReceivedFilter filter = new PostPurchaseEventReceivedFilter();

    @Test
    void filter_shouldReturnFalse_whenDataIsReturnCreatedPayload() {
        // Arrange
        PostPurchaseEventReceived message = new PostPurchaseEventReceived();
        message.setData(new ReturnCreatedPayload());

        // Act
        boolean result = filter.filter(message);

        // Assert
        assertThat(result).isFalse();
    }

    @Test
    void filter_shouldReturnTrue_whenDataIsNotReturnCreatedPayload() {
        // Arrange
        PostPurchaseEventReceived message = new PostPurchaseEventReceived();
        // Use an anonymous subclass to simulate a different type
        AuthorizedPayload subclassPayload = new AuthorizedPayload() {};
        message.setData(subclassPayload);

        // Act
        boolean result = filter.filter(message);

        // Assert
        assertThat(result).isTrue();
    }

    @Test
    void filter_shouldReturnTrue_whenDataIsNull() {
        // Arrange
        PostPurchaseEventReceived message = new PostPurchaseEventReceived();
        message.setData(null);

        // Act
        boolean result = filter.filter(message);

        // Assert
        assertThat(result).isTrue();
    }
}
