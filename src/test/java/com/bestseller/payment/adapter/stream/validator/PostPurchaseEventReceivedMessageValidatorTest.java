package com.bestseller.payment.adapter.stream.validator;

import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.postPurchaseEventReceived.PostPurchaseEventReceived;
import com.bestseller.interfacecontractannotator.model.postpurchaseeventreceived.ReturnCreatedPayload;
import com.bestseller.interfacecontractannotator.model.postpurchaseeventreceived.ReturnRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PostPurchaseEventReceivedMessageValidatorTest {

    private MessageValidator<PostPurchaseEventReceived> validator;

    @Mock
    private Validator beanValidator;

    @BeforeEach
    void setUp() {
        // Initialize the validator with a mock Validator
        validator = new PostPurchaseEventReceivedMessageValidator(beanValidator);
    }

    @Test
    void passesCustomValidation_givenValidMessage_returnsTrue() {
        // Arrange
        PostPurchaseEventReceived message = createValidMessage();

        // Act
        boolean result = validator.passesCustomValidation(message);

        // Assert
        assertThat(result).isTrue();
    }

    @Test
    void isValid_givenNullOrderId_returnsFalse() {
        // Arrange
        PostPurchaseEventReceived message = createValidMessage();
        message.setOrderId(null);

        ConstraintViolation<PostPurchaseEventReceived> violation = mock(ConstraintViolation.class);
        Set<ConstraintViolation<PostPurchaseEventReceived>> violations = Set.of(violation);
        when(beanValidator.validate(message)).thenReturn(violations);

        // Act
        boolean result = validator.isValid(message);

        // Assert
        assertThat(result).isFalse();
    }


    @Test
    void passesCustomValidation_givenNullData_returnsFalse() {
        // Arrange
        PostPurchaseEventReceived message = createValidMessage();
        message.setData(null);

        // Act
        boolean result = validator.passesCustomValidation(message);

        // Assert
        assertThat(result).isFalse();
    }

    @Test
    void passesCustomValidation_whenNullReturnId_returnsFalse() {
        // Arrange
        PostPurchaseEventReceived message = createValidMessage();
        ReturnCreatedPayload payload = (ReturnCreatedPayload) message.getData();
        payload.setReturnId(null);

        // Act
        boolean result = validator.passesCustomValidation(message);

        // Assert
        assertThat(result).isFalse();
    }

    @Test
    void passesCustomValidation_whenEmptyReturnId_returnsFalse() {
        // Arrange
        PostPurchaseEventReceived message = createValidMessage();
        ReturnCreatedPayload payload = (ReturnCreatedPayload) message.getData();
        payload.setReturnId("");

        // Act
        boolean result = validator.passesCustomValidation(message);

        // Assert
        assertThat(result).isFalse();
    }

    @Test
    void passesCustomValidation_whenNullRefundMethod_returnsFalse() {
        // Arrange
        PostPurchaseEventReceived message = createValidMessage();
        ReturnCreatedPayload payload = (ReturnCreatedPayload) message.getData();
        payload.setRefundMethod(null);

        // Act
        boolean result = validator.passesCustomValidation(message);

        // Assert
        assertThat(result).isFalse();
    }

    @Test
    void passesCustomValidation_whenNullReturnRequest_returnsFalse() {
        // Arrange
        PostPurchaseEventReceived message = createValidMessage();
        ReturnCreatedPayload payload = (ReturnCreatedPayload) message.getData();
        payload.setReturnRequest(null);

        // Act
        boolean result = validator.passesCustomValidation(message);

        // Assert
        assertThat(result).isFalse();
    }

    @Test
    void passesCustomValidation_whenEmptyReturnRequest_returnsFalse() {
        // Arrange
        PostPurchaseEventReceived message = createValidMessage();
        ReturnCreatedPayload payload = (ReturnCreatedPayload) message.getData();
        payload.setReturnRequest(List.of());

        // Act
        boolean result = validator.passesCustomValidation(message);

        // Assert
        assertThat(result).isFalse();
    }

    @Test
    void passesCustomValidation_whenReturnRequestWithNullEan_returnsFalse() {
        // Arrange
        PostPurchaseEventReceived message = createValidMessage();
        ReturnCreatedPayload payload = (ReturnCreatedPayload) message.getData();
        ReturnRequest returnRequest = new ReturnRequest();
        returnRequest.setEan(null);
        returnRequest.setQuantity(1);
        payload.setReturnRequest(List.of(returnRequest));

        // Act
        boolean result = validator.passesCustomValidation(message);

        // Assert
        assertThat(result).isFalse();
    }

    @Test
    void passesCustomValidation_whenReturnRequestWithBlankEan_returnsFalse() {
        // Arrange
        PostPurchaseEventReceived message = createValidMessage();
        ReturnCreatedPayload payload = (ReturnCreatedPayload) message.getData();
        ReturnRequest returnRequest = new ReturnRequest();
        returnRequest.setEan("");
        returnRequest.setQuantity(1);
        payload.setReturnRequest(List.of(returnRequest));

        // Act
        boolean result = validator.passesCustomValidation(message);

        // Assert
        assertThat(result).isFalse();
    }

    @Test
    void passesCustomValidation_whenReturnRequestWithNullQuantity_returnsFalse() {
        // Arrange
        PostPurchaseEventReceived message = createValidMessage();
        ReturnCreatedPayload payload = (ReturnCreatedPayload) message.getData();
        ReturnRequest returnRequest = new ReturnRequest();
        returnRequest.setEan("1234567890123");
        returnRequest.setQuantity(null);
        payload.setReturnRequest(List.of(returnRequest));

        // Act
        boolean result = validator.passesCustomValidation(message);

        // Assert
        assertThat(result).isFalse();
    }

    @Test
    void passesCustomValidation_whenReturnRequestWithZeroQuantity_returnsFalse() {
        // Arrange
        PostPurchaseEventReceived message = createValidMessage();
        ReturnCreatedPayload payload = (ReturnCreatedPayload) message.getData();
        ReturnRequest returnRequest = new ReturnRequest();
        returnRequest.setEan("1234567890123");
        returnRequest.setQuantity(0);
        payload.setReturnRequest(List.of(returnRequest));

        // Act
        boolean result = validator.passesCustomValidation(message);

        // Assert
        assertThat(result).isFalse();
    }

    @Test
    void passesCustomValidation_whenMultipleValidReturnRequests_returnsTrue() {
        // Arrange
        PostPurchaseEventReceived message = createValidMessage();
        ReturnCreatedPayload payload = (ReturnCreatedPayload) message.getData();
        ReturnRequest returnRequest1 = new ReturnRequest();
        returnRequest1.setEan("1234567890123");
        returnRequest1.setQuantity(2);
        ReturnRequest returnRequest2 = new ReturnRequest();
        returnRequest2.setEan("9876543210987");
        returnRequest2.setQuantity(1);
        payload.setReturnRequest(List.of(returnRequest1, returnRequest2));

        // Act
        boolean result = validator.passesCustomValidation(message);

        // Assert
        assertThat(result).isTrue();
    }

    private PostPurchaseEventReceived createValidMessage() {
        ReturnRequest returnRequest = new ReturnRequest();
        returnRequest.setEan("1234567890123");
        returnRequest.setQuantity(1);

        ReturnCreatedPayload payload = new ReturnCreatedPayload();
        payload.setReturnId("RETURN-123");
        payload.setReturnRequest(List.of(returnRequest));
        payload.setRefundMethod("giftCard");

        PostPurchaseEventReceived message = new PostPurchaseEventReceived();
        message.setOrderId("ORDER-123");
        message.setData(payload);

        return message;
    }
} 