package com.bestseller.payment.adapter.stream.messagegenerator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequest.PaymentRefundRequest;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.Refund;
import com.bestseller.payment.core.dto.GiftCardRefundSource;
import com.bestseller.payment.core.dto.RefundDto;
import com.bestseller.payment.core.dto.RefundReason;
import com.bestseller.payment.utils.OrderGenerator;
import com.bestseller.payment.utils.RefundGenerator;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.stream.Stream;

import static com.bestseller.payment.utils.OrderGenerator.BRAND;
import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class GiftCardPaymentRefundRequestGeneratorTest {

    @InjectMocks
    GiftCardPaymentRefundRequestGenerator giftCardPaymentRefundRequestGenerator;

    @ParameterizedTest
    @MethodSource("giftCardRefundSourceProvider")
    void generate_givenRefundDto_returnsPaymentRefundRequest(GiftCardRefundSource giftCardRefundSource) {
        // arrange
        Refund refund = RefundGenerator.createRefund();
        Order order = refund.getOrder();
        order.setPayments(List.of(OrderGenerator.createGiftcardPayment()));

        int refundAmountInCents = 10000;
        RefundDto refundDto = createRefundDto(refund, refundAmountInCents, giftCardRefundSource);
        PaymentRefundRequest expected = createExpectedPaymentRefundRequest(refund, order, refundAmountInCents, giftCardRefundSource);

        // act
        var actual = giftCardPaymentRefundRequestGenerator.generate(refundDto);

        // assert
        assertThat(actual).usingRecursiveComparison().isEqualTo(expected);
    }

    private static Stream<Arguments> giftCardRefundSourceProvider() {
        return Stream.of(
            Arguments.of(GiftCardRefundSource.ORIGINAL_PAYMENT),
            Arguments.of(GiftCardRefundSource.CUSTOMER_PREFERENCE)
        );
    }

    private RefundDto createRefundDto(Refund refund, int refundAmountInCents, GiftCardRefundSource giftCardRefundSource) {
        return RefundDto.builder()
            .refund(refund)
            .refundAmountInCents(refundAmountInCents)
            .refundReason(RefundReason.RETURN)
            .giftCardRefundSource(giftCardRefundSource)
            .build();
    }

    private PaymentRefundRequest createExpectedPaymentRefundRequest(Refund refund, Order order, int refundAmountInCents, GiftCardRefundSource giftCardRefundSource) {
        return new PaymentRefundRequest()
            .withCorrelationId(refund.getGiftCardCorrelationId().toString())
            .withOrderId(order.getOrderId())
            .withTotalAmount(refundAmountInCents)
            .withReason(RefundReason.RETURN.name())
            .withCurrency(order.getCurrency())
            .withBillingCountry(order.getBillingCountryCode())
            .withBrand(BRAND.getBrandAbbreviation())
            .withProvider("OPTICARD_PHASE_ONE")
            .withGiftCardRefundSource(giftCardRefundSource.name());
    }
}
