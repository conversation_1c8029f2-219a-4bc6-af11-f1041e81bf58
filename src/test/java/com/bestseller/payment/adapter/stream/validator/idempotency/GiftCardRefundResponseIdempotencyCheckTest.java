package com.bestseller.payment.adapter.stream.validator.idempotency;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.giftCardRefundResponse.GiftCardRefundResponse;
import com.bestseller.payment.core.domain.Refund;
import com.bestseller.payment.core.service.refund.RefundService;
import com.bestseller.payment.utils.RefundGenerator;
import com.logistics.statetransition.RefundState;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class GiftCardRefundResponseIdempotencyCheckTest {

    @InjectMocks
    GiftCardRefundResponseIdempotencyCheck giftCardRefundResponseIdempotencyCheck;

    @Mock
    RefundService refundService;

    Refund refund;
    GiftCardRefundResponse giftCardRefundResponse;

    @BeforeEach
    void setUp() {
        refund = RefundGenerator.createRefund();
        giftCardRefundResponse = new GiftCardRefundResponse()
            .withCorrelationId(UUID.randomUUID().toString())
            .withOrderId(refund.getOrder().getOrderId());

        when(refundService.getRefund(
            refund.getOrder().getOrderId(), UUID.fromString(giftCardRefundResponse.getCorrelationId())))
            .thenReturn(refund);
    }

    @Test
    void test_isDuplicate_givenSuccessfulResponse_returnsTrue() {
        // Arrange
        refund.setRefundState(RefundState.REFUND_SUCCESS);
        giftCardRefundResponse.withStatus(true);


        // Act
        boolean result = giftCardRefundResponseIdempotencyCheck.isDuplicate(giftCardRefundResponse);

        // Assert
        assertThat(result).isTrue();
    }

    @Test
    void test_isDuplicate_givenSuccessfulResponse_returnsFalse() {
        // Arrange
        refund.setRefundState(RefundState.REFUND_REQUESTING);
        giftCardRefundResponse.withStatus(true);

        // Act
        boolean result = giftCardRefundResponseIdempotencyCheck.isDuplicate(giftCardRefundResponse);

        // Assert
        assertThat(result).isFalse();
    }

    @Test
    void test_isDuplicate_givenSuccessfulResponseForFailRefund_returnsFalse() {
        // Arrange
        refund.setRefundState(RefundState.REFUND_FAILED);
        giftCardRefundResponse.withStatus(true);

        // Act
        boolean result = giftCardRefundResponseIdempotencyCheck.isDuplicate(giftCardRefundResponse);

        // Assert
        assertThat(result).isFalse();
    }

    @Test
    void test_isDuplicate_givenUnsuccessfulResponse_returnsTrue() {
        // Arrange
        refund.setRefundState(RefundState.REFUND_FAILED);
        giftCardRefundResponse.withStatus(false);

        // Act
        boolean result = giftCardRefundResponseIdempotencyCheck.isDuplicate(giftCardRefundResponse);

        // Assert
        assertThat(result).isTrue();
    }

    @Test
    void test_isDuplicate_givenUnsuccessfulResponse_returnsFalse() {
        // Arrange
        refund.setRefundState(RefundState.REFUND_REQUESTING);
        giftCardRefundResponse.withStatus(false);

        // Act
        boolean result = giftCardRefundResponseIdempotencyCheck.isDuplicate(giftCardRefundResponse);

        // Assert
        assertThat(result).isFalse();
    }
}
