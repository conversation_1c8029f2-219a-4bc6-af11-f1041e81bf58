package com.bestseller.payment.adapter.stream.consumer;

import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inStoreReturnSettlement.InStoreReturnSettlement;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inStoreReturnSettlement.OrderLine;
import com.bestseller.payment.adapter.stream.validator.InStoreReturnSettlementValidator;
import com.bestseller.payment.adapter.stream.validator.idempotency.InStoreReturnSettlementIdempotencyCheck;
import com.bestseller.payment.core.service.refund.InStoreRefundService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InStoreReturnSettlementConsumerTest {

    @InjectMocks
    InStoreReturnSettlementConsumer inStoreReturnSettlementConsumer;

    @Mock
    InStoreReturnSettlementIdempotencyCheck idempotencyCheckService;

    @Mock
    InStoreReturnSettlementValidator messageValidator;

    @Mock
    MessageFilter<InStoreReturnSettlement> messageFilter;

    @Mock
    InStoreRefundService refundService;

    @Mock
    QueueProducer<InStoreReturnSettlement> queueProducer;

    @Test
    void testConsume() {
        // Arrange
        InStoreReturnSettlement inStoreReturnSettlement = new InStoreReturnSettlement()
            .withTotalRefundAmount(BigDecimal.TEN)
            .withOrderId("orderId")
            .withRefundId(2L)
            .withOrderLines(List.of(new OrderLine()
                .withQuantity(1)
                .withEan("ean")
            ));

        when(messageValidator.isValid(inStoreReturnSettlement)).thenReturn(true);
        when(idempotencyCheckService.isDuplicate(inStoreReturnSettlement)).thenReturn(false);
        when(messageValidator.passesCustomValidation(inStoreReturnSettlement)).thenReturn(true);

        // Act
        inStoreReturnSettlementConsumer.accept(inStoreReturnSettlement);
        // Assert
        verify(refundService).issueRefundForInStoreReturn(inStoreReturnSettlement);
    }
}
