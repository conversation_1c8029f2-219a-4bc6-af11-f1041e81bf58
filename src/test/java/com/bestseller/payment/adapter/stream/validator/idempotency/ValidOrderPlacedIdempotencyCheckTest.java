package com.bestseller.payment.adapter.stream.validator.idempotency;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.payment.adapter.repository.OrderRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ValidOrderPlacedIdempotencyCheckTest {

    private static final String ORDER_ID = "ORDER_ID";

    @InjectMocks
    ValidOrderPlacedIdempotencyCheck validOrderPlacedIdempotencyCheck;

    @Mock
    private OrderRepository orderRepository;

    @Test
    void testIsDuplicateMessage() {
        // Arrange
        ValidOrderPlaced message = new ValidOrderPlaced().withOrderId(ORDER_ID);
        when(orderRepository.existsById(any())).thenReturn(true);

        // Act
        boolean result = validOrderPlacedIdempotencyCheck.isDuplicate(message);

        // Assert
        assertTrue(result);
        verify(orderRepository).existsById(ORDER_ID);
    }

    @Test
    void testIsDuplicateMessage_orderNotExist() {
        // Arrange
        ValidOrderPlaced message = new ValidOrderPlaced().withOrderId(ORDER_ID);
        when(orderRepository.existsById(any())).thenReturn(false);

        // Act
        boolean result = validOrderPlacedIdempotencyCheck.isDuplicate(message);

        // Assert
        assertFalse(result);
        verify(orderRepository).existsById(ORDER_ID);
    }

}
