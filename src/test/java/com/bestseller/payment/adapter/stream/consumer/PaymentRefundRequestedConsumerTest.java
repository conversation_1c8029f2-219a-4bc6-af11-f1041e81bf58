package com.bestseller.payment.adapter.stream.consumer;

import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequested.PaymentRefundRequested;
import com.bestseller.payment.adapter.stream.validator.PaymentRefundRequestedMessageValidator;
import com.bestseller.payment.adapter.stream.validator.idempotency.PaymentRefundRequestedIdempotencyCheckService;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PaymentRefundRequestedConsumerTest {

    private static final String CORRELATION_ID = "1234";

    @InjectMocks
    PaymentRefundRequestedConsumer paymentRefundRequestedConsumer;

    @Mock
    PaymentRefundRequestedIdempotencyCheckService idempotencyCheckService;

    @Mock
    PaymentRefundRequestedMessageValidator messageValidator;

    @Mock
    RefundService refundService;

    @Mock
    MessageFilter<PaymentRefundRequested> messageFilter;

    @Test
    void test_getMessageKey() {
        // Arrange
        PaymentRefundRequested message = new PaymentRefundRequested()
            .withCorrelationId(CORRELATION_ID);

        // Act
        var correlationId = paymentRefundRequestedConsumer.getMessageDetails(message);

        // Assert
        assertThat(correlationId).isEqualTo("correlationId=%s", CORRELATION_ID);
    }

    @Test
    void test_process() {
        // Arrange
        PaymentRefundRequested message = new PaymentRefundRequested()
            .withProvider(ProcessorId.ADYEN.name())
            .withCorrelationId(CORRELATION_ID);

        when(messageValidator.isValid(message)).thenReturn(true);
        when(idempotencyCheckService.isDuplicate(message)).thenReturn(false);
        when(messageValidator.passesCustomValidation(message)).thenReturn(true);

        // Act
        paymentRefundRequestedConsumer.accept(message);

        // Assert
        verify(refundService).updateRefundStatus(1234, RefundState.REFUND_REQUESTED);
    }

}
