package com.bestseller.payment.adapter.stream.validator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementFailed.PaymentSettlementFailed;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PaymentSettlementFailedMessageValidatorTest {
    private static final String ORDER_ID = "ORDER_ID";
    private static final String PROVIDER = ProcessorId.ADYEN.name();

    @InjectMocks
    private PaymentSettlementFailedMessageValidator paymentSettlementFailedMessageValidator;

    @Mock
    private PaymentValidation paymentValidation;

    @Test
    void passesCustomValidation_givenValidMessage_shouldReturnTrue() {
        // Arrange
        PaymentSettlementFailed message = createPaymentSettlementFailed();
        when(paymentValidation.isSettlementSupported(message.getProvider())).thenReturn(true);

        // Act
        boolean result = paymentSettlementFailedMessageValidator.passesCustomValidation(message);

        // Assert
        assertTrue(result);
    }

    @Test
    void passesCustomValidation_givenNullCorrelationId_shouldReturnFalse() {
        // Arrange
        PaymentSettlementFailed message = createPaymentSettlementFailed();
        message.setCorrelationId(null);

        // Act
        boolean result = paymentSettlementFailedMessageValidator.passesCustomValidation(message);

        // Assert
        assertFalse(result);
        verify(paymentValidation, times(1)).isSettlementSupported(message.getProvider());
    }

    @Test
    void passesCustomValidation_givenUnknownProvider_shouldReturnFalse() {
        // Arrange
        PaymentSettlementFailed message = createPaymentSettlementFailed();
        message.setProvider("UNKNOWN");

        // Act
        boolean result = paymentSettlementFailedMessageValidator.passesCustomValidation(message);

        // Assert
        assertFalse(result);
        verify(paymentValidation, times(1)).isSettlementSupported(message.getProvider());
    }

    @Test
    void passesCustomValidation_givenNullProvider_shouldReturnFalse() {
        // Arrange
        PaymentSettlementFailed message = createPaymentSettlementFailed();
        message.setProvider(null);

        // Act
        boolean result = paymentSettlementFailedMessageValidator.passesCustomValidation(message);

        // Assert
        assertFalse(result);
        verify(paymentValidation, times(1)).isSettlementSupported(message.getProvider());
    }

    @Test
    void passesCustomValidation_givenUnsupportedProvider_shouldReturnFalse() {
        // Arrange
        PaymentSettlementFailed message = createPaymentSettlementFailed();
        message.setProvider(ProcessorId.OPTICARD.name());

        // Act
        boolean result = paymentSettlementFailedMessageValidator.passesCustomValidation(message);

        // Assert
        assertFalse(result);
        verify(paymentValidation, times(1)).isSettlementSupported(message.getProvider());
    }

    private PaymentSettlementFailed createPaymentSettlementFailed() {
        return new PaymentSettlementFailed()
            .withCorrelationId(ORDER_ID)
            .withProvider(PROVIDER);
    }
}
