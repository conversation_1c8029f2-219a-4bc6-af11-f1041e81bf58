package com.bestseller.payment.adapter.stream.validator.idempotency;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inStoreReturnSettlement.InStoreReturnSettlement;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inStoreReturnSettlement.OrderLine;
import com.bestseller.payment.adapter.repository.RefundRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InStoreReturnSettlementIdempotencyCheckTest {

    @Mock
    RefundRepository refundRepository;
    @InjectMocks
    InStoreReturnSettlementIdempotencyCheck inStoreReturnSettlementIdempotencyCheck;

    @Test
    void test_repository_call() {
        // Arrange
        InStoreReturnSettlement inStoreReturnSettlement = new InStoreReturnSettlement()
            .withTotalRefundAmount(BigDecimal.TEN)
            .withOrderId("orderId")
            .withRefundId(123456789L)
            .withOrderLines(List.of(new OrderLine()
                .withQuantity(1)
                .withEan("ean")
            ));

        // Act
        inStoreReturnSettlementIdempotencyCheck.isDuplicate(inStoreReturnSettlement);

        // Assert
        verify(refundRepository).existsByOrderIdAndRequestId("orderId", "123456789");
    }

    @Test
    void test_isDuplicateMessage() {
        // Arrange
        InStoreReturnSettlement inStoreReturnSettlement = new InStoreReturnSettlement()
            .withTotalRefundAmount(BigDecimal.TEN)
            .withOrderId("orderId")
            .withRefundId(123456789L)
            .withOrderLines(List.of(new OrderLine()
                .withQuantity(1)
                .withEan("ean")
            ));
        when(refundRepository.existsByOrderIdAndRequestId("orderId", "123456789")).thenReturn(true);

        // Act
        boolean result = inStoreReturnSettlementIdempotencyCheck.isDuplicate(inStoreReturnSettlement);

        // Assert
        assert result;
    }
}
