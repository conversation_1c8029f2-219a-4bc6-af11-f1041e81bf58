package com.bestseller.payment.adapter.stream.consumer;

import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inStoreReturnSettlement.InStoreReturnSettlement;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentAuthorized.PaymentAuthorized;
import com.bestseller.payment.adapter.stream.validator.PaymentAuthorizedMessageValidator;
import com.bestseller.payment.adapter.stream.validator.idempotency.PaymentAuthorizedIdempotencyCheck;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.service.payment.PaymentServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
class PaymentAuthorizedConsumerTest {

    private static final String ORDER_ID = "ORDER_ID";
    private static final String PSP_REFERENCE = "psp-reference";
    @InjectMocks
    PaymentAuthorizedConsumer paymentAuthorizedConsumer;

    @Mock
    PaymentServiceImpl paymentServiceImp;

    @Mock
    PaymentAuthorizedIdempotencyCheck idempotencyCheckService;

    @Mock
    PaymentAuthorizedMessageValidator messageValidator;

    @Mock
    MessageFilter<PaymentAuthorized> messageFilter;

    @Test
    void testAccept() {
        testAccept("ADYEN");
        reset(paymentServiceImp);
        testAccept("KLARNA_PAYMENTS");
        reset(paymentServiceImp);
        testAccept("OPTICARD");
    }

    @Test
    void testGetMessageKey() {
        // Arrange
        PaymentAuthorized paymentAuthorized = new PaymentAuthorized();
        paymentAuthorized.setOrderId(ORDER_ID);

        // Act
        String orderId = paymentAuthorizedConsumer.getMessageDetails(paymentAuthorized);

        // Assert
        assertThat(orderId).isEqualTo("orderId=%s", ORDER_ID);
    }

    private void testAccept(String provider) {
        PaymentAuthorized paymentAuthorized = new PaymentAuthorized();
        paymentAuthorized.setOrderId(ORDER_ID);
        paymentAuthorized.setProvider(provider);
        paymentAuthorized.setPspReference(PSP_REFERENCE);

        when(messageValidator.isValid(paymentAuthorized)).thenReturn(true);
        when(idempotencyCheckService.isDuplicate(paymentAuthorized)).thenReturn(false);
        when(messageValidator.passesCustomValidation(paymentAuthorized)).thenReturn(true);

        paymentAuthorizedConsumer.accept(paymentAuthorized);
        verify(paymentServiceImp).updatePaymentStatus(ORDER_ID, PaymentState.AUTHORISED);
    }
}
