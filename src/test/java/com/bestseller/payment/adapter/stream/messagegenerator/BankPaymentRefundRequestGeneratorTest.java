package com.bestseller.payment.adapter.stream.messagegenerator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequest.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequest.PaymentRefundRequest;
import com.bestseller.payment.core.domain.Refund;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.dto.RefundDto;
import com.bestseller.payment.core.dto.RefundReason;
import com.bestseller.payment.core.utils.PaymentUtils;
import com.bestseller.payment.utils.RefundGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static com.bestseller.payment.utils.OrderGenerator.BRAND;
import static com.bestseller.payment.utils.OrderGenerator.PSP_REFERENCE;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
 class BankPaymentRefundRequestGeneratorTest {

    private static final BigDecimal BD_100 = new BigDecimal(100);

    @InjectMocks
    BankPaymentRefundRequestGenerator bankPaymentRefundRequestGenerator;

    @Test
    void testGenerate() {
        // Arrange
        Refund refund = RefundGenerator.createRefund();
        int refundAmountInCents = 10000;
        RefundDto refundDto = RefundDto.builder()
            .refund(refund)
            .refundAmountInCents(refundAmountInCents)
            .refundReason(RefundReason.RETURN)
            .build();

        var orderLines = new java.util.ArrayList<>(refund.getRefundLines()
            .stream()
            .map(refundLine -> new OrderLine()
                .withEan(refundLine.getOrderLine().getEan())
                .withDescription(refundLine.getOrderLine().getName())
                .withPaidAmount(refundLine.getRefundLineTotal().getGrossDiscountedUnitPrice().multiply(BD_100).intValue())
                .withTaxRate(refundLine.getOrderLine().getTaxRate().multiply(BD_100).multiply(BD_100).intValue())
                .withTaxAmount(refundLine.getRefundLineTotal().getUnitVAT().multiply(BD_100).intValue())
                .withQuantity(refundLine.getQuantity())
            ).toList());
        orderLines.addAll(refund.getRefundCharges()
            .stream()
            .map(refundCharge -> new OrderLine()
                .withEan(refundCharge.getName())
                .withDescription(refundCharge.getName())
                .withPaidAmount(PaymentUtils.toIntCents(refundCharge.getChargeTotal().getGrossDiscountedUnitPrice()))
                .withQuantity(refundCharge.getOpenQty())
                .withTaxRate(refundCharge.getTaxRate().multiply(BD_100).multiply(BD_100).intValue())
                .withTaxAmount(PaymentUtils.toIntCents(refundCharge.getChargeTotal().getUnitVAT()))
            ).toList());

        PaymentRefundRequest expected = new PaymentRefundRequest()
            .withCorrelationId(refund.getId().toString())
            .withTotalAmount(refundAmountInCents)
            .withCurrency(refund.getOrder().getCurrency())
            .withOrderId(refund.getOrder().getOrderId())
            .withProvider("ADYEN")
            .withPspPaymentReference(PSP_REFERENCE)
            .withReason(refundDto.refundReason().name())
            .withBillingCountry(refund.getOrder().getBillingCountryCode())
            .withBrand(BRAND.getBrandAbbreviation())
            .withOrderLines(orderLines);

        // Act
        PaymentRefundRequest actual = bankPaymentRefundRequestGenerator.generate(refundDto);

        // Assert
        assertThat(actual).usingRecursiveComparison().isEqualTo(expected);
    }

    @Test
    void testGenerate_onlyGiftCardPayment() {
        // Arrange
        Refund refund = RefundGenerator.createRefund();
        refund.getOrder().setPayments(
            refund.getOrder().getPayments().stream()
                .filter(payment -> payment.getType().equals(PaymentType.GIFTCARD))
                .toList());
        int refundAmountInCents = 10000;
        RefundDto refundDto = RefundDto.builder()
            .refund(refund)
            .refundAmountInCents(refundAmountInCents)
            .refundReason(RefundReason.RETURN)
            .build();

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> bankPaymentRefundRequestGenerator.generate(refundDto));
    }
}
