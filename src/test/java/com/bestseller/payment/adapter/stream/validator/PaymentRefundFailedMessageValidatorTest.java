package com.bestseller.payment.adapter.stream.validator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundFailed.PaymentRefundFailed;
import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PaymentRefundFailedMessageValidatorTest {

    @InjectMocks
    PaymentRefundFailedMessageValidator paymentRefundFailedMessageValidator;

    @Mock
    RefundService refundService;

    @Test
    void test_getCorrelationId() {
        // Arrange
        PaymentRefundFailed message = new PaymentRefundFailed()
            .withCorrelationId("correlationId");

        // Act
        String correlationId = paymentRefundFailedMessageValidator.getCorrelationId(message);

        // Assert
        assertEquals("correlationId", correlationId);
    }

    @Test
    void passesCustomValidation_givenValidRefundFailedMessage_shouldPass() {
        // Arrange
        PaymentRefundFailed message = new PaymentRefundFailed()
            .withCorrelationId("12345");

        when(refundService.isExists(12345)).thenReturn(true);
        when(refundService.isStateTransitionAllowed(12345, RefundState.REFUND_FAILED)).thenReturn(true);

        // Act
        boolean actual = paymentRefundFailedMessageValidator.passesCustomValidation(message);

        // Assert
        assertTrue(actual);
        verify(refundService).isStateTransitionAllowed(12345, RefundState.REFUND_FAILED);
    }

    @Test
    void passesCustomValidation_givenRefundNotFound_shouldReturnFalse() {
        // Arrange
        PaymentRefundFailed message = new PaymentRefundFailed()
            .withCorrelationId("12345");

        when(refundService.isExists(12345)).thenReturn(false);

        // Act
        boolean actual = paymentRefundFailedMessageValidator.passesCustomValidation(message);

        // Assert
        assertFalse(actual);
    }

    @Test
    void passesCustomValidation_givenInvalidCorrelationId_shouldReturnFalse() {
        // Arrange
        PaymentRefundFailed message = new PaymentRefundFailed()
            .withCorrelationId("invalid");

        // Act
        boolean actual = paymentRefundFailedMessageValidator.passesCustomValidation(message);

        // Assert
        assertFalse(actual);
        verifyNoInteractions(refundService);
    }

    @Test
    void passesCustomValidation_givenNullCorrelationId_shouldReturnFalse() {
        // Arrange
        PaymentRefundFailed message = new PaymentRefundFailed();

        // Act
        boolean actual = paymentRefundFailedMessageValidator.passesCustomValidation(message);

        // Assert
        assertFalse(actual);
        verifyNoInteractions(refundService);
    }

    @Test
    void test_getTargetRefundState() {
        // Act
        var actual = paymentRefundFailedMessageValidator.getTargetRefundState();

        // Assert
        assertEquals(RefundState.REFUND_FAILED, actual);
    }
}
