package com.bestseller.payment.adapter.stream.validator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.giftCardRefundResponse.GiftCardRefundResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
public class GiftCardRefundResponseValidatorTest {
    @InjectMocks
    GiftCardRefundResponseValidator giftCardRefundResponseValidator;

    GiftCardRefundResponse message;

    @BeforeEach
    void setUp() {
        message = new GiftCardRefundResponse()
            .withOrderId("orderId")
            .withCorrelationId(UUID.randomUUID().toString());
    }

    @Test
    void passesCustomValidation_givenCorrelationIdIsNotValidUUID_shouldReturnFalse() {
        // Arrange
        message.setCorrelationId("correlationId");

        // Act
        boolean result = giftCardRefundResponseValidator.passesCustomValidation(message);

        // Assert
        assertThat(result).isFalse();
    }

    @Test
    void passesCustomValidation_givenCorrelationIdIsValidUUID_shouldReturnTrue() {
        // Act
        boolean result = giftCardRefundResponseValidator.passesCustomValidation(message);

        // Assert
        assertThat(result).isTrue();
    }
}
