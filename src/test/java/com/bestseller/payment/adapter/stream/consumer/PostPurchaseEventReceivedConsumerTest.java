package com.bestseller.payment.adapter.stream.consumer;

import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.postPurchaseEventReceived.PostPurchaseEventReceived;
import com.bestseller.interfacecontractannotator.model.postpurchaseeventreceived.ReturnCreatedPayload;
import com.bestseller.payment.adapter.stream.validator.PostPurchaseEventReceivedMessageValidator;
import com.bestseller.payment.adapter.stream.validator.idempotency.PostPurchaseEventReceivedIdempotencyChecker;
import com.bestseller.payment.core.service.customerchoice.CustomerRefundChoiceService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.dao.TransientDataAccessException;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PostPurchaseEventReceivedConsumerTest {

    private static final String ORDER_ID = "ORDER-123";
    private static final String RETURN_ID = "RETURN-456";

    @Mock
    private CustomerRefundChoiceService customerRefundChoiceService;

    @Mock
    private PostPurchaseEventReceivedIdempotencyChecker idempotencyChecker;

    @Mock
    private PostPurchaseEventReceivedMessageValidator messageValidator;

    @Mock
    private QueueProducer<PostPurchaseEventReceived> queueProducer;

    @Mock
    MessageFilter<PostPurchaseEventReceived> messageFilter;

    @InjectMocks
    private PostPurchaseEventReceivedConsumer consumer;

    @Test
    void accept_MessageWithoutRefundObject_willBeIgnored() {
        // Arrange
        var postPurchaseEventReceived = new PostPurchaseEventReceived();
        postPurchaseEventReceived.setOrderId(ORDER_ID);

        when(messageValidator.isValid(any(PostPurchaseEventReceived.class))).thenReturn(true);
        when(messageFilter.filter(postPurchaseEventReceived)).thenReturn(true);

        // Act
        consumer.accept(postPurchaseEventReceived);

        // Assert
        verify(customerRefundChoiceService, times(0)).processPostPurchaseEvent(postPurchaseEventReceived);
    }

    @Test
    void accept_whenValidMessage_customerChoiceServiceIsInvoked() {
        // Arrange
        var returnCreatedPayload = new ReturnCreatedPayload();
        returnCreatedPayload.setReturnId(RETURN_ID);

        var postPurchaseEventReceived = new PostPurchaseEventReceived();
        postPurchaseEventReceived.setOrderId(ORDER_ID);
        postPurchaseEventReceived.setData(returnCreatedPayload);

        when(messageValidator.isValid(postPurchaseEventReceived)).thenReturn(true);
        when(idempotencyChecker.isDuplicate(postPurchaseEventReceived)).thenReturn(false);
        when(messageValidator.passesCustomValidation(postPurchaseEventReceived)).thenReturn(true);


        // Act
        consumer.accept(postPurchaseEventReceived);

        // Assert
        verify(customerRefundChoiceService, times(1)).processPostPurchaseEvent(postPurchaseEventReceived);
    }

    @Test
    void accept_whenDuplicateMessage_customerChoiceServiceIsNotInvoked() {
        // Arrange
        var returnCreatedPayload = new ReturnCreatedPayload();
        returnCreatedPayload.setReturnId(RETURN_ID);

        var postPurchaseEventReceived = new PostPurchaseEventReceived();
        postPurchaseEventReceived.setOrderId(ORDER_ID);
        postPurchaseEventReceived.setData(returnCreatedPayload);

        when(messageValidator.isValid(postPurchaseEventReceived)).thenReturn(true);
        when(idempotencyChecker.isDuplicate(postPurchaseEventReceived)).thenReturn(true);
        when(messageValidator.passesCustomValidation(postPurchaseEventReceived)).thenReturn(true);

        // Act
        consumer.accept(postPurchaseEventReceived);

        // Assert
        verify(customerRefundChoiceService, times(0)).processPostPurchaseEvent(postPurchaseEventReceived);
    }

    @Test
    void accept_whenInvalidMessage_customerChoiceServiceIsNotInvoked() {
        // Arrange
        var returnCreatedPayload = new ReturnCreatedPayload();
        returnCreatedPayload.setReturnId(RETURN_ID);

        var postPurchaseEventReceived = new PostPurchaseEventReceived();
        postPurchaseEventReceived.setOrderId(ORDER_ID);

        postPurchaseEventReceived.setData(returnCreatedPayload);

        when(messageValidator.isValid(postPurchaseEventReceived)).thenReturn(false);

        // Act
        consumer.accept(postPurchaseEventReceived);

        // Assert
        verify(customerRefundChoiceService, times(0)).processPostPurchaseEvent(postPurchaseEventReceived);
    }

    @Test
    void getMessageDetails_returnsFormattedMessageDetails() {
        // Arrange
        var returnCreatedPayload = new ReturnCreatedPayload();
        returnCreatedPayload.setReturnId(RETURN_ID);
        returnCreatedPayload.setRefundMethod("giftCard");

        var postPurchaseEventReceived = new PostPurchaseEventReceived();
        postPurchaseEventReceived.setOrderId(ORDER_ID);

        postPurchaseEventReceived.setData(returnCreatedPayload);

        // Act
        String result = consumer.getMessageDetails(postPurchaseEventReceived);

        // Assert
        assertThat(result).isEqualTo("orderId=ORDER-123, returnId=RETURN-456, refundMethod=giftCard");
    }

    @Test
    void getMessageDetails_unknownPayload_returnsFormattedMessageDetailsWithUnknown() {
        // Arrange
        var postPurchaseEventReceived = new PostPurchaseEventReceived();
        postPurchaseEventReceived.setOrderId(ORDER_ID);

        // Act
        String result = consumer.getMessageDetails(postPurchaseEventReceived);

        // Assert
        assertThat(result).isEqualTo("orderId=ORDER-123, returnId=Unknown, refundMethod=Unknown");
    }

    @Test
    void getMessageKey_returnsOrderId() {
        // Arrange
        var postPurchaseEventReceived = new PostPurchaseEventReceived();
        postPurchaseEventReceived.setOrderId(ORDER_ID);

        // Act
        String result = consumer.getMessageKey(postPurchaseEventReceived);

        // Assert
        assertThat(result).isEqualTo(ORDER_ID);
    }

    @Test
    void consume_directlyInvokesCustomerChoiceService() {
        // Arrange
        var returnCreatedPayload = new ReturnCreatedPayload();
        returnCreatedPayload.setReturnId(RETURN_ID);

        var postPurchaseEventReceived = new PostPurchaseEventReceived();
        postPurchaseEventReceived.setOrderId(ORDER_ID);

        postPurchaseEventReceived.setData(returnCreatedPayload);

        // Act
        consumer.consume(postPurchaseEventReceived);

        // Assert
        verify(customerRefundChoiceService, times(1)).processPostPurchaseEvent(postPurchaseEventReceived);
    }

    @Test
    void accept_whenTransientDataAccessException_thenItShouldEnqueueTheMessage() {
        // Arrange
        var returnCreatedPayload = new ReturnCreatedPayload();
        returnCreatedPayload.setReturnId(RETURN_ID);

        var postPurchaseEventReceived = new PostPurchaseEventReceived();
        postPurchaseEventReceived.setOrderId(ORDER_ID);
        postPurchaseEventReceived.setData(returnCreatedPayload);

        when(messageValidator.isValid(postPurchaseEventReceived)).thenReturn(true);
        when(idempotencyChecker.isDuplicate(postPurchaseEventReceived)).thenReturn(false);
        when(messageValidator.passesCustomValidation(postPurchaseEventReceived)).thenReturn(true);
        doThrow(new TransientDataAccessException("Transient error") {})
            .when(customerRefundChoiceService).processPostPurchaseEvent(postPurchaseEventReceived);

        // Act
        consumer.accept(postPurchaseEventReceived);

        // Assert
        verify(queueProducer, times(1)).enqueue(any(EnqueueParams.class));
    }
} 