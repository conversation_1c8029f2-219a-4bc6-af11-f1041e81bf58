package com.bestseller.payment.adapter.stream.messagegenerator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import com.bestseller.interfacecontractannotator.model.paymentstatusupdated.AuthorizedPayload;
import com.bestseller.interfacecontractannotator.model.paymentstatusupdated.OrderDetails;
import com.bestseller.interfacecontractannotator.model.paymentstatusupdated.OrderLine;
import com.bestseller.interfacecontractannotator.model.paymentstatusupdated.Payment;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.enumeration.Platform;
import com.bestseller.payment.core.domain.payment.NonePayment;
import com.bestseller.payment.utils.OrderGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.List;

import static com.bestseller.payment.utils.OrderGenerator.ORDER_CHARGE_AMOUNT;
import static com.bestseller.payment.utils.OrderGenerator.ORDER_ID;
import static com.bestseller.payment.utils.OrderGenerator.ORDER_LINE_DISCOUNT_AMOUNT;
import static com.bestseller.payment.utils.OrderGenerator.ORDER_LINE_EAN;
import static com.bestseller.payment.utils.OrderGenerator.ORDER_LINE_PRICE;
import static com.bestseller.payment.utils.OrderGenerator.ORDER_TOTAL_PRICE;
import static com.bestseller.payment.utils.OrderGenerator.PAYMENT_METHOD;
import static com.bestseller.payment.utils.OrderGenerator.PAYMENT_TYPE_ADYEN_CARD;
import static com.bestseller.payment.utils.OrderGenerator.SHIPPING_FEES_CANCELLED;
import static com.bestseller.payment.utils.OrderGenerator.TAX_RATE;
import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class PaymentStatusUpdatedGeneratorTest {

    @InjectMocks
    private PaymentStatusUpdatedGenerator paymentStatusUpdatedGenerator;

    @Test
    void testGenerate() {
        // Arrange
        var order = OrderGenerator.createAdyenOrder();
        var expectedPaymentStatusUpdated = new PaymentStatusUpdated()
                .withOrderId(ORDER_ID)
                .withPaymentState(PaymentState.AUTHORISED.toPaymentStatusUpdatedPaymentState())
                .withPayload(AuthorizedPayload.builder()
                        .payments(List.of(Payment.builder()
                                .amount(ORDER_TOTAL_PRICE)
                                .subMethod(PAYMENT_METHOD.name())
                                .name(PAYMENT_TYPE_ADYEN_CARD.name())
                                .build()))
                        .orderDetails(OrderDetails.builder()
                                .orderValue(ORDER_TOTAL_PRICE)
                                .shippingFeesTaxPercentage(TAX_RATE)
                                .shippingFees(ORDER_CHARGE_AMOUNT)
                                .shippingFeesCancelled(SHIPPING_FEES_CANCELLED)
                                .build())
                        .orderLines(List.of(OrderLine.builder()
                                .ean(ORDER_LINE_EAN)
                                .retailPrice(ORDER_LINE_PRICE)
                                .taxPercentage(TAX_RATE)
                                .discountValue(ORDER_LINE_DISCOUNT_AMOUNT)
                                .build()))
                        .build());

        // Act
        var actualPaymentStatusUpdated = paymentStatusUpdatedGenerator.generate(order);

        // Assert
        assertThat(actualPaymentStatusUpdated)
            .usingRecursiveComparison()
            .ignoringFields("timestamp")
            .isEqualTo(expectedPaymentStatusUpdated);
    }

    @Test
    void testGenerate_trade_byte_order() {
        // Arrange
        var order = OrderGenerator.createAdyenOrder();
        var tradeBytePayment = NonePayment.builder()
                .authorisedAmount(ORDER_TOTAL_PRICE.toPlainString())
                .type(PaymentType.NONE)
                .build();
        order.setOrderId("TB12345678");
        order.setPaymentStatus(PaymentState.OFFLINE);
        order.setPayments(List.of(tradeBytePayment));
        order.setPlatform(Platform.TRADEBYTE);
        order.setOfflinePayment(true);

        // Act
        var actualPaymentStatusUpdated = paymentStatusUpdatedGenerator.generate(order);

        // Assert
        assertThat(actualPaymentStatusUpdated.getTimestamp()).isNotNull();
        assertThat(actualPaymentStatusUpdated.getPaymentState().name())
                .isEqualTo(PaymentState.AUTHORISED.name());
        assertThat(((AuthorizedPayload)actualPaymentStatusUpdated.getPayload()).getPayments().get(0).getName())
                .isEqualTo(PaymentType.NONE.name());
        assertThat(((AuthorizedPayload)actualPaymentStatusUpdated.getPayload()).getPayments().get(0).getSubMethod())
                .isNull();
    }

    @Test
    void testGenerate_when_no_order_charge_is_present() {
        // Arrange
        var order = OrderGenerator.createAdyenOrder();
        order.setOrderCharges(List.of());

        // Act
        var actualPaymentStatusUpdated = paymentStatusUpdatedGenerator.generate(order);

        // Assert
        assertThat(((AuthorizedPayload)actualPaymentStatusUpdated.getPayload()).getOrderDetails().getShippingFees())
                .isEqualTo(BigDecimal.ZERO);
        assertThat(((AuthorizedPayload)actualPaymentStatusUpdated.getPayload()).getOrderDetails().getShippingFeesTaxPercentage())
                .isEqualTo(BigDecimal.ZERO);
        assertThat(((AuthorizedPayload)actualPaymentStatusUpdated.getPayload()).getOrderDetails().isShippingFeesCancelled())
                .isFalse();
    }

    @Test
    void testGenerate_when_payment_status_is_cancelled() {
        // Arrange
        var order = OrderGenerator.createAdyenOrder();
        order.setPaymentStatus(PaymentState.CANCELLED);

        // Act
        var actualPaymentStatusUpdated = paymentStatusUpdatedGenerator.generate(order);

        // Assert
        assertThat(actualPaymentStatusUpdated.getPayload()).isNull();
    }


    @Test
    void generate_givenGrossRetailUnitPriceAndUnitDiscount_valuesAreForwardedToThePaymentStatusUpdated() {
        // Arrange
        var order = OrderGenerator.createAdyenOrder();
        final BigDecimal grossRetailUnitPrice = BigDecimal.valueOf(19.0);
        final BigDecimal unitDiscountPrice = BigDecimal.valueOf(3.0);
        order.getOrderLines().stream()
            .findAny()
            .map(item -> {
                item.getOrderLinePaidAmount().setGrossRetailUnitPrice(grossRetailUnitPrice);
                item.getOrderLinePaidAmount().setUnitDiscount(unitDiscountPrice);
                return item;
            });

        var expectedPaymentStatusUpdated = new PaymentStatusUpdated()
            .withOrderId(ORDER_ID)
            .withPaymentState(PaymentState.AUTHORISED.toPaymentStatusUpdatedPaymentState())
            .withPayload(AuthorizedPayload.builder()
                .payments(List.of(Payment.builder()
                    .amount(ORDER_TOTAL_PRICE)
                    .subMethod(PAYMENT_METHOD.name())
                    .name(PAYMENT_TYPE_ADYEN_CARD.name())
                    .build()))
                .orderDetails(OrderDetails.builder()
                    .orderValue(ORDER_TOTAL_PRICE)
                    .shippingFeesTaxPercentage(TAX_RATE)
                    .shippingFees(ORDER_CHARGE_AMOUNT)
                    .shippingFeesCancelled(SHIPPING_FEES_CANCELLED)
                    .build())
                .orderLines(List.of(OrderLine.builder()
                    .ean(ORDER_LINE_EAN)
                    .retailPrice(grossRetailUnitPrice)
                    .taxPercentage(TAX_RATE)
                    .discountValue(unitDiscountPrice)
                    .build()))
                .build());

        // Act
        var actualPaymentStatusUpdated = paymentStatusUpdatedGenerator.generate(order);

        // Assert
        assertThat(actualPaymentStatusUpdated)
            .usingRecursiveComparison()
            .ignoringFields("timestamp")
            .isEqualTo(expectedPaymentStatusUpdated);
    }

    @Test
    void generate_givenGrossRetailUnitPriceAndUnitDiscountNotPresent_fallbacksAreForwarded() {
        // Arrange
        var order = OrderGenerator.createAdyenOrder();

        order.getOrderLines().stream()
            .findAny()
            .map(item -> {
                item.getOrderLinePaidAmount().setGrossRetailUnitPrice(null);
                item.getOrderLinePaidAmount().setUnitDiscount(null);
                return item;
            });

        var expectedPaymentStatusUpdated = new PaymentStatusUpdated()
            .withOrderId(ORDER_ID)
            .withPaymentState(PaymentState.AUTHORISED.toPaymentStatusUpdatedPaymentState())
            .withPayload(AuthorizedPayload.builder()
                .payments(List.of(Payment.builder()
                    .amount(ORDER_TOTAL_PRICE)
                    .subMethod(PAYMENT_METHOD.name())
                    .name(PAYMENT_TYPE_ADYEN_CARD.name())
                    .build()))
                .orderDetails(OrderDetails.builder()
                    .orderValue(ORDER_TOTAL_PRICE)
                    .shippingFeesTaxPercentage(TAX_RATE)
                    .shippingFees(ORDER_CHARGE_AMOUNT)
                    .shippingFeesCancelled(SHIPPING_FEES_CANCELLED)
                    .build())
                .orderLines(List.of(OrderLine.builder()
                    .ean(ORDER_LINE_EAN)
                    .retailPrice(ORDER_LINE_PRICE)
                    .taxPercentage(TAX_RATE)
                    .discountValue(null)
                    .build()))
                .build());

        // Act
        var actualPaymentStatusUpdated = paymentStatusUpdatedGenerator.generate(order);

        // Assert
        assertThat(actualPaymentStatusUpdated)
            .usingRecursiveComparison()
            .ignoringFields("timestamp")
            .isEqualTo(expectedPaymentStatusUpdated);
    }
}
