package com.bestseller.payment.adapter.stream.validator.idempotency;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.RefundCreationRequested;
import com.bestseller.payment.adapter.repository.RefundRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class RefundCreationRequestedIdempotencyCheckTest {

    @InjectMocks
    RefundCreationRequestedIdempotencyCheck refundCreationRequestedIdempotencyCheck;

    @Mock
    RefundRepository refundRepository;

    @Test
    void test_isDuplicateMessage() {
        // Arrange
        UUID requestId = UUID.randomUUID();
        RefundCreationRequested message = new RefundCreationRequested()
            .withRefundCreationRequestedId(requestId)
            .withOrderId("orderId");
        // Act
        refundCreationRequestedIdempotencyCheck.isDuplicate(message);

        // Assert
        verify(refundRepository).existsByOrderIdAndRequestId("orderId", requestId.toString());
    }

}
