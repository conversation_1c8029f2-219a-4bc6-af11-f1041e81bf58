package com.bestseller.payment.adapter.stream.validator.idempotency;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRejected.PaymentRejected;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PaymentRejectedIdempotencyCheckTest {
    private static final String ORDER_ID = "ORDER_ID";

    @InjectMocks
    PaymentRejectedIdempotencyCheck paymentRejectedIdempotencyCheck;

    @Mock
    private OrderRepository orderRepository;

    @Test
    void testIsDuplicateMessage() {
        // Arrange
        PaymentRejected message = new PaymentRejected().withOrderId(ORDER_ID);
        Order order = Order.builder()
            .orderId(ORDER_ID)
            .paymentStatus(spy(PaymentState.CANCELLED))
            .build();

        when(orderRepository.findById(ORDER_ID)).thenReturn(java.util.Optional.of(order));

        // Act
        boolean result = paymentRejectedIdempotencyCheck.isDuplicate(message);

        // Assert
        assertTrue(result);
        verify(orderRepository).findById(ORDER_ID);
        verify(order.getPaymentStatus()).isGreaterOrEqual(PaymentState.CANCELLED);
    }

    @Test
    void testGetMessageKey() {
        // Arrange
        PaymentRejected message = new PaymentRejected().withOrderId(ORDER_ID);

        // Act
        String result = paymentRejectedIdempotencyCheck.getMessageKey(message);

        // Assert
        assertEquals(ORDER_ID, result);
    }

    @Test
    void testIsDuplicateMessage_orderNotFound() {
        // Arrange
        PaymentRejected message = new PaymentRejected().withOrderId(ORDER_ID);
        when(orderRepository.findById(ORDER_ID)).thenReturn(java.util.Optional.empty());

        // Act
        boolean result = paymentRejectedIdempotencyCheck.isDuplicate(message);

        // Assert
        assertFalse(result);
    }

}
