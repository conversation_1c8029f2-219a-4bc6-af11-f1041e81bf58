package com.bestseller.payment.adapter.stream.validator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRejected.PaymentRejected;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.service.payment.PaymentService;
import com.bestseller.payment.core.validation.PaymentAuthorizationValidator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PaymentRejectedMessageValidatorTest {
    private static final String ORDER_ID = "ORDER_ID";
    private static final String PROVIDER = "PROVIDER";
    private static final String PSP_REFERENCE = "PSP_REFERENCE";

    @InjectMocks
    private PaymentRejectedMessageValidator paymentRejectedMessageValidator;

    @Mock
    private PaymentAuthorizationValidator paymentAuthorizationValidator;

    @Mock
    private PaymentService paymentService;

    @Test
    void passesCustomValidation_givenValidMessage_shouldReturnTrue() {
        // Arrange
        PaymentRejected message = createPaymentAuthorized();
        when(paymentAuthorizationValidator.validate(any(), any(), any())).thenReturn(true);
        when(paymentService.checkPaymentStatus(any(), any())).thenReturn(true);

        // Act
        boolean result = paymentRejectedMessageValidator.passesCustomValidation(message);

        // Assert
        assertTrue(result);
        verify(paymentAuthorizationValidator).validate(ORDER_ID, PROVIDER, PSP_REFERENCE);
        verify(paymentService).checkPaymentStatus(ORDER_ID, PaymentState.REVIEW);
    }

    @Test
    void passesCustomValidation_invalidMessage_shouldReturnFalse() {
        // Arrange
        PaymentRejected message = createPaymentAuthorized();
        when(paymentAuthorizationValidator.validate(any(), any(), any())).thenReturn(false);

        // Act
        boolean result = paymentRejectedMessageValidator.passesCustomValidation(message);

        // Assert
        assertFalse(result);
        verify(paymentAuthorizationValidator).validate(ORDER_ID, PROVIDER, PSP_REFERENCE);
    }

    @Test
    public void passesCustomValidation_invalidPaymentStatus_shouldReturnFalse() {
        // Arrange
        PaymentRejected message = createPaymentAuthorized();
        when(paymentAuthorizationValidator.validate(any(), any(), any())).thenReturn(true);
        when(paymentService.checkPaymentStatus(any(), any())).thenReturn(false);

        // Act
        boolean result = paymentRejectedMessageValidator.passesCustomValidation(message);

        // Assert
        assertFalse(result);
        verify(paymentAuthorizationValidator).validate(ORDER_ID, PROVIDER, PSP_REFERENCE);
    }

    private PaymentRejected createPaymentAuthorized() {
        PaymentRejected paymentRejected = new PaymentRejected();
        paymentRejected.setOrderId(ORDER_ID);
        paymentRejected.setProvider(PROVIDER);
        paymentRejected.setPspReference(PSP_REFERENCE);
        return paymentRejected;
    }
}
