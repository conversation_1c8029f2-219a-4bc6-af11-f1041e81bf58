package com.bestseller.payment.adapter.stream.consumer;

import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementFailed.PaymentSettlementFailed;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementSucceeded.PaymentSettlementSucceeded;
import com.bestseller.payment.adapter.stream.validator.PaymentSettlementSucceededMessageValidator;
import com.bestseller.payment.adapter.stream.validator.idempotency.PaymentSettlementSucceededIdempotencyCheck;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.service.payment.PaymentService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PaymentSettlementSucceededConsumerTest {
    private static final String ORDER_ID = "ORDER_ID";
    private static final String PROVIDER = "PROVIDER";
    private static final String INVOICE_NUMBER = "INVOICE_NUMBER";

    @InjectMocks
    PaymentSettlementSucceededConsumer paymentSettlementSucceededConsumer;

    @Mock
    PaymentService paymentServiceImp;

    @Mock
    PaymentSettlementSucceededIdempotencyCheck paymentSettlementSucceededIdempotencyCheck;

    @Mock
    PaymentSettlementSucceededMessageValidator paymentSettlementSucceededMessageValidator;

    @Mock
    MessageFilter<PaymentSettlementSucceeded> messageFilter;

    @Test
    void testAccept() {
        PaymentSettlementSucceeded paymentSettlementRequest = new PaymentSettlementSucceeded()
            .withCorrelationId(ORDER_ID)
            .withProvider(PROVIDER)
            .withInvoiceNumber(INVOICE_NUMBER);

        when(paymentSettlementSucceededMessageValidator.isValid(paymentSettlementRequest)).thenReturn(true);
        when(paymentSettlementSucceededIdempotencyCheck.isDuplicate(paymentSettlementRequest)).thenReturn(false);
        when(paymentSettlementSucceededMessageValidator.passesCustomValidation(paymentSettlementRequest)).thenReturn(true);

        paymentSettlementSucceededConsumer.accept(paymentSettlementRequest);

        verify(paymentServiceImp).updatePaymentStatus(ORDER_ID, PaymentState.SETTLED);
    }

    @Test
    void testGetMessageKey() {
        // Arrange
        PaymentSettlementSucceeded paymentSettlementSucceeded = new PaymentSettlementSucceeded()
            .withCorrelationId(ORDER_ID);

        // Act
        String correlationId = paymentSettlementSucceededConsumer.getMessageDetails(paymentSettlementSucceeded);

        // Assert
        assertThat(correlationId).isEqualTo("correlationId=%s", ORDER_ID);
    }
}
