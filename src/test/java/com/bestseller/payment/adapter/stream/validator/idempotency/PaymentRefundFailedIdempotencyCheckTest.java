package com.bestseller.payment.adapter.stream.validator.idempotency;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundFailed.PaymentRefundFailed;
import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class PaymentRefundFailedIdempotencyCheckTest {

    @InjectMocks
    PaymentRefundFailedIdempotencyCheckService paymentRefundFailedIdempotencyCheckService;

    @Mock
    RefundService refundService;

    @Test
    void test_getMessageKey() {
        // Arrange
        PaymentRefundFailed message = new PaymentRefundFailed()
            .withCorrelationId("correlationId");
        // Act
        String messageKey = paymentRefundFailedIdempotencyCheckService.getMessageKey(message);
        // Assert
        assert "correlationId".equals(messageKey);
    }

    @Test
    void test_isDuplicateMessage_notDuplicate() {
        // Arrange
        String correlationId = "12345";
        PaymentRefundFailed message = new PaymentRefundFailed()
            .withCorrelationId(correlationId);

        when(refundService.isDuplicateRequest(12345, RefundState.REFUND_FAILED)).thenReturn(false);

        // Act
        boolean isDuplicateMessage = paymentRefundFailedIdempotencyCheckService.isDuplicate(message);

        // Assert
        assert !isDuplicateMessage;
    }

    @Test
    void test_isDuplicateMessage_duplicate() {
        // Arrange
        String correlationId = "12345";
        PaymentRefundFailed paymentRefundRequested = new PaymentRefundFailed()
            .withCorrelationId(correlationId);

        when(refundService.isDuplicateRequest(12345, RefundState.REFUND_FAILED)).thenReturn(true);

        // Act
        boolean isDuplicateMessage = paymentRefundFailedIdempotencyCheckService.isDuplicate(paymentRefundRequested);

        // Assert
        assert isDuplicateMessage;
    }
}
