package com.bestseller.payment.adapter.stream.validator;

import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.exception.OrderIsNotRefundableException;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import com.bestseller.payment.core.exception.SettlementNotYetHappenedException;
import com.bestseller.payment.utils.OrderGenerator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

class PaymentValidationTest {
    PaymentValidation paymentValidation;

    @BeforeEach
    void setup() {
        paymentValidation = new PaymentValidationImpl();
    }

    @Test
    void validate_adyenCard_settled() {
        // arrange
        Order order = OrderGenerator.createOrder();
        order.setPaymentStatus(PaymentState.SETTLED);

        // act
        boolean actual = paymentValidation.validateRefundRequest(order);

        // assert
        assertTrue(actual);
    }

    @Test
    void testValidate_adyenCard_settlement_requesting() {
        // Arrange
        Order order = OrderGenerator.createOrder();
        order.setPaymentStatus(PaymentState.SETTLEMENT_REQUESTING);


        // Act & Assert
        assertThrows(SettlementNotYetHappenedException.class, () -> paymentValidation.validateRefundRequest(order));
    }

    @Test
    void validate_adyenBank_authorised() {
        // arrange
        Order order = OrderGenerator.createOrder();
        order.setPaymentStatus(PaymentState.AUTHORISED);
        order.setPayments(List.of(OrderGenerator.createBankPayment()));

        // act
        boolean actual = paymentValidation.validateRefundRequest(order);

        // assert
        assertTrue(actual);
    }

    @Test
    void validate_klarna_settled() {
        // arrange
        Order order = OrderGenerator.createOrder();
        order.setPaymentStatus(PaymentState.SETTLED);
        order.setPayments(List.of(OrderGenerator.createKlarnaPayment()));

        // act
        boolean actual = paymentValidation.validateRefundRequest(order);

        // assert
        assertTrue(actual);
    }

    @Test
    void validate_klarna_settlement_requesting() {
        // arrange
        Order order = OrderGenerator.createOrder();

        order.setPaymentStatus(PaymentState.SETTLEMENT_REQUESTING);
        order.setPayments(List.of(OrderGenerator.createKlarnaPayment()));

        // act & assert
        assertThrows(SettlementNotYetHappenedException.class, () -> paymentValidation.validateRefundRequest(order));
    }

    @Test
    void validate_klarna_settlement_denied() {
        // arrange
        Order order = OrderGenerator.createOrder();
        order.setPaymentStatus(PaymentState.SETTLEMENT_DENIED);
        order.setPayments(List.of(OrderGenerator.createKlarnaPayment()));

        // act & assert
        assertThrows(OrderIsNotRefundableException.class, () -> paymentValidation.validateRefundRequest(order));
    }

    @Test
    void validate_giftCard_authorised() {
        // arrange
        Order order = OrderGenerator.createOrder();
        order.setPaymentStatus(PaymentState.AUTHORISED);
        order.setPayments(List.of(OrderGenerator.createGiftcardPayment()));

        // Act
        boolean actual = paymentValidation.validateRefundRequest(order);

        // assert
        assertTrue(actual);
    }

    @Test
    void validate_mixPayments_settlement_requesting() {
        // arrange
        Order order = OrderGenerator.createOrder();
        order.setPaymentStatus(PaymentState.SETTLEMENT_REQUESTING);
        order.setPayments(List.of(
            OrderGenerator.createGiftcardPayment(),
            OrderGenerator.createAdyenCardPayment()
        ));

        // act & assert
        assertThrows(SettlementNotYetHappenedException.class, () -> paymentValidation.validateRefundRequest(order));
    }

    @Test
    void validate_mixPayments_nonGiftCardPaymentIsCancelled() {
        // arrange
        Order order = OrderGenerator.createOrder();
        order.setPaymentStatus(PaymentState.CANCELLED);
        order.setPayments(List.of(
            OrderGenerator.createGiftcardPayment(),
            OrderGenerator.createAdyenCardPayment()
        ));


        // act
        boolean actual = paymentValidation.validateRefundRequest(order);

        // assert
        assertTrue(actual);
    }

    @ParameterizedTest
    @MethodSource("adyenBankAuthorisedGenerator")
    void isRefundable_adyenBank_authorised(String vatOrderNumber, boolean expected) {
        // arrange
        Order order = OrderGenerator.createOrder();
        order.setPaymentStatus(PaymentState.AUTHORISED);
        order.setPayments(List.of(OrderGenerator.createBankPayment()));
        order.setVatOrderNumber(vatOrderNumber);

        // act
        boolean actual = paymentValidation.isRefundable(order);

        // assert
        assertEquals(expected, actual);

    }

    @Test
    void isRefundable_adyenBank_settlement_denied() {
        // arrange
        Order order = OrderGenerator.createOrder();
        order.setPaymentStatus(PaymentState.SETTLEMENT_DENIED);
        order.setPayments(List.of(OrderGenerator.createBankPayment()));

        // act
        boolean actual = paymentValidation.isRefundable(order);

        // assert
        assertFalse(actual);
    }

    @Test
    void isRefundable_klarna_settlement_denied_returnsFalse() {
        // arrange
        Order order = OrderGenerator.createOrder();
        order.setPaymentStatus(PaymentState.SETTLEMENT_DENIED);
        order.setPayments(List.of(OrderGenerator.createKlarnaPayment()));
        order.setVatOrderNumber("vatOrderNumber");

        // act
        boolean actual = paymentValidation.isRefundable(order);

        // assert
        assertFalse(actual);
    }

    @Test
    void isSettlementSupported_givenValidProvider_returnTrue() {
        // act & assert
        assertTrue(paymentValidation.isSettlementSupported(ProcessorId.ADYEN.name()));
    }

    @Test
    void isSettlementSupported_givenNotValidProvider_returnTrue() {
        // act & assert
        assertFalse(paymentValidation.isSettlementSupported(ProcessorId.OFFLINE.name()));
    }

    private static Stream<Arguments> adyenBankAuthorisedGenerator() {
        return Stream.of(
            Arguments.of("vatOrderNumber", true),
            Arguments.of("", false),
            Arguments.of(null, false)
        );
    }
}
