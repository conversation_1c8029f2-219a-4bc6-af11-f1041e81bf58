package com.bestseller.payment.adapter.stream.consumer;

import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.payment.adapter.stream.validator.idempotency.ValidOrderPlacedIdempotencyCheck;
import com.bestseller.payment.core.service.order.OrderService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ValidOrderPlacedConsumerTest {

    private static final String ORDER_ID = "ORDER_ID";
    @InjectMocks
    private ValidOrderPlacedConsumer consumer;

    @Mock
    private OrderService orderService;

    @Mock
    private ValidOrderPlacedIdempotencyCheck idempotencyCheck;

    @Mock
    MessageFilter<ValidOrderPlaced> messageFilter;

    @Mock
    MessageValidator<ValidOrderPlaced> messageValidator;

    @Test
    void testAccept() {
        ValidOrderPlaced validOrderPlaced = mock(ValidOrderPlaced.class);
        when(messageValidator.isValid(validOrderPlaced)).thenReturn(true);
        when(idempotencyCheck.isDuplicate(validOrderPlaced)).thenReturn(false);
        when(messageValidator.passesCustomValidation(validOrderPlaced)).thenReturn(true);

        consumer.accept(validOrderPlaced);
        verify(orderService, times(1)).processValidOrderPlaced(validOrderPlaced);
    }

    @Test
    void testGetMessageKey() {
        // Arrange
        ValidOrderPlaced validOrderPlaced = new ValidOrderPlaced();
        validOrderPlaced.setOrderId(ORDER_ID);

        // Act
        String orderId = consumer.getMessageDetails(validOrderPlaced);

        // Assert
        assertThat(orderId).isEqualTo("orderId=%s", ORDER_ID);
    }
}
