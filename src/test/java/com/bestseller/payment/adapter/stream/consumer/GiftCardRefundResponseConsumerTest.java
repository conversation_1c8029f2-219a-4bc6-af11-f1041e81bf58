package com.bestseller.payment.adapter.stream.consumer;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.giftCardRefundResponse.GiftCardRefundResponse;
import com.bestseller.payment.core.service.refund.RefundService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class GiftCardRefundResponseConsumerTest {

    @InjectMocks
    GiftCardRefundResponseConsumer giftCardRefundResponseConsumer;

    @Mock
    IdempotencyChecker<GiftCardRefundResponse> idempotencyChecker;

    @Mock
    MessageValidator<GiftCardRefundResponse> messageValidator;

    @Mock
    RefundService refundService;

    @Mock
    QueueProducer<GiftCardRefundResponse> queueProducer;

    GiftCardRefundResponse message;

    @BeforeEach
    void setUp() {
        message = new GiftCardRefundResponse()
            .withOrderId("orderId")
            .withCorrelationId("correlationId")
            .withStatus(true);
    }

    @Test
    void test_consume() {
        // Act
        giftCardRefundResponseConsumer.consume(message);

        // Assert
        verify(refundService).processGiftCardRefundResponse(message);
    }

    @Test
    void test_getMessageDetails() {
        // Act
        String result = giftCardRefundResponseConsumer.getMessageDetails(message);


        // Assert
        assertEquals("orderId=orderId, correlationId=correlationId, status=true", result);
    }

    @Test
    void test_getMessageKey() {
        // Act
        String result = giftCardRefundResponseConsumer.getMessageKey(message);

        // Assert
        assertEquals("orderId", result);
    }
}
