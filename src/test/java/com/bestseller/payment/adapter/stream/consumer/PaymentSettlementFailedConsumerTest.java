package com.bestseller.payment.adapter.stream.consumer;

import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRejected.PaymentRejected;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementFailed.PaymentSettlementFailed;
import com.bestseller.payment.adapter.stream.validator.PaymentSettlementFailedMessageValidator;
import com.bestseller.payment.adapter.stream.validator.idempotency.PaymentSettlementFailedIdempotencyCheck;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.service.payment.PaymentService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PaymentSettlementFailedConsumerTest {
    private static final String ORDER_ID = UUID.randomUUID().toString();
    private static final String PROVIDER = "PROVIDER";
    @InjectMocks
    PaymentSettlementFailedConsumer paymentSettlementFailedConsumer;

    @Mock
    PaymentService paymentServiceImp;

    @Mock
    PaymentSettlementFailedIdempotencyCheck paymentSettlementFailedIdempotencyCheck;

    @Mock
    PaymentSettlementFailedMessageValidator paymentSettlementFailedMessageValidator;

    @Mock
    MessageFilter<PaymentSettlementFailed> messageFilter;

    @Test
    void testAccept() {
        PaymentSettlementFailed paymentSettlementFailed = new PaymentSettlementFailed()
            .withCorrelationId(ORDER_ID)
            .withProvider(PROVIDER);

        when(paymentSettlementFailedMessageValidator.isValid(paymentSettlementFailed)).thenReturn(true);
        when(paymentSettlementFailedIdempotencyCheck.isDuplicate(paymentSettlementFailed)).thenReturn(false);
        when(paymentSettlementFailedMessageValidator.passesCustomValidation(paymentSettlementFailed)).thenReturn(true);

        paymentSettlementFailedConsumer.accept(paymentSettlementFailed);

        verify(paymentServiceImp).updatePaymentStatus(ORDER_ID, PaymentState.SETTLEMENT_DENIED);
    }

    @Test
    void testGetMessageKey() {
        // Arrange
        PaymentSettlementFailed paymentSettlementFailed = new PaymentSettlementFailed()
            .withCorrelationId(ORDER_ID);

        // Act
        String correlationId = paymentSettlementFailedConsumer.getMessageDetails(paymentSettlementFailed);

        // Assert
        assertThat(correlationId).isEqualTo("correlationId=%s", ORDER_ID);
    }
}
