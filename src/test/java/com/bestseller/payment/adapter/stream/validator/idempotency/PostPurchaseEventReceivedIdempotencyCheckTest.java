package com.bestseller.payment.adapter.stream.validator.idempotency;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.postPurchaseEventReceived.PostPurchaseEventReceived;
import com.bestseller.interfacecontractannotator.model.postpurchaseeventreceived.ReturnCreatedPayload;
import com.bestseller.interfacecontractannotator.model.postpurchaseeventreceived.ReturnRequest;
import com.bestseller.payment.adapter.repository.CustomerRefundChoiceRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PostPurchaseEventReceivedIdempotencyCheckTest {

    @Mock
    private CustomerRefundChoiceRepository customerRefundChoiceRepository;

    @InjectMocks
    private PostPurchaseEventReceivedIdempotencyChecker idempotencyChecker;

    @Test
    void isDuplicate_whenNoExistingCustomerChoices_returnsFalse() {
        // Arrange
        PostPurchaseEventReceived message = createValidMessage();
        when(customerRefundChoiceRepository.existsByOrderIdAndReturnId(anyString(), anyString())).thenReturn(false);

        // Act
        boolean result = idempotencyChecker.isDuplicate(message);

        // Assert
        assertThat(result).isFalse();
    }

    @Test
    void isDuplicate_whenExistingCustomerChoices_returnsTrue() {
        // Arrange
        PostPurchaseEventReceived message = createValidMessage();
        when(customerRefundChoiceRepository.existsByOrderIdAndReturnId(anyString(), anyString())).thenReturn(true);

        // Act
        boolean result = idempotencyChecker.isDuplicate(message);

        // Assert
        assertThat(result).isTrue();
    }

    @Test
    void isDuplicate_whenNullReturnId_returnsFalse() {
        // Arrange
        PostPurchaseEventReceived message = createValidMessage();
        ReturnCreatedPayload payload = (ReturnCreatedPayload) message.getData();
        payload.setReturnId(null);

        // Act
        boolean result = idempotencyChecker.isDuplicate(message);

        // Assert
        // Should return false because null returnId is now validated in the idempotency checker
        assertThat(result).isFalse();
    }

    @Test
    void isDuplicate_whenEmptyReturnId_returnsFalse() {
        // Arrange
        PostPurchaseEventReceived message = createValidMessage();
        ReturnCreatedPayload payload = (ReturnCreatedPayload) message.getData();
        payload.setReturnId("");

        // Act
        boolean result = idempotencyChecker.isDuplicate(message);

        // Assert
        // Should return false because empty returnId is now validated in the idempotency checker
        assertThat(result).isFalse();
    }

    private PostPurchaseEventReceived createValidMessage() {
        ReturnRequest returnRequest = new ReturnRequest();
        returnRequest.setEan("1234567890123");
        returnRequest.setQuantity(1);

        ReturnCreatedPayload payload = new ReturnCreatedPayload();
        payload.setReturnId("RETURN-123");
        payload.setReturnRequest(List.of(returnRequest));
        payload.setRefundMethod("giftCard");

        PostPurchaseEventReceived message = new PostPurchaseEventReceived();
        message.setOrderId("ORDER-123");
        message.setData(payload);

        return message;
    }
} 