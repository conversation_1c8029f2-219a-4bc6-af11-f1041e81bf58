package com.bestseller.payment.adapter.stream.messagegenerator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundStatusUpdated.RefundStatusUpdated;
import com.bestseller.payment.core.domain.Refund;
import com.bestseller.payment.utils.RefundGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class RefundStatusUpdatedGeneratorTest {

    @InjectMocks
    private RefundStatusUpdatedGenerator refundStatusUpdatedGenerator;

    @Test
    void testGenerate() {
        // Arrange
        Refund refund = RefundGenerator.createRefund();

        // Act
        var result = refundStatusUpdatedGenerator.generate(refund);

        // Assert
        assertThat(result.getTimestamp()).isNotNull();
        assertEquals(refund.getId().toString(), result.getRefundId());
        assertEquals(refund.getOrder().getOrderId(), result.getOrderId());
        assertEquals(RefundStatusUpdated.Status.valueOf(refund.getRefundState().name()), result.getStatus());
    }
}
