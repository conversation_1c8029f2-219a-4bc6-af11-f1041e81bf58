package com.bestseller.payment.adapter.stream.validator.idempotency;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequested.PaymentRefundRequested;
import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class PaymentRefundRequestedIdempotencyCheckTest {

    @InjectMocks
    PaymentRefundRequestedIdempotencyCheckService paymentRefundRequestedIdempotencyCheckService;

    @Mock
    RefundService refundService;

    @Test
    void test_getMessageKey() {
        // Arrange
        PaymentRefundRequested paymentRefundRequested = new PaymentRefundRequested()
            .withCorrelationId("correlationId");
        // Act
        String messageKey = paymentRefundRequestedIdempotencyCheckService.getMessageKey(paymentRefundRequested);
        // Assert
        assert "correlationId".equals(messageKey);
    }

    @Test
    void test_isDuplicateMessage_notDuplicate() {
        // Arrange
        String correlationId = "12345";
        PaymentRefundRequested paymentRefundRequested = new PaymentRefundRequested()
            .withCorrelationId(correlationId);

        when(refundService.isDuplicateRequest(12345, RefundState.REFUND_REQUESTED)).thenReturn(false);

        // Act
        boolean isDuplicateMessage = paymentRefundRequestedIdempotencyCheckService.isDuplicate(paymentRefundRequested);

        // Assert
        assert !isDuplicateMessage;
    }

    @Test
    void test_isDuplicateMessage_duplicate() {
        // Arrange
        String correlationId = "12345";
        PaymentRefundRequested paymentRefundRequested = new PaymentRefundRequested()
            .withCorrelationId(correlationId);

        when(refundService.isDuplicateRequest(12345, RefundState.REFUND_REQUESTED)).thenReturn(true);

        // Act
        boolean isDuplicateMessage = paymentRefundRequestedIdempotencyCheckService.isDuplicate(paymentRefundRequested);

        // Assert
        assert isDuplicateMessage;
    }
}
