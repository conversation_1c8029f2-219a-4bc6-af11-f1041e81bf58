package com.bestseller.payment.adapter.stream.consumer;

import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.postPurchaseEventReceived.PostPurchaseEventReceived;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.RefundCreationRequested;
import com.bestseller.payment.adapter.stream.validator.RefundCreationRequestedMessageValidator;
import com.bestseller.payment.adapter.stream.validator.idempotency.RefundCreationRequestedIdempotencyCheck;
import com.bestseller.payment.core.converter.OrderItemToRefundMapper;
import com.bestseller.payment.core.dto.RefundOptions;
import com.bestseller.payment.core.dto.RefundReason;
import com.bestseller.payment.core.service.refund.EcomRefundService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.UUID;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RefundCreationRequestedConsumerTest {
    @InjectMocks
    RefundCreationRequestedConsumer refundCreationRequestedConsumer;

    @Mock
    RefundCreationRequestedIdempotencyCheck idempotencyCheckService;

    @Mock
    RefundCreationRequestedMessageValidator messageValidator;

    @Mock
    OrderItemToRefundMapper orderItemToRefundMapper;

    @Mock
    EcomRefundService ecomRefundService;

    @Mock
    MessageFilter<RefundCreationRequested> messageFilter;

    @Test
    void testConsume() {
        // Arrange
        RefundCreationRequested refundCreationRequested = createRefundCreationRequested();

        when(messageValidator.isValid(refundCreationRequested)).thenReturn(true);
        when(idempotencyCheckService.isDuplicate(refundCreationRequested)).thenReturn(false);
        when(messageValidator.passesCustomValidation(refundCreationRequested)).thenReturn(true);
        when(orderItemToRefundMapper.mapToOrderItemToRefundList(refundCreationRequested.getItemsToRefund()))
            .thenReturn(List.of());

        // Act
        refundCreationRequestedConsumer.accept(refundCreationRequested);

        // Assert
        verify(ecomRefundService).refund(
            refundCreationRequested.getOrderId(),
            List.of(),
            RefundOptions.builder()
                .chargeReturnFee(refundCreationRequested.getChargeReturnFee())
                .refundShippingFee(refundCreationRequested.getRefundShippingFee())
                .refundReason(RefundReason.RETURN)
                .build(),
            refundCreationRequested.getRefundCreationRequestedId().toString());
    }

    @Test
    void testGetMessageKey() {
        // Arrange
        String orderId = "orderId";
        RefundCreationRequested refundCreationRequested = new RefundCreationRequested()
            .withOrderId(orderId);

        // Act
        String actualOrderId = refundCreationRequestedConsumer.getMessageKey(refundCreationRequested);

        // Assert
        assert actualOrderId.equals(orderId);
    }

    private RefundCreationRequested createRefundCreationRequested() {
        return new RefundCreationRequested()
            .withOrderId(UUID.randomUUID().toString())
            .withRefundShippingFee(true)
            .withItemsToRefund(List.of())
            .withRefundCreationRequestedId(UUID.randomUUID())
            .withChargeReturnFee(true);
    }
}
