package com.bestseller.payment.adapter.stream.validator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.RefundCreationRequested;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.exception.OrderNotFoundException;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RefundCreationRequestedMessageValidatorTest {

    @InjectMocks
    RefundCreationRequestedMessageValidator refundCreationRequestedMessageValidator;

    @Mock
    OrderRepository orderRepository;

    @Mock
    Validator validator;

    @Mock
    PaymentValidation paymentValidation;

    @Test
    void isValid_givenViolations_shouldReturnFalse() {
        // Arrange
        RefundCreationRequested refundCreationRequested = null;
        when(validator.validate(isNull())).thenReturn(Set.of(mock(ConstraintViolation.class)));

        // Act and Assert
        boolean actual = refundCreationRequestedMessageValidator.isValid(refundCreationRequested);
        assertFalse(actual);
    }

    @Test
    void passesCustomValidation_givenOrderNotFound_shouldThrowException() {
        // Arrange
        RefundCreationRequested refundCreationRequested = new RefundCreationRequested()
            .withOrderId("orderId");
        when(orderRepository.findById("orderId")).thenReturn(Optional.empty());

        // Act and Assert
        assertThrows(OrderNotFoundException.class,
            () -> refundCreationRequestedMessageValidator.passesCustomValidation(refundCreationRequested));
    }

    @Test
    void passesCustomValidation_givenOrderNotRefundable_shouldReturnFalse() {
        // Arrange
        RefundCreationRequested refundCreationRequested = new RefundCreationRequested()
            .withOrderId("orderId");
        Optional<Order> order = Optional.of(mock(Order.class));
        when(orderRepository.findById("orderId")).thenReturn(order);
        when(paymentValidation.validateRefundRequest(any())).thenReturn(false);

        // Act and Assert
        assertFalse(refundCreationRequestedMessageValidator.passesCustomValidation(refundCreationRequested));
    }
}
