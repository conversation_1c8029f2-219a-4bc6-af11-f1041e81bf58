package com.bestseller.payment.adapter.stream.validator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementSucceeded.PaymentSettlementSucceeded;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PaymentSettlementSucceededMessageValidatorTest {
    private static final String ORDER_ID = "ORDER_ID";
    private static final String PROVIDER = ProcessorId.ADYEN.name();
    private static final String INVOICE_NUMBER = "INVOICE_NUMBER";

    @InjectMocks
    private PaymentSettlementSucceededMessageValidator paymentSettlementSucceededMessageValidator;

    @Mock
    private PaymentValidation paymentValidation;

    @Test
    void passesCustomValidation_givenValidMessage_shouldReturnTrue() {
        // Arrange
        PaymentSettlementSucceeded message = createPaymentSettlementSucceeded();
        when(paymentValidation.isSettlementSupported(message.getProvider())).thenReturn(true);

        // Act
        boolean result = paymentSettlementSucceededMessageValidator.passesCustomValidation(message);

        // Assert
        assertTrue(result);
    }

    @Test
    void passesCustomValidation_givenNullCorrelationId_shouldReturnFalse() {
        // Arrange
        PaymentSettlementSucceeded message = createPaymentSettlementSucceeded();
        message.setCorrelationId(null);

        // Act
        boolean result = paymentSettlementSucceededMessageValidator.passesCustomValidation(message);

        // Assert
        assertFalse(result);
    }

    @Test
    void passesCustomValidation_givenNullProvider_shouldReturnFalse() {
        // Arrange
        PaymentSettlementSucceeded message = createPaymentSettlementSucceeded();
        message.setProvider(null);

        // Act
        boolean result = paymentSettlementSucceededMessageValidator.passesCustomValidation(message);

        // Assert
        assertFalse(result);
    }

    @Test
    void passesCustomValidation_givenUnknownProvider_shouldReturnFalse() {
        // Arrange
        PaymentSettlementSucceeded message = createPaymentSettlementSucceeded();
        message.setProvider("UNKNOWN_PROVIDER");

        // Act
        boolean result = paymentSettlementSucceededMessageValidator.passesCustomValidation(message);

        // Assert
        assertFalse(result);
    }

    @Test
    void passesCustomValidation_givenInvalidProvider_shouldReturnFalse() {
        // Arrange
        PaymentSettlementSucceeded message = createPaymentSettlementSucceeded();
        message.setProvider(ProcessorId.OPTICARD.name());

        // Act
        boolean result = paymentSettlementSucceededMessageValidator.passesCustomValidation(message);

        // Assert
        assertFalse(result);
    }

    private PaymentSettlementSucceeded createPaymentSettlementSucceeded() {
        return new PaymentSettlementSucceeded()
            .withCorrelationId(ORDER_ID)
            .withProvider(PROVIDER)
            .withInvoiceNumber(INVOICE_NUMBER);
    }
}
