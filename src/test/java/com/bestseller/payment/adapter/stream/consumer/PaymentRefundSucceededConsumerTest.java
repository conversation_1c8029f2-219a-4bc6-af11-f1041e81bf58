package com.bestseller.payment.adapter.stream.consumer;

import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundSucceeded.PaymentRefundSucceeded;
import com.bestseller.payment.adapter.stream.validator.PaymentRefundSucceededMessageValidator;
import com.bestseller.payment.adapter.stream.validator.idempotency.PaymentRefundSucceededIdempotencyCheckService;
import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PaymentRefundSucceededConsumerTest {

    private static final String CORRELATION_ID = "1234";

    @InjectMocks
    PaymentRefundSucceededConsumer paymentRefundSucceededConsumer;

    @Mock
    PaymentRefundSucceededIdempotencyCheckService idempotencyCheckService;

    @Mock
    PaymentRefundSucceededMessageValidator messageValidator;

    @Mock
    RefundService refundService;

    @Mock
    MessageFilter<PaymentRefundSucceeded> messageFilter;

    @Test
    void test_getMessageKey() {
        // Arrange
        PaymentRefundSucceeded message = new PaymentRefundSucceeded()
            .withCorrelationId(CORRELATION_ID);

        // Act
        var correlationId = paymentRefundSucceededConsumer.getMessageDetails(message);

        // Assert
        assertThat(correlationId).isEqualTo("correlationId=%s", CORRELATION_ID);
    }

    @Test
    void test_process() {
        // Arrange
        PaymentRefundSucceeded message = new PaymentRefundSucceeded()
            .withCorrelationId(CORRELATION_ID);

        when(messageValidator.isValid(message)).thenReturn(true);
        when(idempotencyCheckService.isDuplicate(message)).thenReturn(false);
        when(messageValidator.passesCustomValidation(message)).thenReturn(true);

        // Act
        paymentRefundSucceededConsumer.accept(message);

        // Assert
        verify(refundService).updateRefundStatus(1234, RefundState.REFUND_SUCCESS);
    }

}
