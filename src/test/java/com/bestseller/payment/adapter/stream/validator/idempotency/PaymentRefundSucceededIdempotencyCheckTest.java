package com.bestseller.payment.adapter.stream.validator.idempotency;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundSucceeded.PaymentRefundSucceeded;
import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class PaymentRefundSucceededIdempotencyCheckTest {

    @InjectMocks
    PaymentRefundSucceededIdempotencyCheckService paymentRefundSucceededIdempotencyCheckService;

    @Mock
    RefundService refundService;

    @Test
    void test_getMessageKey() {
        // Arrange
        PaymentRefundSucceeded message = new PaymentRefundSucceeded()
            .withCorrelationId("correlationId");
        // Act
        String messageKey = paymentRefundSucceededIdempotencyCheckService.getMessageKey(message);
        // Assert
        assert "correlationId".equals(messageKey);
    }

    @Test
    void test_isDuplicateMessage_notDuplicate() {
        // Arrange
        String correlationId = "12345";
        PaymentRefundSucceeded message = new PaymentRefundSucceeded()
            .withCorrelationId(correlationId);

        when(refundService.isDuplicateRequest(12345, RefundState.REFUND_SUCCESS)).thenReturn(false);

        // Act
        boolean isDuplicateMessage = paymentRefundSucceededIdempotencyCheckService.isDuplicate(message);

        // Assert
        assert !isDuplicateMessage;
    }

    @Test
    void test_isDuplicateMessage_duplicate() {
        // Arrange
        String correlationId = "12345";
        PaymentRefundSucceeded paymentRefundRequested = new PaymentRefundSucceeded()
            .withCorrelationId(correlationId);

        when(refundService.isDuplicateRequest(12345, RefundState.REFUND_SUCCESS)).thenReturn(true);

        // Act
        boolean isDuplicateMessage = paymentRefundSucceededIdempotencyCheckService.isDuplicate(paymentRefundRequested);

        // Assert
        assert isDuplicateMessage;
    }
}
