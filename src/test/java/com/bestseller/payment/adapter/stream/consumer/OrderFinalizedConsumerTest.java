package com.bestseller.payment.adapter.stream.consumer;

import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderFinalized;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderLine;
import com.bestseller.payment.adapter.stream.validator.idempotency.OrderFinalizedIdempotencyCheck;
import com.bestseller.payment.core.service.order.OrderService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderFinalizedConsumerTest {

    private static final String ORDER_ID = "ORDER_ID";

    @InjectMocks
    private OrderFinalizedConsumer consumer;

    @Mock
    private OrderService orderService;

    @Mock
    private OrderFinalizedIdempotencyCheck idempotencyCheckService;

    @Mock
    MessageFilter<OrderFinalized> messageFilter;

    @Mock
    MessageValidator<OrderFinalized> messageValidator;

    @Test
    void testAccept() {
        // Arrange
        OrderFinalized orderFinalized = new OrderFinalized();
        orderFinalized.setOrderId("ORDERID");
        OrderLine orderLine = new OrderLine();
        orderLine.setLineNumber(1);
        orderLine.setEan("EAN");
        orderLine.setQuantityStatuses(List.of("DISPATCHED"));
        orderFinalized.setOrderLines(List.of(orderLine));

        when(messageValidator.isValid(orderFinalized)).thenReturn(true);
        when(idempotencyCheckService.isDuplicate(orderFinalized)).thenReturn(false);
        when(messageValidator.passesCustomValidation(orderFinalized)).thenReturn(true);

        // Act
        consumer.accept(orderFinalized);

        // Assert
        verify(orderService, times(1)).processOrderFinalizedMessage(orderFinalized);
    }

    @Test
    void testGetMessageKey() {
        // Arrange
        OrderFinalized orderFinalized = new OrderFinalized();
        orderFinalized.setOrderId(ORDER_ID);

        // Act
        String orderId = consumer.getMessageDetails(orderFinalized);

        // Assert
        assertThat(orderId).isEqualTo("orderId=ORDER_ID, orderLines=[]");
    }
}
