package com.bestseller.payment.adapter.stream.validator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inStoreReturnSettlement.InStoreReturnSettlement;
import com.bestseller.payment.adapter.api.RefundOptionsService;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderLine;
import com.bestseller.payment.core.domain.Refund;
import com.bestseller.payment.core.domain.RefundLine;
import com.bestseller.payment.core.dto.RefundOptionsResponse;
import com.bestseller.payment.core.exception.OrderNotFoundException;
import com.bestseller.payment.core.exception.RefundOptionsProviderServiceNotAvailable;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class InStoreReturnSettlementValidatorTest {

    @Mock
    private OrderRepository orderRepository;

    @Mock
    private RefundOptionsService refundOptionsService;

    @Mock
    private PaymentValidation paymentValidation;

    @InjectMocks
    private InStoreReturnSettlementValidator validator;

    @Test
    void passesCustomValidation_givenOrderNotFound_shouldThrowException() {
        // Arrange
        InStoreReturnSettlement inStoreReturnSettlement = new InStoreReturnSettlement()
            .withOrderId("orderId")
            .withRefundId(2L);
        when(orderRepository.findById("orderId")).thenReturn(Optional.empty());

        // Act and Assert
        assertThrows(OrderNotFoundException.class, () -> validator.passesCustomValidation(inStoreReturnSettlement));
    }

    @Test
    void passesCustomValidation_givenNegativeRefundAMount_shouldReturnFalse() {
        // Arrange
        InStoreReturnSettlement inStoreReturnSettlement = new InStoreReturnSettlement()
            .withOrderId("orderId")
            .withRefundId(2L)
            .withTotalRefundAmount(BigDecimal.valueOf(-1));
        when(orderRepository.findById("orderId")).thenReturn(Optional.of(mock(Order.class)));

        // Act and Assert
        assertFalse(validator.passesCustomValidation(inStoreReturnSettlement));
        verifyNoInteractions(paymentValidation);
        verifyNoInteractions(refundOptionsService);
    }

    @Test
    void passesCustomValidation_givenNoRefundOptions_shouldThrowException() {
        // Arrange
        InStoreReturnSettlement inStoreReturnSettlement = new InStoreReturnSettlement()
            .withOrderId("orderId")
            .withRefundId(2L)
            .withTotalRefundAmount(BigDecimal.valueOf(1))
            .withOrderLines(List.of(new com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inStoreReturnSettlement.OrderLine()));
        when(orderRepository.findById("orderId")).thenReturn(Optional.of(new Order()));
        when(refundOptionsService.getRefundOptionsByOrderId("orderId")).thenReturn(Optional.empty());

        // Act and Assert
        assertThrows(RefundOptionsProviderServiceNotAvailable.class, () -> validator.passesCustomValidation(inStoreReturnSettlement));
        verifyNoInteractions(paymentValidation);
    }

    @Test
    void passesCustomValidation_givenInvalidOrderLines_shouldReturnFalse() {
        // Arrange
        when(paymentValidation.validateRefundRequest(any())).thenReturn(true);
        InStoreReturnSettlement inStoreReturnSettlement = new InStoreReturnSettlement()
            .withOrderId("orderId")
            .withRefundId(2L)
            .withTotalRefundAmount(BigDecimal.valueOf(1))
            .withOrderLines(List.of(new com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inStoreReturnSettlement.OrderLine()
                .withQuantity(1)
                .withEan("ean")));
        when(orderRepository.findById("orderId"))
            .thenReturn(Optional.of(Order.builder()
                .orderLines(List.of(com.bestseller.payment.core.domain.OrderLine.builder().ean("invalidEan").build()))
                .build()));
        when(refundOptionsService.getRefundOptionsByOrderId("orderId")).thenReturn(Optional.of(RefundOptionsResponse.builder().chargeReturnFee(true).refundShippingFee(true).build()));

        // Act and Assert
        assertFalse(validator.passesCustomValidation(inStoreReturnSettlement));
        verify(paymentValidation, times(1)).validateRefundRequest(any());

    }

    @Test
    void passesCustomValidation_givenTooManyRefunds_shouldReturnFalse() {
        // Arrange
        InStoreReturnSettlement inStoreReturnSettlement = new InStoreReturnSettlement()
            .withOrderId("orderId")
            .withRefundId(2L)
            .withTotalRefundAmount(BigDecimal.valueOf(1))
            .withOrderLines(List.of(new com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inStoreReturnSettlement.OrderLine()
                .withQuantity(2)
                .withEan("ean")));
        when(orderRepository.findById("orderId"))
            .thenReturn(Optional.of(Order.builder()
                .orderLines(List.of(com.bestseller.payment.core.domain.OrderLine.builder()
                    .ean("ean")
                    .originalQty(2)
                    .openQty(1)
                    .build()))
                .refunds(List.of(Refund.builder()
                    .refundLines(List.of(RefundLine.builder()
                        .quantity(1)
                        .orderLine(OrderLine.builder()
                            .ean("ean")
                            .build())
                        .build()))
                    .build()))
                .build()));
        when(refundOptionsService.getRefundOptionsByOrderId("orderId")).thenReturn(Optional.of(RefundOptionsResponse.builder().chargeReturnFee(true).refundShippingFee(true).build()));
        when(paymentValidation.validateRefundRequest(any())).thenReturn(true);

        // Act and Assert
        assertFalse(validator.passesCustomValidation(inStoreReturnSettlement));
        verify(paymentValidation, times(1)).validateRefundRequest(any());
    }
}
