package com.bestseller.payment.adapter.stream.validator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentAuthorized.PaymentAuthorized;
import com.bestseller.payment.core.validation.PaymentAuthorizationValidator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PaymentAuthorizedMessageValidatorTest {

    private static final String ORDER_ID = "ORDER_ID";
    private static final String PROVIDER = "PROVIDER";
    private static final String PSP_REFERENCE = "PSP_REFERENCE";

    @InjectMocks
    private PaymentAuthorizedMessageValidator paymentAuthorizedMessageValidator;

    @Mock
    private PaymentAuthorizationValidator paymentAuthorizationValidator;

    @Test
    void passesCustomValidation_givenValidPaymentAuthorizedMessage_shouldPass() {
        // Arrange
        PaymentAuthorized message = createPaymentAuthorized();
        when(paymentAuthorizationValidator.validate(any(), any(), any())).thenReturn(true);

        // Act
        boolean result = paymentAuthorizedMessageValidator.passesCustomValidation(message);

        // Assert
        assertTrue(result);
        verify(paymentAuthorizationValidator).validate(ORDER_ID, PROVIDER, PSP_REFERENCE);
    }

    @Test
    public void passesCustomValidation_givenInvalidPaymentAuthorizedMessage_shouldReturnFalse() {
        // Arrange
        PaymentAuthorized message = createPaymentAuthorized();
        when(paymentAuthorizationValidator.validate(any(), any(), any())).thenReturn(false);

        // Act
        boolean result = paymentAuthorizedMessageValidator.passesCustomValidation(message);

        // Assert
        assertFalse(result);
        verify(paymentAuthorizationValidator).validate(ORDER_ID, PROVIDER, PSP_REFERENCE);
    }

    private PaymentAuthorized createPaymentAuthorized() {
        PaymentAuthorized paymentAuthorized = new PaymentAuthorized();
        paymentAuthorized.setOrderId(ORDER_ID);
        paymentAuthorized.setProvider(PROVIDER);
        paymentAuthorized.setPspReference(PSP_REFERENCE);
        return paymentAuthorized;
    }
}
