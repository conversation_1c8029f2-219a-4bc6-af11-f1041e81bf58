package com.bestseller.payment.adapter.stream.validator.idempotency;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderFinalized;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderFinalizedIdempotencyCheckTest {
    private static final String ORDER_ID = "ORDER_ID";

    @InjectMocks
    OrderFinalizedIdempotencyCheck orderFinalizedIdempotencyCheck;

    @Mock
    OrderRepository orderRepository;

    @Test
    void testIsDuplicateMessage() {
        // Arrange
        OrderFinalized message = new OrderFinalized().withOrderId(ORDER_ID);
        Order order = Order.builder()
            .orderId(ORDER_ID)
            .paymentStatus(spy(PaymentState.SETTLED))
            .prevPaymentStatus(PaymentState.AUTHORISED)
            .build();

        when(orderRepository.findById(ORDER_ID)).thenReturn(java.util.Optional.of(order));

        // Act
        boolean result = orderFinalizedIdempotencyCheck.isDuplicate(message);

        // Assert
        assertTrue(result);
        verify(orderRepository).findById(ORDER_ID);
        verify(order.getPaymentStatus()).isGreaterOrEqual(PaymentState.SETTLEMENT_REQUESTING);
    }

    @Test
    void testIsDuplicateMessage_orderPaidByDebitCard() {
        // Arrange
        OrderFinalized message = new OrderFinalized().withOrderId(ORDER_ID);
        Order order = Order.builder()
            .orderId(ORDER_ID)
            .paymentStatus(spy(PaymentState.AUTHORISED))
            .prevPaymentStatus(PaymentState.START)
            .vatOrderNumber("VAT_ORDER_NUMBER")
            .build();

        when(orderRepository.findById(ORDER_ID)).thenReturn(java.util.Optional.of(order));

        // Act
        boolean result = orderFinalizedIdempotencyCheck.isDuplicate(message);

        // Assert
        assertTrue(result);
        verify(orderRepository).findById(ORDER_ID);
        verify(order.getPaymentStatus()).isGreaterOrEqual(PaymentState.SETTLEMENT_REQUESTING);
    }

    @Test
    void testIsDuplicateMessage_offlinePayment() {
        // Arrange
        OrderFinalized message = new OrderFinalized().withOrderId(ORDER_ID);
        Order order = Order.builder()
            .orderId(ORDER_ID)
            .paymentStatus(PaymentState.OFFLINE)
            .prevPaymentStatus(PaymentState.START)
            .build();

        when(orderRepository.findById(ORDER_ID)).thenReturn(java.util.Optional.of(order));

        // Act
        boolean result = orderFinalizedIdempotencyCheck.isDuplicate(message);

        // Assert
        assertFalse(result);
        verify(orderRepository).findById(ORDER_ID);
    }

    @Test
    void testIsDuplicateMessage_orderNotFound() {
        // Arrange
        OrderFinalized message = new OrderFinalized().withOrderId(ORDER_ID);
        when(orderRepository.findById(ORDER_ID)).thenReturn(java.util.Optional.empty());

        // Act
        boolean result = orderFinalizedIdempotencyCheck.isDuplicate(message);

        // Assert
        assertFalse(result);
    }

    @Test
    void testIsDuplicateMessage_orderIsManuallyCancelled() {
        // Arrange
        OrderFinalized message = new OrderFinalized().withOrderId(ORDER_ID);
        Order order = Order.builder()
            .orderId(ORDER_ID)
            .paymentStatus(PaymentState.CANCELLED)
            .prevPaymentStatus(PaymentState.REVIEW)
            .build();

        when(orderRepository.findById(ORDER_ID)).thenReturn(java.util.Optional.of(order));

        // Act
        boolean result = orderFinalizedIdempotencyCheck.isDuplicate(message);

        // Assert
        assertFalse(result);
        verify(orderRepository).findById(ORDER_ID);
    }

}
