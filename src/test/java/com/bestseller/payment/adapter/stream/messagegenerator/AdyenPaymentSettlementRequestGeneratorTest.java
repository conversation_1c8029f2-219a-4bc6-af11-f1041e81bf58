package com.bestseller.payment.adapter.stream.messagegenerator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementRequest.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementRequest.PaymentSettlementRequest;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderEntryAmount;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import com.bestseller.payment.core.domain.payment.GiftcardPayment;
import com.bestseller.payment.core.exception.PaymentNotFoundException;
import com.bestseller.payment.core.utils.PaymentUtils;
import com.bestseller.payment.utils.OrderGenerator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.stream.IntStream;

import static com.bestseller.payment.utils.OrderGenerator.ORDER_CHARGE_AMOUNT;
import static com.bestseller.payment.utils.OrderGenerator.ORDER_CHARGE_LINE_UNIT;
import static com.bestseller.payment.utils.OrderGenerator.ORDER_ID;
import static com.bestseller.payment.utils.OrderGenerator.ORDER_LINE_EAN;
import static com.bestseller.payment.utils.OrderGenerator.ORDER_LINE_GROSS_DISCOUNTED_UNIT_PRICE;
import static com.bestseller.payment.utils.OrderGenerator.ORDER_LINE_NAME;
import static com.bestseller.payment.utils.OrderGenerator.ORDER_LINE_QTY;
import static com.bestseller.payment.utils.OrderGenerator.ORDER_LINE_UNIT_VAT;
import static com.bestseller.payment.utils.OrderGenerator.PSP_REFERENCE;
import static com.bestseller.payment.utils.OrderGenerator.SHIPPING;
import static com.bestseller.payment.utils.OrderGenerator.TAX_RATE;
import static com.bestseller.payment.utils.OrderGenerator.TOTAL_ORIGINAL_GROSS_DISCOUNTED_TOTAL;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertThrows;

@ExtendWith(MockitoExtension.class)
 class AdyenPaymentSettlementRequestGeneratorTest {
    private static final BigDecimal BD_100 = new BigDecimal("100.00");
    @InjectMocks
    private AdyenPaymentSettlementRequestGenerator adyenPaymentSettlementRequestGenerator;

    @Test
    void testGenerate() {
        // Arrange
        final var order = OrderGenerator.createOrder();

        final var expectedOrderLines = new ArrayList<>(
            IntStream.range(0, 3)
                .mapToObj(i -> new OrderLine()
                    .withEan(ORDER_LINE_EAN + (i + 1))
                    .withQuantity(ORDER_LINE_QTY.get(i))
                    .withPaidAmount(PaymentUtils.toIntCents(ORDER_LINE_GROSS_DISCOUNTED_UNIT_PRICE.get(i)))
                    .withDescription(ORDER_LINE_NAME + (i + 1))
                    .withTaxRate(PaymentUtils.toIntCents(TAX_RATE.multiply(BD_100)))
                    .withTaxAmount(PaymentUtils.toIntCents(ORDER_LINE_UNIT_VAT.get(i)))
                ).toList()
        );
        expectedOrderLines.add(new OrderLine()
            .withEan(SHIPPING)
            .withQuantity(1)
            .withPaidAmount(PaymentUtils.toIntCents(ORDER_CHARGE_AMOUNT))
            .withTaxRate(PaymentUtils.toIntCents(TAX_RATE.multiply(BD_100)))
            .withTaxAmount(PaymentUtils.toIntCents(ORDER_CHARGE_LINE_UNIT))
            .withDescription(SHIPPING));

        final var expectedPaymentSettlementRequest = new PaymentSettlementRequest()
            .withOrderId(ORDER_ID)
            .withOrderId(order.getOrderId())
            .withCorrelationId(order.getOrderId())
            .withCurrency(order.getCurrency())
            .withPspReference(PSP_REFERENCE)
            .withTotalAmount(PaymentUtils.toIntCents(TOTAL_ORIGINAL_GROSS_DISCOUNTED_TOTAL))
            .withProvider(ProcessorId.ADYEN.name())
            .withOrderLines(expectedOrderLines);
        // Act
        final var actualPaymentSettlementRequest = adyenPaymentSettlementRequestGenerator.generate(order);

        // Assert
        assertThat(actualPaymentSettlementRequest)
            .usingRecursiveComparison()
            .isEqualTo(expectedPaymentSettlementRequest);
    }

    @Test
    void testGenerate_allCancelled() {
        // Arrange
        final var order = OrderGenerator.createOrder();
        cancelOrder(order);

        // Act
        final var actualPaymentSettlementRequest = adyenPaymentSettlementRequestGenerator.generate(order);

        // Assert
        assertThat(actualPaymentSettlementRequest.getTotalAmount()).isZero();
    }

    @Test
    void testGenerate_oneCancelledMixPayment() {
        // Arrange
        final var order = OrderGenerator.createOrder();
        cancelOrderMixPayment(order);

        // Act
        final var actualPaymentSettlementRequest = adyenPaymentSettlementRequestGenerator.generate(order);

        // Assert
        assertThat(actualPaymentSettlementRequest.getTotalAmount()).isZero();
    }

    @Test
    void testGenerate_giftCardPayment() {
        final var order = OrderGenerator.createOrder();
        changePaymentToGiftCard(order);
        // Act & Assert
        assertThrows(PaymentNotFoundException.class, () -> adyenPaymentSettlementRequestGenerator.generate(order));
    }

    private void cancelOrder(Order order) {
        order.getOrderLines().forEach(orderLine -> {
            orderLine.setOpenQty(0);
            final var paidEntryAmount = orderLine.getOrderLinePaidAmount();
            orderLine.setOrderLineCancelledAmount(OrderEntryAmount.builder()
                .grossDiscountedUnitPrice(paidEntryAmount.getGrossDiscountedUnitPrice())
                .originalGrossDiscountedTotal(paidEntryAmount.getGrossDiscountedTotal())
                .unitVAT(paidEntryAmount.getUnitVAT())
                .build());
            paidEntryAmount.setGrossDiscountedTotal(BigDecimal.ZERO);
        });
        order.getOrderCharges().forEach(orderCharge -> {
            orderCharge.setOpenQty(0);
            orderCharge.setCancelled(true);
        });
        order.getTotalPaidPrice().setGrossDiscountedTotal(BigDecimal.ZERO);
    }

    private void cancelOrderMixPayment(Order order) {
        cancelOrder(order);
        final var payments = new ArrayList<>(order.getPayments());
        payments.add(GiftcardPayment.builder()
            .authorisedAmount(BigDecimal.ONE.toString())
            .build());
        order.setPayments(payments);
    }

    private static void changePaymentToGiftCard(Order order) {
        order.getPayments().get(0).setType(PaymentType.GIFTCARD);
        order.getPayments().get(0).setProcessorId(ProcessorId.OPTICARD);
    }
}
