package com.bestseller.payment.adapter.stream.messagegenerator;

import com.bestseller.payment.utils.RefundGenerator;
import com.logistics.statetransition.RefundState;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

public class RefundCompletedGeneratorTest {

    RefundCompletedGenerator refundCompletedGenerator;

    @BeforeEach
    public void setUp() {
        refundCompletedGenerator = new RefundCompletedGenerator();
    }

    @Test
    void test_generate() {
        // Arrange
        var refundId = "US241000000001-REF-1";
        var refundState = RefundState.REFUND_SUCCESS;
        var refund = RefundGenerator.createRefund();
        refund.setRequestId("1");
        refund.setRefundId(refundId);
        refund.setRefundState(refundState);

        // Act
        var result = refundCompletedGenerator.generate(refund);

        // Assert
        assertNotNull(result);
        assertEquals(1L, result.getRefundId());
        assertEquals(true, result.getRefundSucceeded());
        assertEquals(RefundState.REFUND_SUCCESS.getDescription(), result.getRefundStatus());
    }

    @Test
    void test_generate_refundNotSucceed() {
        // Arrange
        var refundState = RefundState.REFUND_FAILED;
        var refund = RefundGenerator.createRefund();
        refund.setRequestId("1");
        refund.setRefundId(null);
        refund.setRefundState(refundState);

        // Act
        var result = refundCompletedGenerator.generate(refund);

        // Assert
        assertNotNull(result);
        assertEquals(1L, result.getRefundId());
        assertEquals(false, result.getRefundSucceeded());
        assertEquals(RefundState.REFUND_FAILED.getDescription(), result.getRefundStatus());
    }
}
