package com.bestseller.payment.adapter.stream.consumer;

import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRejected.PaymentRejected;
import com.bestseller.payment.adapter.stream.validator.PaymentRejectedMessageValidator;
import com.bestseller.payment.adapter.stream.validator.idempotency.PaymentRejectedIdempotencyCheck;
import com.bestseller.payment.core.service.order.OrderService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PaymentRejectedConsumerTest {

    private static final String ORDER_ID = "ORDER_ID";

    private static final String PSP_REFERENCE = "psp-reference";

    @InjectMocks
    PaymentRejectedConsumer paymentRejectedConsumer;

    @Mock
    OrderService orderService;

    @Mock
    PaymentRejectedIdempotencyCheck paymentRejectedIdempotencyCheck;

    @Mock
    PaymentRejectedMessageValidator paymentRejectedMessageValidator;

    @Mock
    MessageFilter<PaymentRejected> messageFilter;

    @Test
    public void testAccept() {
        testAccept("ADYEN");
        reset(orderService);
        testAccept("KLARNA_PAYMENTS");
        reset(orderService);
        testAccept("OPTICARD");
    }

    @Test
    void testGetMessageKey() {
        // Arrange
        PaymentRejected paymentRejected = new PaymentRejected();
        paymentRejected.setOrderId(ORDER_ID);

        // Act
        String orderId = paymentRejectedConsumer.getMessageKey(paymentRejected);

        // Assert
        assert orderId.equals(ORDER_ID);
    }

    private void testAccept(String provider) {
        PaymentRejected paymentRejected = new PaymentRejected();
        paymentRejected.setOrderId(ORDER_ID);
        paymentRejected.setProvider(provider);
        paymentRejected.setPspReference(PSP_REFERENCE);

        when(paymentRejectedMessageValidator.isValid(paymentRejected)).thenReturn(true);
        when(paymentRejectedIdempotencyCheck.isDuplicate(paymentRejected)).thenReturn(false);
        when(paymentRejectedMessageValidator.passesCustomValidation(paymentRejected)).thenReturn(true);

        paymentRejectedConsumer.accept(paymentRejected);
        verify(orderService).cancelOrder(ORDER_ID);
    }
}
