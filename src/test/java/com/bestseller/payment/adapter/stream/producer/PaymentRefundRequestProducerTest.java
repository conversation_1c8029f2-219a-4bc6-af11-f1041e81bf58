package com.bestseller.payment.adapter.stream.producer;

import com.bestseller.dbqueue.kafka.producer.queue.KafkaMessageNotProducedException;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequest.PaymentRefundRequest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.stream.function.StreamBridge;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class PaymentRefundRequestProducerTest {

    private static final String BINDING_NAME = "paymentRefundRequestProducer-out-0";

    @InjectMocks
    private PaymentRefundRequestProducer producer;

    @Mock
    private StreamBridge streamBridge;

    @Test
    void produce_givenValidMessage_messageIsProduced() {
        // arrange
        var message = new PaymentRefundRequest();
        when(streamBridge.send(BINDING_NAME, message)).thenReturn(true);

        // act & assert
        // no exception thrown
        assertDoesNotThrow(() -> producer.produce(message));
    }

    @Test
    void produce_givenMessage_messageNotProduced() {
        // arrange
        var message = new PaymentRefundRequest();
        message.setOrderId("OL123456");
        message.setCorrelationId("12345");
        when(streamBridge.send(BINDING_NAME, message)).thenReturn(false);

        // act & assert
        var exception = assertThrows(KafkaMessageNotProducedException.class, () -> producer.produce(message));

        assertThat(exception)
            .as("Exception is valid")
            .hasMessage("Kafka message couldn't be produced for topic PaymentRefundRequest: orderId=OL123456, correlationId=12345");
    }
}
