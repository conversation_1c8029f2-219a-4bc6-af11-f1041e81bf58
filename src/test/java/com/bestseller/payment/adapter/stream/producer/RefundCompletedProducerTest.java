package com.bestseller.payment.adapter.stream.producer;

import com.bestseller.dbqueue.kafka.producer.queue.KafkaMessageNotProducedException;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCompleted.RefundCompleted;
import com.logistics.statetransition.RefundState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.stream.function.StreamBridge;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class RefundCompletedProducerTest {

    private static final String BINDING_NAME = "refundCompletedProducer-out-0";

    @InjectMocks
    private RefundCompletedProducer producer;

    @Mock
    private StreamBridge streamBridge;

    @Test
    void produce_givenValidMessage_messageIsProduced() {
        // arrange
        var message = new RefundCompleted();
        when(streamBridge.send(BINDING_NAME, message)).thenReturn(true);

        // act & assert
        // no exception thrown
        assertDoesNotThrow(() -> producer.produce(message));
    }

    @Test
    void produce_givenMessage_messageNotProduced() {
        // arrange
        var message = new RefundCompleted();
        message.setRefundId(12345L);
        message.setRefundStatus(RefundState.CREATED.getDescription());
        when(streamBridge.send(BINDING_NAME, message)).thenReturn(false);

        // act & assert
        var exception = assertThrows(KafkaMessageNotProducedException.class, () -> producer.produce(message));

        assertThat(exception)
            .as("Exception is valid")
            .hasMessage("Kafka message couldn't be produced for topic RefundCompleted: refundId=12345, refundStatus=CREATED");
    }
}
