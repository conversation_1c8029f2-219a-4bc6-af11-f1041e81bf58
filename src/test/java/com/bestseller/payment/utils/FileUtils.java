package com.bestseller.payment.utils;

import org.apache.commons.io.IOUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;

public class FileUtils {
    public static <T> T loadSampleKafkaMessage(String filename, Class<T> type) throws IOException {
        final String jsonString = IOUtils.toString(Objects.requireNonNull(FileUtils.class.getResourceAsStream(filename)), StandardCharsets.UTF_8);
        return JsonUtils.deserialize(jsonString, type);
    }

    public static <T> T loadSampleKafkaMessage(String filename, Class<T> type, List<String> eans) throws IOException {
        String jsonString = IOUtils.toString(Objects.requireNonNull(FileUtils.class.getResourceAsStream(filename)), StandardCharsets.UTF_8);
        for (int i = 0; i < eans.size(); i++) {
            String placeholder = "\\$\\{ean" + (i + 1) + "\\}";
            jsonString = jsonString.replaceAll(placeholder, eans.get(i));
        }
        return JsonUtils.deserialize(jsonString, type);
    }
}
