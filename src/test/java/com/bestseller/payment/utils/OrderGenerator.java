package com.bestseller.payment.utils;

import com.bestseller.payment.adapter.api.dto.OrderChargeDto;
import com.bestseller.payment.adapter.api.dto.OrderDto;
import com.bestseller.payment.adapter.api.dto.OrderLineDto;
import com.bestseller.payment.adapter.api.dto.PaymentDto;
import com.bestseller.payment.adapter.api.dto.RefundDto;
import com.bestseller.payment.adapter.api.dto.RefundsInfoDto;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderCharge;
import com.bestseller.payment.core.domain.OrderEntryAmount;
import com.bestseller.payment.core.domain.OrderLine;
import com.bestseller.payment.core.domain.OverallTotal;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.enumeration.Brand;
import com.bestseller.payment.core.domain.enumeration.ChargedRefundReason;
import com.bestseller.payment.core.domain.enumeration.EntryType;
import com.bestseller.payment.core.domain.enumeration.PaymentMethod;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import com.bestseller.payment.core.domain.payment.AdyenBankPayment;
import com.bestseller.payment.core.domain.payment.AdyenCardPayment;
import com.bestseller.payment.core.domain.payment.GiftcardPayment;
import com.bestseller.payment.core.domain.payment.KlarnaPayment;
import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class OrderGenerator {
    public static final String ORDER_ID = "OL12345678";
    public static final Date ORDER_DATE = getOrderDate();
    public static final PaymentState PAYMENT_STATE_AUTHORISED = PaymentState.AUTHORISED;
    public static final PaymentState PREVIOUS_PAYMENT_STATE_START = PaymentState.START;
    public static final String AUTHORISED_AMOUNT = "20.0";
    public static final ProcessorId PROCESSOR_ID_ADYEN = ProcessorId.ADYEN;
    public static final String PSP_REFERENCE = "psp-reference";
    public static final PaymentType PAYMENT_TYPE_ADYEN_CARD = PaymentType.ADYEN_CARD;
    public static final String SUB_METHOD = "-1";
    public static final PaymentMethod PAYMENT_METHOD = PaymentMethod.ADYEN_CREDIT_CARD;
    public static final Integer PAYMENT_ID = 1;
    public static final BigDecimal TAX_RATE = BigDecimal.valueOf(0.19);
    public static final BigDecimal ORDER_CHARGE_AMOUNT = BigDecimal.valueOf(3.95);
    public static final BigDecimal ORDER_CHARGE_LINE_VAT = BigDecimal.valueOf(0.63);
    public static final BigDecimal ORDER_CHARGE_LINE_UNIT = BigDecimal.valueOf(0.63);
    public static final BigDecimal ORDER_LINE_PRICE = BigDecimal.valueOf(15.0);
    public static final BigDecimal ORDER_TOTAL_PRICE = BigDecimal.valueOf(20.0);
    public static final String ORDER_LINE_EAN = "ean";
    public static final String ORDER_LINE_NAME = "name";
    public static final String SHIPPING_COUNTRY = "DE";
    public static final Brand BRAND = Brand.JJ;

    public static final String SHIPPING = "SHIPPING";
    public static final BigDecimal ORDER_LINE_DISCOUNT_AMOUNT = new BigDecimal("8.0");
    public static final boolean SHIPPING_FEES_CANCELLED = Boolean.FALSE;

    public static final List<BigDecimal> ORDER_LINE_DISCOUNT_AMOUNTS = List.of(
        new BigDecimal("5.00"),
        new BigDecimal("5.00"),
        new BigDecimal("25.00")
    );

    public static final List<BigDecimal> ORDER_LINE_GROSS_RETAIL_UNIT_PRICE = List.of(
        new BigDecimal("9.99"),
        new BigDecimal("9.99"),
        new BigDecimal("49.99")
    );

    public static final List<BigDecimal> ORDER_LINE_GROSS_DISCOUNTED_UNIT_PRICE = List.of(
        new BigDecimal("4.99"),
        new BigDecimal("4.99"),
        new BigDecimal("24.99")
    );

    public static final List<BigDecimal> ORDER_LINE_GROSS_DISCOUNTED_TOTAL = List.of(
        new BigDecimal("9.98"),
        new BigDecimal("9.98"),
        new BigDecimal("24.99")
    );

    public static final List<BigDecimal> ORDER_LINE_LINE_VAT = List.of(
        new BigDecimal("1.60"),
        new BigDecimal("1.60"),
        new BigDecimal("3.99")
    );

    public static final List<BigDecimal> ORDER_LINE_UNIT_VAT = List.of(
        new BigDecimal("0.80"),
        new BigDecimal("0.80"),
        new BigDecimal("3.99")
    );

    public static final List<BigDecimal> ORDER_LINE_ORIGINAL_GROSS_DISCOUNTED_TOTAL = List.of(
        new BigDecimal("9.98"),
        new BigDecimal("9.98"),
        new BigDecimal("24.99")
    );

    public static final List<Integer> ORDER_LINE_QTY = List.of(2, 2, 1);

    public static final BigDecimal TOTAL_ORIGINAL_GROSS_DISCOUNTED_TOTAL = new BigDecimal("48.90");
    public static final BigDecimal TOTAL_GROSS_DISCOUNTED_TOTAL = new BigDecimal("48.90");
    public static final BigDecimal TOTAL_GROSS_SUBTOTAL = new BigDecimal("44.95");
    public static final BigDecimal TOTAL_VAT = new BigDecimal("7.82");

    public static Order createAdyenOrder() {
        return Order.builder()
            .orderId(ORDER_ID)
            .shippingCountryCode(SHIPPING_COUNTRY)
            .orderDate(ORDER_DATE)
            .brand(BRAND)
            .paymentStatus(PAYMENT_STATE_AUTHORISED)
            .prevPaymentStatus(PREVIOUS_PAYMENT_STATE_START)
            .createdTS(ORDER_DATE.toInstant())
            .lastModifiedTS(ORDER_DATE.toInstant())
            .payments(
                List.of(
                    AdyenCardPayment.builder()
                        .paymentId(PAYMENT_ID)
                        .authorisedAmount(AUTHORISED_AMOUNT)
                        .processorId(PROCESSOR_ID_ADYEN)
                        .paymentReference(PSP_REFERENCE)
                        .type(PAYMENT_TYPE_ADYEN_CARD)
                        .subMethod(SUB_METHOD)
                        .subMethodName(PAYMENT_METHOD)
                        .build()
                )
            )
            .orderLines(
                List.of(OrderLine.builder()
                    .ean(ORDER_LINE_EAN)
                    .standardRetailPrice(ORDER_LINE_PRICE)
                    .taxRate(TAX_RATE)
                    .lineNumber(1)
                    .orderLinePaidAmount(
                        OrderEntryAmount.builder()
                            .grossRetailUnitPrice(ORDER_LINE_PRICE)
                            .grossDiscountedUnitPrice(ORDER_LINE_PRICE.subtract(ORDER_LINE_DISCOUNT_AMOUNT))
                            .unitDiscount(ORDER_LINE_DISCOUNT_AMOUNT)
                            .build()
                    )
                    .build())
            )
            .orderCharges(
                List.of(OrderCharge.builder()
                    .createdTS(new Date().toInstant())
                    .lastModifiedTS(new Date().toInstant())
                    .taxRate(TAX_RATE)
                    .cancelled(SHIPPING_FEES_CANCELLED)
                    .type(EntryType.SHIPMENT_FEE)
                    .chargeTotal(
                        OrderEntryAmount.builder()
                            .grossDiscountedTotal(ORDER_CHARGE_AMOUNT)
                            .grossDiscountedUnitPrice(ORDER_CHARGE_AMOUNT)
                            .build()
                    )
                    .build())
            )
            .totalPaidPrice(
                OverallTotal.builder()
                    .grossDiscountedTotal(ORDER_TOTAL_PRICE)
                    .build()
            )
            .build();
    }

    public static Order createMixedPaymentOrder() {
        return createOrder(true);
    }

    public static Order createOrder() {
        return createOrder(false);
    }

    public static Order createOrder(boolean mixedPayment) {

        var adyenCardPayment = createAdyenCardPayment();
        var giftcardPayment = createGiftcardPayment();
        List<PaymentInfo> payments = new ArrayList<>();
        payments.add(adyenCardPayment);
        if (mixedPayment) {
            payments.add(giftcardPayment);
        }

        return Order.builder()
            .orderId(ORDER_ID)
            .brand(BRAND)
            .shippingCountryCode(SHIPPING_COUNTRY)
            .orderDate(ORDER_DATE)
            .paymentStatus(PAYMENT_STATE_AUTHORISED)
            .prevPaymentStatus(PREVIOUS_PAYMENT_STATE_START)
            .createdTS(ORDER_DATE.toInstant())
            .lastModifiedTS(ORDER_DATE.toInstant())
            .payments(payments)
            .orderLines(
                createOrderLines()
            )
            .orderCharges(createOrderCharges())
            .totalPaidPrice(
                OverallTotal.builder()
                    .grossDiscountedTotal(TOTAL_GROSS_DISCOUNTED_TOTAL)
                    .originalGrossDiscountedTotal(TOTAL_ORIGINAL_GROSS_DISCOUNTED_TOTAL)
                    .grossSubTotal(TOTAL_GROSS_SUBTOTAL)
                    .vat(TOTAL_VAT)
                    .build()
            )
            .refunds(new ArrayList<>())
            .build();
    }

    public static Order createOrderPaidOnlyByGiftCard() {
        return Order.builder()
            .orderId(ORDER_ID)
            .brand(BRAND)
            .shippingCountryCode(SHIPPING_COUNTRY)
            .orderDate(ORDER_DATE)
            .paymentStatus(PAYMENT_STATE_AUTHORISED)
            .prevPaymentStatus(PREVIOUS_PAYMENT_STATE_START)
            .createdTS(ORDER_DATE.toInstant())
            .lastModifiedTS(ORDER_DATE.toInstant())
            .payments(
                List.of(
                    createGiftcardPayment()
                )
            )
            .orderLines(
                createOrderLines()
            )
            .orderCharges(createOrderCharges())
            .totalPaidPrice(
                OverallTotal.builder()
                    .grossDiscountedTotal(TOTAL_GROSS_DISCOUNTED_TOTAL)
                    .originalGrossDiscountedTotal(TOTAL_ORIGINAL_GROSS_DISCOUNTED_TOTAL)
                    .grossSubTotal(TOTAL_GROSS_SUBTOTAL)
                    .vat(TOTAL_VAT)
                    .build()
            )
            .refunds(new ArrayList<>())
            .build();
    }

    private static @NotNull List<OrderCharge> createOrderCharges() {
        List<OrderCharge> orderCharges = new ArrayList<>();
        orderCharges.add(
            OrderCharge.builder()
                .createdTS(new Date().toInstant())
                .lastModifiedTS(new Date().toInstant())
                .name(SHIPPING)
                .ean(SHIPPING)
                .taxRate(TAX_RATE)
                .cancelled(Boolean.FALSE)
                .refunded(Boolean.FALSE)
                .type(EntryType.SHIPMENT_FEE)
                .openQty(1)
                .chargeTotal(
                    OrderEntryAmount.builder()
                        .grossDiscountedTotal(ORDER_CHARGE_AMOUNT)
                        .grossDiscountedUnitPrice(ORDER_CHARGE_AMOUNT)
                        .originalGrossDiscountedTotal(ORDER_CHARGE_AMOUNT)
                        .grossRetailUnitPrice(ORDER_CHARGE_AMOUNT)
                        .lineVAT(ORDER_CHARGE_LINE_VAT)
                        .unitVAT(ORDER_CHARGE_LINE_UNIT)
                        .build()
                )
                .build()
        );
        return orderCharges;
    }

    private static List<OrderLine> createOrderLines() {
        return IntStream.range(0, 3)
            .mapToObj((i) -> OrderLine.builder()
                .orderEntryId(i + 1)
                .lineNumber(i + 1)
                .ean(ORDER_LINE_EAN + (i + 1))
                .name(ORDER_LINE_NAME + (i + 1))
                .standardRetailPrice(ORDER_LINE_DISCOUNT_AMOUNTS.get(i))
                .taxRate(TAX_RATE)
                .orderLinePaidAmount(
                    OrderEntryAmount.builder()
                        .grossRetailUnitPrice(ORDER_LINE_GROSS_RETAIL_UNIT_PRICE.get(i))
                        .grossDiscountedUnitPrice(ORDER_LINE_GROSS_DISCOUNTED_UNIT_PRICE.get(i))
                        .unitDiscount(ORDER_LINE_DISCOUNT_AMOUNTS.get(i))
                        .originalGrossDiscountedTotal(ORDER_LINE_ORIGINAL_GROSS_DISCOUNTED_TOTAL.get(i))
                        .grossDiscountedTotal(ORDER_LINE_GROSS_DISCOUNTED_TOTAL.get(i))
                        .lineVAT(ORDER_LINE_LINE_VAT.get(i))
                        .unitVAT(ORDER_LINE_UNIT_VAT.get(i))
                        .build()
                )
                .openQty(ORDER_LINE_QTY.get(i))
                .originalQty(ORDER_LINE_QTY.get(i))
                .build())
            .collect(Collectors.toList());

    }

    public static AdyenBankPayment createBankPayment() {
        return AdyenBankPayment.builder()
            .paymentId(1)
            .authorisedAmount("20.0")
            .processorId(ProcessorId.ADYEN)
            .paymentReference("psp-reference")
            .type(PaymentType.ADYEN_BANK)
            .subMethod("-1")
            .subMethodName(PaymentMethod.ADYEN_IDEAL)
            .build();

    }

    public static KlarnaPayment createKlarnaPayment() {
        return KlarnaPayment.builder()
            .paymentId(1)
            .authorisedAmount("20.0")
            .processorId(ProcessorId.KLARNA_PAYMENTS)
            .paymentReference("psp-reference")
            .type(PaymentType.KLARNA)
            .subMethod("-1")
            .subMethodName(PaymentMethod.KLARNA_ACCOUNT)
            .build();
    }

    public static GiftcardPayment createGiftcardPayment() {
        return GiftcardPayment.builder()
            .paymentId(1)
            .authorisedAmount("20.0")
            .processorId(ProcessorId.OPTICARD)
            .paymentReference("psp-reference")
            .type(PaymentType.GIFTCARD)
            .subMethod("-1")
            .subMethodName(PaymentMethod.OC_GIFTCARD)
            .build();
    }

    public static AdyenCardPayment createAdyenCardPayment() {
        return AdyenCardPayment.builder()
            .paymentId(1)
            .authorisedAmount("20.0")
            .processorId(ProcessorId.ADYEN)
            .paymentReference("psp-reference")
            .type(PaymentType.ADYEN_CARD)
            .subMethod("-1")
            .subMethodName(PaymentMethod.ADYEN_CREDIT_CARD)
            .build();
    }

    public static OrderDto createOrderDto(Order order) {
        return OrderDto.builder()
            .orderId(order.getOrderId())
            .orderCharges(createOrderChargeDtos(order))
            .refundsInfo(createRefundsInfoDto(order))
            .payment(createPaymentsDto(order))
            .orderLines(createOrderLinesDto(order))
            .build();
    }

    private static RefundsInfoDto createRefundsInfoDto(Order order) {
        return RefundsInfoDto.builder()
            .refunds(
                order.getRefunds()
                    .stream()
                    .map(refund -> RefundDto.builder()
                        .id(refund.getId())
                        .vatId(refund.getRefundId())
                        .refundDate(ZonedDateTime.ofInstant(refund.getCreatedTS(), ZoneId.systemDefault()))
                        .refundStatus(refund.getRefundState().name())
                        .build()
                    )
                    .toList()
            )
            .totalAmount(
                order.getRefunds()
                    .stream()
                    .map(refund -> refund.getRefundTotal().getGrossDiscountedTotal())
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
            )
            .build();
    }

    private static List<OrderChargeDto> createOrderChargeDtos(Order order) {
        return order.getOrderCharges()
            .stream()
            .map(orderCharge -> OrderChargeDto.builder()
                .name(orderCharge.getName())
                .id(orderCharge.getOrderEntryId())
                .ean(orderCharge.getEan())
                .cancelled(orderCharge.getCancelled())
                .refunded(orderCharge.getRefunded())
                .chargeTotal(orderCharge.getChargeTotal().getGrossDiscountedTotal())
                .type(orderCharge.getType().name())
                .allowedRefundReasons(
                    Arrays.stream(ChargedRefundReason.values())
                        .filter(ChargedRefundReason::getEnabled)
                        .filter(reason -> reason.getType().equals(orderCharge.getType()))
                        .map(ChargedRefundReason::getDescription)
                        .toList()
                )
                .build()
            ).toList();
    }

    private static PaymentDto createPaymentsDto(Order order) {
        var nonGiftCardPayment = order.getPayments()
            .stream()
            .filter(payment -> !(payment instanceof GiftcardPayment))
            .findFirst()
            .orElse(null);

        var giftCardPayment = order.getPayments()
            .stream()
            .filter(payment -> payment instanceof GiftcardPayment)
            .findFirst()
            .orElse(null);

        return PaymentDto.builder()
            .subMethodType(nonGiftCardPayment.getSubMethod())
            .subMethodName(nonGiftCardPayment.getSubMethodName().getDescription())
            .transactionId(nonGiftCardPayment.getPaymentReference())
            .paymentType(nonGiftCardPayment.getType().getDescription())
            .paymentTypeName(nonGiftCardPayment.getType().name())
            .processorId(nonGiftCardPayment.getProcessorId().name())
            .subMethodId(nonGiftCardPayment.getSubMethodName().name())
            .nonGiftCardAuthorisedAmount(new BigDecimal(nonGiftCardPayment.getAuthorisedAmount()))
            .paymentStatus(order.getPaymentStatus().name())
            .giftCardAuthorisedAmount(new BigDecimal(giftCardPayment.getAuthorisedAmount()))
            .originalGrossDiscountedTotal(order.getTotalPaidPrice().getOriginalGrossDiscountedTotal())
            .grossDiscountedTotal(order.getTotalPaidPrice().getGrossDiscountedTotal())
            .build();
    }

    private static Date getOrderDate() {
        try {
            return new SimpleDateFormat("yyyy-MM-dd").parse("2018-09-09");
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    private static List<OrderLineDto> createOrderLinesDto(Order order) {
        return order.getOrderLines().stream()
            .map(orderLine -> OrderLineDto.builder()
                .ean(orderLine.getEan())
                .standardRetailPrice(orderLine.getStandardRetailPrice())
                .pricePaid(orderLine.getOrderLinePaidAmount().getGrossDiscountedUnitPrice())
                .discount(orderLine.getOrderLinePaidAmount().getUnitDiscount())
                .build()
            )
            .toList();
    }
}
