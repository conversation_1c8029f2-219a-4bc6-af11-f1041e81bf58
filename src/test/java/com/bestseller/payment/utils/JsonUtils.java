package com.bestseller.payment.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import static com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_INVALID_SUBTYPE;
import static com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_MISSING_EXTERNAL_TYPE_ID_PROPERTY;

public class JsonUtils {
    private static final JsonUtils INSTANCE = new JsonUtils();
    private final ObjectMapper objectMapper;

    public JsonUtils() {
        this.objectMapper = new ObjectMapper();
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                .disable(FAIL_ON_MISSING_EXTERNAL_TYPE_ID_PROPERTY)
                .disable(FAIL_ON_INVALID_SUBTYPE);
        objectMapper.registerModule(new JavaTimeModule());
    }

    private String serializeToJson(Object message) throws JsonProcessingException {
        return objectMapper.writeValueAsString(message);
    }

    private <T> T deserializeJson(String json, Class<T> targetType) throws JsonProcessingException {
        return objectMapper.readValue(json, targetType);
    }

    public static String serialize(Object message) throws JsonProcessingException {
        return INSTANCE.serializeToJson(message);
    }

    public static <T> T deserialize(String json, Class<T> targetType) throws JsonProcessingException {
        return INSTANCE.deserializeJson(json, targetType);
    }

    public static <T> T deepCopy(T object) throws JsonProcessingException {
        return deserialize(serialize(object), (Class<T>) object.getClass());
    }
}
