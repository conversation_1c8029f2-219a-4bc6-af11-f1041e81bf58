package com.bestseller.payment.utils;

import com.bestseller.payment.core.domain.enumeration.PaymentMethod;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import com.bestseller.payment.core.domain.payment.AdyenCardPayment;
import com.bestseller.payment.core.domain.payment.KlarnaPayment;
import com.bestseller.payment.core.domain.payment.NonePayment;

public class PaymentInfoGenerator {

    public static final Integer PAYMENT_ID = 1;
    public static final String ORDER_ID = "ORDER_ID";
    public static final String PAYMENT_REFERENCE = "PAYMENT_REFERENCE";
    public static final String AUTHORISED_AMOUNT = "11.11";
    public static final String SUB_METHOD = "subMethod";


    public static AdyenCardPayment generateAdyenCardPayment(PaymentMethod paymentMethod) {
        return AdyenCardPayment.builder()
            .paymentId(PAYMENT_ID)
            .paymentReference(PAYMENT_REFERENCE)
            .orderId(ORDER_ID)
            .authorisedAmount(AUTHORISED_AMOUNT)
            .processorId(ProcessorId.ADYEN)
            .type(PaymentType.ADYEN_CARD)
            .subMethod(SUB_METHOD)
            .subMethodName(paymentMethod)
            .build();
    }

    public static KlarnaPayment generateKlarnaPayment(PaymentMethod paymentMethod) {
        return KlarnaPayment.builder()
            .paymentId(PAYMENT_ID)
            .paymentReference(PAYMENT_REFERENCE)
            .orderId(ORDER_ID)
            .authorisedAmount(AUTHORISED_AMOUNT)
            .processorId(ProcessorId.KLARNA_PAYMENTS)
            .type(PaymentType.KLARNA)
            .subMethod(SUB_METHOD)
            .subMethodName(paymentMethod)
            .build();
    }

    public static NonePayment generateNonePayment() {
        return NonePayment.builder()
            .paymentId(PAYMENT_ID)
            .paymentReference(PAYMENT_REFERENCE)
            .orderId(ORDER_ID)
            .authorisedAmount(AUTHORISED_AMOUNT)
            .processorId(ProcessorId.OFFLINE)
            .type(PaymentType.NONE)
            .subMethod(null)
            .subMethodName(null)
            .build();
    }
}
