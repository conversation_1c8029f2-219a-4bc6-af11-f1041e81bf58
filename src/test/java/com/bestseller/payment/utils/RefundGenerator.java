package com.bestseller.payment.utils;

import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderCharge;
import com.bestseller.payment.core.domain.OrderEntryAmount;
import com.bestseller.payment.core.domain.OverallTotal;
import com.bestseller.payment.core.domain.Refund;
import com.bestseller.payment.core.domain.RefundLine;
import com.bestseller.payment.core.domain.enumeration.ChargedRefundReason;
import com.bestseller.payment.core.domain.enumeration.EntryType;
import com.bestseller.payment.core.domain.enumeration.RefundType;
import com.logistics.statetransition.RefundState;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import java.util.stream.IntStream;

public class RefundGenerator {

    public final static UUID REQUEST_ID = UUID.randomUUID();
    public final static RefundState REFUND_STATE = RefundState.REFUND_REQUESTED;
    public final static String CSR_INITIALS = "CSR";
    public final static int REFUND_LINE_QTY = 1;
    public final static String REFUND_LINE_NUMBER = "1";
    public final static BigDecimal REFUND_LINE_GROSS_DISCOUNTED_UNIT_PRICE = new BigDecimal("100.00");
    public final static BigDecimal REFUND_LINE_GROSS_DISCOUNTED_TOTAL_PRICE = new BigDecimal("100.00");
    public final static BigDecimal REFUND_LINE_UNIT_VAT = new BigDecimal("10.00");
    public final static BigDecimal RETURN_FEE = new BigDecimal("10.00");
    public static final BigDecimal UNIT_VAT = new BigDecimal("2.00");
    public static final String REFUND_ID = "REFUND_ID0";

    public static Refund createRefund() {
        return createRefund(true);
    }

    public static Refund createRefund(boolean mixedPayment) {
        Order order = mixedPayment ? OrderGenerator.createMixedPaymentOrder() : OrderGenerator.createOrder();

        return Refund.builder()
            .createdTS(Instant.ofEpochSecond(System.currentTimeMillis()))
            .lastModifiedTS(Instant.ofEpochSecond(System.currentTimeMillis()))
            .requestId(REQUEST_ID.toString())
            .order(order)
            .refundLines(createRefundLines(order))
            .refundCharges(createRefundCharges())
            .refundTotal(OverallTotal.builder()
                .grossDiscountedTotal(new BigDecimal("100.00"))
                .grossSubTotal(new BigDecimal("90.00"))
                .originalGrossDiscountedTotal(new BigDecimal("10.00"))
                .build())
            .refundState(REFUND_STATE)
            .csrInitials(CSR_INITIALS)
            .id(1)
            .refundType(mixedPayment ? RefundType.MIXED : RefundType.BANK_TRANSACTION)
            .giftCardCorrelationId(mixedPayment ? UUID.randomUUID() : null)
            .build();
    }

    public static List<RefundLine> createRefundLines(Order order) {
        return List.of(RefundLine.builder()
            .quantity(REFUND_LINE_QTY)
            .lineNumber(REFUND_LINE_NUMBER)
            .orderLine(order.getOrderLines().get(0))
            .refundLineTotal(
                OrderEntryAmount.builder()
                    .grossDiscountedUnitPrice(REFUND_LINE_GROSS_DISCOUNTED_UNIT_PRICE)
                    .grossDiscountedTotal(REFUND_LINE_GROSS_DISCOUNTED_TOTAL_PRICE)
                    .unitVAT(REFUND_LINE_UNIT_VAT)
                    .lineVAT(REFUND_LINE_UNIT_VAT)
                    .build()
            )
            .build());
    }

    public static List<OrderCharge> createRefundCharges() {
        return List.of(OrderCharge.builder()
            .refunded(true)
            .ean("RETURN_FEE")
            .name("Return fee")
            .openQty(-1)
            .type(EntryType.RETURN_FEE)
            .refundReason(ChargedRefundReason.SYSTEM)
            .taxRate(REFUND_LINE_UNIT_VAT)
            .cancelled(false)
            .refunded(false)
            .chargeTotal(OrderEntryAmount.builder()
                .grossRetailUnitPrice(RETURN_FEE)
                .grossDiscountedUnitPrice(RETURN_FEE)
                .grossDiscountedTotal(RETURN_FEE)
                .lineVAT(UNIT_VAT)
                .unitVAT(UNIT_VAT)
                .build())
            .build());
    }

    public static List<Refund> generateRefunds(int count) {
        return IntStream.range(0, count)
            .mapToObj(i -> {
                var refund = RefundGenerator.createRefund();
                refund.setRefundId(refund.getRefundId() + i);
                refund.setId(i);
                refund.setRefundId(REFUND_ID);
                return refund;
            }).toList();
    }
}
