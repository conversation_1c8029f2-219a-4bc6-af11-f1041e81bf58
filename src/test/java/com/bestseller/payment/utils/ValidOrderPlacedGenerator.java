package com.bestseller.payment.utils;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.Address;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.CustomerInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.OrderDetails;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.Payment;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ShippingCharge;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ShippingInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;

public class ValidOrderPlacedGenerator {

    public static class StoreProperties {
        public static final String TRADEBYTE_STORE = "TRADEBYTE";
        public static final String DEMANDWARE_STORE = "DEMANDWARE";
    }

    public static class AddressProperties {
        public static final String ADDRESS_LINE_1 = "Street 1";

        public static final String ADDRESS_LINE_2 = "line2";
        public static final String ADDRESS_LINE_3 = "line3";
        public static final String CITY = "DUBLIN";
        public static final String CITY_AMSTERDAM = "AMSTERDAM";

        public static final String COUNTRY_NL = "NL";
        public static final String COUNTRY_DK = "DK";
        public static final String FIRST_NAME = "firstName";
        public static final String LAST_NAME = "lastName";
        public static final String HOUSE_NUMBER = "66";
        public static final String HOUSE_NUMBER_EXTENDED = "A";
        public static final String PHONE_NUMBER = "+3456000000";
        public static final String ZIPCODE = "1232 KL";
    }

    public static class CustomerInformationProperties {
        public static final String EMAIL_ADDRESS = "<EMAIL>";
        public static final String CUSTOMER_ID = "12344321";
        public static final String CUSTOMER_LOCALE = "NL-nl";
    }

    public static class OrderDetailsProperties {
        public static final BigDecimal ORDER_VALUE = BigDecimal.valueOf(413.50);
    }

    public static final BigDecimal SHIPPING_FEES = new BigDecimal("3.95");
    public static final BigDecimal SHIPPING_TAX_RATE = new BigDecimal(".19");
    public static final BigDecimal SHIPPING_VAT = new BigDecimal(".63");
    public static final String STANDARD_SHIPPING = "STANDARD_SHIPPING";
    public static final String SHIPPING = "Shipping";

    private static ValidOrderPlaced createValidOrderPlaced() {
        Address billingAddress = new Address();
        billingAddress.withAddressLine1(AddressProperties.ADDRESS_LINE_1)
            .withAddressLine2(AddressProperties.ADDRESS_LINE_2)
            .withAddressLine3(AddressProperties.ADDRESS_LINE_3)
            .withCity(AddressProperties.CITY)
            .withCountry(AddressProperties.COUNTRY_DK)
            .withFirstName(AddressProperties.FIRST_NAME)
            .withLastName(AddressProperties.LAST_NAME)
            .withHouseNumber(AddressProperties.HOUSE_NUMBER)
            .withHouseNumberExtended(AddressProperties.HOUSE_NUMBER_EXTENDED)
            .withPhoneNumber(AddressProperties.PHONE_NUMBER)
            .withZipcode(AddressProperties.ZIPCODE);

        CustomerInformation customerInformation = new CustomerInformation()
            .withCustomerLocale(CustomerInformationProperties.CUSTOMER_LOCALE)
            .withEmail(CustomerInformationProperties.EMAIL_ADDRESS)
            .withBillingAddress(billingAddress)
            .withCustomerId(CustomerInformationProperties.CUSTOMER_ID);

        OrderLine orderLine = createOrderLine(BigDecimal.valueOf(20.95)
            , BigDecimal.valueOf(20.95)
            , BigDecimal.valueOf(20.95)
            , BigDecimal.valueOf(0.21)
            , 1
            , BigDecimal.valueOf(3.64), null);
        Payment payment = new Payment()
            .withAmount(BigDecimal.valueOf(20.95))
            .withMethod("ADYEN")
            .withState("AUTHORISED")
            .withPspReference("**********")
            .withAdditionalReference("**********")
            .withProvider("ADYEN");

        return new ValidOrderPlaced()
            .withPayments(List.of(payment))
            .withCustomerInformation(customerInformation)
            .withOrderId("OL12345")
            .withOrderLines(Collections.singletonList(orderLine))
            .withOrderDetails(createOrderDetails())
            .withShippingInformation(createDefaultShippingInformation());
    }

    public static ShippingInformation createDefaultShippingInformation() {
        ShippingCharge shippingCharge = new ShippingCharge()
            .withShippingName(SHIPPING)
            .withShippingFeesEan(STANDARD_SHIPPING)
            .withShippingVatAmount(SHIPPING_VAT)
            .withShippingTaxRate(SHIPPING_TAX_RATE)
            .withBaseGrossUnitPrice(SHIPPING_FEES)
            .withGrossUnitPriceAfterDiscounts(SHIPPING_FEES)
            .withGrossPriceAfterDiscounts(SHIPPING_FEES)
            .withPromotionId("CS-free-shipping-Nordic")
            .withCampaignId("CS-free-shipping-Nordic")
            .withCouponId("CSFS-OILR-MEIL-NIEO-EZTY");
        return new ShippingInformation()
            .withShippingCharges(List.of(shippingCharge))
            .withShippingAddress(
                new Address()
                    .withFirstName(AddressProperties.FIRST_NAME)
                    .withLastName(AddressProperties.LAST_NAME)
                    .withAddressLine1(AddressProperties.ADDRESS_LINE_1)
                    .withCity(AddressProperties.CITY_AMSTERDAM)
                    .withCountry(AddressProperties.COUNTRY_NL));
    }

    public static OrderLine createOrderLine(BigDecimal discountedTotalPrice, BigDecimal discountedUnitPrice,
                                            BigDecimal retailPrice, BigDecimal vat,
                                            Integer quantity,
                                            BigDecimal taxUnitPrice, String ean) {
        return new OrderLine()
            .withId(UUID.randomUUID())
            .withLineNumber(1)
            .withBonusProduct(false)
            .withBrand("JJ")
            .withEan(ean == null ? "58963214789" : ean)
            .withDiscountedTotalPrice(discountedTotalPrice)
            .withDiscountedUnitPrice(discountedUnitPrice)
            .withRetailPrice(retailPrice)
            .withVat(vat)
            .withQuantity(quantity)
            .withTaxUnitPrice(taxUnitPrice)
            .withProductName("Jumpsuit")
            .withExternalItemId(*********);
    }

    public static ValidOrderPlaced createBseOrderPlacedMessage() {
        return createValidOrderPlaced()
            .withOrderId("OL12345")
            .withStore(StoreProperties.DEMANDWARE_STORE)
            .withPlacedDate(ZonedDateTime.now());

    }

    public static ValidOrderPlaced createTradebyteOrderPlacedMessageWithShippingInformation() {
        OrderDetails orderDetails = new OrderDetails()
            .withOrderValue(OrderDetailsProperties.ORDER_VALUE)
            .withBrandedShipping("JJ")
            .withCheckout("jj");
        return createValidOrderPlaced()
            .withOrderId("TB12345")
            .withStore(StoreProperties.TRADEBYTE_STORE)
            .withPlacedDate(ZonedDateTime.now())
            .withOrderDetails(orderDetails)
            .withOfflinePayment(true)
            .withShippingInformation(createDefaultShippingInformation());
    }

    public static ValidOrderPlaced createTradebyteOrderPlacedMessageWithoutPaymentInformation() {
        OrderDetails orderDetails = new OrderDetails()
            .withOrderValue(OrderDetailsProperties.ORDER_VALUE)
            .withBrandedShipping("JJ")
            .withCheckout("jj");
        return createValidOrderPlaced()
            .withOrderId("TB12346")
            .withStore(StoreProperties.TRADEBYTE_STORE)
            .withPlacedDate(ZonedDateTime.now())
            .withOrderDetails(orderDetails)
            .withPayments(new ArrayList<>());
    }

    public static ValidOrderPlaced createBseOrderPlacedMessage_Adyen(String method, String subMethod, String state) {

        BigDecimal price = BigDecimal.valueOf(4);
        String shippingName = "Shipping";
        BigDecimal shippingTaxRate = BigDecimal.valueOf(0.21);
        BigDecimal shippingVatAmount = BigDecimal.valueOf(0.7);
        String shippingFeeEan = "STANDARD_SHIPPING";
        ShippingCharge shippingCharge = new ShippingCharge(shippingName, shippingFeeEan, shippingTaxRate, shippingVatAmount, price,
            price, price,
            null, null, null);

        ShippingInformation shippingInformation = new ShippingInformation()
            .withShippingCharges(Collections.singletonList(shippingCharge));

        String pspReference = "**********";
        String additionalReference = null;
        String provider = "ADYEN";
        BigDecimal amount = BigDecimal.valueOf(24.95);
        Payment payment = new Payment(method, subMethod, pspReference, additionalReference, provider,
            amount, state, null, null, null);
        ArrayList<Payment> payments = new ArrayList<>();
        payments.add(payment);

        return createValidOrderPlaced()
            .withStore(StoreProperties.DEMANDWARE_STORE)
            .withPlacedDate(ZonedDateTime.now())
            .withPayments(payments.stream().toList())
            .withOrderDetails(createOrderDetails())
            .withShippingInformation(shippingInformation);

    }

    public static ValidOrderPlaced createBseOrderWithShippingCharge(String method, String subMethod, String state) {

        BigDecimal price = BigDecimal.valueOf(4);

        String pspReference = "**********";
        String additionalReference = null;
        String provider = "ADYEN";
        BigDecimal amount = BigDecimal.valueOf(24.95);
        Payment payment = new Payment(method, subMethod, pspReference, additionalReference, provider,
            amount, state, null, null, null);
        ArrayList<Payment> payments = new ArrayList<>();
        payments.add(payment);

        return createValidOrderPlaced()
            .withStore(StoreProperties.DEMANDWARE_STORE)
            .withPlacedDate(ZonedDateTime.now())
            .withPayments(payments.stream().toList())
            .withOrderDetails(createOrderDetails())
            .withShippingInformation(createDefaultShippingInformation());

    }

    public static ValidOrderPlaced createOrderWithKlarnaPayment() {
        BigDecimal price = BigDecimal.valueOf(4);
        String shippingName = "Shipping";
        BigDecimal shippingTaxRate = BigDecimal.valueOf(0.21);
        BigDecimal shippingVatAmount = BigDecimal.valueOf(0.7);
        String shippingFeeEan = "STANDARD_SHIPPING";
        ShippingCharge shippingCharge = new ShippingCharge(shippingName, shippingFeeEan, shippingTaxRate, shippingVatAmount, price,
            price, price,
            null, null, null);

        ShippingInformation shippingInformation = new ShippingInformation()
            .withShippingCharges(Collections.singletonList(shippingCharge));

        String pspReference = "31e364b5-f197-426a-b322-4c7096311588";
        String provider = "KLARNA_PAYMENTS";
        BigDecimal amount = BigDecimal.valueOf(301.89);
        String state = "AUTH";
        Payment payment = new Payment(null, null, pspReference, null, provider,
            amount, state, null, null, null);
        ArrayList<Payment> payments = new ArrayList<>();
        payments.add(payment);

        return createValidOrderPlaced()
            .withStore(StoreProperties.DEMANDWARE_STORE)
            .withPlacedDate(ZonedDateTime.now())
            .withPayments(payments.stream().toList())
            .withOrderDetails(createOrderDetails())
            .withShippingInformation(shippingInformation);
    }

    public static ValidOrderPlaced createOrderWithMultipleNonGiftcardPayments() {
        BigDecimal price = BigDecimal.valueOf(4);
        String shippingName = "Shipping";
        BigDecimal shippingTaxRate = BigDecimal.valueOf(0.21);
        BigDecimal shippingVatAmount = BigDecimal.valueOf(0.7);
        String shippingFeeEan = "STANDARD_SHIPPING";
        ShippingCharge shippingCharge = new ShippingCharge(shippingName, shippingFeeEan, shippingTaxRate, shippingVatAmount, price,
            price, price,
            null, null, null);

        ShippingInformation shippingInformation = new ShippingInformation()
            .withShippingCharges(Collections.singletonList(shippingCharge));

        String method = "ADYEN_IDEAL";
        String subMethod = "ideal";
        String pspReference = "**********";
        String additionalReference = null;
        String provider = "ADYEN";
        BigDecimal amount = BigDecimal.valueOf(24.95);
        String state = "AUTH";
        Payment payment = new Payment(method, subMethod, pspReference, additionalReference, provider,
            amount, state, null, null, null);

        method = "KLARNA_INVOICE";
        pspReference = "31e364b5-f197-426a-b322-4c7096311588";
        provider = "KLARNA_PAYMENTS";
        subMethod = "-1";
        amount = BigDecimal.valueOf(301.89);
        state = "AUTHORISED";
        Payment payment1 = new Payment(method, subMethod, pspReference, additionalReference, provider,
            amount, state, null, null, null);

        ArrayList<Payment> payments = new ArrayList<>(Stream.of(payment, payment1).toList());

        return createValidOrderPlaced()
            .withStore(StoreProperties.DEMANDWARE_STORE)
            .withPlacedDate(ZonedDateTime.now())
            .withPayments(payments.stream().toList())
            .withShippingInformation(shippingInformation);
    }

    public static ValidOrderPlaced createBseOrderPlacedMessage_One_GiftCard() {

        BigDecimal price = BigDecimal.valueOf(0);
        String shippingName = "Shipping";
        BigDecimal shippingTaxRate = BigDecimal.valueOf(0.21);
        BigDecimal shippingVatAmount = BigDecimal.valueOf(0);
        String shippingFeeEan = "STANDARD_SHIPPING";
        ShippingCharge shippingCharge = new ShippingCharge(shippingName, shippingFeeEan, shippingTaxRate, shippingVatAmount, price,
            price, price,
            null, null, null);

        ShippingInformation shippingInformation = new ShippingInformation()
            .withShippingCharges(Collections.singletonList(shippingCharge));

        String method = "OC_GIFTCARD";
        String pspReference = "6299201243102467987";
        String additionalReference = "2a7d7992-7c77-4698-89c5-08fae14ad9bc";
        String provider = "OPTICARD";
        String subMethod = null;
        BigDecimal amount = BigDecimal.valueOf(5.01);
        String state = "AUTH";
        Payment payment1 = new Payment(method, subMethod, pspReference, additionalReference, provider,
            amount, state, null, null, null);


        ArrayList<Payment> payments = new ArrayList<>();
        payments.addAll(List.of(payment1).stream().toList());

        return createValidOrderPlaced()
            .withStore(StoreProperties.DEMANDWARE_STORE)
            .withPlacedDate(ZonedDateTime.now())
            .withPayments(payments.stream().toList())
            .withOrderDetails(createOrderDetails())
            .withShippingInformation(shippingInformation);
    }

    public static ValidOrderPlaced createBseOrderPlacedMessage_Multi_Payment() {
        return createBseOrderPlacedMessage_Multi_Payment("AUTH");
    }

    public static ValidOrderPlaced createBseOrderPlacedMessage_Multi_Payment(String noneGiftcardState) {

        BigDecimal price = BigDecimal.valueOf(4);
        String shippingName = "Shipping";
        BigDecimal shippingTaxRate = BigDecimal.valueOf(0.21);
        BigDecimal shippingVatAmount = BigDecimal.valueOf(0.7);
        String shippingFeeEan = "STANDARD_SHIPPING";
        ShippingCharge shippingCharge = new ShippingCharge(shippingName, shippingFeeEan, shippingTaxRate, shippingVatAmount, price,
            price, price,
            null, null, null);

        ShippingInformation shippingInformation = new ShippingInformation()
            .withShippingCharges(Collections.singletonList(shippingCharge));

        String method = "ADYEN_IDEAL";
        String subMethod = "ideal";
        String pspReference = "**********";
        String additionalReference = null;
        String provider = "ADYEN";
        BigDecimal amount = BigDecimal.valueOf(24.95);
        Payment payment1 = new Payment(method, subMethod, pspReference, additionalReference, provider,
            amount, noneGiftcardState, null, null, null);

        method = "OC_GIFTCARD";
        pspReference = "6299201155483449386";
        additionalReference = "bb06fcec-837e-4ca0-8636-4fbd7f312908";
        provider = "OPTICARD";
        subMethod = null;
        amount = BigDecimal.valueOf(50.0);
        String state = "AUTH";
        Payment payment2 = new Payment(method, subMethod, pspReference, additionalReference, provider,
            amount, state, null, null, null);


        ArrayList<Payment> payments = new ArrayList<>(Stream.of(payment1, payment2).toList());

        return createValidOrderPlaced()
            .withStore(StoreProperties.DEMANDWARE_STORE)
            .withPlacedDate(ZonedDateTime.now())
            .withPayments(payments.stream().toList())
            .withOrderDetails(createOrderDetails())
            .withShippingInformation(shippingInformation);
    }

    public static ValidOrderPlaced createBseOrderPlacedMessage_Multi_GiftCard() {

        BigDecimal price = BigDecimal.valueOf(0);
        String shippingName = "Shipping";
        BigDecimal shippingTaxRate = BigDecimal.valueOf(0.21);
        BigDecimal shippingVatAmount = BigDecimal.valueOf(0);
        String shippingFeeEan = "STANDARD_SHIPPING";
        ShippingCharge shippingCharge = new ShippingCharge(shippingName, shippingFeeEan, shippingTaxRate, shippingVatAmount, price,
            price, price,
            null, null, null);

        ShippingInformation shippingInformation = new ShippingInformation()
            .withShippingCharges(Collections.singletonList(shippingCharge));

        String method = "OC_GIFTCARD";
        String pspReference = "6299201243102467987";
        String additionalReference = "2a7d7992-7c77-4698-89c5-08fae14ad9bc";
        String provider = "OPTICARD";
        String subMethod = null;
        BigDecimal amount = BigDecimal.valueOf(5.01);
        String state = "AUTH";
        Payment payment1 = new Payment(method, subMethod, pspReference, additionalReference, provider,
            amount, state, null, null, null);

        method = "OC_GIFTCARD";
        pspReference = "6299201155483449386";
        additionalReference = "bb06fcec-837e-4ca0-8636-4fbd7f312908";
        provider = "OPTICARD";
        amount = BigDecimal.valueOf(50.0);
        state = "AUTH";
        Payment payment2 = new Payment(method, subMethod, pspReference, additionalReference, provider,
            amount, state, null, null, null);

        method = "OC_GIFTCARD";
        pspReference = "6299201155483464468";
        additionalReference = "b4a16539-7904-40af-a0de-2d1a71e03d0f";
        provider = "OPTICARD";
        amount = BigDecimal.valueOf(50.0);
        state = "AUTH";
        Payment payment3 = new Payment(method, subMethod, pspReference, additionalReference, provider,
            amount, state, null, null, null);

        method = "OC_GIFTCARD";
        pspReference = "6299201155483476843";
        additionalReference = "a194f0a1-a06c-43fe-afe7-ede47a72f08a";
        provider = "OPTICARD";
        amount = BigDecimal.valueOf(14.98);
        state = "AUTHORISED";
        Payment payment4 = new Payment(method, subMethod, pspReference, additionalReference, provider,
            amount, state, null, null, null);

        ArrayList<Payment> payments = new ArrayList<>(Stream.of(payment1, payment2, payment3, payment4).toList());

        return createValidOrderPlaced()
            .withStore(StoreProperties.DEMANDWARE_STORE)
            .withPlacedDate(ZonedDateTime.now())
            .withPayments(payments.stream().toList())
            .withOrderDetails(createOrderDetails())
            .withShippingInformation(shippingInformation);
    }

    public static OrderDetails createOrderDetails() {
        return new OrderDetails()
            .withShippingMethod("STANDARD")
            .withOrderValue(BigDecimal.valueOf(413.50))
            .withCheckout("jj")
            .withBrandedShipping("JJ")
            .withCurrency("EUR");
    }


}
