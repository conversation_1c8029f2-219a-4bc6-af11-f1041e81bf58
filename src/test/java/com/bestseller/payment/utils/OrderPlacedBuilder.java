package com.bestseller.payment.utils;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.Address;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.CustomerInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.OrderDetails;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.OrderPlaced;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.Payment;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.ShippingCharge;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.ShippingInformation;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class OrderPlacedBuilder {

    private static final String ORDER_ID = "ORDER_ID";
    private static final BigDecimal DEFAULT_ORDER_VALUE = new BigDecimal(8.0);
    private static final BigDecimal DEFAULT_SHIPPING_FEE = new BigDecimal(1.5);
    private static final BigDecimal DEFAULT_TAX_RATE = new BigDecimal(0.5);
    private static final BigDecimal DEFAULT_PAYMENT_AMOUNT = new BigDecimal(8.0);
    private static final String DEFAULT_PAYMENT_STATE = "AUTH";
    private static final String DEFAULT_STORE = "DEMANDWARE";
    private static final String DEFAULT_BILLING_COUNTRY_CODE = "NL";
    private static final String DEFAULT_SHIPPING_NAME = "shipping";
    private static final String DEFAULT_SHIPPING_FEES_EAN = "STANDARD_SHIPPING";
    private String store = DEFAULT_STORE;

    private String billingCountryCode = DEFAULT_BILLING_COUNTRY_CODE;

    private List<Payment> payments = new ArrayList<>();

    private Boolean offlinePayment;

    private BigDecimal orderValue = DEFAULT_ORDER_VALUE;
    private BigDecimal shippingFee = DEFAULT_SHIPPING_FEE;
    private String shippingFeesEan = DEFAULT_SHIPPING_FEES_EAN;
    private String shippingName = DEFAULT_SHIPPING_NAME;

    List<OrderLine> orderLines = new ArrayList<>();

    public OrderPlaced build() {
        OrderPlaced orderPlaced = new OrderPlaced();
        orderPlaced.withStore(store);
        orderPlaced.withOrderId(ORDER_ID);
        final OrderDetails orderDetails = new OrderDetails()
            .withOrderValue(orderValue)
            .withCurrency("EUR");
        orderPlaced.withOrderDetails(orderDetails);
        orderPlaced.withOrderLines(orderLines);
        orderPlaced.withCustomerInformation(getCustomerInformation(billingCountryCode));
        orderPlaced.withPayments(payments);

        final ShippingCharge shippingCharge = new ShippingCharge()
                .withShippingName(shippingName)
                .withGrossUnitPriceAfterDiscounts(shippingFee)
                .withBaseGrossUnitPrice(shippingFee)
                .withGrossPriceAfterDiscounts(shippingFee)
                .withShippingFeesEan(shippingFeesEan)
                .withShippingTaxRate(DEFAULT_TAX_RATE);

        final ShippingInformation shippingInformation = new ShippingInformation()
                .withShippingCharges(List.of(shippingCharge));
        orderPlaced.withShippingInformation(shippingInformation);
        orderPlaced.withOfflinePayment(offlinePayment);

        return orderPlaced;
    }

    public OrderPlacedBuilder withDefaultOrderLines() {
        this.orderLines = getOrderLines();
        return this;
    }

    public OrderPlacedBuilder withDefaultValuesPaidWithKlarna() {
        this.orderLines = getOrderLines();
        this.payments.add(getKlarnaPayment("KLARNA_ACCOUNT", "-1", DEFAULT_PAYMENT_AMOUNT, DEFAULT_PAYMENT_STATE));
        return this;
    }

    public OrderPlacedBuilder withDefaultValuesPaidWithAdyen() {
        this.orderLines = getOrderLines();
        this.payments.add(getAdyenPayment("ADYEN_CREDIT_CARD", "visa", DEFAULT_PAYMENT_AMOUNT, DEFAULT_PAYMENT_STATE));
        return this;
    }

    public OrderPlacedBuilder withDefaultValuesPaidWithGiftCard() {
        this.orderLines = getOrderLines();
        this.payments.add(getGiftCard(DEFAULT_PAYMENT_AMOUNT));
        return this;
    }

    public OrderPlacedBuilder withStore(String store) {
        this.store = store;
        return this;
    }

    public OrderPlacedBuilder withBillingCountryCode(String countryCode) {
        this.billingCountryCode = countryCode;
        return this;
    }

    public OrderPlacedBuilder withPayments(List<Payment> payments) {
        this.payments = payments;
        return this;
    }

    public OrderPlacedBuilder withOfflinePayment(Boolean offlinePayment) {
        this.offlinePayment = offlinePayment;
        return this;
    }

    public OrderPlacedBuilder withKlarnaPayment(String method, String submethod, BigDecimal amount, String state) {
        payments.add(getKlarnaPayment(method, submethod, amount, state));
        return this;
    }

    public OrderPlacedBuilder withAdyenPayment(String method, String submethod, BigDecimal amount, String state) {
        payments.add(getAdyenPayment(method, submethod, amount, state));
        return this;
    }

    public OrderPlacedBuilder withGiftCard(BigDecimal amount) {
        payments.add(getGiftCard(amount));
        return this;
    }

    public OrderPlacedBuilder withOrderValue(BigDecimal orderValue) {
        this.orderValue = orderValue;
        return this;
    }

    public OrderPlacedBuilder withShippingFee(BigDecimal shippingFee) {
        this.shippingFee = shippingFee;
        return this;
    }

    public OrderPlacedBuilder withShippingFeesEan(String shippingFeesEan) {
        this.shippingFeesEan = shippingFeesEan;
        return this;
    }

    public OrderPlacedBuilder withShippingName(String shippingName) {
        this.shippingName = shippingName;
        return this;
    }

    public OrderPlacedBuilder withOrderLines(List<OrderLine> orderLines) {
        this.orderLines = orderLines;
        return this;
    }

    private CustomerInformation getCustomerInformation(String countryCode) {
        return new CustomerInformation().withBillingAddress(new Address(
                "addressLine1",
                "addressLine2",
                "addressLine3",
                "state",
                "city",
                countryCode,
                "firstName",
                "houseNumber",
                "houseNumberExtended",
                "lastName",
                "phoneNumber",
                "salutation",
                "zipcode"));
    }

    private List<OrderLine> getOrderLines() {
        List<OrderLine> orderLines = new ArrayList<>();
        orderLines.add(new OrderLine()
                .withQuantity(1)
                .withLineNumber(1)
                .withRetailPrice(new BigDecimal(6.5))
                .withDiscountedTotalPrice(new BigDecimal(6.5))
                .withVat(new BigDecimal(.5))
        );
        return orderLines;
    }

    private Payment getKlarnaPayment(String method, String submethod, BigDecimal amount, String state) {
        Payment klarnaPayment = new Payment();
        klarnaPayment.setMethod(method);
        klarnaPayment.setSubMethod(submethod);
        klarnaPayment.setProvider("KLARNA_PAYMENTS");
        klarnaPayment.setPspReference("klarna-reference-number");
        klarnaPayment.setAmount(amount);
        klarnaPayment.setState(state);
        return klarnaPayment;
    }

    private Payment getAdyenPayment(String method, String submethod, BigDecimal amount, String state) {
        Payment adyenPayment = new Payment();
        adyenPayment.setMethod(method);
        adyenPayment.setSubMethod(submethod);
        adyenPayment.setProvider("ADYEN");
        adyenPayment.setPspReference("adyen-reference-number");
        adyenPayment.setAmount(amount);
        adyenPayment.setState(state);
        return adyenPayment;
    }

    private Payment getGiftCard(BigDecimal amount) {
        Payment gcPayment = new Payment();
        gcPayment.setMethod("OC_GIFTCARD");
        gcPayment.setProvider("OPTICARD");
        gcPayment.setPspReference(ORDER_ID);
        gcPayment.setAdditionalReference("gift-card-number-xxxx");
        gcPayment.setAmount(amount);
        gcPayment.setState("AUTH");
        return gcPayment;
    }
}
