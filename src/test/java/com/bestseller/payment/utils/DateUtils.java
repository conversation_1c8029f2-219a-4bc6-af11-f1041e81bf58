package com.bestseller.payment.utils;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

public class DateUtils {
    /**
     * converts {@link LocalDate} to {@link java.util.Date} to have .If input is null will return null.
     *
     * @param localDate LocalDate
     * @return Date
     */
    public static Date toDate(@NotNull LocalDate localDate) {
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }
}
