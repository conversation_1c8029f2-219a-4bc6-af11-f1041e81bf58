package com.bestseller.payment.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.Task;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.postPurchaseEventReceived.PostPurchaseEventReceived;
import com.bestseller.interfacecontractannotator.model.postpurchaseeventreceived.ReturnCreatedPayload;
import com.bestseller.payment.core.queue.PostPurchaseEventReceivedQueueConsumer;
import com.bestseller.payment.core.service.customerchoice.CustomerRefundChoiceService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PostPurchaseEventReceivedQueueConsumerTest {

    @Mock
    private CustomerRefundChoiceService customerRefundChoiceService;

    @Mock
    private IdempotencyChecker<PostPurchaseEventReceived> idempotencyChecker;

    @Mock
    private MessageValidator<PostPurchaseEventReceived> messageValidator;

    @Mock
    private QueueProducer<PostPurchaseEventReceived> queueProducer;

    @InjectMocks
    private PostPurchaseEventReceivedQueueConsumer consumer;

    @Test
    void process_whenValidMessage_customerChoiceServiceIsInvoked() {
        // Arrange
        PostPurchaseEventReceived message = createValidMessage();
        when(idempotencyChecker.isDuplicate(message)).thenReturn(false);
        when(messageValidator.isValid(message)).thenReturn(true);

        var task = mock(Task.class);
        when(task.getPayloadOrThrow()).thenReturn(message);

        // Act
        consumer.execute(task);

        // Assert
        verify(customerRefundChoiceService, times(1)).processPostPurchaseEvent(message);
    }

    @Test
    void process_whenDuplicateMessage_customerChoiceServiceIsNotInvoked() {
        // Arrange
        PostPurchaseEventReceived message = createValidMessage();
        when(idempotencyChecker.isDuplicate(message)).thenReturn(true);

        var task = mock(Task.class);
        when(task.getPayloadOrThrow()).thenReturn(message);

        // Act
        consumer.execute(task);

        // Assert
        verify(customerRefundChoiceService, times(0)).processPostPurchaseEvent(any());
    }

    @Test
    void process_whenInvalidMessage_customerChoiceServiceIsNotInvoked() {
        // Arrange
        PostPurchaseEventReceived message = createValidMessage();
        when(idempotencyChecker.isDuplicate(message)).thenReturn(false);
        when(messageValidator.isValid(message)).thenReturn(false);

        var task = mock(Task.class);
        when(task.getPayloadOrThrow()).thenReturn(message);

        // Act
        consumer.execute(task);

        // Assert
        verify(customerRefundChoiceService, times(0)).processPostPurchaseEvent(any());
    }

    @Test
    void process_whenExceptionOccurs_throwsException() {
        // Arrange
        PostPurchaseEventReceived message = createValidMessage();
        doThrow(new RuntimeException("Test exception"))
            .when(customerRefundChoiceService).processPostPurchaseEvent(message);

        // Act & Assert
        org.junit.jupiter.api.Assertions.assertThrows(RuntimeException.class, () -> {
            consumer.process(message);
        });
    }

    private PostPurchaseEventReceived createValidMessage() {
        ReturnCreatedPayload payload = new ReturnCreatedPayload();
        payload.setReturnId("RETURN-123");

        PostPurchaseEventReceived message = new PostPurchaseEventReceived();
        message.setOrderId("ORDER-123");
        message.setData(payload);
        payload.setRefundMethod("giftCard");

        return message;
    }
} 