package com.bestseller.payment.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.Task;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.OrderDetails;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.payment.core.queue.ValidOrderPlacedQueueConsumer;
import com.bestseller.payment.core.service.order.OrderService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ValidOrderPlacedQueueConsumerTest {

    @InjectMocks
    private ValidOrderPlacedQueueConsumer consumer;

    @Mock
    private IdempotencyChecker<ValidOrderPlaced> idempotencyChecker;

    @Mock
    private QueueConfig queueConfig;

    @Mock
    private TaskPayloadTransformer<ValidOrderPlaced> taskPayloadTransformer;

    @Mock
    private OrderService orderService;

    @Mock
    private MessageValidator<ValidOrderPlaced> messageValidator;

    @Test
    void execute_givenTask_orderServiceIsInvoked() {
        // arrange
        var validOrderPlaced = new ValidOrderPlaced();
        validOrderPlaced.withOrderDetails(new OrderDetails().withBrandedShipping("ON"))
            .withOrderId("orderId")
            .withStore("DEMANDWARE");
        var task = mock(Task.class);
        when(task.getPayloadOrThrow()).thenReturn(validOrderPlaced);
        when(messageValidator.isValid(validOrderPlaced))
            .thenReturn(true);

        // act
        consumer.execute(task);

        // assert
        verify(orderService).processValidOrderPlaced(validOrderPlaced);
    }

    @Test
    void execute_givenTaskWithTBBrand_orderServiceIsInvoked() {
        // arrange
        var validOrderPlaced = new ValidOrderPlaced();
        validOrderPlaced.withOrderDetails(new OrderDetails().withBrandedShipping("TB"))
            .withOrderId("orderId");
        var task = mock(Task.class);
        when(task.getPayloadOrThrow()).thenReturn(validOrderPlaced);
        when(messageValidator.isValid(validOrderPlaced))
            .thenReturn(true);

        // act
        consumer.execute(task);

        // assert
        verify(orderService).processValidOrderPlaced(validOrderPlaced);
    }

    @Test
    void getQueueConfig_returnsQueueConfig() {
        // act
        var actual = consumer.getQueueConfig();

        // assert
        assertThat(actual).isEqualTo(queueConfig);
    }

    @Test
    void getTaskPayloadTransformer_returnsTaskPayloadTransformer() {
        // act
        var actual = consumer.getPayloadTransformer();

        // assert
        assertThat(actual).isEqualTo(taskPayloadTransformer);
    }

}
