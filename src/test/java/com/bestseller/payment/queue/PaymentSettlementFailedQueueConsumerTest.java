package com.bestseller.payment.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.Task;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementFailed.PaymentSettlementFailed;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.queue.PaymentSettlementFailedQueueConsumer;
import com.bestseller.payment.core.service.payment.PaymentService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PaymentSettlementFailedQueueConsumerTest {

    @InjectMocks
    private PaymentSettlementFailedQueueConsumer consumer;

    @Mock
    private IdempotencyChecker<PaymentSettlementFailed> idempotencyChecker;

    @Mock
    private QueueConfig queueConfig;

    @Mock
    private TaskPayloadTransformer<PaymentSettlementFailed> taskPayloadTransformer;

    @Mock
    private PaymentService paymentService;

    @Mock
    private MessageValidator<PaymentSettlementFailed> messageValidator;

    @Test
    void execute_givenTask_paymentServiceIsInvoked() {
        // arrange
        var paymentSettlementFailed = new PaymentSettlementFailed()
            .withCorrelationId("correlationId");
        var task = mock(Task.class);
        when(task.getPayloadOrThrow()).thenReturn(paymentSettlementFailed);
        when(messageValidator.isValid(paymentSettlementFailed)).thenReturn(true);

        // act
        consumer.execute(task);

        // assert
        verify(paymentService)
            .updatePaymentStatus(paymentSettlementFailed.getCorrelationId(), PaymentState.SETTLEMENT_DENIED);
    }

    @Test
    void getQueueConfig_returnsQueueConfig() {
        // act
        var actual = consumer.getQueueConfig();

        // assert
        assertThat(actual).isEqualTo(queueConfig);
    }

    @Test
    void getTaskPayloadTransformer_returnsTaskPayloadTransformer() {
        // act
        var actual = consumer.getPayloadTransformer();

        // assert
        assertThat(actual).isEqualTo(taskPayloadTransformer);
    }

}
