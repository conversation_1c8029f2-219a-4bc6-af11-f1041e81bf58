package com.bestseller.payment.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.Task;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderFinalized;
import com.bestseller.payment.core.queue.OrderFinalizedQueueConsumer;
import com.bestseller.payment.core.service.order.OrderService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderFinalizedQueueConsumerTest {

    @InjectMocks
    private OrderFinalizedQueueConsumer consumer;

    @Mock
    private IdempotencyChecker<OrderFinalized> idempotencyChecker;

    @Mock
    private QueueConfig queueConfig;

    @Mock
    private TaskPayloadTransformer<OrderFinalized> taskPayloadTransformer;

    @Mock
    private OrderService orderService;

    @Mock
    private MessageValidator<OrderFinalized> messageValidator;

    @Test
    void execute_givenTask_orderServiceIsInvoked() {
        // arrange
        var orderFinalized = new OrderFinalized();
        var task = mock(Task.class);
        when(task.getPayloadOrThrow()).thenReturn(orderFinalized);
        when(messageValidator.isValid(orderFinalized)).thenReturn(true);

        // act
        consumer.execute(task);

        // assert
        verify(orderService).processOrderFinalizedMessage(orderFinalized);
    }

    @Test
    void getQueueConfig_returnsQueueConfig() {
        // act
        var actual = consumer.getQueueConfig();

        // assert
        assertThat(actual).isEqualTo(queueConfig);
    }

    @Test
    void getTaskPayloadTransformer_returnsTaskPayloadTransformer() {
        // act
        var actual = consumer.getPayloadTransformer();

        // assert
        assertThat(actual).isEqualTo(taskPayloadTransformer);
    }

}
