package com.bestseller.payment.queue;

import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.Task;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inStoreReturnSettlement.InStoreReturnSettlement;
import com.bestseller.payment.adapter.stream.validator.idempotency.InStoreReturnSettlementIdempotencyCheck;
import com.bestseller.payment.core.queue.InStoreReturnSettlementQueueConsumer;
import com.bestseller.payment.core.service.refund.InStoreRefundService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class InStoreReturnSettlementQueueConsumerTest {

    @InjectMocks
    InStoreReturnSettlementQueueConsumer inStoreReturnSettlementQueueConsumer;

    @Mock
    QueueConfig queueConfig;

    @Mock
    TaskPayloadTransformer<InStoreReturnSettlement> taskPayloadTransformer;

    @Mock
    InStoreRefundService inStoreRefundService;

    @Mock
    InStoreReturnSettlementIdempotencyCheck idempotencyCheck;

    @Mock
    MessageValidator<InStoreReturnSettlement> messageValidator;

    @Test
    void testExecute() {
        // Arrange
        var inStoreReturnSettlement = new InStoreReturnSettlement();
        var task = mock(Task.class);

        when(task.getPayloadOrThrow()).thenReturn(inStoreReturnSettlement);
        when(messageValidator.isValid(inStoreReturnSettlement)).thenReturn(true);

        // Act
        inStoreReturnSettlementQueueConsumer.execute(task);

        // Assert
        verify(inStoreRefundService).issueRefundForInStoreReturn(inStoreReturnSettlement);
    }

    @Test
    void testGetQueueConfig() {
        // Act
        var actual = inStoreReturnSettlementQueueConsumer.getQueueConfig();

        // Assert
        assertEquals(queueConfig, actual);
    }

    @Test
    void testGetPayloadTransformer() {
        // Act
        var actual = inStoreReturnSettlementQueueConsumer.getPayloadTransformer();

        // Assert
        assertEquals(taskPayloadTransformer, actual);
    }
}
