package com.bestseller.payment.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.Task;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.payment.core.queue.AbstractValidatorQueueConsumer;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@Slf4j
public class AbstractValidatorQueueConsumerTest {

    AbstractValidatorQueueConsumer<String> abstractValidatorQueueConsumer;

    @Mock
    QueueConfig queueConfig;

    @Mock
    TaskPayloadTransformer<String> taskPayloadTransformer;

    @Mock
    IdempotencyChecker<String> idempotencyChecker;

    @Mock
    MessageValidator messageValidator;

    @BeforeEach
    void setUp() {
        abstractValidatorQueueConsumer = Mockito.spy(new AbstractValidatorQueueConsumer<String>(queueConfig, taskPayloadTransformer, idempotencyChecker, messageValidator) {

            @Override
            protected String getMessageDetails(String s) {
                return "MessageDetails";
            }

            @Override
            public void process(String message) {
                // some implementation
            }
        });
    }

    @Test
    void consume_givenValidMessage_shouldProcessMessage() {
        // Arrange
        String message = "message";
        when(messageValidator.isValid(message)).thenReturn(true);
        var task = Mockito.mock(Task.class);
        when(task.getPayloadOrThrow()).thenReturn(message);

        // Act
        abstractValidatorQueueConsumer.execute(task);

        // Assert
        Mockito.verify(abstractValidatorQueueConsumer, Mockito.times(1)).process(message);
    }

    @Test
    void consume_givenInvalidMessage_shouldNotProcessMessage() {
        // Arrange
        String message = "message";
        when(messageValidator.isValid(message)).thenReturn(false);
        var task = Mockito.mock(Task.class);
        when(task.getPayloadOrThrow()).thenReturn(message);

        // Act
        abstractValidatorQueueConsumer.execute(task);

        // Assert
        Mockito.verify(abstractValidatorQueueConsumer, Mockito.never()).process(message);
    }
}
