package com.bestseller.payment.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.core.api.Task;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.giftCardRefundResponse.GiftCardRefundResponse;
import com.bestseller.payment.core.queue.GiftCardRefundResponseQueueConsumer;
import com.bestseller.payment.core.service.refund.RefundService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class GiftCardRefundResponseQueueConsumerTest {

    @InjectMocks
    GiftCardRefundResponseQueueConsumer giftCardRefundResponseQueueConsumer;

    @Mock
    IdempotencyChecker<GiftCardRefundResponse> idempotencyChecker;

    @Mock
    RefundService refundService;

    @Mock
    QueueConfig queueConfig;

    @Mock
    TaskPayloadTransformer<GiftCardRefundResponse> taskPayloadTransformer;

    GiftCardRefundResponse message;

    @BeforeEach
    void setUp() {
        message = new GiftCardRefundResponse();
    }

    @Test
    void test_execute() {
        // Arrange
        var task = mock(Task.class);

        when(task.getPayloadOrThrow()).thenReturn(message);
        when(idempotencyChecker.isDuplicate(message)).thenReturn(false);

        // Act
        giftCardRefundResponseQueueConsumer.execute(task);

        // Assert
        verify(refundService).processGiftCardRefundResponse(message);
    }

    @Test
    void test_getQueueConfig() {
        // Act
        var actual = giftCardRefundResponseQueueConsumer.getQueueConfig();

        // Assert
        assertEquals(queueConfig, actual);
    }

    @Test
    void test_getPayloadTransformer() {
        // Act
        var actual = giftCardRefundResponseQueueConsumer.getPayloadTransformer();

        // Assert
        assertEquals(taskPayloadTransformer, actual);
    }
}
