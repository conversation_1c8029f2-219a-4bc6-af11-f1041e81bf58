package com.bestseller.payment.queue;

import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.Task;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.RefundCreationRequested;
import com.bestseller.payment.adapter.stream.validator.idempotency.RefundCreationRequestedIdempotencyCheck;
import com.bestseller.payment.core.converter.OrderItemToRefundMapper;
import com.bestseller.payment.core.queue.RefundCreationRequestedQueueConsumer;
import com.bestseller.payment.core.service.refund.EcomRefundService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class RefundCreationRequestedQueueConsumerTest {

    @InjectMocks
    RefundCreationRequestedQueueConsumer refundCreationRequestedQueueConsumer;

    @Mock
    QueueConfig queueConfig;

    @Mock
    TaskPayloadTransformer<RefundCreationRequested> taskPayloadTransformer;

    @Mock
    EcomRefundService refundService;

    @Mock
    OrderItemToRefundMapper orderItemToRefundMapper;

    @Mock
    RefundCreationRequestedIdempotencyCheck idempotencyChecker;

    @Mock
    MessageValidator<RefundCreationRequested> messageValidator;

    @Test
    void testExecute() {
        // Arrange
        UUID uuid = UUID.randomUUID();
        var refundCreationRequested = new RefundCreationRequested();
        refundCreationRequested.setOrderId("orderId");
        refundCreationRequested.setChargeReturnFee(true);
        refundCreationRequested.setRefundShippingFee(true);
        refundCreationRequested.setRefundCreationRequestedId(uuid);
        var task = mock(Task.class);

        when(task.getPayloadOrThrow()).thenReturn(refundCreationRequested);
        when(messageValidator.isValid(refundCreationRequested)).thenReturn(true);

        // Act
        refundCreationRequestedQueueConsumer.execute(task);

        // Assert
        verify(refundService).refund(eq("orderId"), anyList(), any(), eq(uuid.toString()));
    }

    @Test
    void testGetQueueConfig() {
        // Act
        var actual = refundCreationRequestedQueueConsumer.getQueueConfig();

        // Assert
        assertEquals(queueConfig, actual);
    }

    @Test
    void testGetPayloadTransformer() {
        // Act
        var actual = refundCreationRequestedQueueConsumer.getPayloadTransformer();

        // Assert
        assertEquals(taskPayloadTransformer, actual);
    }
}
