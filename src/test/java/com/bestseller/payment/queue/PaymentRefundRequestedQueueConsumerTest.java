package com.bestseller.payment.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.Task;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequested.PaymentRefundRequested;
import com.bestseller.payment.core.queue.PaymentRefundRequestedQueueConsumer;
import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PaymentRefundRequestedQueueConsumerTest {

    @InjectMocks
    private PaymentRefundRequestedQueueConsumer consumer;

    @Mock
    private IdempotencyChecker<PaymentRefundRequested> idempotencyChecker;

    @Mock
    private QueueConfig queueConfig;

    @Mock
    private TaskPayloadTransformer<PaymentRefundRequested> taskPayloadTransformer;

    @Mock
    private RefundService refundService;

    @Mock
    private MessageValidator<PaymentRefundRequested> messageValidator;

    @Test
    void execute_givenTask_refundServiceIsInvoked() {
        // arrange
        var paymentRefundRequested = new PaymentRefundRequested()
            .withCorrelationId("12345");
        var task = mock(Task.class);
        when(task.getPayloadOrThrow()).thenReturn(paymentRefundRequested);

        when(messageValidator.isValid(any())).thenReturn(true);

        // act
        consumer.execute(task);

        // assert
        verify(refundService).updateRefundStatus(
            Integer.parseInt(paymentRefundRequested.getCorrelationId()), RefundState.REFUND_REQUESTED);
    }

    @Test
    void getQueueConfig_returnsQueueConfig() {
        // act
        var actual = consumer.getQueueConfig();

        // assert
        assertThat(actual).isEqualTo(queueConfig);
    }

    @Test
    void getTaskPayloadTransformer_returnsTaskPayloadTransformer() {
        // act
        var actual = consumer.getPayloadTransformer();

        // assert
        assertThat(actual).isEqualTo(taskPayloadTransformer);
    }

}
