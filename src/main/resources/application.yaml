info:
  app:
    name: Payment Management Service
    version: ${SPRING_PROFILES_ACTIVE:docker}
gateway:
  services:
    fulfilment-core-service:
      url: ${FCS_API_URL:http://localhost:8080}
      auth: true
      username: ${FCS_API_USERNAME:admin}
      password: ${FCS_API_PASSWORD:admin}
fcs:
  max-retry-attempts: 5
  retry-backoff: 3S


management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,bindings
  datadog:
    metrics:
      export:
        api-key: ${DD_API_KEY}
        application-key: ${DD_APPLICATION_KEY}
        enabled: ${DD_ENABLED:false}
  metrics:
    tags:
      service: pms
      env: ${spring.profiles.active}

spring:
  main:
    banner-mode: OFF
  jackson:
    deserialization:
      fail-on-missing-external-type-id-property: false
  profiles:
    active: ${APPLICATION_ENV:docker}
  # Database
  datasource:
    url: ${PMS_DB_URL:**********************************}
    username: ${PMS_DB_USERNAME:pms}
    password: ${PMS_DB_PASSWORD:pms}
    driverClassName: com.mysql.cj.jdbc.Driver

  # Flyway configuration
  flyway:
    user: ${PMS_FLYWAY_USERNAME:pms}
    password: ${PMS_FLYWAY_PASSWORD:pms}

  # Spring JPA configuration
  jpa:
    hibernate:
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    open-in-view: true
  # Kafka
  cloud:
    function:
      definition: >
        paymentAuthorizedConsumer;
        paymentRejectedConsumer;
        validOrderPlacedConsumer;
        orderFinalizedConsumer;
        paymentSettlementSucceededConsumer;
        paymentSettlementFailedConsumer;
        refundCreationRequestedConsumer;
        inStoreReturnSettlementConsumer;
        paymentRefundRequestedConsumer;
        paymentRefundFailedConsumer;
        paymentRefundSucceededConsumer;
        giftCardRefundResponseConsumer;
        postPurchaseEventReceivedConsumer
    stream:
      kafka:
        binder:
          brokers: ${PMS_KAFKA_BROKER_LIST:localhost:9092}
          configuration:
            security:
              protocol: PLAINTEXT
        bindings:
          default:
            consumer:
              ack-mode: record
        default:
          content-type: application/json
          consumer:
            maxAttempts: 3
            start-offset: latest
      bindings:
        #Consumer Kafka Topics
        paymentAuthorizedConsumer-in-0:
          destination: PaymentAuthorized
        paymentRejectedConsumer-in-0:
          destination: PaymentRejected
        validOrderPlacedConsumer-in-0:
          destination: ValidOrderPlaced
        orderFinalizedConsumer-in-0:
          destination: OrderFinalized
        paymentSettlementSucceededConsumer-in-0:
          destination: PaymentSettlementSucceeded
        paymentSettlementFailedConsumer-in-0:
          destination: PaymentSettlementFailed
        refundCreationRequestedConsumer-in-0:
          destination: RefundCreationRequested
        paymentRefundRequestedConsumer-in-0:
          destination: PaymentRefundRequested
        paymentRefundFailedConsumer-in-0:
          destination: PaymentRefundFailed
        paymentRefundSucceededConsumer-in-0:
          destination: PaymentRefundSucceeded
        inStoreReturnSettlementConsumer-in-0:
          destination: InStoreReturnSettlement
        giftCardRefundResponseConsumer-in-0:
          destination: GiftCardRefundResponse
        postPurchaseEventReceivedConsumer-in-0:
          destination: PostPurchaseEventReceived
        #Producer Kafka Topics
        paymentStatusUpdatedProducer-out-0:
          destination: PaymentStatusUpdated
        paymentSettlementRequestProducer-out-0:
          destination: PaymentSettlementRequest
        paymentRefundRequestProducer-out-0:
          destination: PaymentRefundRequest
        refundStatusUpdatedProducer-out-0:
          destination: RefundStatusUpdated
          producer:
            partition-key-expression: payload.refundId
            partition-count: 2
        refundCompletedProducer-out-0:
          destination: RefundCompleted
        default:
          content-type: application/json
      default:
        group: paymentManagementService
        producer:
          header-mode: none

aws:
  s3:
    region: eu-west-1
    bucket:
      name: ${S3_BUCKET_NAME:pms-bucket}
    local:
      endpoint: http://localhost:4566

vat:
  sequence:
    number:
      length: 9

refund:
  in-store:
    infix: -REF-

springdoc:
  swagger-ui:
    path: /

server:
  tomcat:
    mbeanregistry:
      enabled: true

database-queue:
  refund-creation-requested-queue-properties:
    queue-location:
      queue-id: refund-creation-requested-queue
  in-store-return-settlement-queue-properties:
    queue-location:
      queue-id: in-store-return-settlement-queue
  payment-refund-request-producer-queue-properties:
    queue-location:
      queue-id: payment-refund-request-producer-queue
  payment-settlement-request-producer-queue-properties:
    queue-location:
      queue-id: payment-settlement-request-producer-queue
  payment-status-updated-producer-queue-properties:
    queue-location:
      queue-id: payment-status-updated-producer-queue
  refund-completed-producer-queue-properties:
    queue-location:
      queue-id: refund-completed-producer-queue
  refund-status-updated-producer-queue-properties:
    queue-location:
      queue-id: refund-status-updated-producer-queue
  order-finalized-queue-properties:
    queue-location:
      queue-id: order-finalized-queue
  payment-authorized-queue-properties:
    queue-location:
      queue-id: payment-authorized-queue
  payment-refund-failed-queue-properties:
    queue-location:
      queue-id: payment-refund-failed-queue
  payment-refund-requested-queue-properties:
    queue-location:
      queue-id: payment-refund-requested-queue
  payment-refund-succeeded-queue-properties:
    queue-location:
      queue-id: payment-refund-succeeded-queue
  payment-rejected-queue-properties:
    queue-location:
      queue-id: payment-rejected-queue
  payment-settlement-failed-queue-properties:
    queue-location:
      queue-id: payment-settlement-failed-queue
  payment-settlement-succeeded-queue-properties:
    queue-location:
      queue-id: payment-settlement-succeeded-queue
  valid-order-placed-queue-properties:
    queue-location:
      queue-id: valid-order-placed-queue
  gift-card-refund-response-queue-properties:
    queue-location:
      queue-id: gift-card-refund-response-queue
  post-purchase-event-received-queue-properties:
    queue-location:
      queue-id: post-purchase-event-received-queue

