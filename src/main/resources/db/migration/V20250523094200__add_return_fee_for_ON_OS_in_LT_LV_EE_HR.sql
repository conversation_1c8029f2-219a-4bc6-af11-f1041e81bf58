INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'ON', id, CONVERT_TZ('2025-05-21 00:00:00', 'CET', 'UTC'), 2.95
FROM CountryToReturnFeePricing
WHERE country = 'LT';

INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'ON', id, CONVERT_TZ('2025-05-21 00:00:00', 'CET', 'UTC'), 2.95
FROM CountryToReturnFeePricing
WHERE country = 'LV';

INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'ON', id, CONVERT_TZ('2025-05-21 00:00:00', 'CET', 'UTC'), 2.95
FROM CountryToReturnFeePricing
WHERE country = 'EE';

INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'ON', id, CONVERT_TZ('2025-05-21 00:00:00', 'CET', 'UTC'), 2.95
FROM CountryToReturnFeePricing
WHERE country = 'HR';

INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'OS', id, CONVERT_TZ('2025-05-21 00:00:00', 'CET', 'UTC'), 2.95
FROM CountryToReturnFeePricing
WHERE country = 'LT';

INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'OS', id, CONVERT_TZ('2025-05-21 00:00:00', 'CET', 'UTC'), 2.95
FROM CountryToReturnFeePricing
WHERE country = 'LV';

INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'OS', id, CONVERT_TZ('2025-05-21 00:00:00', 'CET', 'UTC'), 2.95
FROM CountryToReturnFeePricing
WHERE country = 'EE';

INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'OS', id, CONVERT_TZ('2025-05-21 00:00:00', 'CET', 'UTC'), 2.95
FROM CountryToReturnFeePricing
WHERE country = 'HR';
