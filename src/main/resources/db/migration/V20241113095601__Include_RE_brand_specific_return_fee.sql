insert into BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
select 'RE' as brand,
       BrandSpecificReturnFee.countryToReturnFeePricingId,
       BrandSpecificReturnFee.effectiveDate,
       BrandSpecificReturnFee.returnFee
from BrandSpecificReturnFee
         join CountryToReturnFeePricing
              on BrandSpecificReturnFee.countryToReturnFeePricingId = CountryToReturnFeePricing.id
where BrandSpecificReturnFee.brand = 'VL';