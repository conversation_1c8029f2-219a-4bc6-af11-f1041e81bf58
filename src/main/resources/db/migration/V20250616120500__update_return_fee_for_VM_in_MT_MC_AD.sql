INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'VM', id, CONVERT_TZ('2025-06-18 08:00:00', 'CET', 'UTC'), 9.99
FROM CountryToReturnFeePricing
WHERE country = 'AD';

INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'VM', id, CONVERT_TZ('2025-06-18 08:00:00', 'CET', 'UTC'), 7.99
FROM CountryToReturnFeePricing
WHERE country = 'MT';

INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'VM', id, CONVERT_TZ('2025-06-18 08:00:00', 'CET', 'UTC'), 3.99
FROM CountryToReturnFeePricing
WHERE country = 'MC';
