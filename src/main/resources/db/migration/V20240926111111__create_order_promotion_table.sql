CREATE TABLE Promotion (
            `id` int NOT NULL AUTO_INCREMENT,
            `orderId` varchar(50),
            `campaignId` varchar(255),
            `promotionId` varchar(255),
            `grossPrice` decimal(10,2),
            `netPrice` decimal(10,2),
            `createdTS` datetime,
            `lastModifiedTS` datetime,
            `version` int,
            constraint PK_Promotion primary key (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;


ALTER TABLE Promotion add constraint FK_promotion_orders
    FOREIGN KEY (orderId) REFERENCES Orders (orderId);