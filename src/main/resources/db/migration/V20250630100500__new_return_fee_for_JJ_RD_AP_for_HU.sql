INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'RD', id, CONVERT_TZ('2025-07-03 08:00:00', 'CET', 'UTC'), 1200
FROM CountryToReturnFeePricing
WHERE country = 'HU';

INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'AP', id, CONVERT_TZ('2025-07-03 08:00:00', 'CET', 'UTC'), 1200
FROM CountryToReturnFeePricing
WHERE country = 'HU';


INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'JJ', id, CONVERT_TZ('2025-07-03 08:00:00', 'CET', 'UTC'), 1200
FROM CountryToReturnFeePricing
WHERE country = 'HU';
