INSERT INTO BrandSpecificReturnFee (brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT
    'ON',
    c.id,
    '2025-08-31',
    2.95
FROM CountryToReturnFeePricing c
WHERE c.country = 'CY'
UNION ALL
SELECT
    'OS',
    c.id,
    '2025-08-31',
    2.95
FROM CountryToReturnFeePricing c
WHERE c.country = 'CY'
UNION ALL
SELECT
    'ON',
    c.id,
    '2025-08-31',
    2.95
FROM CountryToReturnFeePricing c
WHERE c.country = 'SK'
UNION ALL
SELECT
    'OS',
    c.id,
    '2025-08-31',
    2.95
FROM CountryToReturnFeePricing c
WHERE c.country = 'SK'
UNION ALL
SELECT
    'ON',
    c.id,
    '2025-08-31',
    2.95
FROM CountryToReturnFeePricing c
WHERE c.country = 'SI'
UNION ALL
SELECT
    'OS',
    c.id,
    '2025-08-31',
    2.95
FROM CountryToReturnFeePricing c
WHERE c.country = 'SI'
UNION ALL
SELECT
    'ON',
    c.id,
    '2025-08-31',
    1177
FROM CountryToReturnFeePricing c
WHERE c.country = 'HU' AND returnFeeAmount = 1500
UNION ALL
SELECT
    'OS',
    c.id,
    '2025-08-31',
    1177
FROM CountryToReturnFeePricing c
WHERE c.country = 'HU' AND returnFeeAmount = 1500
UNION ALL
SELECT
    'ON',
    c.id,
    '2025-08-31',
    2.95
FROM CountryToReturnFeePricing c
WHERE c.country = 'BG'
UNION ALL
SELECT
    'OS',
    c.id,
    '2025-08-31',
    2.95
FROM CountryToReturnFeePricing c
WHERE c.country = 'BG'
UNION ALL
SELECT
    'ON',
    c.id,
    '2025-08-31',
    15
FROM CountryToReturnFeePricing c
WHERE c.country = 'RO'
UNION ALL
SELECT
    'OS',
    c.id,
    '2025-08-31',
    15
FROM CountryToReturnFeePricing c
WHERE c.country = 'RO';