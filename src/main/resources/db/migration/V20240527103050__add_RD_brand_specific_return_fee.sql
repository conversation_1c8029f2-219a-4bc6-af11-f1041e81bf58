insert into brandSpecificReturnFees(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
select
    'RD',
    brandSpecificReturnFees.countryToReturnFeePricingId,
    brandSpecificReturnFees.effectiveDate,
    brandSpecificReturnFees.returnFee
from brandSpecificReturnFees join countryToReturnFeePricings
    on brandSpecificReturnFees.countryToReturnFeePricingId = countryToReturnFeePricings.id
where brandSpecificReturnFees.brand = 'JJ';