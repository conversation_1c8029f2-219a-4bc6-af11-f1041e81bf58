INSERT INTO BrandSpecificReturnFee (brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT brands.brand,
       ctrfp2.id,
       CONVERT_TZ('2025-07-18 10:00:00', 'Europe/Berlin', 'UTC'),
       2.99
FROM CountryToReturnFeePricing ctrfp2
         INNER JOIN (SELECT country,
                            MAX(ctrfp.effectiveDate) as date
                     FROM CountryToReturnFeePricing ctrfp
                     WHERE ctrfp.country IN (
                                             'AL',
                                             'AD',
                                             'AT',
                                             'BE',
                                             'BA',
                                             'BG',
                                             'HR',
                                             'CY',
                                             'EE',
                                             'FI',
                                             'FR',
                                             'GR',
                                             'IE',
                                             'IT',
                                             'JO',
                                             'LV',
                                             'LT',
                                             'LU',
                                             'MT',
                                             'MC',
                                             'MA',
                                             'NL',
                                             'PT',
                                             'RO',
                                             'RS',
                                             'SK',
                                             'SI',
                                             'ES',
                                             'DZ',
                                             'AM',
                                             'ME',
                                             'MK',
                                             'TN'
                         )
                     GROUP BY country) innerTable
                    ON innerTable.`date` = ctrfp2.effectiveDate AND innerTable.country = ctrfp2.country
         INNER JOIN (SELECT 'AP' as brand
                     UNION
                     SELECT 'JJ' as brand
                     UNION
                     SELECT 'RD' as brand) brands ON 1 = 1
WHERE ctrfp2.returnFeeAmount = 1.99