DELIMITER //

CREATE PROCEDURE update_sequence_and_get_last_id(IN countryCode VARCHAR(255), IN sequenceYear INT,
                                                 IN vatType VARCHAR(255), OUT nextSequence INT)
BEGIN


    DECLARE exit handler for SQLEXCEPTION
        BEGIN
            ROLLBACK;
            RESIGNAL;
        END;

    START TRANSACTION;

    INSERT INTO VatSequence (vatType, countryCode, sequenceYear, currentSequence, createdTS, lastModifiedTS, version)
    VALUES (vatType, countryCode, sequenceYear, 0, NOW(), NOW(), 1)
    ON DUPLICATE KEY UPDATE currentSequence = currentSequence, lastModifiedTS = NOW(), version = version + 1;

    SELECT currentSequence,
           lastModifiedTS
    FROM VatSequence sequence
    WHERE sequence.sequenceYear = sequenceYear
      AND sequence.countryCode = countryCode
      AND sequence.vatType = vatType
        FOR
    UPDATE;

    UPDATE VatSequence sequence
    SET currentSequence = LAST_INSERT_ID(currentSequence + 1),
        lastModifiedTS  = NOW()
    WHERE sequence.sequenceYear = sequenceYear
      AND sequence.countryCode = countryCode
      AND sequence.vatType = vatType;
    COMMIT;

    SELECT LAST_INSERT_ID() into nextSequence;
END //

DELIMITER ;
