CREATE PROCEDURE create_index_if_not_exists(
    IN p_table_name VA<PERSON>HAR(64),
    IN p_index_name VARCHAR(64),
    IN p_column_names VARCHAR(255)
)
BEGIN
    DECLARE index_exists INT;

SELECT COUNT(1)
INTO index_exists
FROM information_schema.STATISTICS
WHERE table_schema = DATABASE()
  AND table_name = p_table_name
  AND index_name = p_index_name;

IF index_exists = 0 THEN
        SET @create_index_query = CONCAT(
                'CREATE INDEX ', p_index_name, ' ON ', p_table_name, ' (', p_column_names, ')'
                                  );
PREPARE stmt FROM @create_index_query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
END IF;
END;