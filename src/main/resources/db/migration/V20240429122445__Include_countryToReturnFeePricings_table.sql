CREATE TABLE `countryToReturnFeePricings`
(
    `id`               INT(11)        NOT NULL AUTO_INCREMENT,
    `country`          varchar(255)   NOT NULL,
    `effectiveDate`    datetime       NOT NULL,
    `returnFeeAmount`  decimal(19, 2) NOT NULL,
    `returnFeeTaxRate` decimal(10, 4) NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `country_effectiveDate` (`country`, `effectiveDate`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  COLLATE = utf8_bin;