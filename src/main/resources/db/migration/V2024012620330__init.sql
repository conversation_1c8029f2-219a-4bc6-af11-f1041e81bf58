CREATE TABLE IF NOT EXISTS `overallTotals` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `createdTS` DATETIME DEFAULT NULL,
    `lastModifiedTS` DATETIME DEFAULT NULL,
    `version` INT(11) DEFAULT NULL,
    `grossDiscountedTotal` DECIMAL(10,2) DEFAULT NULL,
    `grossSubTotal` DECIMAL(10,2) DEFAULT NULL,
    `originalGrossDiscountedTotal` DECIMAL(10,2) DEFAULT NULL,
    `vat` DECIMAL(10,2) DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

CREATE TABLE IF NOT EXISTS `orders` (
    `orderId` VARCHAR(50) COLLATE UTF8_BIN NOT NULL,
    `paymentStatus` INT(11) DEFAULT NULL,
    `prevPaymentStatus` INT(11) DEFAULT NULL,
    `orderDate` DATETIME NOT NULL,
    `createdTS` DATETIME DEFAULT NULL,
    `lastModifiedTS` DATETIME DEFAULT NULL,
    `version` INT(11) DEFAULT NULL,
    `totalPaidPriceId` INT(11) DEFAULT NULL,
    `totalCancelledPriceId` INT(11) DEFAULT NULL,
    PRIMARY KEY (`orderId`),
    KEY `idx_total_paid_price` (`totalPaidPriceId`),
    KEY `idx_total_cancelled_price` (`totalCancelledPriceId`),
    KEY `idx_prev_payment_status` (`prevPaymentStatus`),
    KEY `idx_payment_status` (`paymentStatus`),
    KEY `idx_orderDate` (`orderDate`),
    KEY `idx_cloud_data_row_last_updated_ts` (`lastModifiedTS`),
    KEY `idx_cloud_data_order_id_lastmodifiedts` (`orderId` , `lastModifiedTS`),
    KEY `idx_orderid_paymentstatus_orderdate` (`orderId` , `paymentStatus` , `orderDate`),
    CONSTRAINT `FK_orders_overallTotals_paid` FOREIGN KEY (`totalPaidPriceId`) REFERENCES `overallTotals` (`id`),
    CONSTRAINT `FK_orders_overallTotals_cancelled` FOREIGN KEY (`totalCancelledPriceId`) REFERENCES `overallTotals` (`id`)
    )  ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE = UTF8_BIN;

CREATE TABLE IF NOT EXISTS `payments` (
    `type` VARCHAR(15) COLLATE UTF8_BIN NOT NULL,
    `paymentId` INT(11) NOT NULL AUTO_INCREMENT,
    `createdTS` DATETIME DEFAULT NULL,
    `lastModifiedTS` DATETIME DEFAULT NULL,
    `version` INT(11) DEFAULT NULL,
    `authorisedAmount` VARCHAR(12) COLLATE UTF8_BIN NOT NULL,
    `processorId` VARCHAR(45) COLLATE UTF8_BIN NOT NULL,
    `paymentReference` VARCHAR(100) CHARACTER SET UTF8 DEFAULT NULL,
    `subMethod` VARCHAR(20) COLLATE UTF8_BIN DEFAULT NULL,
    `subMethodName` VARCHAR(32) COLLATE UTF8_BIN DEFAULT NULL,
    `orderId` VARCHAR(50) COLLATE UTF8_BIN DEFAULT NULL,
    `indexCol` INT(11) DEFAULT NULL,
    PRIMARY KEY (`paymentId`),
    KEY `idx_orderId` (`orderId`),
    CONSTRAINT `FK_payments_orders` FOREIGN KEY (`orderId`)
    REFERENCES `orders` (`orderId`)
    )  ENGINE=INNODB DEFAULT CHARSET=UTF8 COLLATE = UTF8_BIN;

CREATE TABLE IF NOT EXISTS `orderEntryAmounts` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `createdTS` DATETIME DEFAULT NULL,
    `lastModifiedTS` DATETIME DEFAULT NULL,
    `version` INT(11) DEFAULT NULL,
    `grossDiscountedTotal` DECIMAL(10,2) DEFAULT NULL,
    `grossDiscountedUnitPrice` DECIMAL(10,2) DEFAULT NULL,
    `grossRetailUnitPrice` DECIMAL(10,2) DEFAULT NULL,
    `lineVAT` DECIMAL(10,2) DEFAULT NULL,
    `originalGrossDiscountedTotal` DECIMAL(10,2) DEFAULT NULL,
    `unitDiscount` DECIMAL(10,2) DEFAULT NULL,
    `unitVAT` DECIMAL(10,2) DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=INNODB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

CREATE TABLE IF NOT EXISTS  `orderLineDiscounts` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `createdTS` datetime DEFAULT NULL,
    `lastModifiedTS` datetime DEFAULT NULL,
    `version` int(11) DEFAULT NULL,
    `discountRate` varchar(4) COLLATE utf8_bin DEFAULT NULL,
    `discountAmount` decimal(10,2) DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

CREATE TABLE IF NOT EXISTS `orderLines` (
    `orderEntryId` INT(11) NOT NULL AUTO_INCREMENT,
    `createdTS` DATETIME DEFAULT NULL,
    `lastModifiedTS` DATETIME DEFAULT NULL,
    `version` INT(11) DEFAULT NULL,
    `campaignId` VARCHAR(256) COLLATE utf8_bin,
    `costPrice` DECIMAL(10,2) DEFAULT NULL,
    `couponId` VARCHAR(256) COLLATE utf8_bin,
    `ean` VARCHAR(24) COLLATE utf8_bin DEFAULT NULL,
    `name` VARCHAR(256) COLLATE utf8_bin,
    `openQty` INT(11) DEFAULT NULL,
    `originalQty` INT(11) DEFAULT NULL,
    `promotionId` VARCHAR(256) COLLATE utf8_bin,
    `skuId` VARCHAR(64) COLLATE utf8_bin DEFAULT NULL,
    `standardRetailPrice` DECIMAL(10,2) DEFAULT NULL,
    `taxRate` DECIMAL(10,4) DEFAULT NULL,
    `vatClassId` VARCHAR(100) COLLATE utf8_bin DEFAULT NULL,
    `bonusProduct` BIT(1) DEFAULT NULL,
    `lineNumber` INT(11) DEFAULT NULL,
    `type` VARCHAR(20) COLLATE utf8_bin DEFAULT NULL,
    `orderLinePaidAmountId` INT(11) DEFAULT NULL,
    `orderLineCancelledAmountId` INT(11) DEFAULT NULL,
    `orderLineDiscountId` INT(11) DEFAULT NULL,
    `orderId` VARCHAR(50) COLLATE utf8_bin DEFAULT NULL,
    `indexCol` INT(11) DEFAULT NULL,
    `partnerReference` VARCHAR(256) COLLATE utf8_bin,
    `size` VARCHAR(255) COLLATE utf8_bin DEFAULT NULL,
    `ChannelScopeID` VARCHAR(255) COLLATE utf8_bin DEFAULT NULL,
    `barcodeScanStoreNumber` VARCHAR(50) COLLATE utf8_bin DEFAULT NULL,
    `brand` VARCHAR(63) COLLATE utf8_bin DEFAULT NULL,
    PRIMARY KEY (`orderEntryId`),
    KEY `idx_orderId` (`orderId`),
    KEY `idx_orderLinePaidAmount` (`orderLinePaidAmountId`),
    KEY `idx_orderLineCancelledAmount` (`orderLineCancelledAmountId`),
    KEY `idx_orderLineDiscount` (`orderLineDiscountId`),
    KEY `idx_type` (`type`),
    CONSTRAINT `FK_orderLines_orders` FOREIGN KEY (`orderId`) REFERENCES `orders` (`orderId`),
    CONSTRAINT `FK_orderLines_orderEntryAmounts_paid` FOREIGN KEY (`orderLinePaidAmountId`) REFERENCES `orderEntryAmounts` (`id`),
    CONSTRAINT `FK_orderLines_orderEntryAmounts_cancelled` FOREIGN KEY (`orderLineCancelledAmountId`) REFERENCES `orderEntryAmounts` (`id`),
    CONSTRAINT `FK_orderLines_orderLineDiscounts` FOREIGN KEY (`orderLineDiscountId`) REFERENCES `orderLineDiscounts` (`id`)
    ) ENGINE=INNODB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

CREATE TABLE IF NOT EXISTS `orderLineQtyStatus` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `createdTS` DATETIME DEFAULT NULL,
    `lastModifiedTS` DATETIME DEFAULT NULL,
    `version` INT(11) DEFAULT NULL,
    `orderLineId` INT(11) DEFAULT NULL,
    `prevStatus` VARCHAR(24) COLLATE utf8_bin DEFAULT NULL,
    `status` VARCHAR(24) COLLATE utf8_bin DEFAULT NULL,
    `indexCol` INT(11) DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_status` (`status`),
    KEY `idx_prevStatus` (`prevStatus`),
    KEY `idx_orderLineId` (`orderLineId`),
    KEY `idx_orderlineId_status` (`orderLineId`,`status`),
    CONSTRAINT `FK_orderLineQtyStatus_orderLines` FOREIGN KEY (`orderLineId`) REFERENCES `orderLines` (`orderEntryId`)
    ) ENGINE=INNODB DEFAULT CHARSET=utf8 COLLATE=utf8_bin;

CREATE TABLE IF NOT EXISTS orderCharges (
    `orderEntryId` INT(11) NOT NULL AUTO_INCREMENT,
    `createdTS` DATETIME DEFAULT NULL,
    `lastModifiedTS` DATETIME DEFAULT NULL,
    `version` INT(11) DEFAULT NULL,
    `campaignId` VARCHAR(256) COLLATE utf8_bin,
    `costPrice` DECIMAL(10,2) DEFAULT NULL,
    `couponId` VARCHAR(256) COLLATE utf8_bin,
    `ean` VARCHAR(24) COLLATE utf8_bin DEFAULT NULL,
    `name` VARCHAR(256) COLLATE utf8_bin,
    `openQty` INT(11) DEFAULT NULL,
    `originalQty` INT(11) DEFAULT NULL,
    `promotionId` VARCHAR(256) COLLATE utf8_bin,
    `skuId` VARCHAR(64) COLLATE utf8_bin DEFAULT NULL,
    `standardRetailPrice` DECIMAL(10,2) DEFAULT NULL,
    `taxRate` DECIMAL(10,4) DEFAULT NULL,
    `vatClassId` VARCHAR(100) COLLATE utf8_bin DEFAULT NULL,
    `partnerReference` VARCHAR(256) COLLATE utf8_bin,
    `refunded` BOOLEAN DEFAULT NULL,
    `cancelled` BOOLEAN DEFAULT NULL,
    `type` VARCHAR(20) COLLATE utf8_bin DEFAULT NULL,
    `orderId` VARCHAR(50) COLLATE utf8_bin DEFAULT NULL,
    `chargeTotalId` INT(11),
    `indexCol` INT(11) DEFAULT NULL,
    PRIMARY KEY (`orderEntryId`),
    KEY `idx_orderId` (`orderId`),
    KEY `idx_type` (`type`),
    KEY `idx_chargeTotalId` (`chargeTotalId`),
    CONSTRAINT `FK_orderCharges_orders` FOREIGN KEY (`orderId`) REFERENCES `orders` (`orderId`),
    CONSTRAINT `FK_orderCharges_orderEntryAmounts` FOREIGN KEY (`chargeTotalId`) REFERENCES `orderEntryAmounts` (`id`)
);