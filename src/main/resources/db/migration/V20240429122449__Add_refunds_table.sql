CREATE TABLE `refunds`
(
    `id`             INT(11)     NOT NULL AUTO_INCREMENT,
    `createdTS`      DATETIME    DEFAULT NULL,
    `lastModifiedTS` DATETIME    DEFAULT NULL,
    `version`        INT(11)     DEFAULT NULL,
    `refundId`       VARCHAR(20) DEFAULT NULL,
    `orderId`        VARCHAR(50) NOT NULL,
    `refundState`    INT(11)     NOT NULL,
    `refundTotal`    INT(11)     DEFAULT NULL,
    `csrInitials`    VARCHAR(15) DEFAULT NULL,
    `requestId`      VARCHAR(36) DEFAULT NULL UNIQUE,
    PRIMARY KEY (`id`),
    <PERSON><PERSON>Y `IDX_refunds_refundState` (`refundState`),
    KEY `IDX_orderId` (`orderId`),
    KEY `IDX_refundTotal` (`refundTotal`),
    <PERSON><PERSON>Y `IDX_refundId` (`refundId`),
    CONSTRAINT `FK_refunds_orderId` FOREIGN KEY (`orderId`) REFERENCES `orders` (`orderId`),
    CONSTRAINT `FK_refunds_refundTotal` FOREIGN KEY (`refundTotal`) REFERENCES `overallTotals` (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  COLLATE = utf8_bin;