SET @NewEffectiveDate = '2025-02-28 00:00:00';

INSERT INTO `CountryToReturnFeePricing` (`country`, `effectiveDate`, `returnFeeAmount`, `returnFeeTaxRate`, `threshold`)
SELECT
    `country`,
    @NewEffectiveDate,
    CASE `country`
        WHEN 'EG' THEN '17.99'
        WHEN 'MA' THEN '17.99'
        WHEN 'MC' THEN '2.99'
        END,
    `returnFeeTaxRate`,
    `threshold`
FROM `CountryToReturnFeePricing`
WHERE country IN ('EG', 'MA', 'MC');