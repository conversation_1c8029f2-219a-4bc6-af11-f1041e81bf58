CREATE TABLE `brandSpecificReturnFees`
(
    `id`                          INT(11)        NOT NULL AUTO_INCREMENT,
    `brand`                       VARCHAR(2)     NOT NULL,
    `countryToReturnFeePricingId` INT(11)        NOT NULL,
    `effectiveDate`               datetime       NOT NULL,
    `returnFee`                   decimal(19, 2) NOT NULL,
    PRIMARY KEY (`id`),
    <PERSON>EY `foreign_key` (`countryToReturnFeePricingId`),
    CONSTRAINT `foreign_key` FOREIGN KEY (`countryToReturnFeePricingId`) REFERENCES `countryToReturnFeePricings` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  COLLATE = utf8_bin;