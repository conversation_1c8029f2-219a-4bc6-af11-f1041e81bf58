INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'ON', id, '2025-03-04 00:00:00', 2.95
FROM CountryToReturnFeePricing
WHERE country = 'PT'
  AND returnFeeAmount = 1.99
UNION ALL
SELECT 'OS', id, '2025-03-04 00:00:00', 2.95
FROM CountryToReturnFeePricing
WHERE country = 'PT'
  AND returnFeeAmount = 1.99;


INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'ON', id, '2025-03-04 00:00:00', 2.95
FROM CountryToReturnFeePricing
WHERE country = 'CZ'
  AND returnFeeAmount = 1.99
UNION ALL
SELECT 'OS', id, '2025-03-04 00:00:00', 2.95
FROM CountryToReturnFeePricing
WHERE country = 'CZ'
  AND returnFeeAmount = 1.99;


INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'ON', id, '2025-03-04 00:00:00', 2.95
FROM CountryToReturnFeePricing
WHERE country = 'GR'
  AND returnFeeAmount = 1.99
UNION ALL
SELECT 'OS', id, '2025-03-04 00:00:00', 2.95
FROM CountryToReturnFeePricing
WHERE country = 'GR'
  AND returnFeeAmount = 1.99;