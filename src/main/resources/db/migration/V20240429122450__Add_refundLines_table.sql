CREATE TABLE `refundLines`
(
    `id`              INT(11) NOT NULL AUTO_INCREMENT,
    `createdTS`       datetime     DEFAULT NULL,
    `lastModifiedTS`  datetime     DEFAULT NULL,
    `version`         INT(11)      DEFAULT NULL,
    `lineNumber`      varchar(255) DEFAULT NULL,
    `quantity`        INT(11)      DEFAULT NULL,
    `orderLineId`     INT(11)      DEFAULT NULL,
    `refundId`        INT(11)      DEFAULT NULL,
    `refundLineTotal` INT(11)      DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `IDX_refundLines_refundLineTotal` (`refundLineTotal`),
    KEY `IDX_refundLines_orderLineId` (`orderLineId`),
    KEY `IDX_refundLines_refundId` (`refundId`),
    CONSTRAINT `FK_refundLines_refundId` FOREIGN KEY (`refundId`) REFERENCES `refunds` (`id`),
    CONSTRAINT `FK_refundLines_refundLineTotal` FOREIGN KEY (`refundLineTotal`) REFERENCES `orderEntryAmounts` (`id`),
    CONSTRAINT `FK_refundLines_orderLineId` FOREIGN KEY (`orderLineId`) REFERENCES `orderLines` (`orderEntryId`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  COLLATE = utf8_bin;