INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'VM', id, '2025-02-25 00:00:00', 2.99
FROM CountryToReturnFeePricing
WHERE country = 'EE'
  AND returnFeeAmount = 1.99;

INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'VM', id, '2025-02-25 00:00:00', 2.99
FROM CountryToReturnFeePricing
WHERE country = 'LT'
  AND returnFeeAmount = 1.99;

INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'VM', id, '2025-02-25 00:00:00', 2.99
FROM CountryToReturnFeePricing
WHERE country = 'LV'
  AND returnFeeAmount = 1.99;

INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'VM', id, '2025-02-25 00:00:00', 2.99
FROM CountryToReturnFeePricing
WHERE country = 'HR'
  AND returnFeeAmount = 1.99;

INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'VM', id, '2025-02-25 00:00:00', 2.99
FROM CountryToReturnFeePricing
WHERE country = 'CZ'
  AND returnFeeAmount = 1.99;

INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'VM', id, '2025-02-25 00:00:00', 2.99
FROM CountryToReturnFeePricing
WHERE country = 'GR'
  AND returnFeeAmount = 1.99;

INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'VM', id, '2025-02-25 00:00:00', 2.99
FROM CountryToReturnFeePricing
WHERE country = 'SI'
  AND returnFeeAmount = 1.99;