INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'VM', id, CONVERT_TZ('2025-05-21 00:00:00', 'CET', 'UTC'), 1500.00
FROM CountryToReturnFeePricing
WHERE country = 'HU';

INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'VM', id, CONVERT_TZ('2025-05-21 00:00:00', 'CET', 'UTC'), 2.99
FROM CountryToReturnFeePricing
WHERE country = 'PT';

INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'VM', id, CONVERT_TZ('2025-05-21 00:00:00', 'CET', 'UTC'), 2.99
FROM CountryToReturnFeePricing
WHERE country = 'SK';

INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'VM', id, CONVERT_TZ('2025-05-21 00:00:00', 'CET', 'UTC'), 2.99
FROM CountryToReturnFeePricing
WHERE country = 'AD';

INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'VM', id, CONVERT_TZ('2025-05-21 00:00:00', 'CET', 'UTC'), 3.99
FROM CountryToReturnFeePricing
WHERE country = 'MT';

INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'VM', id, CONVERT_TZ('2025-05-21 00:00:00', 'CET', 'UTC'), 2.99
FROM CountryToReturnFeePricing
WHERE country = 'MC';

INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'VM', id, CONVERT_TZ('2025-05-21 00:00:00', 'CET', 'UTC'), 2.99
FROM CountryToReturnFeePricing
WHERE country = 'LU';
