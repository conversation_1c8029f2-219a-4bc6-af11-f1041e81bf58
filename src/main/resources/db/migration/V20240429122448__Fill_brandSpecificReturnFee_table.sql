INSERT INTO `brandSpecificReturnFees` (`brand`, `countryToReturnFeePricingId`, `effectiveDate`, `returnFee`)
VALUES ('JJ', 12, '2022-08-22 00:00:00', 0.00),
       ('SL', 24, '2022-08-22 00:00:00', 0.00),
       ('SL', 22, '2022-08-22 00:00:00', 0.00),
       ('SL', 23, '2022-08-22 00:00:00', 0.00),
       ('NI', 24, '2022-08-22 00:00:00', 0.00),
       ('NI', 22, '2022-08-22 00:00:00', 0.00),
       ('NI', 23, '2022-08-22 00:00:00', 0.00),
       ('NI', 22, '2022-12-26 00:00:00', 1.99),
       ('SL', 22, '2022-12-26 00:00:00', 1.99),
       ('ON', 23, '2022-12-26 00:00:00', 2.95),
       ('ON', 27, '2022-12-26 00:00:00', 2.95),
       ('ON', 24, '2022-12-26 00:00:00', 2.95),
       ('ON', 22, '2022-12-26 00:00:00', 2.95),
       ('ON', 29, '2022-12-26 00:00:00', 2.95),
       ('ON', 35, '2022-12-26 00:00:00', 13.95),
       ('ON', 30, '2022-12-26 00:00:00', 2.95),
       ('ON', 31, '2022-12-26 00:00:00', 2.95),
       ('ON', 25, '2022-12-26 00:00:00', 2.95),
       ('ON', 19, '2022-12-26 00:00:00', 29.95),
       ('ON', 20, '2022-12-26 00:00:00', 39.95),
       ('ON', 21, '2022-12-26 00:00:00', 39.95),
       ('ON', 33, '2022-12-26 00:00:00', 3.49),
       ('ON', 28, '2022-12-26 00:00:00', 3.95),
       ('ON', 26, '2022-12-26 00:00:00', 3.95),
       ('VM', 27, '2023-01-26 00:00:00', 2.99),
       ('VM', 24, '2023-01-26 00:00:00', 2.99),
       ('VM', 25, '2023-01-26 00:00:00', 2.99),
       ('VM', 22, '2023-01-26 00:00:00', 2.99),
       ('VM', 23, '2023-01-26 00:00:00', 2.99),
       ('VM', 19, '2023-01-26 00:00:00', 29.95),
       ('VM', 30, '2023-01-26 00:00:00', 2.99),
       ('VM', 26, '2023-01-26 00:00:00', 2.99),
       ('VM', 33, '2023-01-26 00:00:00', 3.49),
       ('VM', 28, '2023-01-26 00:00:00', 2.99),
       ('VM', 29, '2023-01-26 00:00:00', 2.99),
       ('VM', 31, '2023-01-26 00:00:00', 2.99),
       ('VM', 21, '2023-01-26 00:00:00', 29.95),
       ('VM', 35, '2023-01-26 00:00:00', 29.50),
       ('VM', 20, '2023-01-26 00:00:00', 29.95),
       ('MM', 27, '2023-01-26 00:00:00', 2.99),
       ('MM', 24, '2023-01-26 00:00:00', 2.99),
       ('MM', 25, '2023-01-26 00:00:00', 2.99),
       ('MM', 22, '2023-01-26 00:00:00', 2.99),
       ('MM', 23, '2023-01-26 00:00:00', 2.99),
       ('MM', 19, '2023-01-26 00:00:00', 29.95),
       ('MM', 30, '2023-01-26 00:00:00', 2.99),
       ('MM', 26, '2023-01-26 00:00:00', 2.99),
       ('MM', 33, '2023-01-26 00:00:00', 3.49),
       ('MM', 28, '2023-01-26 00:00:00', 2.99),
       ('MM', 29, '2023-01-26 00:00:00', 2.99),
       ('MM', 31, '2023-01-26 00:00:00', 2.99),
       ('MM', 21, '2023-01-26 00:00:00', 29.95),
       ('MM', 35, '2023-01-26 00:00:00', 29.50),
       ('MM', 20, '2023-01-26 00:00:00', 29.95),
       ('IQ', 27, '2023-01-26 00:00:00', 2.99),
       ('IQ', 24, '2023-01-26 00:00:00', 2.99),
       ('IQ', 25, '2023-01-26 00:00:00', 2.99),
       ('IQ', 22, '2023-01-26 00:00:00', 2.99),
       ('IQ', 23, '2023-01-26 00:00:00', 2.99),
       ('IQ', 19, '2023-01-26 00:00:00', 24.95),
       ('IQ', 30, '2023-01-26 00:00:00', 2.99),
       ('IQ', 26, '2023-01-26 00:00:00', 2.99),
       ('IQ', 33, '2023-01-26 00:00:00', 2.99),
       ('IQ', 28, '2023-01-26 00:00:00', 2.99),
       ('IQ', 29, '2023-01-26 00:00:00', 2.99),
       ('IQ', 31, '2023-01-26 00:00:00', 2.99),
       ('IQ', 21, '2023-01-26 00:00:00', 24.95),
       ('IQ', 35, '2023-01-26 00:00:00', 14.50),
       ('IQ', 20, '2023-01-26 00:00:00', 24.95),
       ('OC', 27, '2023-02-14 00:00:00', 2.99),
       ('OC', 24, '2023-02-14 00:00:00', 2.99),
       ('OC', 25, '2023-02-14 00:00:00', 2.99),
       ('OC', 22, '2023-02-14 00:00:00', 2.99),
       ('OC', 23, '2023-02-14 00:00:00', 2.99),
       ('OC', 19, '2023-02-14 00:00:00', 29.95),
       ('OC', 30, '2023-02-14 00:00:00', 2.99),
       ('OC', 26, '2023-02-14 00:00:00', 2.99),
       ('OC', 33, '2023-02-14 00:00:00', 3.49),
       ('OC', 28, '2023-02-14 00:00:00', 2.99),
       ('OC', 29, '2023-02-14 00:00:00', 2.99),
       ('OC', 31, '2023-02-14 00:00:00', 2.99),
       ('OC', 21, '2023-02-14 00:00:00', 29.95),
       ('OC', 35, '2023-02-14 00:00:00', 29.50),
       ('OC', 20, '2023-02-14 00:00:00', 29.95),
       ('PC', 27, '2023-02-14 00:00:00', 2.99),
       ('PC', 24, '2023-02-14 00:00:00', 2.99),
       ('PC', 25, '2023-02-14 00:00:00', 2.99),
       ('PC', 22, '2023-02-14 00:00:00', 2.99),
       ('PC', 23, '2023-02-14 00:00:00', 2.99),
       ('PC', 19, '2023-02-14 00:00:00', 29.95),
       ('PC', 30, '2023-02-14 00:00:00', 2.99),
       ('PC', 26, '2023-02-14 00:00:00', 2.99),
       ('PC', 33, '2023-02-14 00:00:00', 3.49),
       ('PC', 28, '2023-02-14 00:00:00', 2.99),
       ('PC', 29, '2023-02-14 00:00:00', 2.99),
       ('PC', 31, '2023-02-14 00:00:00', 2.99),
       ('PC', 21, '2023-02-14 00:00:00', 29.95),
       ('PC', 35, '2023-02-14 00:00:00', 29.50),
       ('PC', 20, '2023-02-14 00:00:00', 29.95),
       ('YS', 27, '2023-02-14 00:00:00', 2.99),
       ('YS', 24, '2023-02-14 00:00:00', 2.99),
       ('YS', 25, '2023-02-14 00:00:00', 2.99),
       ('YS', 22, '2023-02-14 00:00:00', 2.99),
       ('YS', 23, '2023-02-14 00:00:00', 2.99),
       ('YS', 19, '2023-02-14 00:00:00', 29.95),
       ('YS', 30, '2023-02-14 00:00:00', 2.99),
       ('YS', 26, '2023-02-14 00:00:00', 2.99),
       ('YS', 33, '2023-02-14 00:00:00', 3.49),
       ('YS', 28, '2023-02-14 00:00:00', 2.99),
       ('YS', 29, '2023-02-14 00:00:00', 2.99),
       ('YS', 31, '2023-02-14 00:00:00', 2.99),
       ('YS', 21, '2023-02-14 00:00:00', 29.95),
       ('YS', 35, '2023-02-14 00:00:00', 29.50),
       ('YS', 20, '2023-02-14 00:00:00', 29.95),
       ('VL', 27, '2023-02-14 00:00:00', 2.99),
       ('VL', 24, '2023-02-14 00:00:00', 2.99),
       ('VL', 25, '2023-02-14 00:00:00', 2.99),
       ('VL', 22, '2023-02-14 00:00:00', 2.99),
       ('VL', 23, '2023-02-14 00:00:00', 2.99),
       ('VL', 19, '2023-02-14 00:00:00', 29.95),
       ('VL', 30, '2023-02-14 00:00:00', 2.99),
       ('VL', 26, '2023-02-14 00:00:00', 2.99),
       ('VL', 33, '2023-02-14 00:00:00', 3.49),
       ('VL', 28, '2023-02-14 00:00:00', 2.99),
       ('VL', 29, '2023-02-14 00:00:00', 2.99),
       ('VL', 31, '2023-02-14 00:00:00', 2.99),
       ('VL', 21, '2023-02-14 00:00:00', 29.95),
       ('VL', 35, '2023-02-14 00:00:00', 29.50),
       ('VL', 20, '2023-02-14 00:00:00', 29.95),
       ('NI', 24, '2023-02-14 00:00:00', 1.99),
       ('NI', 23, '2023-02-14 00:00:00', 1.99),
       ('SL', 27, '2023-04-13 00:00:00', 2.99),
       ('SL', 24, '2023-04-13 00:00:00', 1.99),
       ('SL', 30, '2023-04-13 00:00:00', 2.99),
       ('SL', 26, '2023-04-13 00:00:00', 2.99),
       ('SL', 33, '2023-04-13 00:00:00', 3.49),
       ('SL', 28, '2023-04-13 00:00:00', 3.99),
       ('SL', 29, '2023-04-13 00:00:00', 2.99),
       ('SL', 21, '2023-04-13 00:00:00', 39.95),
       ('SL', 20, '2023-04-13 00:00:00', 29.95),
       ('NM', 27, '2023-05-11 00:00:00', 2.95),
       ('NM', 24, '2023-05-11 00:00:00', 2.95),
       ('NM', 25, '2023-05-11 00:00:00', 2.95),
       ('NM', 22, '2023-05-11 00:00:00', 2.95),
       ('NM', 23, '2023-05-11 00:00:00', 2.95),
       ('NM', 19, '2023-05-11 00:00:00', 29.95),
       ('NM', 30, '2023-05-11 00:00:00', 2.95),
       ('NM', 26, '2023-05-11 00:00:00', 3.95),
       ('NM', 33, '2023-05-11 00:00:00', 3.49),
       ('NM', 28, '2023-05-11 00:00:00', 3.95),
       ('NM', 29, '2023-05-11 00:00:00', 2.95),
       ('NM', 31, '2023-05-11 00:00:00', 2.95),
       ('NM', 21, '2023-05-11 00:00:00', 39.95),
       ('NM', 35, '2023-05-11 00:00:00', 13.95),
       ('NM', 20, '2023-05-11 00:00:00', 39.95),
       ('OS', 27, '2023-05-11 00:00:00', 2.99),
       ('OS', 24, '2023-05-11 00:00:00', 2.99),
       ('OS', 25, '2023-05-11 00:00:00', 2.99),
       ('OS', 22, '2023-05-11 00:00:00', 2.99),
       ('OS', 23, '2023-05-11 00:00:00', 2.99),
       ('OS', 19, '2023-05-11 00:00:00', 29.95),
       ('OS', 30, '2023-05-11 00:00:00', 2.99),
       ('OS', 26, '2023-05-11 00:00:00', 2.99),
       ('OS', 33, '2023-05-11 00:00:00', 3.49),
       ('OS', 28, '2023-05-11 00:00:00', 2.99),
       ('OS', 29, '2023-05-11 00:00:00', 2.99),
       ('OS', 31, '2023-05-11 00:00:00', 2.99),
       ('OS', 21, '2023-05-11 00:00:00', 29.95),
       ('OS', 35, '2023-05-11 00:00:00', 29.50),
       ('OS', 20, '2023-05-11 00:00:00', 29.95);