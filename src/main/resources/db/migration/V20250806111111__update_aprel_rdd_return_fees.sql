INSERT INTO BrandSpecificReturnFee (brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'AP', id, '2025-08-06 00:00:00', 1.99
FROM (SELECT id
      FROM CountryToReturnFeePricing
      WHERE country = 'MC'
      ORDER BY effectiveDate DESC
      LIMIT 1) AS ap_sub
UNION ALL
SELECT 'RD', id, '2025-08-06 00:00:00', 1.99
FROM (SELECT id
      FROM CountryToReturnFeePricing
      WHERE country = 'MC'
      ORDER BY effectiveDate DESC
      LIMIT 1) AS rd_sub;