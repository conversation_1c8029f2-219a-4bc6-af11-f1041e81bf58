INSERT INTO BrandSpecificReturnFee (brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'JJ',
       c.id,
       CONVERT_TZ('2025-09-29 00:00:00', 'Europe/Berlin', 'UTC'),
       15
FROM CountryToReturnFeePricing c
WHERE c.country = 'RO'
UNION ALL
SELECT 'AP',
       c.id,
       CONVERT_TZ('2025-09-29 00:00:00', 'Europe/Berlin', 'UTC'),
       15
FROM CountryToReturnFeePricing c
WHERE c.country = 'RO'
UNION ALL
SELECT 'RD',
       c.id,
       CONVERT_TZ('2025-09-29 00:00:00', 'Europe/Berlin', 'UTC'),
       15
FROM CountryToReturnFeePricing c
WHERE c.country = 'RO';