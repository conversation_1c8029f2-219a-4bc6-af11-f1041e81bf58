INSERT INTO BrandSpecificReturnFee (brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'VM', id, NOW(), 2.99
FROM (SELECT id
      from CountryToReturnFeePricing
      WHERE country = 'BG'
      ORDER BY effectiveDate DESC LIMIT 1) as vm_bg
UNION ALL
SELECT 'VM', id, NOW(), 15
FROM (SELECT id
      from CountryToReturnFeePricing
      WHERE country = 'RO'
      ORDER BY effectiveDate DESC LIMIT 1) as vm_ro
UNION ALL
SELECT 'VM', id, NOW(), 17.99
FROM (SELECT id
      from CountryToReturnFeePricing
      WHERE country = 'CY'
      ORDER BY effectiveDate DESC LIMIT 1) as vm_cy
UNION ALL
SELECT 'VM', id, NOW(), 17.99
FROM (SELECT id
      from CountryToReturnFeePricing
      WHERE country = 'ME'
      ORDER BY effectiveDate DESC LIMIT 1) as vm_me

