INSERT INTO BrandSpecificReturnFee (brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'AN', /* ANNARR */
       ctrfp.id,
       '2025-03-10 00:00:00',
       brands.returnFee AS brandSpecificReturnFee
FROM CountryToReturnFeePricing ctrfp
         INNER JOIN (SELECT country, MAX(id) AS max_id
                     FROM CountryToReturnFeePricing
                     WHERE
                         country IN ('BE', 'ES', 'IT', 'AT', 'FI', 'FR', 'NL', 'PL', 'SE', 'DE', 'IE', 'NO', 'CH', 'GB')
                     GROUP BY country) AS max_ids
                    ON ctrfp.country = max_ids.country AND ctrfp.id = max_ids.max_id
         LEFT JOIN (SELECT bsrf2.returnFee,
                           bsrf2.countryToReturnFeePricingId
                    FROM BrandSpecificReturnFee bsrf2
                             INNER JOIN (SELECT MAX(bsrf.id) as max_id,
                                                bsrf.countryToReturnFeePricingId
                                         FROM BrandSpecificReturnFee bsrf
                                         WHERE bsrf.brand = 'SL' /* SELECTED */
                                         GROUP BY bsrf.countryToReturnFeePricingId) as brand_max_ids
                                        ON brand_max_ids.max_id = bsrf2.id) as brands
                   ON brands.countryToReturnFeePricingId = ctrfp.id
WHERE brands.returnFee IS NOT NULL