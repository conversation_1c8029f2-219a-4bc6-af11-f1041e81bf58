CREATE PROCEDURE delete_order_safely(IN p_orderIds TEXT)
BEGIN
    DECLARE v_orderLineIds TEXT;
    DECLARE v_orderChargeIds TEXT;
    DECLARE v_refundLineIds TEXT;

    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    SELECT GROUP_CONCAT(rl.id) INTO v_refundLineIds
     FROM RefundLine rl
     JOIN Refund r ON rl.refundId = r.id
    WHERE FIND_IN_SET(orderId, p_orderIds);

    DELETE rl FROM RefundLine rl
      JOIN Refund r ON rl.refundId = r.id
     WHERE FIND_IN_SET(r.orderId, p_orderIds);

    DELETE FROM Refund WHERE FIND_IN_SET(orderId, p_orderIds);

    SELECT GROUP_CONCAT(orderEntryId) INTO v_orderChargeIds
      FROM OrderCharge
     WHERE FIND_IN_SET(orderId, p_orderIds);

    DELETE FROM OrderCharge WHERE FIND_IN_SET(orderId, p_orderIds);

    SELECT GROUP_CONCAT(orderEntryId) INTO v_orderLineIds
     FROM OrderLine
    WHERE FIND_IN_SET(orderId, p_orderIds);

    DELETE FROM OrderLine WHERE FIND_IN_SET(orderId, p_orderIds);

    DELETE old FROM OrderLineDiscount old
      LEFT JOIN OrderLine ol ON old.id = ol.orderLineDiscountId
     WHERE FIND_IN_SET(ol.orderEntryId, v_orderLineIds);

    DELETE FROM Promotion WHERE FIND_IN_SET(orderId, p_orderIds);

    DELETE FROM Payment WHERE FIND_IN_SET(orderId, p_orderIds);

    DELETE FROM Orders WHERE FIND_IN_SET(orderId, p_orderIds);

    DELETE ot FROM OverallTotal ot
      LEFT JOIN Orders o ON ot.id = o.totalPaidPriceId OR ot.id = o.totalCancelledPriceId
    WHERE FIND_IN_SET(o.orderId, p_orderIds);

    DELETE oea FROM OrderEntryAmount oea
     LEFT JOIN OrderLine ol ON oea.id = ol.orderLinePaidAmountId OR oea.id = ol.orderLineCancelledAmountId
    WHERE FIND_IN_SET(ol.orderEntryId, v_orderLineIds);

    DELETE oea FROM OrderEntryAmount oea
      LEFT JOIN OrderCharge oc ON oea.id = oc.chargeTotalId
     WHERE FIND_IN_SET(oc.orderEntryId, v_orderChargeIds);

    DELETE oea FROM OrderEntryAmount oea
      LEFT JOIN RefundLine rl ON oea.id = rl.refundLineTotal
    WHERE FIND_IN_SET(rl.id, v_refundLineIds);

COMMIT;
END;