INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'JJ', `id`, '2025-01-20 00:00:00', 2.99
FROM CountryToReturnFeePricing
WHERE `country` = 'DE'
  AND `returnFeeAmount` = 1.99;

INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'JX', `id`, '2025-01-20 00:00:00', 2.99
FROM CountryToReturnFeePricing
WHERE `country` = 'DE'
  AND `returnFeeAmount` = 1.99;

INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'AP', `id`, '2025-01-20 00:00:00', 2.99
FROM CountryToReturnFeePricing
WHERE `country` = 'DE'
  AND `returnFeeAmount` = 1.99;

INSERT INTO BrandSpecificReturnFee(brand, countryToReturnFeePricingId, effectiveDate, returnFee)
SELECT 'RD', `id`, '2025-01-20 00:00:00', 2.99
FROM CountryToReturnFeePricing
WHERE `country` = 'DE'
  AND `returnFeeAmount` = 1.99;