CREATE TABLE IF NOT EXISTS `CustomerRefundChoice` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `createdTS` DATETIME DEFAULT NULL,
    `lastModifiedTS` DATETIME DEFAULT NULL,
    `version` INT(11) DEFAULT NULL,
    `customerRefundMethod` VARCHAR(50) COLLATE utf8_bin NOT NULL,
    `returnId` VARCHAR(255) COLLATE utf8_bin NOT NULL,
    `orderEntryId` INT(11) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_orderLineId` (`orderEntryId`),
    KEY `idx_returnId` (`returnId`),
    KEY `idx_customerRefundMethod` (`customerRefundMethod`),
    CONSTRAINT `FK_CustomerRefundChoice_OrderLine` FOREIGN KEY (`orderEntryId`) REFERENCES `OrderLine` (`orderEntryId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin; 