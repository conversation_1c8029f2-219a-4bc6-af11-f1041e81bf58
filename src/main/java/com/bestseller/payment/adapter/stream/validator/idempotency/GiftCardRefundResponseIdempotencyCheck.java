package com.bestseller.payment.adapter.stream.validator.idempotency;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.giftCardRefundResponse.GiftCardRefundResponse;
import com.bestseller.payment.core.domain.Refund;
import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
public class GiftCardRefundResponseIdempotencyCheck implements IdempotencyChecker<GiftCardRefundResponse> {

    private final RefundService refundService;

    public GiftCardRefundResponseIdempotencyCheck(RefundService refundService) {
        this.refundService = refundService;
    }

    @Override
    public boolean isDuplicate(GiftCardRefundResponse message) {
        UUID giftCardCorrelationId = UUID.fromString(message.getCorrelationId());
        Refund refund = refundService.getRefund(message.getOrderId(), giftCardCorrelationId);
        return refund.getRefundState().equals(
            message.getStatus() ? RefundState.REFUND_SUCCESS : RefundState.REFUND_FAILED);
    }
}
