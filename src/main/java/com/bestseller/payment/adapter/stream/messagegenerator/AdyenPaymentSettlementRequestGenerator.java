package com.bestseller.payment.adapter.stream.messagegenerator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementRequest.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementRequest.PaymentSettlementRequest;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderCharge;
import com.bestseller.payment.core.utils.PaymentUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@RequiredArgsConstructor
@Service
public class AdyenPaymentSettlementRequestGenerator extends PaymentSettlementRequestGenerator {
    private static final BigDecimal BD_100 = new BigDecimal("100.00");

    @Override
    public PaymentSettlementRequest generate(Order order) {
        return super.generate(order)
            .withPspReference(getPaymentInfo(order).getPaymentReference());
    }

    @Override
    protected List<OrderLine> getOrderLines(Order order) {
        return order.getOrderLines()
            .stream()
            .filter(orderLine -> orderLine.getOpenQty() > 0)
            .map(orderLine -> new OrderLine()
                .withEan(orderLine.getEan())
                .withDescription(orderLine.getName())
                .withQuantity(orderLine.getOpenQty())
                .withPaidAmount(PaymentUtils.toIntCents(orderLine.getOrderLinePaidAmount().getGrossDiscountedUnitPrice()))
                .withTaxRate(PaymentUtils.toIntCents(orderLine.getTaxRate().multiply(BD_100)))
                .withTaxAmount(PaymentUtils.toIntCents(orderLine.getOrderLinePaidAmount().getUnitVAT()))
            ).toList();
    }

    @Override
    protected List<OrderLine> convertOrderChargesToOrderLines(List<OrderCharge> orderCharges) {
        return orderCharges
            .stream()
            .map(orderCharge -> new OrderLine()
                .withEan(orderCharge.getName())
                .withDescription(orderCharge.getName())
                .withQuantity(orderCharge.getOpenQty())
                .withPaidAmount(PaymentUtils.toIntCents(orderCharge.getChargeTotal().getGrossDiscountedUnitPrice()))
                .withTaxRate(PaymentUtils.toIntCents(orderCharge.getTaxRate().multiply(BD_100)))
                .withTaxAmount(PaymentUtils.toIntCents(orderCharge.getChargeTotal().getUnitVAT()))
            ).toList();
    }
}
