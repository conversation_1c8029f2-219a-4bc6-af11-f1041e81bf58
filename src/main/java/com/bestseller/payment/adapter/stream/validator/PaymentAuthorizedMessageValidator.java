package com.bestseller.payment.adapter.stream.validator;

import com.bestseller.dbqueue.consumer.wrapper.BaseMessageValidator;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentAuthorized.PaymentAuthorized;
import com.bestseller.payment.core.validation.PaymentAuthorizationValidator;
import jakarta.validation.Validator;
import org.springframework.stereotype.Component;

@Component
public class PaymentAuthorizedMessageValidator extends BaseMessageValidator<PaymentAuthorized> {

    private final PaymentAuthorizationValidator paymentAuthorizationValidator;

    public PaymentAuthorizedMessageValidator(Validator validator,
                                             PaymentAuthorizationValidator paymentAuthorizationValidator) {
        super(validator);
        this.paymentAuthorizationValidator = paymentAuthorizationValidator;
    }

    @Override
    public boolean passesCustomValidation(PaymentAuthorized paymentAuthorized) {
        return paymentAuthorizationValidator.validate(
            paymentAuthorized.getOrderId(),
            paymentAuthorized.getProvider(),
            paymentAuthorized.getPspReference()
        );
    }
}
