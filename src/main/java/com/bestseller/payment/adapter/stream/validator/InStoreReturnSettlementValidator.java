package com.bestseller.payment.adapter.stream.validator;

import com.bestseller.dbqueue.consumer.wrapper.BaseMessageValidator;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inStoreReturnSettlement.InStoreReturnSettlement;
import com.bestseller.payment.adapter.api.RefundOptionsService;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderEntry;
import com.bestseller.payment.core.domain.OrderLine;
import com.bestseller.payment.core.domain.RefundLine;
import com.bestseller.payment.core.exception.OrderNotFoundException;
import com.bestseller.payment.core.exception.RefundOptionsProviderServiceNotAvailable;
import jakarta.validation.Validator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class InStoreReturnSettlementValidator extends BaseMessageValidator<InStoreReturnSettlement> {

    private final OrderRepository orderRepository;
    private final RefundOptionsService refundOptionsService;
    private final PaymentValidation paymentValidation;

    protected InStoreReturnSettlementValidator(Validator validator,
                                               OrderRepository orderRepository,
                                               RefundOptionsService refundOptionsService,
                                               PaymentValidation paymentValidation) {
        super(validator);
        this.orderRepository = orderRepository;
        this.refundOptionsService = refundOptionsService;
        this.paymentValidation = paymentValidation;
    }

    @Override
    public boolean passesCustomValidation(InStoreReturnSettlement inStoreReturnSettlement) {
        String orderId = inStoreReturnSettlement.getOrderId();
        Order order = orderRepository.findById(orderId)
            .orElseThrow(() -> {
                log.error("Order with id {} not found", orderId);
                return new OrderNotFoundException(orderId);
            });

        Long refundId = inStoreReturnSettlement.getRefundId();
        if (inStoreReturnSettlement.getTotalRefundAmount().compareTo(BigDecimal.ZERO) < 0) {
            log.error("Negative refund amount specified for order with ID {} and refund with ID {}",
                orderId, refundId);
            return false;
        }

        if (refundOptionsService.getRefundOptionsByOrderId(orderId).isEmpty()) {
            throw new RefundOptionsProviderServiceNotAvailable(orderId);
        }

        // validate the payment
        if (!paymentValidation.validateRefundRequest(order)) {
            return false;
        }

        return inStoreReturnSettlement.getOrderLines()
            .stream()
            .allMatch(refundOrderLine -> validateRefundOrderLine(order, refundOrderLine));
    }

    private boolean validateRefundOrderLine(Order order,
                                            com.bestseller
                                                .generated
                                                .interfacecontracts
                                                .kafkamessages
                                                .pojos
                                                .inStoreReturnSettlement
                                                .OrderLine refundOrderLine) {


        if (refundOrderLine.getQuantity() <= 0L) {
            log.error("Invalid quantity {} of order line with ID {} from order with ID {} for in store return.",
                refundOrderLine.getQuantity(), refundOrderLine.getId(), order.getOrderId());
            return false;
        }

        Optional<OrderLine> orderLineOptional = order.getOrderLines()
            .stream()
            .filter(orderLine -> orderLine.getEan().equals(refundOrderLine.getEan()))
            .findFirst();

        if (orderLineOptional.isPresent()) {
            if (!validateNotTooManyItemsRefunded(order, refundOrderLine.getEan(),
                refundOrderLine.getQuantity())) {
                log.error("Duplicate refund for order {} and  order line id {}, EAN {}",
                    order.getOrderId(), refundOrderLine.getId(), refundOrderLine.getEan());
                return false;
            }
        } else {
            log.error("Validation error for in store return: Could not find order line with ID {}, EAN {} in order with ID {}",
                refundOrderLine.getId(), refundOrderLine.getEan(), order.getOrderId());
            return false;
        }
        return true;
    }

    private boolean validateNotTooManyItemsRefunded(Order order, String ean, Integer requestedQuantity) {
        List<RefundLine> alreadyRefunded = order.getRefunds()
            .stream()
            .flatMap(refund -> refund.getRefundLines().stream())
            .toList();

        int openQuantity = order.getOrderLines()
            .stream()
            .filter(orderLine -> orderLine.getEan().equals(ean))
            .mapToInt(OrderEntry::getOpenQty).sum();

        int originalQuantity = order.getOrderLines()
            .stream()
            .filter(orderLine -> orderLine.getEan().equals(ean))
            .mapToInt(OrderEntry::getOriginalQty)
            .sum();

        int refundedQuantity = alreadyRefunded
            .stream()
            .filter(refundLine -> refundLine.getOrderLine().getEan().equals(ean))
            .mapToInt(RefundLine::getQuantity)
            .sum();
        return requestedQuantity <= openQuantity && refundedQuantity + requestedQuantity <= originalQuantity;
    }
}


