package com.bestseller.payment.adapter.stream.messagegenerator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequest.PaymentRefundRequest;
import com.bestseller.payment.core.dto.RefundDto;
import org.springframework.stereotype.Service;

@Service
public class GiftCardPaymentRefundRequestGenerator implements MessageGenerator<RefundDto, PaymentRefundRequest> {
    // todo: Remove `PROVIDER_OPTICARD` and use `ProcessorId` enum instead
    private static final String PROVIDER_OPTICARD = "OPTICARD_PHASE_ONE";

    @Override
    public PaymentRefundRequest generate(RefundDto data) {
        final var order = data.refund().getOrder();
        return new PaymentRefundRequest()
            .withCorrelationId(data.refund().getGiftCardCorrelationId().toString())
            .withOrderId(order.getOrderId())
            .withTotalAmount(data.refundAmountInCents())
            .withReason(data.refundReason().name())
            .withCurrency(order.getCurrency())
            .withBillingCountry(order.getBillingCountryCode())
            .withBrand(order.getBrand().getBrandAbbreviation())
            .withProvider(PROVIDER_OPTICARD)
            .withGiftCardRefundSource(data.giftCardRefundSource().name());
    }
}
