package com.bestseller.payment.adapter.stream.messagegenerator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import com.bestseller.interfacecontractannotator.model.paymentstatusupdated.AuthorizedPayload;
import com.bestseller.interfacecontractannotator.model.paymentstatusupdated.OrderDetails;
import com.bestseller.interfacecontractannotator.model.paymentstatusupdated.OrderLine;
import com.bestseller.interfacecontractannotator.model.paymentstatusupdated.Payment;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderCharge;
import com.bestseller.payment.core.domain.OrderEntry;
import com.bestseller.payment.core.domain.OrderEntryAmount;
import com.bestseller.payment.core.domain.enumeration.EntryType;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;

@RequiredArgsConstructor
@Service
public class PaymentStatusUpdatedGenerator implements MessageGenerator<Order, PaymentStatusUpdated> {

    private static final List<PaymentState> AUTHORIZED_STATES = List.of(PaymentState.OFFLINE, PaymentState.AUTHORISED);

    @Override
    public PaymentStatusUpdated generate(Order order) {
        boolean isAuthorized = AUTHORIZED_STATES.contains(order.getPaymentStatus());

        return new PaymentStatusUpdated()
            .withOrderId(order.getOrderId())
            .withPaymentState(
                (order.isOfflinePayment() ? PaymentState.AUTHORISED : order.getPaymentStatus())
                    .toPaymentStatusUpdatedPaymentState())
            .withTimestamp(ZonedDateTime.now(ZoneOffset.UTC))
            .withPayload(isAuthorized ? generatePayload(order) : null);
    }

    private AuthorizedPayload generatePayload(Order order) {
        return AuthorizedPayload.builder()
                .payments(generatePayments(order))
                .orderDetails(generateOrderDetails(order))
                .orderLines(generateOrderLines(order))
                .build();
    }

    private List<OrderLine> generateOrderLines(Order order) {
        return order.getOrderLines().stream()
                .map(orderLine -> OrderLine.builder()
                        .ean(orderLine.getEan())
                        .retailPrice(Optional.ofNullable(orderLine.getOrderLinePaidAmount())
                            .map(OrderEntryAmount::getGrossRetailUnitPrice)
                            .orElse(orderLine.getStandardRetailPrice()))
                        .taxPercentage(orderLine.getTaxRate())
                        .discountValue(Optional.ofNullable(orderLine.getOrderLinePaidAmount())
                            .map(OrderEntryAmount::getUnitDiscount)
                            .orElse(null))
                        .build())
                .toList();
    }

    private List<Payment> generatePayments(Order order) {
        return order.getPayments().stream().map(payment -> {
            BigDecimal amount = null;
            try {
                amount = new BigDecimal(payment.getAuthorisedAmount());
            } catch (NumberFormatException ignored) {
                // ignored
            }
            return Payment.builder()
                    .amount(amount)
                    .name(payment.getType().name())
                    .subMethod(payment.getSubMethodName() == null ? null : payment.getSubMethodName().name())
                    .build();
        }).toList();
    }

    private OrderDetails generateOrderDetails(Order order) {
        var orderCharges = order.getOrderCharges().stream()
                .filter(orderCharge -> orderCharge.getType().equals(EntryType.SHIPMENT_FEE))
                .findFirst();
        var shippingFees = orderCharges
                .map(orderCharge -> orderCharge.getChargeTotal().getGrossDiscountedTotal())
                .orElse(BigDecimal.ZERO);
        var shippingFeesTaxPercentage = orderCharges
                .map(OrderEntry::getTaxRate)
                .orElse(BigDecimal.ZERO);
        var shippingFeesCancelled = orderCharges
                .map(OrderCharge::getCancelled)
                .orElse(Boolean.FALSE);

        return OrderDetails.builder()
                .orderValue(order.getTotalPaidPrice().getGrossDiscountedTotal())
                .shippingFees(shippingFees)
                .shippingFeesTaxPercentage(shippingFeesTaxPercentage)
                .shippingFeesCancelled(shippingFeesCancelled)
                .build();
    }
}
