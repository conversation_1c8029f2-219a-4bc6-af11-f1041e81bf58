package com.bestseller.payment.adapter.stream.validator.idempotency;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRejected.PaymentRejected;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import org.springframework.stereotype.Component;

@Component
public class PaymentRejectedIdempotencyCheck extends AbstractPaymentEventsIdempotencyCheck<PaymentRejected> {

    public PaymentRejectedIdempotencyCheck(OrderRepository orderRepository) {
        super(orderRepository);
    }

    @Override
    protected PaymentState getTargetPaymentStatus() {
        return PaymentState.CANCELLED;
    }

    @Override
    protected String getMessageKey(PaymentRejected message) {
        return message.getOrderId();
    }
}
