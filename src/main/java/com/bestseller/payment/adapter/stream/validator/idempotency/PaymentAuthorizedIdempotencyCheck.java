package com.bestseller.payment.adapter.stream.validator.idempotency;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentAuthorized.PaymentAuthorized;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import org.springframework.stereotype.Component;

@Component
public class PaymentAuthorizedIdempotencyCheck extends AbstractPaymentEventsIdempotencyCheck<PaymentAuthorized> {

    public PaymentAuthorizedIdempotencyCheck(OrderRepository orderRepository) {
        super(orderRepository);
    }

    protected PaymentState getTargetPaymentStatus() {
        return PaymentState.AUTHORISED;
    }

    protected String getMessageKey(PaymentAuthorized message) {
        return message.getOrderId();
    }
}
