package com.bestseller.payment.adapter.stream.validator.idempotency;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import lombok.AllArgsConstructor;

import java.util.Optional;

@AllArgsConstructor
public abstract class AbstractPaymentEventsIdempotencyCheck<T> implements IdempotencyChecker<T> {
    private final OrderRepository orderRepository;

    protected abstract PaymentState getTargetPaymentStatus();

    protected abstract String getMessageKey(T message);

    @Override
    public boolean isDuplicate(T message) {
        Optional<Order> optionalOrder = orderRepository.findById(this.getMessageKey(message));
        if (optionalOrder.isEmpty()) {
            return false;
        }
        return optionalOrder
            .filter(order -> order.getPaymentStatus().isGreaterOrEqual(getTargetPaymentStatus()))
            .isPresent();
    }

}
