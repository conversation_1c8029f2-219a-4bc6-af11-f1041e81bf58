package com.bestseller.payment.adapter.stream.consumer;

import com.bestseller.dbqueue.consumer.AbstractRetryableConsumer;
import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.payment.core.service.order.OrderService;
import org.springframework.classify.BinaryExceptionClassifier;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.stereotype.Component;

@Component
public class ValidOrderPlacedConsumer extends AbstractRetryableConsumer<ValidOrderPlaced> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s";
    private static final BinaryExceptionClassifier EXCEPTION_CLASSIFIER = BinaryExceptionClassifier.builder()
        .retryOn(TransientDataAccessException.class)
        .build();

    private final OrderService orderService;

    public ValidOrderPlacedConsumer(
        MessageFilter<ValidOrderPlaced> messageFilter,
        IdempotencyChecker<ValidOrderPlaced> idempotencyChecker,
        MessageValidator<ValidOrderPlaced> messageValidator,
        OrderService orderService,
        QueueProducer<ValidOrderPlaced> queueProducer
    ) {
        super(messageFilter, messageValidator, idempotencyChecker, EXCEPTION_CLASSIFIER, queueProducer);
        this.orderService = orderService;
    }

    @Override
    public void consume(ValidOrderPlaced message) {
        orderService.processValidOrderPlaced(message);
    }

    @Override
    protected String getMessageDetails(ValidOrderPlaced message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId());
    }

    @Override
    protected String getMessageKey(ValidOrderPlaced message) {
        return message.getOrderId();
    }
}
