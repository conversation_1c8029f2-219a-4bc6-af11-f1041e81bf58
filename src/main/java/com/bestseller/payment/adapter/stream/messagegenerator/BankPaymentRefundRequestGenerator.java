package com.bestseller.payment.adapter.stream.messagegenerator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequest.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequest.PaymentRefundRequest;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderCharge;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.RefundLine;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.dto.RefundDto;
import com.bestseller.payment.core.utils.PaymentUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class BankPaymentRefundRequestGenerator implements MessageGenerator<RefundDto, PaymentRefundRequest> {

    private static final BigDecimal BD_100 = new BigDecimal(100);

    @Override
    public PaymentRefundRequest generate(RefundDto data) {
        final var order = data.refund().getOrder();
        final var refund = data.refund();
        final var refundAmountInCents = data.refundAmountInCents();
        final var payment = getPayment(order);
        final var orderLines = new ArrayList<OrderLine>();

        orderLines.addAll(convertRefundLines(refund.getRefundLines()));
        orderLines.addAll(convertRefundCharges(refund.getRefundCharges()));

        return new PaymentRefundRequest()
            .withCorrelationId(refund.getId().toString())
            .withTotalAmount(refundAmountInCents)
            .withCurrency(order.getCurrency())
            .withOrderId(order.getOrderId())
            .withProvider(payment.getProcessorId().name())
            .withPspPaymentReference(payment.getPaymentReference())
            .withBillingCountry(order.getBillingCountryCode())
            .withReason(data.refundReason().name())
            .withBrand(order.getBrand().getBrandAbbreviation())
            .withOrderLines(orderLines);
    }

    private PaymentInfo getPayment(Order order) {
        return order.getPayments().stream()
            .filter(payment -> !PaymentType.GIFTCARD.equals(payment.getType()))
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("No non-gift card payment found for order: " + order.getOrderId()));
    }

    private List<OrderLine> convertRefundLines(List<RefundLine> refundLines) {
        return refundLines.stream()
            .map(refundLine -> new OrderLine()
                .withEan(refundLine.getOrderLine().getEan())
                .withDescription(refundLine.getOrderLine().getName())
                .withPaidAmount(PaymentUtils.toIntCents(refundLine.getRefundLineTotal().getGrossDiscountedUnitPrice()))
                .withTaxRate(refundLine.getOrderLine().getTaxRate().multiply(BD_100).multiply(BD_100).intValue())
                .withTaxAmount(PaymentUtils.toIntCents(refundLine.getRefundLineTotal().getUnitVAT()))
                .withQuantity(refundLine.getQuantity())
            ).collect(Collectors.toList());
    }

    private List<OrderLine> convertRefundCharges(List<OrderCharge> refundCharges) {
        return refundCharges.stream()
            .map(refundCharge -> new OrderLine()
                .withEan(refundCharge.getName())
                .withDescription(refundCharge.getName())
                .withPaidAmount(PaymentUtils.toIntCents(refundCharge.getChargeTotal().getGrossDiscountedUnitPrice()))
                .withQuantity(refundCharge.getOpenQty())
                .withTaxRate(refundCharge.getTaxRate().multiply(BD_100).multiply(BD_100).intValue())
                .withTaxAmount(PaymentUtils.toIntCents(refundCharge.getChargeTotal().getUnitVAT()))
            ).collect(Collectors.toList());
    }
}
