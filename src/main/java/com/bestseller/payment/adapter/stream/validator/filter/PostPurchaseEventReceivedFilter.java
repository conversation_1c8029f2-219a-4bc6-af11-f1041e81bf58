package com.bestseller.payment.adapter.stream.validator.filter;

import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.postPurchaseEventReceived.PostPurchaseEventReceived;
import com.bestseller.interfacecontractannotator.model.postpurchaseeventreceived.ReturnCreatedPayload;
import org.springframework.stereotype.Component;

@Component
public class PostPurchaseEventReceivedFilter implements MessageFilter<PostPurchaseEventReceived> {

    @Override
    public boolean filter(PostPurchaseEventReceived message) {
        return !(message.getData() instanceof ReturnCreatedPayload);
    }
}
