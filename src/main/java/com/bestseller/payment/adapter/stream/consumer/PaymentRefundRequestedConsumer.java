package com.bestseller.payment.adapter.stream.consumer;

import com.bestseller.dbqueue.consumer.AbstractRetryableConsumer;
import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequested.PaymentRefundRequested;
import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;
import org.springframework.classify.BinaryExceptionClassifier;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.stereotype.Component;

@Component
public class PaymentRefundRequestedConsumer extends AbstractRetryableConsumer<PaymentRefundRequested> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "correlationId=%s";
    private static final BinaryExceptionClassifier EXCEPTION_CLASSIFIER = BinaryExceptionClassifier.builder()
        .retryOn(TransientDataAccessException.class)
        .build();

    private final RefundService refundService;

    public PaymentRefundRequestedConsumer(
        MessageFilter<PaymentRefundRequested> messageFilter,
        IdempotencyChecker<PaymentRefundRequested> idempotencyChecker,
        MessageValidator<PaymentRefundRequested> messageValidator,
        RefundService refundService,
        QueueProducer<PaymentRefundRequested> queueProducer
    ) {
        super(messageFilter, messageValidator, idempotencyChecker, EXCEPTION_CLASSIFIER, queueProducer);
        this.refundService = refundService;
    }

    @Override
    public void consume(PaymentRefundRequested message) {
        refundService.updateRefundStatus(Integer.parseInt(message.getCorrelationId()), RefundState.REFUND_REQUESTED);
    }

    @Override
    protected String getMessageDetails(PaymentRefundRequested message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getCorrelationId());
    }

    @Override
    protected String getMessageKey(PaymentRefundRequested message) {
        return message.getCorrelationId();
    }
}
