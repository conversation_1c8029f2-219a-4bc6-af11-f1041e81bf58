package com.bestseller.payment.adapter.stream.validator;

import com.bestseller.dbqueue.consumer.wrapper.BaseMessageValidator;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementSucceeded.PaymentSettlementSucceeded;
import jakarta.validation.Validator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PaymentSettlementSucceededMessageValidator extends BaseMessageValidator<PaymentSettlementSucceeded> {

    private final PaymentValidation paymentValidation;

    protected PaymentSettlementSucceededMessageValidator(Validator validator, PaymentValidation paymentValidation) {
        super(validator);
        this.paymentValidation = paymentValidation;
    }

    @Override
    public boolean passesCustomValidation(PaymentSettlementSucceeded paymentSettlementSucceeded) {
        return paymentValidation.isSettlementSupported(paymentSettlementSucceeded.getProvider());
    }
}
