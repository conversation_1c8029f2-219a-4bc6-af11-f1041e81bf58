package com.bestseller.payment.adapter.stream.validator;

import com.bestseller.dbqueue.consumer.wrapper.BaseMessageValidator;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.postPurchaseEventReceived.PostPurchaseEventReceived;
import com.bestseller.interfacecontractannotator.model.postpurchaseeventreceived.ReturnCreatedPayload;
import com.bestseller.interfacecontractannotator.model.postpurchaseeventreceived.ReturnRequest;
import jakarta.validation.Validator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class PostPurchaseEventReceivedMessageValidator extends BaseMessageValidator<PostPurchaseEventReceived> {

    protected PostPurchaseEventReceivedMessageValidator(Validator validator) {
        super(validator);
    }

    @Override
    public boolean passesCustomValidation(PostPurchaseEventReceived message) {
        ReturnCreatedPayload payload = (ReturnCreatedPayload) message.getData();
        // there is no constraint annotation on ReturnCreatedPayload for not null checks,
        // so we did it manually here to avoid unnecessary validation errors

        if (payload == null) {
            return false;
        }

        if (payload.getReturnId() == null || payload.getReturnId().isBlank()) {
            log.warn("ReturnCreatedPayload returnId is null or blank");
            return false;
        }
        if (payload.getRefundMethod() == null) {
            return false;
        }
        List<ReturnRequest> returnRequests = payload.getReturnRequest();
        if (returnRequests == null || returnRequests.isEmpty()) {
            log.warn("ReturnCreatedPayload returnRequest is null or empty");
            return false;
        }
        for (ReturnRequest req : returnRequests) {
            if (req.getEan() == null || req.getEan().isBlank()) {
                log.warn("ReturnRequest ean is null or blank");
                return false;
            }
            if (req.getQuantity() == null || req.getQuantity() <= 0) {
                log.warn("ReturnRequest quantity is null or not greater than 0");
                return false;
            }
        }
        return true;
    }
}

