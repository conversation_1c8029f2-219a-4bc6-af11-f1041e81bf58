package com.bestseller.payment.adapter.stream.consumer;

import com.bestseller.dbqueue.consumer.AbstractRetryableConsumer;
import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundSucceeded.PaymentRefundSucceeded;
import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;
import org.springframework.classify.BinaryExceptionClassifier;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.stereotype.Component;

@Component
public class PaymentRefundSucceededConsumer extends AbstractRetryableConsumer<PaymentRefundSucceeded> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "correlationId=%s";
    private static final BinaryExceptionClassifier EXCEPTION_CLASSIFIER = BinaryExceptionClassifier.builder()
        .retryOn(TransientDataAccessException.class)
        .build();

    private final RefundService refundService;

    public PaymentRefundSucceededConsumer(
        MessageFilter<PaymentRefundSucceeded> messageFilter,
        IdempotencyChecker<PaymentRefundSucceeded> idempotencyChecker,
        MessageValidator<PaymentRefundSucceeded> messageValidator,
        RefundService refundService,
        QueueProducer<PaymentRefundSucceeded> queueProducer
    ) {
        super(messageFilter, messageValidator, idempotencyChecker, EXCEPTION_CLASSIFIER, queueProducer);
        this.refundService = refundService;
    }

    @Override
    public void consume(PaymentRefundSucceeded message) {
        refundService.updateRefundStatus(Integer.parseInt(message.getCorrelationId()), RefundState.REFUND_SUCCESS);
    }

    @Override
    protected String getMessageDetails(PaymentRefundSucceeded message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getCorrelationId());
    }

    @Override
    protected String getMessageKey(PaymentRefundSucceeded message) {
        return message.getCorrelationId();
    }
}
