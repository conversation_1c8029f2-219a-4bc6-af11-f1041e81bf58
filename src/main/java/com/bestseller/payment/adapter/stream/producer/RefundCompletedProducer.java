package com.bestseller.payment.adapter.stream.producer;

import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCompleted.RefundCompleted;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.stereotype.Component;

@Component
public class RefundCompletedProducer extends AbstractKafkaMessageProducer<RefundCompleted> {

    private static final Logger LOGGER = LoggerFactory.getLogger(RefundCompletedProducer.class);
    private static final String BINDING_NAME = "refundCompletedProducer-out-0";
    private static final String MESSAGE_DETAILS_TEMPLATE = "refundId=%s, refundStatus=%s";

    public RefundCompletedProducer(StreamBridge streamBridge) {
        super(BINDING_NAME, streamBridge, LOGGER);
    }

    @Override
    protected String getMessageDetails(RefundCompleted message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getRefundId(), message.getRefundStatus());
    }
}
