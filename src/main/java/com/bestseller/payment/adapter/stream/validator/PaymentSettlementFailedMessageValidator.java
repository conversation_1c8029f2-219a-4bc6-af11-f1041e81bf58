package com.bestseller.payment.adapter.stream.validator;

import com.bestseller.dbqueue.consumer.wrapper.BaseMessageValidator;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementFailed.PaymentSettlementFailed;
import jakarta.validation.Validator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PaymentSettlementFailedMessageValidator extends BaseMessageValidator<PaymentSettlementFailed> {

    private final PaymentValidation paymentValidation;

    protected PaymentSettlementFailedMessageValidator(Validator validator, PaymentValidation paymentValidation) {
        super(validator);
        this.paymentValidation = paymentValidation;
    }

    @Override
    public boolean passesCustomValidation(PaymentSettlementFailed paymentSettlementFailed) {
        return paymentValidation.isSettlementSupported(paymentSettlementFailed.getProvider());
    }
}
