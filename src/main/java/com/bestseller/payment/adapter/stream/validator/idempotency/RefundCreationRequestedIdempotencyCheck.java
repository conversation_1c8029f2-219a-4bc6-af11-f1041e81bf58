package com.bestseller.payment.adapter.stream.validator.idempotency;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.RefundCreationRequested;
import com.bestseller.payment.adapter.repository.RefundRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@AllArgsConstructor
@Component
public class RefundCreationRequestedIdempotencyCheck implements IdempotencyChecker<RefundCreationRequested> {

    private final RefundRepository refundRepository;

    @Override
    public boolean isDuplicate(RefundCreationRequested message) {
        return refundRepository.existsByOrderIdAndRequestId(message.getOrderId(),
            message.getRefundCreationRequestedId().toString());
    }

}
