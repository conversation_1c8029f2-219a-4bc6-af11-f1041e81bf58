package com.bestseller.payment.adapter.stream.validator.idempotency;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public abstract class AbstractRefundEventsIdempotencyCheck<T> implements IdempotencyChecker<T> {
    private final RefundService refundService;

    protected abstract RefundState getTargetRefundStatus();

    protected abstract String getMessageKey(T message);

    @Override
    public boolean isDuplicate(T message) {
        int refundId = Integer.parseInt(this.getMessageKey(message));
        return refundService.isDuplicateRequest(refundId, getTargetRefundStatus());
    }

}
