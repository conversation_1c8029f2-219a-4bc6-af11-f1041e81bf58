package com.bestseller.payment.adapter.stream.validator.idempotency;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundFailed.PaymentRefundFailed;
import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;
import org.springframework.stereotype.Component;

@Component
public class PaymentRefundFailedIdempotencyCheckService extends AbstractRefundEventsIdempotencyCheck<PaymentRefundFailed> {

    public PaymentRefundFailedIdempotencyCheckService(RefundService refundService) {
        super(refundService);
    }

    @Override
    protected RefundState getTargetRefundStatus() {
        return RefundState.REFUND_FAILED;
    }

    protected String getMessageKey(PaymentRefundFailed message) {
        return message.getCorrelationId();
    }
}
