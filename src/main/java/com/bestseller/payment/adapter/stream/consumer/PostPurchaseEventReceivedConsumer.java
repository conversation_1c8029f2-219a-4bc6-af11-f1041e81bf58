package com.bestseller.payment.adapter.stream.consumer;

import com.bestseller.dbqueue.consumer.AbstractRetryableConsumer;
import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.postPurchaseEventReceived.PostPurchaseEventReceived;
import com.bestseller.interfacecontractannotator.model.postpurchaseeventreceived.ReturnCreatedPayload;
import com.bestseller.payment.core.service.customerchoice.CustomerRefundChoiceService;
import org.springframework.classify.BinaryExceptionClassifier;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.stereotype.Component;

@Component
public class PostPurchaseEventReceivedConsumer extends AbstractRetryableConsumer<PostPurchaseEventReceived> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, returnId=%s, refundMethod=%s";
    private static final String UNKNOWN = "Unknown";
    private static final BinaryExceptionClassifier EXCEPTION_CLASSIFIER = BinaryExceptionClassifier.builder()
        .retryOn(TransientDataAccessException.class)
        .build();

    private final CustomerRefundChoiceService customerRefundChoiceService;

    public PostPurchaseEventReceivedConsumer(
        MessageFilter<PostPurchaseEventReceived> messageFilter,
        MessageValidator<PostPurchaseEventReceived> messageValidator,
        IdempotencyChecker<PostPurchaseEventReceived> idempotencyChecker,
        CustomerRefundChoiceService customerRefundChoiceService,
        QueueProducer<PostPurchaseEventReceived> queueProducer
    ) {
        super(messageFilter, messageValidator, idempotencyChecker, EXCEPTION_CLASSIFIER, queueProducer);
        this.customerRefundChoiceService = customerRefundChoiceService;
    }

    @Override
    public void consume(PostPurchaseEventReceived message) {
        customerRefundChoiceService.processPostPurchaseEvent(message);
    }

    @Override
    protected String getMessageDetails(PostPurchaseEventReceived message) {
        if (message.getData() instanceof ReturnCreatedPayload payload) {
            return MESSAGE_DETAILS_TEMPLATE.formatted(
                message.getOrderId(),
                payload.getReturnId(),
                payload.getRefundMethod()
            );
        }
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), UNKNOWN, UNKNOWN);
    }

    @Override
    protected String getMessageKey(PostPurchaseEventReceived message) {
        return message.getOrderId();
    }
}
