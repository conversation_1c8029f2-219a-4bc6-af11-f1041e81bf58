package com.bestseller.payment.adapter.stream.validator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundFailed.PaymentRefundFailed;
import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;
import jakarta.validation.Validator;
import org.springframework.stereotype.Component;

@Component
public class PaymentRefundFailedMessageValidator extends AbstractRefundEventsMessageValidator<PaymentRefundFailed> {

    public PaymentRefundFailedMessageValidator(Validator validator, RefundService refundService) {
        super(validator, refundService);
    }

    @Override
    protected String getCorrelationId(PaymentRefundFailed message) {
        return message.getCorrelationId();
    }

    @Override
    protected RefundState getTargetRefundState() {
        return RefundState.REFUND_FAILED;
    }
}
