package com.bestseller.payment.adapter.stream.producer;

import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementRequest.PaymentSettlementRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.stereotype.Component;

@Component
public class PaymentSettlementRequestProducer extends AbstractKafkaMessageProducer<PaymentSettlementRequest> {

    private static final Logger LOGGER = LoggerFactory.getLogger(PaymentSettlementRequestProducer.class);
    private static final String BINDING_NAME = "paymentSettlementRequestProducer-out-0";
    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, correlationId=%s";

    public PaymentSettlementRequestProducer(StreamBridge streamBridge) {
        super(BINDING_NAME, streamBridge, LOGGER);
    }

    @Override
    protected String getMessageDetails(PaymentSettlementRequest message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getCorrelationId());
    }
}
