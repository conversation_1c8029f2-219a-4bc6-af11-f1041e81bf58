package com.bestseller.payment.adapter.stream.validator;

import com.bestseller.dbqueue.consumer.wrapper.BaseMessageValidator;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRejected.PaymentRejected;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.service.payment.PaymentService;
import com.bestseller.payment.core.validation.PaymentAuthorizationValidator;
import jakarta.validation.Validator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PaymentRejectedMessageValidator extends BaseMessageValidator<PaymentRejected> {

    private final PaymentAuthorizationValidator paymentAuthorizationValidator;
    private final PaymentService paymentService;

    public PaymentRejectedMessageValidator(Validator validator,
                                           PaymentAuthorizationValidator paymentAuthorizationValidator,
                                           PaymentService paymentService) {
        super(validator);
        this.paymentAuthorizationValidator = paymentAuthorizationValidator;
        this.paymentService = paymentService;
    }

    @Override
    public boolean passesCustomValidation(PaymentRejected paymentRejected) {
        if (!paymentAuthorizationValidator.validate(
            paymentRejected.getOrderId(),
            paymentRejected.getProvider(),
            paymentRejected.getPspReference())) {
            return false;
        }

        if (!validatePaymentStatus(paymentRejected)) {
            log.warn("Payment status is not in {} state for order: {}. Skipping the message.",
                PaymentState.REVIEW, paymentRejected.getOrderId());
            return false;
        }
        return true;
    }

    private boolean validatePaymentStatus(PaymentRejected paymentRejected) {
        return paymentService.checkPaymentStatus(paymentRejected.getOrderId(), PaymentState.REVIEW);
    }
}
