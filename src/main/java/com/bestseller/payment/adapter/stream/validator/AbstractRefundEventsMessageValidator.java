package com.bestseller.payment.adapter.stream.validator;

import com.bestseller.dbqueue.consumer.wrapper.BaseMessageValidator;
import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;
import jakarta.validation.Validator;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class AbstractRefundEventsMessageValidator<T> extends BaseMessageValidator<T> {

    private final RefundService refundService;

    public AbstractRefundEventsMessageValidator(Validator validator, RefundService refundService) {
        super(validator);
        this.refundService = refundService;
    }

    protected abstract String getCorrelationId(T message);

    protected abstract RefundState getTargetRefundState();

    /**
     * Validates the message.
     *
     * @param message the message to validate
     * @return true if the message is valid, false otherwise
     */
    @Override
    public boolean passesCustomValidation(T message) {
        final String topicName = message.getClass().getSimpleName();
        final String correlationId = getCorrelationId(message);

        try {
            var refundId = Integer.parseInt(correlationId);
            if (!refundService.isExists(refundId)) {
                log.error("Refund with id '{}' does not exist.", refundId);
                return false;
            }
            boolean isValid = refundService.isStateTransitionAllowed(refundId, getTargetRefundState());
            if (!isValid) {
                log.warn("Transition to '{}' state is not allowed for refund with id '{}'.",
                    getTargetRefundState(), refundId);
            }
            return isValid;
        } catch (NumberFormatException e) {
            log.warn("CorrelationId '{}' is not a number in {}", correlationId, topicName);
            return false;
        }
    }
}
