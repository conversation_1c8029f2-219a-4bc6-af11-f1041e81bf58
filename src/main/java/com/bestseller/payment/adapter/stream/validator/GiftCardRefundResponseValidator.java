package com.bestseller.payment.adapter.stream.validator;

import com.bestseller.dbqueue.consumer.wrapper.BaseMessageValidator;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.giftCardRefundResponse.GiftCardRefundResponse;
import jakarta.validation.Validator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Slf4j
@Component
public class GiftCardRefundResponseValidator extends BaseMessageValidator<GiftCardRefundResponse> {

    protected GiftCardRefundResponseValidator(Validator validator) {
        super(validator);
    }

    @Override
    public boolean passesCustomValidation(GiftCardRefundResponse message) {
        try {
            UUID.fromString(message.getCorrelationId());
            return true;
        } catch (IllegalArgumentException e) {
            log.warn("CorrelationId is not a valid UUID: {}. orderId={}",
                message.getCorrelationId(), message.getOrderId());
            return false;
        }
    }
}
