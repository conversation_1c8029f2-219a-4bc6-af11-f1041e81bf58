package com.bestseller.payment.adapter.stream.messagegenerator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCompleted.RefundCompleted;
import com.bestseller.payment.core.domain.Refund;
import com.logistics.statetransition.RefundState;
import org.springframework.stereotype.Service;

@Service
public class RefundCompletedGenerator implements MessageGenerator<Refund, RefundCompleted> {
    @Override
    public RefundCompleted generate(Refund refund) {
        return new RefundCompleted()
            .withRefundId(Long.parseLong(refund.getRequestId()))
            .withRefundStatus(refund.getRefundState().getDescription())
            .withRefundSucceeded(RefundState.REFUND_SUCCESS.equals(refund.getRefundState()));
    }
}
