package com.bestseller.payment.adapter.stream.validator.idempotency;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementSucceeded.PaymentSettlementSucceeded;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import org.springframework.stereotype.Component;

@Component
public class PaymentSettlementSucceededIdempotencyCheck
    extends AbstractPaymentEventsIdempotencyCheck<PaymentSettlementSucceeded> {

    public PaymentSettlementSucceededIdempotencyCheck(OrderRepository orderRepository) {
        super(orderRepository);
    }

    @Override
    protected PaymentState getTargetPaymentStatus() {
        return PaymentState.SETTLED;
    }

    @Override
    protected String getMessageKey(PaymentSettlementSucceeded message) {
        return message.getCorrelationId();
    }
}
