package com.bestseller.payment.adapter.stream.consumer;

import com.bestseller.dbqueue.consumer.AbstractRetryableConsumer;
import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.giftCardRefundResponse.GiftCardRefundResponse;
import com.bestseller.payment.core.service.refund.RefundService;
import org.springframework.classify.BinaryExceptionClassifier;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.stereotype.Component;

@Component
public class GiftCardRefundResponseConsumer extends AbstractRetryableConsumer<GiftCardRefundResponse> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, correlationId=%s, status=%s";
    private static final BinaryExceptionClassifier EXCEPTION_CLASSIFIER = BinaryExceptionClassifier.builder()
        .retryOn(TransientDataAccessException.class)
        .build();

    private final RefundService refundService;

    @SuppressWarnings("MissingJavadocMethod")
    public GiftCardRefundResponseConsumer(
        MessageFilter<GiftCardRefundResponse> messageFilter,
        IdempotencyChecker<GiftCardRefundResponse> idempotencyChecker,
        MessageValidator<GiftCardRefundResponse> messageValidator,
        RefundService refundService,
        QueueProducer<GiftCardRefundResponse> queueProducer
    ) {
        super(messageFilter, messageValidator, idempotencyChecker, EXCEPTION_CLASSIFIER, queueProducer);
        this.refundService = refundService;
    }

    @Override
    public void consume(GiftCardRefundResponse message) {
        refundService.processGiftCardRefundResponse(message);
    }

    @Override
    protected String getMessageDetails(GiftCardRefundResponse message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(
            message.getOrderId(), message.getCorrelationId(), message.getStatus());
    }

    @Override
    protected String getMessageKey(GiftCardRefundResponse message) {
        return message.getOrderId();
    }
}
