package com.bestseller.payment.adapter.stream.consumer;

import com.bestseller.dbqueue.consumer.AbstractRetryableConsumer;
import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderFinalized;
import com.bestseller.payment.core.service.order.OrderService;
import org.springframework.classify.BinaryExceptionClassifier;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.stereotype.Component;

@Component
public class OrderFinalizedConsumer extends AbstractRetryableConsumer<OrderFinalized> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, orderLines=%s";
    private static final BinaryExceptionClassifier EXCEPTION_CLASSIFIER = BinaryExceptionClassifier.builder()
        .retryOn(TransientDataAccessException.class)
        .build();

    private final OrderService orderService;

    @SuppressWarnings("MissingJavadocMethod")
    public OrderFinalizedConsumer(
        MessageFilter<OrderFinalized> messageFilter,
        IdempotencyChecker<OrderFinalized> idempotencyChecker,
        MessageValidator<OrderFinalized> messageValidator,
        OrderService orderService,
        QueueProducer<OrderFinalized> queueProducer
    ) {
        super(messageFilter, messageValidator, idempotencyChecker, EXCEPTION_CLASSIFIER, queueProducer);
        this.orderService = orderService;
    }

    @Override
    public void consume(OrderFinalized message) {
        orderService.processOrderFinalizedMessage(message);
    }

    @Override
    protected String getMessageDetails(OrderFinalized message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getOrderLines());
    }

    @Override
    protected String getMessageKey(OrderFinalized message) {
        return message.getOrderId();
    }
}
