package com.bestseller.payment.adapter.stream.consumer;

import com.bestseller.dbqueue.consumer.AbstractRetryableConsumer;
import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRejected.PaymentRejected;
import com.bestseller.payment.core.exception.OrderNotFoundException;
import com.bestseller.payment.core.service.order.OrderService;
import org.springframework.classify.BinaryExceptionClassifier;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.stereotype.Component;

@Component
public class PaymentRejectedConsumer extends AbstractRetryableConsumer<PaymentRejected> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s";
    private static final BinaryExceptionClassifier EXCEPTION_CLASSIFIER = BinaryExceptionClassifier.builder()
        .retryOn(TransientDataAccessException.class)
        .retryOn(OrderNotFoundException.class)
        .build();

    private final OrderService orderService;

    public PaymentRejectedConsumer(
        MessageFilter<PaymentRejected> messageFilter,
        IdempotencyChecker<PaymentRejected> idempotencyChecker,
        MessageValidator<PaymentRejected> messageValidator,
        OrderService orderService,
        QueueProducer<PaymentRejected> queueProducer
    ) {
        super(messageFilter, messageValidator, idempotencyChecker, EXCEPTION_CLASSIFIER, queueProducer);
        this.orderService = orderService;
    }

    @Override
    public void consume(PaymentRejected message) {
        orderService.cancelOrder(message.getOrderId());
    }

    @Override
    protected String getMessageDetails(PaymentRejected message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId());
    }

    @Override
    protected String getMessageKey(PaymentRejected message) {
        return message.getOrderId();
    }
}
