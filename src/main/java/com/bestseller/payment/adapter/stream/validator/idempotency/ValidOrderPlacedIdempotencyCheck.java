package com.bestseller.payment.adapter.stream.validator.idempotency;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.payment.adapter.repository.OrderRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ValidOrderPlacedIdempotencyCheck implements IdempotencyChecker<ValidOrderPlaced> {

    private final OrderRepository orderRepository;

    @Override
    public boolean isDuplicate(ValidOrderPlaced message) {
        return orderRepository.existsById(message.getOrderId());
    }

}
