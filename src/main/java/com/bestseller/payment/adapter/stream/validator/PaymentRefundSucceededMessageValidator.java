package com.bestseller.payment.adapter.stream.validator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundSucceeded.PaymentRefundSucceeded;
import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;
import jakarta.validation.Validator;
import org.springframework.stereotype.Component;

@Component
public class PaymentRefundSucceededMessageValidator
    extends AbstractRefundEventsMessageValidator<PaymentRefundSucceeded> {

    public PaymentRefundSucceededMessageValidator(
        Validator validator,
        RefundService refundService) {
        super(validator, refundService);
    }

    @Override
    protected String getCorrelationId(PaymentRefundSucceeded message) {
        return message.getCorrelationId();
    }

    @Override
    protected RefundState getTargetRefundState() {
        return RefundState.REFUND_SUCCESS;
    }
}
