package com.bestseller.payment.adapter.stream.validator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequested.PaymentRefundRequested;
import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;
import jakarta.validation.Validator;
import org.springframework.stereotype.Component;

@Component
public class PaymentRefundRequestedMessageValidator
    extends AbstractRefundEventsMessageValidator<PaymentRefundRequested> {

    public PaymentRefundRequestedMessageValidator(
        Validator validator,
        RefundService refundService) {
        super(validator, refundService);
    }

    @Override
    protected String getCorrelationId(PaymentRefundRequested message) {
        return message.getCorrelationId();
    }

    @Override
    protected RefundState getTargetRefundState() {
        return RefundState.REFUND_REQUESTED;
    }
}
