package com.bestseller.payment.adapter.stream.producer;

import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundStatusUpdated.RefundStatusUpdated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.stereotype.Component;

@Component
public class RefundStatusUpdatedProducer extends AbstractKafkaMessageProducer<RefundStatusUpdated> {

    private static final Logger LOGGER = LoggerFactory.getLogger(RefundStatusUpdatedProducer.class);
    private static final String BINDING_NAME = "refundStatusUpdatedProducer-out-0";
    private static final String MESSAGE_DETAILS_TEMPLATE = "refundId=%s, status=%s";

    public RefundStatusUpdatedProducer(StreamBridge streamBridge) {
        super(BINDING_NAME, streamBridge, LOGGER);
    }

    @Override
    protected String getMessageDetails(RefundStatusUpdated message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getRefundId(), message.getStatus());
    }
}
