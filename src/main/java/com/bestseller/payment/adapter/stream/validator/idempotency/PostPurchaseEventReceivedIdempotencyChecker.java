package com.bestseller.payment.adapter.stream.validator.idempotency;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.postPurchaseEventReceived.PostPurchaseEventReceived;
import com.bestseller.interfacecontractannotator.model.postpurchaseeventreceived.ReturnCreatedPayload;
import com.bestseller.payment.adapter.repository.CustomerRefundChoiceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class PostPurchaseEventReceivedIdempotencyChecker implements IdempotencyChecker<PostPurchaseEventReceived> {

    private final CustomerRefundChoiceRepository customerRefundChoiceRepository;

    /**
     * Checks if the message is a duplicate based on the order ID and return ID.
     *
     * @param message The PostPurchaseEventReceived message to check.
     * @return true if a duplicate exists, false otherwise.
     * here we assumed customer can't have multiple refund choices for the same ean and quantity
     * so as long as the item has quantity customer can create label but when there is no quantity left
     * the customer can't create label for the same item again
     */

    @Override
    public boolean isDuplicate(PostPurchaseEventReceived message) {
        ReturnCreatedPayload payload = (ReturnCreatedPayload) message.getData();
        return customerRefundChoiceRepository.existsByOrderIdAndReturnId(
            message.getOrderId(),
            payload.getReturnId()
        );
    }
}
