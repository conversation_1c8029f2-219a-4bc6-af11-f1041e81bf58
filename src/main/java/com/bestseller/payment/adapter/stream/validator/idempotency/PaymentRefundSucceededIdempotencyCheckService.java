package com.bestseller.payment.adapter.stream.validator.idempotency;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundSucceeded.PaymentRefundSucceeded;
import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;
import org.springframework.stereotype.Component;

@Component
public class PaymentRefundSucceededIdempotencyCheckService
    extends AbstractRefundEventsIdempotencyCheck<PaymentRefundSucceeded> {

    public PaymentRefundSucceededIdempotencyCheckService(RefundService refundService) {
        super(refundService);
    }

    @Override
    protected RefundState getTargetRefundStatus() {
        return RefundState.REFUND_SUCCESS;
    }

    @Override
    protected String getMessageKey(PaymentRefundSucceeded message) {
        return message.getCorrelationId();
    }
}
