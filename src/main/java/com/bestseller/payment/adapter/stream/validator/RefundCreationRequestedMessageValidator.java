package com.bestseller.payment.adapter.stream.validator;

import com.bestseller.dbqueue.consumer.wrapper.BaseMessageValidator;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.RefundCreationRequested;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.exception.OrderNotFoundException;
import jakarta.validation.Validator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class RefundCreationRequestedMessageValidator extends BaseMessageValidator<RefundCreationRequested> {

    private final OrderRepository orderRepository;
    private final PaymentValidation paymentValidation;

    protected RefundCreationRequestedMessageValidator(Validator validator,
                                                      OrderRepository orderRepository,
                                                      PaymentValidation paymentValidation) {
        super(validator);
        this.orderRepository = orderRepository;
        this.paymentValidation = paymentValidation;
    }

    @Override
    public boolean passesCustomValidation(RefundCreationRequested refundCreationRequested) {
        String orderId = refundCreationRequested.getOrderId();
        Order order = orderRepository.findById(orderId).orElseThrow(() -> {
            log.error("Order with id {} not found", orderId);
            return new OrderNotFoundException(orderId);
        });

        return paymentValidation.validateRefundRequest(order);
    }
}
