package com.bestseller.payment.adapter.stream.producer;

import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.stereotype.Component;

@Component
public class PaymentStatusUpdatedProducer extends AbstractKafkaMessageProducer<PaymentStatusUpdated> {

    private static final Logger LOGGER = LoggerFactory.getLogger(PaymentStatusUpdatedProducer.class);
    private static final String BINDING_NAME = "paymentStatusUpdatedProducer-out-0";
    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, status=%s";

    public PaymentStatusUpdatedProducer(StreamBridge streamBridge) {
        super(BINDING_NAME, streamBridge, LOGGER);
    }

    @Override
    protected String getMessageDetails(PaymentStatusUpdated message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getPaymentState());
    }
}
