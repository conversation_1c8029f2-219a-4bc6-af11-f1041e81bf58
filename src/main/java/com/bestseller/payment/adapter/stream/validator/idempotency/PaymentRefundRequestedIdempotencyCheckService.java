package com.bestseller.payment.adapter.stream.validator.idempotency;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequested.PaymentRefundRequested;
import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;
import org.springframework.stereotype.Component;

@Component
public class PaymentRefundRequestedIdempotencyCheckService
    extends AbstractRefundEventsIdempotencyCheck<PaymentRefundRequested> {

    public PaymentRefundRequestedIdempotencyCheckService(RefundService refundService) {
        super(refundService);
    }

    @Override
    protected RefundState getTargetRefundStatus() {
        return RefundState.REFUND_REQUESTED;
    }

    @Override
    protected String getMessageKey(PaymentRefundRequested message) {
        return message.getCorrelationId();
    }
}
