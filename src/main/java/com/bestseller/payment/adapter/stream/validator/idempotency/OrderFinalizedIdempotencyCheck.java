package com.bestseller.payment.adapter.stream.validator.idempotency;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderFinalized;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@RequiredArgsConstructor
public class OrderFinalizedIdempotencyCheck implements IdempotencyChecker<OrderFinalized> {

    private final OrderRepository orderRepository;

    @Override
    public boolean isDuplicate(OrderFinalized message) {
        Optional<Order> optionalOrder = orderRepository.findById(message.getOrderId());
        if (optionalOrder.isEmpty()) {
            return false;
        }
        boolean paymentStatusIsGreaterThanSettlementRequesting = optionalOrder
            .filter(order -> order.getPaymentStatus().isGreaterOrEqual(PaymentState.SETTLEMENT_REQUESTING))
            .isPresent();

        boolean paymentIsCancelled = optionalOrder
            .filter(order -> order.getPaymentStatus() == PaymentState.CANCELLED)
            .isPresent();

        boolean wasPreviouslyInReviewStatus = optionalOrder
            .filter(order -> order.getPrevPaymentStatus() == PaymentState.REVIEW)
            .isPresent();

        boolean isManualCancellation = paymentIsCancelled && wasPreviouslyInReviewStatus;

        PaymentState paymentState = optionalOrder.get().getPaymentStatus();
        boolean isOfflinePayment = paymentState == PaymentState.OFFLINE;

        if (paymentStatusIsGreaterThanSettlementRequesting && !isOfflinePayment && !isManualCancellation) {
            return true;
        }

        Order order = optionalOrder.get();
        return order.getVatOrderNumber() != null && !order.getVatOrderNumber().isEmpty();
    }

}
