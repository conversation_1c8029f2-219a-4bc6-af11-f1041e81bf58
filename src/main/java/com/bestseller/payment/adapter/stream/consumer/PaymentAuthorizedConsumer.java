package com.bestseller.payment.adapter.stream.consumer;

import com.bestseller.dbqueue.consumer.AbstractRetryableConsumer;
import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentAuthorized.PaymentAuthorized;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.exception.OrderNotFoundException;
import com.bestseller.payment.core.service.payment.PaymentService;
import org.springframework.classify.BinaryExceptionClassifier;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.stereotype.Component;

@Component
public class PaymentAuthorizedConsumer extends AbstractRetryableConsumer<PaymentAuthorized> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s";
    private static final BinaryExceptionClassifier EXCEPTION_CLASSIFIER = BinaryExceptionClassifier.builder()
        .retryOn(TransientDataAccessException.class)
        .retryOn(OrderNotFoundException.class)
        .build();

    private final PaymentService paymentService;

    @SuppressWarnings("MissingJavadocMethod")
    public PaymentAuthorizedConsumer(
        MessageFilter<PaymentAuthorized> messageFilter,
        IdempotencyChecker<PaymentAuthorized> idempotencyChecker,
        MessageValidator<PaymentAuthorized> messageValidator,
        PaymentService paymentService,
        QueueProducer<PaymentAuthorized> queueProducer
    ) {
        super(messageFilter, messageValidator, idempotencyChecker, EXCEPTION_CLASSIFIER, queueProducer);
        this.paymentService = paymentService;
    }

    @Override
    public void consume(PaymentAuthorized message) {
        paymentService.updatePaymentStatus(message.getOrderId(), PaymentState.AUTHORISED);
    }

    @Override
    protected String getMessageDetails(PaymentAuthorized message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId());
    }

    @Override
    protected String getMessageKey(PaymentAuthorized message) {
        return message.getOrderId();
    }
}
