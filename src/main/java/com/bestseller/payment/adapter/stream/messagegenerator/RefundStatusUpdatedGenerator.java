package com.bestseller.payment.adapter.stream.messagegenerator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundStatusUpdated.RefundStatusUpdated;
import com.bestseller.payment.core.domain.Refund;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;

@RequiredArgsConstructor
@Service
public class RefundStatusUpdatedGenerator implements MessageGenerator<Refund, RefundStatusUpdated> {

    @Override
    public RefundStatusUpdated generate(Refund refund) {
        return new RefundStatusUpdated()
            .withOrderId(refund.getOrder().getOrderId())
            .withRefundId(refund.getId().toString())
            .withTimestamp(ZonedDateTime.now(ZoneOffset.UTC))
            .withStatus(RefundStatusUpdated.Status.valueOf(refund.getRefundState().name()));
    }
}
