package com.bestseller.payment.adapter.stream.validator.idempotency;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementFailed.PaymentSettlementFailed;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import org.springframework.stereotype.Component;

@Component
public class PaymentSettlementFailedIdempotencyCheck
    extends AbstractPaymentEventsIdempotencyCheck<PaymentSettlementFailed> {

    public PaymentSettlementFailedIdempotencyCheck(OrderRepository orderRepository) {
        super(orderRepository);
    }

    @Override
    protected PaymentState getTargetPaymentStatus() {
        return PaymentState.SETTLEMENT_DENIED;
    }

    @Override
    protected String getMessageKey(PaymentSettlementFailed message) {
        return message.getCorrelationId();
    }
}
