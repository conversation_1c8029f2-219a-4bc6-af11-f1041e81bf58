package com.bestseller.payment.adapter.stream.producer;

import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequest.PaymentRefundRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.stereotype.Component;

@Component
public class PaymentRefundRequestProducer extends AbstractKafkaMessageProducer<PaymentRefundRequest> {

    private static final Logger LOGGER = LoggerFactory.getLogger(PaymentRefundRequestProducer.class);
    private static final String BINDING_NAME = "paymentRefundRequestProducer-out-0";
    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, correlationId=%s";

    public PaymentRefundRequestProducer(StreamBridge streamBridge) {
        super(BINDING_NAME, streamBridge, LOGGER);
    }

    @Override
    protected String getMessageDetails(PaymentRefundRequest message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getCorrelationId());
    }
}
