package com.bestseller.payment.adapter.stream.consumer;

import com.bestseller.dbqueue.consumer.AbstractRetryableConsumer;
import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inStoreReturnSettlement.InStoreReturnSettlement;
import com.bestseller.payment.core.exception.RefundOptionsProviderServiceNotAvailable;
import com.bestseller.payment.core.exception.SettlementNotYetHappenedException;
import com.bestseller.payment.core.service.refund.InStoreRefundService;
import org.springframework.classify.BinaryExceptionClassifier;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.stereotype.Component;

@Component
public class InStoreReturnSettlementConsumer extends AbstractRetryableConsumer<InStoreReturnSettlement> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, refundId=%s, orderLines=%s";
    private static final BinaryExceptionClassifier EXCEPTION_CLASSIFIER = BinaryExceptionClassifier.builder()
        .retryOn(RefundOptionsProviderServiceNotAvailable.class)
        .retryOn(TransientDataAccessException.class)
        .retryOn(SettlementNotYetHappenedException.class)
        .build();

    private final InStoreRefundService inStoreRefundService;

    public InStoreReturnSettlementConsumer(
        MessageFilter<InStoreReturnSettlement> messageFilter,
        MessageValidator<InStoreReturnSettlement> messageValidator,
        IdempotencyChecker<InStoreReturnSettlement> idempotencyChecker,
        QueueProducer<InStoreReturnSettlement> queueProducer,
        InStoreRefundService inStoreRefundService
    ) {
        super(messageFilter, messageValidator, idempotencyChecker, EXCEPTION_CLASSIFIER, queueProducer);
        this.inStoreRefundService = inStoreRefundService;
    }

    @Override
    public void consume(InStoreReturnSettlement message) {
        inStoreRefundService.issueRefundForInStoreReturn(message);
    }

    @Override
    protected String getMessageDetails(InStoreReturnSettlement message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getRefundId(), message.getOrderLines());
    }

    @Override
    protected String getMessageKey(InStoreReturnSettlement message) {
        return message.getOrderId();
    }
}
