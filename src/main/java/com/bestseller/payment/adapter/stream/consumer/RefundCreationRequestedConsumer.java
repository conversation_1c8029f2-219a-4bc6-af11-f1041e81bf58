package com.bestseller.payment.adapter.stream.consumer;

import com.bestseller.dbqueue.consumer.AbstractRetryableConsumer;
import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.RefundCreationRequested;
import com.bestseller.payment.core.converter.OrderItemToRefundMapper;
import com.bestseller.payment.core.dto.RefundOptions;
import com.bestseller.payment.core.dto.RefundReason;
import com.bestseller.payment.core.exception.SettlementNotYetHappenedException;
import com.bestseller.payment.core.service.refund.EcomRefundService;
import org.springframework.classify.BinaryExceptionClassifier;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.stereotype.Component;

@Component
public class RefundCreationRequestedConsumer extends AbstractRetryableConsumer<RefundCreationRequested> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, refundCreationRequestedId=%s, itemsToRefund=%s";
    private static final BinaryExceptionClassifier EXCEPTION_CLASSIFIER = BinaryExceptionClassifier.builder()
        .retryOn(SettlementNotYetHappenedException.class)
        .retryOn(TransientDataAccessException.class)
        .build();

    private final OrderItemToRefundMapper orderItemToRefundMapper;
    private final EcomRefundService ecomRefundService;

    /**
     * Constructor.
     *
     * @param idempotencyChecker      idempotency check service
     * @param messageValidator        message validator
     * @param queueProducer           queue producer
     * @param orderItemToRefundMapper order item to refund mapper
     * @param ecomRefundService       ecom refund service
     */
    public RefundCreationRequestedConsumer(
        MessageFilter<RefundCreationRequested> messageFilter,
        IdempotencyChecker<RefundCreationRequested> idempotencyChecker,
        MessageValidator<RefundCreationRequested> messageValidator,
        QueueProducer<RefundCreationRequested> queueProducer,
        EcomRefundService ecomRefundService,
        OrderItemToRefundMapper orderItemToRefundMapper
    ) {
        super(messageFilter, messageValidator, idempotencyChecker, EXCEPTION_CLASSIFIER, queueProducer);
        this.orderItemToRefundMapper = orderItemToRefundMapper;
        this.ecomRefundService = ecomRefundService;
    }

    @Override
    public void consume(RefundCreationRequested message) {
        ecomRefundService.refund(
            message.getOrderId(),
            orderItemToRefundMapper.mapToOrderItemToRefundList(message.getItemsToRefund()),
            RefundOptions.builder()
                .chargeReturnFee(message.getChargeReturnFee())
                .refundShippingFee(message.getRefundShippingFee())
                .refundReason(RefundReason.RETURN)
                .build(),
            message.getRefundCreationRequestedId().toString()
        );
    }

    @Override
    protected String getMessageKey(RefundCreationRequested message) {
        return message.getOrderId();
    }

    @Override
    protected String getMessageDetails(RefundCreationRequested message) {
        return MESSAGE_DETAILS_TEMPLATE
            .formatted(message.getOrderId(), message.getRefundCreationRequestedId(), message.getItemsToRefund());
    }
}
