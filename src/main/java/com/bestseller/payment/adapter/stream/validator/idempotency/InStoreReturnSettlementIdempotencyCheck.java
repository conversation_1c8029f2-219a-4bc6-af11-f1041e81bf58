package com.bestseller.payment.adapter.stream.validator.idempotency;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inStoreReturnSettlement.InStoreReturnSettlement;
import com.bestseller.payment.adapter.repository.RefundRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class InStoreReturnSettlementIdempotencyCheck implements IdempotencyChecker<InStoreReturnSettlement> {

    private final RefundRepository refundRepository;

    @Override
    public boolean isDuplicate(InStoreReturnSettlement message) {
        return refundRepository.existsByOrderIdAndRequestId(message.getOrderId(),
            message.getRefundId().toString());
    }

}
