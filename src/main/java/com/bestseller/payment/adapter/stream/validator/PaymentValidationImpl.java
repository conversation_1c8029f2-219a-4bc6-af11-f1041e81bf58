package com.bestseller.payment.adapter.stream.validator;

import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import com.bestseller.payment.core.domain.enumeration.RefundablePaymentSetting;
import com.bestseller.payment.core.domain.payment.GiftcardPayment;
import com.bestseller.payment.core.exception.NoRefundablePaymentSettingFoundException;
import com.bestseller.payment.core.exception.OrderIsNotRefundableException;
import com.bestseller.payment.core.exception.PaymentNotFoundException;
import com.bestseller.payment.core.exception.SettlementNotYetHappenedException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Optional;

@Slf4j
@Component
public class PaymentValidationImpl implements PaymentValidation {

    /**
     * Validates the payment of an order.
     *
     * @param order the order to validate
     * @return true if the payment is valid, false otherwise
     */
    public boolean validateRefundRequest(Order order) {
        try {
            return isOrderRefundable(order);
        } catch (NoRefundablePaymentSettingFoundException e) {
            if (order.getPaymentStatus() == PaymentState.SETTLEMENT_DENIED) {
                log.warn("Order with id {} has a payment status of SETTLEMENT_DENIED, which is not refundable.", order.getOrderId());
            }
            log.error(e.getMessage());
            return false;
        } catch (PaymentNotFoundException e) {
            log.error(e.getMessage());
            return false;
        }
    }

    @Override
    public boolean isRefundable(Order order) {
        try {
            return isOrderRefundable(order) && StringUtils.hasText(order.getVatOrderNumber());
        } catch (SettlementNotYetHappenedException
                 | OrderIsNotRefundableException
                 | PaymentNotFoundException
                 | NoRefundablePaymentSettingFoundException e) {
            return false;
        }
    }

    @Override
    public boolean isSettlementSupported(String provider) {
        return org.apache.commons.lang.StringUtils.isNotBlank(provider)
            && Optional.ofNullable(ProcessorId.findByName(provider))
            .map(ProcessorId::isSupportsSettlement)
            .orElse(false);
    }

    private boolean isOrderRefundable(Order order) {
        final PaymentInfo nonGiftCardPayment = order.getPayments()
            .stream()
            .filter(paymentInfo -> !paymentInfo.getType().equals(PaymentType.GIFTCARD))
            .findFirst()
            .orElse(null);

        final GiftcardPayment giftCardPayment = order.getPayments()
            .stream()
            .filter(paymentInfo -> paymentInfo.getType().equals(PaymentType.GIFTCARD))
            .map(paymentInfo -> (GiftcardPayment) paymentInfo)
            .findFirst()
            .orElse(null);

        /* If the order has two payments, one of them is a gift card payment
         * and the other is a non-gift card payment, then it is a mixed payment
         * In case the order has paid with multiple gift cards, PMS has already
         * mixed them into a single gift card payment
         */

        var paymentInfo = nonGiftCardPayment != null && !order.getPaymentStatus().equals(PaymentState.CANCELLED)
            ? nonGiftCardPayment : giftCardPayment;

        if (paymentInfo == null) {
            throw new PaymentNotFoundException(
                "Order with id %s has no payment information".formatted(order.getOrderId()));
        }
        /* In case of mixed payment, if the non-gift card payment is cancelled,
         * we should consider the order as authorised
         */

        PaymentState paymentStatus = paymentInfo.getType().equals(PaymentType.GIFTCARD) ? PaymentState.AUTHORISED : order.getPaymentStatus();

        RefundablePaymentSetting refundablePaymentSetting = RefundablePaymentSetting.find(paymentInfo.getType(), paymentStatus);
        if (refundablePaymentSetting == null) {
            throw new NoRefundablePaymentSettingFoundException(
                order.getOrderId(), paymentInfo.getType(), paymentStatus);
        }

        // Throw the appropriate exception if the payment is not refundable
        refundablePaymentSetting.throwExceptionIfNotRefundable(order.getOrderId());

        return true;
    }
}
