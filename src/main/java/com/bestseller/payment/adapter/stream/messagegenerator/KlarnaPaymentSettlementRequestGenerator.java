package com.bestseller.payment.adapter.stream.messagegenerator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementRequest.OrderLine;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderCharge;
import com.bestseller.payment.core.utils.PaymentUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@RequiredArgsConstructor
@Service
public class KlarnaPaymentSettlementRequestGenerator extends PaymentSettlementRequestGenerator {

    private static final String STANDARD_SHIPPING = "STANDARD_SHIPPING";
    private static final String SHIPPING = "Shipping";
    private static final BigDecimal BD_100 = new BigDecimal("100.00");
    private static final int ONE = 1;

    @Override
    protected List<OrderLine> getOrderLines(Order order) {
        return order.getOrderLines()
                .stream()
                .map(orderLine -> new OrderLine()
                        .withEan(orderLine.getEan())
                        .withDescription(orderLine.getName())
                        .withQuantity(orderLine.getOpenQty())
                        .withPaidAmount(PaymentUtils.toIntCents(orderLine.getOrderLinePaidAmount().getGrossDiscountedTotal())))
                .toList();
    }

    @Override
    protected List<OrderLine> convertOrderChargesToOrderLines(List<OrderCharge> orderCharges) {
        return orderCharges
                .stream()
                .map(orderCharge -> new OrderLine()
                        .withEan(getProperOrderChargeEan(orderCharge.getEan()))
                        .withDescription(orderCharge.getName())
                        .withQuantity(ONE)
                        .withPaidAmount(PaymentUtils.toIntCents(orderCharge.getChargeTotal().getGrossDiscountedUnitPrice()))
                        .withTaxRate(PaymentUtils.toIntCents(orderCharge.getTaxRate().multiply(BD_100)))
                        .withTaxAmount(PaymentUtils.toIntCents(orderCharge.getChargeTotal().getUnitVAT()))
                ).toList();
    }

    private String getProperOrderChargeEan(String ean) {
        return STANDARD_SHIPPING.equalsIgnoreCase(ean) ? SHIPPING : ean;
    }
}
