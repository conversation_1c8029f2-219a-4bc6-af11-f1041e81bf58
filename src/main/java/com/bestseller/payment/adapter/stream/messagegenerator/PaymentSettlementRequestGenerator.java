package com.bestseller.payment.adapter.stream.messagegenerator;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementRequest.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementRequest.PaymentSettlementRequest;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderCharge;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.payment.GiftcardPayment;
import com.bestseller.payment.core.exception.PaymentNotFoundException;
import com.bestseller.payment.core.utils.PaymentUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public abstract class PaymentSettlementRequestGenerator implements MessageGenerator<Order, PaymentSettlementRequest> {

    @Override
    public PaymentSettlementRequest generate(Order order) {
        final PaymentInfo paymentInfo = getPaymentInfo(order);
        final ArrayList<OrderLine> orderLines = new ArrayList<>(getOrderLines(order));
        orderLines.addAll(convertOrderChargesToOrderLines(order.getOrderCharges()));
        return new PaymentSettlementRequest()
                .withOrderId(order.getOrderId())
                .withCorrelationId(order.getOrderId())
                .withCurrency(order.getCurrency())
                .withTotalAmount(PaymentUtils.toIntCents(calculateSettlementAmount(order)))
                .withProvider(paymentInfo.getProcessorId().name())
                .withOrderLines(orderLines);
    }

    /**
     * Retrieves the PaymentInfo for the given order, excluding gift card payments.
     *
     * @param order the order to retrieve the payment info from
     * @return the PaymentInfo for the order
     * @throws PaymentNotFoundException if no payment is found for the order
     */
    protected PaymentInfo getPaymentInfo(Order order) {
        return order.getPayments().stream()
                .filter(payment -> !PaymentType.GIFTCARD.equals(payment.getType()))
                .findFirst()
                .orElseThrow(() -> new PaymentNotFoundException("No payment found for order: " + order.getOrderId()));
    }

    private BigDecimal calculateGiftCardCardPayments(List<PaymentInfo> payments) {
        return payments.stream()
                .filter(payment -> payment instanceof GiftcardPayment)
                .map(payment -> new BigDecimal(payment.getAuthorisedAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal calculateSettlementAmount(Order order) {
        return BigDecimal.ZERO.max(
                order.getTotalPaidPrice().getGrossDiscountedTotal().subtract(calculateGiftCardCardPayments(order.getPayments()))
        );
    }

    protected abstract List<OrderLine> getOrderLines(Order order);

    protected abstract List<OrderLine> convertOrderChargesToOrderLines(List<OrderCharge> orderCharges);
}
