package com.bestseller.payment.adapter.stream.consumer;

import com.bestseller.dbqueue.consumer.AbstractRetryableConsumer;
import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageFilter;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementFailed.PaymentSettlementFailed;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.service.payment.PaymentService;
import org.springframework.classify.BinaryExceptionClassifier;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.stereotype.Component;

@Component
public class PaymentSettlementFailedConsumer extends AbstractRetryableConsumer<PaymentSettlementFailed> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "correlationId=%s";
    private static final BinaryExceptionClassifier EXCEPTION_CLASSIFIER = BinaryExceptionClassifier.builder()
        .retryOn(TransientDataAccessException.class)
        .build();

    private final PaymentService paymentService;

    @SuppressWarnings("MissingJavadocMethod")
    public PaymentSettlementFailedConsumer(
        MessageFilter<PaymentSettlementFailed> messageFilter,
        IdempotencyChecker<PaymentSettlementFailed> idempotencyChecker,
        MessageValidator<PaymentSettlementFailed> messageValidator,
        PaymentService paymentService,
        QueueProducer<PaymentSettlementFailed> queueProducer
    ) {
        super(messageFilter, messageValidator, idempotencyChecker, EXCEPTION_CLASSIFIER, queueProducer);
        this.paymentService = paymentService;
    }

    @Override
    public void consume(PaymentSettlementFailed message) {
        paymentService.updatePaymentStatus(message.getCorrelationId(), PaymentState.SETTLEMENT_DENIED);
    }

    @Override
    protected String getMessageDetails(PaymentSettlementFailed message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getCorrelationId());
    }

    @Override
    protected String getMessageKey(PaymentSettlementFailed message) {
        return message.getCorrelationId();
    }
}
