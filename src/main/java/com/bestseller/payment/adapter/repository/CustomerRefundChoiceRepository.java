package com.bestseller.payment.adapter.repository;

import com.bestseller.payment.core.domain.CustomerRefundChoice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CustomerRefundChoiceRepository extends JpaRepository<CustomerRefundChoice, Integer> {
    @Query("SELECT CASE WHEN COUNT(cc) > 0 THEN TRUE ELSE FALSE END "
        + "FROM Order o"
        + " JOIN o.orderLines ol "
        + " JOIN ol.customerRefundChoices cc "
        + "WHERE o.orderId = :orderId AND cc.returnId = :returnId")
    boolean existsByOrderIdAndReturnId(String orderId, String returnId);

    @Query("SELECT COUNT(cc) FROM Order o"
        + " JOIN o.orderLines ol "
        + " JOIN ol.customerRefundChoices cc "
        + "WHERE o.orderId = :orderId AND ol.orderEntryId = :orderEntryId")
    int countByOrderEntryId(String orderId, Integer orderEntryId);
}
