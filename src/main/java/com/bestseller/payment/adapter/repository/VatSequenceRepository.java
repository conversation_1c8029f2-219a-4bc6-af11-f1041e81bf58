package com.bestseller.payment.adapter.repository;

import com.bestseller.payment.core.domain.VatSequence;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.stereotype.Repository;

@Repository
public interface VatSequenceRepository extends JpaRepository<VatSequence, VatSequence.VatSequenceId> {
    @Procedure("update_sequence_and_get_last_id")
    Long updateSequenceAndGetLastId(String countryCode, int sequenceYear, String vatType);
}
