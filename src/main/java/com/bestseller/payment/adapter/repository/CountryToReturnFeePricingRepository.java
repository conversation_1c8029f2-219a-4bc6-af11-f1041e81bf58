package com.bestseller.payment.adapter.repository;

import com.bestseller.payment.core.domain.CountryToReturnFeePricing;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.Optional;

@Repository
public interface CountryToReturnFeePricingRepository extends JpaRepository<CountryToReturnFeePricing, Integer> {
    Optional<CountryToReturnFeePricing> findFirstByCountryAndEffectiveDateLessThanEqualOrderByEffectiveDateDesc(
            String country, Date effectiveDate);
}
