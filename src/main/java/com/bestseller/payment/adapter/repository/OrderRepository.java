package com.bestseller.payment.adapter.repository;

import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.Set;

@Repository
public interface OrderRepository extends JpaRepository<Order, String> {
    boolean existsByOrderIdAndPaymentsPaymentReference(String orderId, String paymentReference);

    Set<Order> findAllByPaymentStatusEqualsAndLastModifiedTSLessThanEqual(PaymentState paymentState,
                                                                          Instant currentTimeStamp);

    boolean existsByOrderIdAndPaymentStatus(String orderId, PaymentState paymentState);
}
