package com.bestseller.payment.adapter.repository;

import com.bestseller.payment.core.domain.Refund;
import com.logistics.statetransition.RefundState;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface RefundRepository extends CrudRepository<Refund, Integer> {
    @Query("SELECT "
        + "CASE WHEN COUNT(r) > 0 "
        + "THEN TRUE "
        + "ELSE FALSE "
        + "END "
        + "FROM Refund r"
        + " WHERE r.order.orderId = :orderId AND r.requestId= :requestId")
    boolean existsByOrderIdAndRequestId(String orderId, String requestId);

    @Query("SELECT r.refundState FROM Refund r WHERE r.id = :id")
    Optional<RefundState> getRefundStateById(int id);

    Optional<Refund> getRefundByOrderOrderIdAndGiftCardCorrelationId(String orderId, UUID giftCardCorrelationId);
}
