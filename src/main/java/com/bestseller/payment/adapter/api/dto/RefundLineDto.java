package com.bestseller.payment.adapter.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundLineDto {
    private Integer lineNumber;
    private String ean;
    private String description;
    private Integer quantity;
    private BigDecimal unitDiscountedPrice;
    private BigDecimal pricePaid;
}
