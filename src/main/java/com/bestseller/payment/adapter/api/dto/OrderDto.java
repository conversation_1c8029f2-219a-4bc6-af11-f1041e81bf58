package com.bestseller.payment.adapter.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Builder
@AllArgsConstructor
public class OrderDto {
    private String orderId;
    private List<OrderChargeDto> orderCharges;
    private PaymentDto payment;
    private RefundsInfoDto refundsInfo;
    private boolean isRefundable;
    private List<OrderLineDto> orderLines;
}
