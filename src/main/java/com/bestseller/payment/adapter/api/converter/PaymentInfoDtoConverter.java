package com.bestseller.payment.adapter.api.converter;

import com.bestseller.payment.adapter.api.dto.PaymentInfoDto;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.enumeration.PaymentMethod;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.NullValueMappingStrategy;

@Mapper(nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
public interface PaymentInfoDtoConverter {

    @Mapping(target = "subMethodType", source = "subMethod")
    @Mapping(target = "transactionId", source = "paymentReference")
    @Mapping(target = "paymentType", source = "type", qualifiedByName = "toPaymentType")
    @Mapping(target = "subMethodId", source = "subMethodName")
    @Mapping(target = "subMethodName", source = "subMethodName",
        qualifiedByName = "toPaymentSubMethodName", defaultValue = "OFFLINE")
    PaymentInfoDto toPaymentInfoDto(PaymentInfo paymentInfo);

    /**
     * Convert the subMethodName to the description of the {@link PaymentMethod}.
     *
     * @param input the subMethodName
     * @return the description of the {@link PaymentMethod}
     */
    @Named("toPaymentSubMethodName")
    default String toPaymentSubMethodName(String input) {
        try {
            return PaymentMethod.valueOf(input).getDescription();
        } catch (IllegalArgumentException e) {
            return input;
        }
    }

    @Named("toPaymentType")
    default String toPaymentType(String input) {
        return PaymentType.valueOf(input).getDescription();
    }
}
