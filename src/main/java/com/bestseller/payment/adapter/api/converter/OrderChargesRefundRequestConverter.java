package com.bestseller.payment.adapter.api.converter;

import com.bestseller.payment.adapter.api.dto.OrderChargeRefundDto;
import com.bestseller.payment.adapter.api.dto.OrderChargesRefundRequestDto;
import com.bestseller.payment.core.dto.OrderChargeRefund;
import com.bestseller.payment.core.dto.OrderChargesRefundRequest;
import org.mapstruct.Mapper;

@Mapper
public interface OrderChargesRefundRequestConverter {
    OrderChargeRefund toOrderChargeRefund(OrderChargeRefundDto orderChargeRefundDto);

    OrderChargesRefundRequest toOrderChargeRefundRequest(String orderId,
                                                         OrderChargesRefundRequestDto orderChargesRefundRequestDto);
}
