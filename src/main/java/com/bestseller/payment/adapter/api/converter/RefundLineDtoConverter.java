package com.bestseller.payment.adapter.api.converter;

import com.bestseller.payment.adapter.api.dto.RefundLineDto;
import com.bestseller.payment.core.domain.RefundLine;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.util.List;

@Mapper(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface RefundLineDtoConverter {

    @Mapping(target = "pricePaid", expression = "java(refundLine.getRefundLineTotal().getGrossDiscountedTotal())")
    @Mapping(target = "unitDiscountedPrice",
        expression = "java(refundLine.getRefundLineTotal().getGrossDiscountedUnitPrice())")
    @Mapping(target = "description", expression = "java(refundLine.getOrderLine().getName())")
    @Mapping(target = "ean", source = "orderLine.ean")
    RefundLineDto toRefundLineDto(RefundLine refundLine);

    List<RefundLineDto> toRefundLineDto(List<RefundLine> refundLine);
}
