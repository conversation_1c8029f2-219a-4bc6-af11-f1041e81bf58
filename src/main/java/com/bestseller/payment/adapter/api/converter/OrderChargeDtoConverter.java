package com.bestseller.payment.adapter.api.converter;

import com.bestseller.payment.adapter.api.dto.OrderChargeDto;
import com.bestseller.payment.core.domain.OrderCharge;
import com.bestseller.payment.core.domain.enumeration.ChargedRefundReason;
import com.bestseller.payment.core.domain.enumeration.EntryType;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.Arrays;
import java.util.List;

@Mapper
public interface OrderChargeDtoConverter {

    @Mapping(target = "id", source = "orderEntryId")
    @Mapping(target = "allowedRefundReasons", source = "type", qualifiedByName = "getOrderChargeRefundReasons")
    @Mapping(target = "chargeTotal", expression = "java(orderCharge.getChargeTotal().getGrossDiscountedTotal())")
    OrderChargeDto toOrderChargeDto(OrderCharge orderCharge);

    List<OrderChargeDto> toOrderChargeDtoList(List<OrderCharge> orderCharges);

    /**
     * Returns a list of refund reasons for the given entry type.
     *
     * @param entryType the entry type
     * @return the list of refund reasons
     */
    @Named("getOrderChargeRefundReasons")
    default List<String> getOrderChargeRefundReasons(EntryType entryType) {
        return Arrays.stream(ChargedRefundReason.values())
            .filter(ChargedRefundReason::getEnabled)
            .filter(reason -> reason.getType().equals(entryType))
            .map(ChargedRefundReason::getDescription)
            .toList();
    }
}
