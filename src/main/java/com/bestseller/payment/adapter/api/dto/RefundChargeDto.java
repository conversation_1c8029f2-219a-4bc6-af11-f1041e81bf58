package com.bestseller.payment.adapter.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundChargeDto {
    private String title;
    private BigDecimal amount;
    private Boolean refunded;
    private String description;
    private Boolean isReturnFee;
    private String csrInitials;
}
