package com.bestseller.payment.adapter.api;

import com.bestseller.payment.core.exception.InvalidRefundStateTransitionException;
import com.bestseller.payment.core.exception.NotFoundException;
import com.bestseller.payment.core.exception.OrderCancellationMissingOrderLinesException;
import com.bestseller.payment.core.exception.RefundOperationNotAllowedException;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

@SuppressWarnings("MultipleStringLiterals")
@RestControllerAdvice
@Slf4j
public class ApiExceptionHandler extends ResponseEntityExceptionHandler {

    /**
     * Handles bad requests.
     *
     * @param e Exception
     * @return Error message that caused the exception
     */
    @ExceptionHandler(value = {
        RefundOperationNotAllowedException.class,
        InvalidRefundStateTransitionException.class,
        OrderCancellationMissingOrderLinesException.class
    })
    public ResponseEntity<Object> handleBadRequests(Exception e) {
        log.warn("Handle bad request exception: message={}", e.getMessage(), e);
        return createResponseEntity(e, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handles not found exceptions.
     *
     * @param e NotFoundException
     * @return Error message that caused the exception
     */
    @ExceptionHandler(value = NotFoundException.class)
    public ResponseEntity<Object> handleNotFoundExceptions(NotFoundException e) {
        log.warn("Handle not found exception: message={}", e.getMessage(), e);
        return createResponseEntity(e, HttpStatus.NOT_FOUND);
    }

    /**
     * Handles all unhandled exceptions.
     *
     * @param e Exception
     * @return Error message that caused the exception
     */
    @ExceptionHandler(value = Exception.class)
    public ResponseEntity<Object> handleException(Exception e) {
        log.error("Handle unexpected exception: message={}", e.getMessage(), e);
        return new ResponseEntity<>(
            createErrorBody("Something went wrong!"), new HttpHeaders(), HttpStatus.INTERNAL_SERVER_ERROR);
    }

    private ResponseEntity<Object> createResponseEntity(Exception e, HttpStatus status) {
        return new ResponseEntity<>(createErrorBody(e), new HttpHeaders(), status);
    }

    private ErrorBody createErrorBody(Exception e) {
        return createErrorBody(e.getMessage());
    }

    private ErrorBody createErrorBody(String message) {
        return ErrorBody.builder()
            .message(message)
            .build();
    }

    @Builder
    private record ErrorBody(String message) {
    }
}
