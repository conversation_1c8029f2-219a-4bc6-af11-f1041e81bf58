package com.bestseller.payment.adapter.api.controller;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import com.bestseller.payment.adapter.api.converter.OrderChargesRefundRequestConverter;
import com.bestseller.payment.adapter.api.converter.OrderDtoConverter;
import com.bestseller.payment.adapter.api.dto.OrderChargesRefundRequestDto;
import com.bestseller.payment.adapter.api.dto.OrderDto;
import com.bestseller.payment.adapter.stream.messagegenerator.PaymentStatusUpdatedGenerator;
import com.bestseller.payment.adapter.stream.validator.PaymentValidation;
import com.bestseller.payment.core.service.order.OrderService;
import com.bestseller.payment.core.service.refund.OrderChargesRefundService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.stream.Stream;

@RequiredArgsConstructor
@RestController
@RequestMapping("api/v1/orders")
@Slf4j
public class OrderController {

    private final OrderService orderService;
    private final OrderDtoConverter orderDtoConverter;
    private final PaymentValidation paymentValidation;
    private final OrderChargesRefundService orderChargesRefundService;
    private final OrderChargesRefundRequestConverter orderChargesRefundRequestConverter;
    private final PaymentStatusUpdatedGenerator paymentStatusUpdatedGenerator;

    /**
     * Get order by order id.
     *
     * @param orderId Order id
     * @return OrderDto Order
     */
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Order found and returned"),
        @ApiResponse(responseCode = "404", description = "Order not found")
    })
    @Operation(summary = "Get order by order id")
    @GetMapping(value = "/{orderId}")
    public OrderDto getOrder(@PathVariable("orderId") String orderId) {
        log.info("Incoming request to get order by orderId = {}", orderId);
        var order = orderService.getOrderById(orderId);
        return orderDtoConverter.toOrderDto(order, paymentValidation.isRefundable(order));
    }

    /**
     * Refund order charges.
     *
     * @param orderChargesRefundRequestDto Order charges refund request
     */
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Order charges refunded"),
        @ApiResponse(responseCode = "404", description = "Order not found"),
        @ApiResponse(responseCode = "400", description = "Invalid refund request")
    })
    @Operation(summary = "Refund order charges")
    @PostMapping(path = "{orderId}/refund-order-charges")
    public void refundOrderCharges(@PathVariable("orderId") String orderId,
                                   @RequestBody @Valid OrderChargesRefundRequestDto orderChargesRefundRequestDto) {
        log.info("Incoming request to refund order charges by orderId={}", orderId);
        orderChargesRefundService.refundOrderCharges(
            orderChargesRefundRequestConverter.toOrderChargeRefundRequest(orderId, orderChargesRefundRequestDto));
        log.info("Refunding order charges by orderId={} has done successfully", orderId);
    }

    /**
     * Generate paymentStatusUpdatedMessage by order id.
     * It is to publish OrderForFulfillmentMessage manually after an order unblocked in FCS.
     *
     * @param orderId Order id
     * @return PaymentStatusUpdated paymentStatusUpdated message
     */
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Order found and message returned"),
        @ApiResponse(responseCode = "404", description = "Order not found")
    })
    @Operation(summary = "Get paymentStatusUpdated message by order id")
    @GetMapping(value = "/{orderId}/payment-status-updated-message")
    public PaymentStatusUpdated getPaymentStatusUpdatedMessageByOrderId(@PathVariable("orderId") String orderId) {
        log.info("Incoming request to get paymentStatusUpdated message by orderId = {}", orderId);
        var order = orderService.getOrderById(orderId);

        // Because PaymentStatusUpdated.PaymentState is not aligned with Order.PaymentStatus
        // We need to check if paymentStatus exists in PaymentState enum
        boolean paymentStateExists = Stream.of(PaymentStatusUpdated.PaymentState.values())
            .anyMatch(state -> state.name().equals(order.getPaymentStatus().name()));

        if (order.isOfflinePayment() || paymentStateExists) {
            return paymentStatusUpdatedGenerator.generate(order);
        }
        return null;
    }

    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Order cancelled successfully"),
        @ApiResponse(responseCode = "404", description = "Order not found"),
        @ApiResponse(responseCode = "400", description = "Bad request, order cannot be cancelled")
    })
    @Operation(summary = "Cancel order by order id")
    @PutMapping("/{orderId}/cancel")
    public void cancelOrder(@PathVariable String orderId) {
        log.info("Incoming request to cancel order by orderId = {}", orderId);
        orderService.cancelOrder(orderId);
        log.info("Order with id {} has been cancelled successfully", orderId);
    }
}
