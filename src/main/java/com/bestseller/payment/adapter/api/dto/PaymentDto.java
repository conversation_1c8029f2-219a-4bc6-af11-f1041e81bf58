package com.bestseller.payment.adapter.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PaymentDto {
    private String paymentTypeName;
    private String processorId;
    private String paymentType;
    private String subMethodId;
    private String subMethodName;
    private String subMethodType;
    private String transactionId;
    private String paymentStatus;
    private BigDecimal nonGiftCardAuthorisedAmount;
    private BigDecimal giftCardAuthorisedAmount;
    private BigDecimal originalGrossDiscountedTotal;
    private BigDecimal grossDiscountedTotal;
}
