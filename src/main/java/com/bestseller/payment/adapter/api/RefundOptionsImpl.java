package com.bestseller.payment.adapter.api;

import com.bestseller.payment.core.dto.RefundOptionsResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.time.Duration;
import java.util.Optional;

import static reactor.util.retry.Retry.backoff;

@Component
@RequiredArgsConstructor
@Slf4j
public class RefundOptionsImpl implements RefundOptionsService {

    private final WebClient fcsClient;

    @Value("${fcs.max-retry-attempts}")
    private int maxAttempts;
    @Value("${fcs.retry-backoff}")
    private Duration backoffDuration;

    @SuppressWarnings("IllegalCatch")
    @Override
    public Optional<RefundOptionsResponse> getRefundOptionsByOrderId(String orderId) {
        try {
            return fcsClient.get().uri(uriBuilder -> uriBuilder.path("orders/{id}/refund-options")
                        .build(orderId))
                    .retrieve()
                    .bodyToMono(RefundOptionsResponse.class)
                    .retryWhen(backoff(maxAttempts, backoffDuration)
                            .doBeforeRetry(retrySignal -> log.info("Retrying refund options for order with ID {}", orderId))
                            .filter(throwable -> !(throwable instanceof WebClientResponseException)
                                    || ((WebClientResponseException) throwable).getStatusCode() != HttpStatus.NOT_FOUND))
                    .blockOptional();
        } catch (Exception e) {
            log.error("Error getting refund options for order with ID {}", orderId, e);
            return Optional.empty();
        }
    }
}
