package com.bestseller.payment.adapter.api.converter;

import com.bestseller.payment.adapter.api.dto.OrderChargeDto;
import com.bestseller.payment.adapter.api.dto.OrderDto;
import com.bestseller.payment.adapter.api.dto.PaymentDto;
import com.bestseller.payment.adapter.api.dto.PaymentInfoDto;
import com.bestseller.payment.adapter.api.dto.RefundsInfoDto;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderCharge;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.payment.GiftcardPayment;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.List;

@Mapper(uses = OrderLineDtoConverter.class)
public interface OrderDtoConverter {

    @Mapping(target = "isRefundable", source = "isRefundable")
    @Mapping(target = "orderCharges", source = "order.orderCharges", qualifiedByName = "toOrderChargeDtoList")
    @Mapping(target = "refundsInfo", source = "order", qualifiedByName = "toRefundsInfoDto")
    @Mapping(target = "payment", source = "order", qualifiedByName = "toPaymentDto")
    OrderDto toOrderDto(Order order, boolean isRefundable);

    /**
     * Converts an {@link Order} to a {@link PaymentDto}.
     *
     * @param order the order to convert
     * @return the converted {@link PaymentDto}
     */
    @Named("toPaymentDto")
    default PaymentDto toPaymentDto(Order order) {
        var mainPayment = order.getPayments()
            .stream()
            .filter(payment -> !payment.getType().equals(PaymentType.GIFTCARD))
            .findFirst()
            .orElse(null);

        BigDecimal giftCardPaymentAuthorisedAmount = order.getPayments()
            .stream()
            .filter(payment -> payment instanceof GiftcardPayment)
            .findFirst()
            .map(PaymentInfo::getAuthorisedAmount)
            .map(BigDecimal::new)
            .orElse(BigDecimal.ZERO);

        PaymentInfoDto nonGiftCardPaymentInfo = Mappers
            .getMapper(PaymentInfoDtoConverter.class)
            .toPaymentInfoDto(mainPayment);

        return PaymentDto.builder()
            .subMethodType(nonGiftCardPaymentInfo.getSubMethodType())
            .subMethodName(nonGiftCardPaymentInfo.getSubMethodName())
            .subMethodId(nonGiftCardPaymentInfo.getSubMethodId())
            .transactionId(nonGiftCardPaymentInfo.getTransactionId())
            .paymentType(nonGiftCardPaymentInfo.getPaymentType())
            .processorId(nonGiftCardPaymentInfo.getProcessorId())
            .paymentTypeName(nonGiftCardPaymentInfo.getType())
            .nonGiftCardAuthorisedAmount(nonGiftCardPaymentInfo.getAuthorisedAmount())
            .paymentStatus(order.getPaymentStatus().name())
            .giftCardAuthorisedAmount(giftCardPaymentAuthorisedAmount)
            .originalGrossDiscountedTotal(order.getTotalPaidPrice().getOriginalGrossDiscountedTotal())
            .grossDiscountedTotal(order.getTotalPaidPrice().getGrossDiscountedTotal())
            .build();
    }

    /**
     * Converts an {@link Order} to a {@link RefundsInfoDto}.
     *
     * @param order the order to convert
     * @return the converted {@link RefundsInfoDto}
     */
    @Named("toRefundsInfoDto")
    default RefundsInfoDto toRefundsInfoDto(Order order) {
        return Mappers.getMapper(RefundsInfoDtoConverter.class).toRefundsInfoDto(order.getRefunds());
    }

    /**
     * Converts a list of {@link OrderCharge} to a list of {@link OrderChargeDto}.
     *
     * @param orderCharges the list of order charges to convert
     * @return the converted list of order charge dtos
     */
    @Named("toOrderChargeDtoList")
    default List<OrderChargeDto> toOrderChargeDtoList(List<OrderCharge> orderCharges) {
        return Mappers.getMapper(OrderChargeDtoConverter.class).toOrderChargeDtoList(orderCharges);
    }
}
