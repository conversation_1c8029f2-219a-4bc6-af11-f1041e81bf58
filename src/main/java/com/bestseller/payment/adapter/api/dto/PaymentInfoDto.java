package com.bestseller.payment.adapter.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PaymentInfoDto {
    private String type;
    private String paymentType;
    private String subMethodId;
    private String subMethodName;
    private String subMethodType;
    private BigDecimal authorisedAmount;
    private String transactionId;
    private String processorId;
}
