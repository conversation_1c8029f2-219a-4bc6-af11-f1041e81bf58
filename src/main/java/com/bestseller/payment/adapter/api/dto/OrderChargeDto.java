package com.bestseller.payment.adapter.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@Builder
@AllArgsConstructor
public class OrderChargeDto {
    private Integer id;
    private String name;
    private String ean;
    private String type;
    private Boolean cancelled;
    private Boolean refunded;
    private BigDecimal chargeTotal;
    private List<String> allowedRefundReasons;
}

