package com.bestseller.payment.adapter.api.controller;

import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("api/v1/refunds")
@Slf4j
public class RefundController {

    private final RefundService refundService;

    /**
     * Mark refund as refund_success.
     *
     * @param refundId Refund id
     */
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Refund marked as refund_success"),
        @ApiResponse(responseCode = "404", description = "Refund not found"),
        @ApiResponse(responseCode = "400", description = "Invalid refund state transition")
    })
    @Operation(summary = "Mark refund as refund_success")
    @PutMapping(path = "/{refundId}/refund-success")
    public void markRefundAsRefundSuccess(@PathVariable("refundId") Integer refundId) {
        log.info("Incoming request to mark refund as refund_success by refundId = {}", refundId);
        refundService.updateRefundStatus(refundId, RefundState.REFUND_SUCCESS);
        log.info("Marking refund as refund_success state by refundId={} has done successfully", refundId);
    }

    /**
     * Mark refund as refund_cancelled.
     *
     * @param refundId Refund id
     */
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Refund marked as refund_cancelled"),
        @ApiResponse(responseCode = "404", description = "Refund not found"),
        @ApiResponse(responseCode = "400", description = "Invalid refund state transition")
    })
    @Operation(summary = "Mark refund as refund_cancelled")
    @PutMapping(path = "/{refundId}/refund-cancel")
    public void markRefundAsRefundCancelled(@PathVariable("refundId") Integer refundId) {
        log.info("Incoming request to mark refund as refund_cancelled by refundId={}", refundId);
        refundService.updateRefundStatus(refundId, RefundState.REFUND_CANCELLED);
        log.info("Marking refund as refund_cancelled state by refundId={} has done successfully", refundId);
    }
}
