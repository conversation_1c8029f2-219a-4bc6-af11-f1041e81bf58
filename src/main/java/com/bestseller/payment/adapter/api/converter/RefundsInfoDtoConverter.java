package com.bestseller.payment.adapter.api.converter;

import com.bestseller.payment.adapter.api.dto.RefundDto;
import com.bestseller.payment.adapter.api.dto.RefundLineDto;
import com.bestseller.payment.adapter.api.dto.RefundLinesInfoDto;
import com.bestseller.payment.adapter.api.dto.RefundsInfoDto;
import com.bestseller.payment.core.domain.Refund;
import com.logistics.statetransition.RefundState;
import com.logistics.statetransition.RefundStateTransition;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;

@Mapper
public interface RefundsInfoDtoConverter {

    /**
     * Converts a list of Refund entities to a RefundsInfoDto.
     *
     * @param refunds List of Refund entities
     * @return RefundsInfoDto
     */
    default RefundsInfoDto toRefundsInfoDto(List<Refund> refunds) {
        RefundLineDtoConverter refundLineDtoConverter = Mappers.getMapper(RefundLineDtoConverter.class);
        RefundChargeDtoConverter refundChargeDtoConverter = Mappers.getMapper(RefundChargeDtoConverter.class);

        List<RefundDto> refundDtoList = new ArrayList<>();
        BigDecimal totalAmount = BigDecimal.ZERO;
        if (!refunds.isEmpty()) {
            for (Refund refund : refunds) {
                refundDtoList.add(
                    RefundDto.builder().vatId(refund.getRefundId())
                        .id(refund.getId())
                        .refundStatus(refund.getRefundState().getDescription())
                        .refundDate(ZonedDateTime.ofInstant(refund.getCreatedTS(), ZoneId.systemDefault()))
                        .lastUpdate(ZonedDateTime.ofInstant(refund.getLastModifiedTS(), ZoneId.systemDefault()))
                        .refundLinesInfo(
                            toRefundLinesInfo(refundLineDtoConverter.toRefundLineDto(refund.getRefundLines())))
                        .refundCharges(refundChargeDtoConverter
                            .toRefundChargeDtoList(refund.getRefundCharges(), refund.getCsrInitials()))
                        .totalAmount(refund.getRefundTotal().getGrossDiscountedTotal())
                        .canBeMarkedAsRefunded(
                            RefundStateTransition.isTransitionAllowed(
                                refund.getRefundState(), RefundState.REFUND_SUCCESS))
                        .canBeMarkedAsCancelled(
                            RefundStateTransition.isTransitionAllowed(
                                refund.getRefundState(), RefundState.REFUND_CANCELLED))
                        .refundRequestId(refund.getRequestId())
                        .build());
                totalAmount = totalAmount.add(refund.getRefundTotal().getGrossDiscountedTotal());
            }
        }
        return RefundsInfoDto
            .builder()
            .refunds(refundDtoList)
            .totalAmount(totalAmount)
            .build();
    }

    /**
     * Converts a list of RefundLineDto to a RefundLinesInfoDto.
     *
     * @param refundLines List of RefundLineDto
     * @return RefundLinesInfoDto
     */
    default RefundLinesInfoDto toRefundLinesInfo(List<RefundLineDto> refundLines) {
        return RefundLinesInfoDto.builder()
            .refundLines(refundLines)
            .totalAmount(refundLines.stream()
                .map(RefundLineDto::getPricePaid)
                .reduce(BigDecimal.ZERO, BigDecimal::add))
            .build();
    }
}
