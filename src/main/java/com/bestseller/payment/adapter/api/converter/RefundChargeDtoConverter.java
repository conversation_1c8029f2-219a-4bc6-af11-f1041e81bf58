package com.bestseller.payment.adapter.api.converter;

import com.bestseller.payment.adapter.api.dto.RefundChargeDto;
import com.bestseller.payment.core.domain.OrderCharge;
import com.bestseller.payment.core.domain.enumeration.ChargedRefundReason;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

@Mapper
public interface RefundChargeDtoConverter {

    @Mapping(target = "isReturnFee",
        expression = "java(com.bestseller.payment.core.domain.enumeration.EntryType.RETURN_FEE.equals(refundCharge.getType()))")
    @Mapping(target = "description", source = "refundReason", qualifiedByName = "getRefundReason")
    @Mapping(target = "amount", expression = "java(refundCharge.getChargeTotal().getGrossDiscountedTotal())")
    @Mapping(target = "title", source = "name")
    RefundChargeDto toRefundChargeDtoInternal(OrderCharge refundCharge);

    /**
     * Converts an OrderCharge entity to a RefundChargeDto.
     *
     * @param refundCharge OrderCharge entity
     * @param csrInitials  CSR initials
     * @return RefundChargeDto
     */
    default RefundChargeDto toRefundChargeDto(OrderCharge refundCharge, String csrInitials) {
        var refundChargeDto = toRefundChargeDtoInternal(refundCharge);
        if (refundChargeDto == null) {
            return null;
        }
        refundChargeDto.setCsrInitials(csrInitials);
        return refundChargeDto;
    }

    /**
     * Converts a list of OrderCharge entities to a list of RefundChargeDto.
     *
     * @param refundCharges List of OrderCharge entities
     * @param csrInitials   CSR initials
     * @return List of RefundChargeDto
     */
    default List<RefundChargeDto> toRefundChargeDtoList(List<OrderCharge> refundCharges, String csrInitials) {
        if (refundCharges == null) {
            return null;
        }
        return refundCharges.stream()
            .map(refundCharge -> toRefundChargeDto(refundCharge, csrInitials))
            .toList();
    }

    /**
     * Get refund reason description.
     *
     * @param refundReason refund reason
     * @return refund reason description
     */
    @Named("getRefundReason")
    default String getRefundReason(ChargedRefundReason refundReason) {
        if (refundReason == null) {
            return null;
        }
        return refundReason.getDescription();
    }
}
