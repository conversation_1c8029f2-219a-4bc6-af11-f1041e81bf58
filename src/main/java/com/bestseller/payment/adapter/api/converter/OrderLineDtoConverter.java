package com.bestseller.payment.adapter.api.converter;

import com.bestseller.payment.adapter.api.dto.OrderLineDto;
import com.bestseller.payment.core.domain.OrderEntryAmount;
import com.bestseller.payment.core.domain.OrderLine;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.math.BigDecimal;

@Mapper
public interface OrderLineDtoConverter {

    @Mapping(target = "pricePaid", source = "orderLine.orderLinePaidAmount", qualifiedByName = "toPricePaid")
    @Mapping(target = "discount", source = "orderLine.orderLinePaidAmount", qualifiedByName = "toDiscount")
    OrderLineDto toOrderLineDto(OrderLine orderLine);

    /**
     * Converts an {@link OrderEntryAmount} to a price paid.
     *
     * @param orderLinePaidAmount the order line paid amount to convert
     * @return the converted price paid
     */
    @Named("toPricePaid")
    default BigDecimal toPricePaid(OrderEntryAmount orderLinePaidAmount) {
        if (orderLinePaidAmount == null) {
            return null;
        }
        return orderLinePaidAmount.getGrossDiscountedUnitPrice();
    }

    /**
     * Converts an {@link OrderEntryAmount} to a discount.
     *
     * @param orderLinePaidAmount the order line paid amount to convert
     * @return the converted discount
     */
    @Named("toDiscount")
    default BigDecimal toDiscount(OrderEntryAmount orderLinePaidAmount) {
        if (orderLinePaidAmount == null) {
            return null;
        }
        return orderLinePaidAmount.getUnitDiscount();
    }

}
