package com.bestseller.payment.adapter.api.controller;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.OrderPlaced;
import com.bestseller.payment.adapter.api.dto.PaymentValidationResponse;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.service.payment.PaymentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("api/v1/payments")
@Slf4j
public class PaymentController {

    private final PaymentService paymentService;

    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "OrderPlaced validated"),
        @ApiResponse(responseCode = "400", description = "OrderPlaced validation failed")
    })
    @Operation(summary = "Validate payment details")
    @PostMapping("/validate-order")
    @ResponseStatus(HttpStatus.OK)
    public PaymentValidationResponse validatePaymentDetails(@RequestBody OrderPlaced orderPlaced) {
        return paymentService.validateOrderPlaced(orderPlaced);
    }

    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payment validated for cancellation"),
        @ApiResponse(responseCode = "404", description = "Order not found")
    })
    @Operation(summary = "Validate if cancel payment can be done")
    @GetMapping(value = "/{orderId}/validate-cancellation")
    public PaymentValidationResponse isValidToCancelPayment(@PathVariable("orderId") String orderId) {
        log.info("Incoming request to validate if cancel payment for orderId={} can be done", orderId);
        return paymentService.validateStateTransition(orderId, PaymentState.CANCELLED);
    }
}
