package com.bestseller.payment.adapter.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RefundDto {
    private int id;
    private String vatId;
    private String refundStatus;
    private ZonedDateTime refundDate;
    private ZonedDateTime lastUpdate;
    private RefundLinesInfoDto refundLinesInfo;
    private List<RefundChargeDto> refundCharges;
    private BigDecimal totalAmount;
    private boolean canBeMarkedAsRefunded;
    private boolean canBeMarkedAsCancelled;
    private String refundRequestId;
}

