package com.bestseller.payment.adapter.api.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentValidationResponse {

    @JsonProperty(value = "isSuccess")
    private boolean isSuccess;
    private List<String> errorMessages;

}
