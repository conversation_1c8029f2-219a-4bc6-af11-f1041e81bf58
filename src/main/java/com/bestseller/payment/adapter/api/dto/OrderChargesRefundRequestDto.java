package com.bestseller.payment.adapter.api.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderChargesRefundRequestDto {
    @NotBlank
    private String csrInitials;

    @Valid
    @NotEmpty
    private List<OrderChargeRefundDto> orderChargesRefundRequestList;
}
