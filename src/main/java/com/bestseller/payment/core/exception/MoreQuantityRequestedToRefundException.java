package com.bestseller.payment.core.exception;

public class MoreQuantityRequestedToRefundException extends RuntimeException {
    public MoreQuantityRequestedToRefundException(String ean, int requestedQuantity, int availableQuantity, String orderId, String refundId) {
        super(String.format("ean %s: Requested quantity: %d exceeds available quantity: %d for orderId: %s and refundId: %s",
            ean, requestedQuantity, availableQuantity, orderId, refundId));
    }
}
