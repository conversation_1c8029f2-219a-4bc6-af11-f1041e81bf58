package com.bestseller.payment.core.exception;

import com.bestseller.payment.core.domain.enumeration.PaymentState;

public class SettlementNotYetHappenedException extends RuntimeException {
    public SettlementNotYetHappenedException(String orderId, PaymentState paymentStatus) {
        super("Order with id %s is not in a payment state that allows refund creation: state = %s"
            .formatted(orderId, paymentStatus));
    }
}
