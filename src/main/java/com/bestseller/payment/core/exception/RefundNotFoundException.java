package com.bestseller.payment.core.exception;

import java.util.UUID;

public class RefundNotFoundException extends NotFoundException {
    private static final String MESSAGE = "Refund not found with id: %s";

    public RefundNotFoundException(int refundId) {
        super(MESSAGE.formatted(refundId));
    }

    public RefundNotFoundException(String refundId) {
        super(MESSAGE.formatted(refundId));
    }

    public RefundNotFoundException(String orderId, UUID giftCardCorrelationId) {
        super("Refund not found with orderId: %s and giftCardCorrelationId: %s".formatted(
            orderId, giftCardCorrelationId));
    }
}
