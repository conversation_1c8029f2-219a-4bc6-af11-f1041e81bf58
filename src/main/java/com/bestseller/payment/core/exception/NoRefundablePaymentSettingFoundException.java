package com.bestseller.payment.core.exception;

import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.domain.enumeration.PaymentType;

public class NoRefundablePaymentSettingFoundException extends RuntimeException {
    /**
     * Exception to be thrown when no refundable payment setting is found for a given order id, payment type and payment state.
     *
     * @param orderId      the order id
     * @param paymentType  the payment type
     * @param paymentState the payment state
     */
    public NoRefundablePaymentSettingFoundException(String orderId,
                                                    PaymentType paymentType,
                                                    PaymentState paymentState) {
        super(
            "No refundable payment setting found for order id: %s, payment type: %s, payment state: %s"
                .formatted(orderId, paymentType, paymentState));
    }
}
