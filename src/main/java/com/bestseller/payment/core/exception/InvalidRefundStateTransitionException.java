package com.bestseller.payment.core.exception;

import com.logistics.statetransition.RefundState;

public class InvalidRefundStateTransitionException extends StateTransitionException {
    /**
     * Constructor.
     *
     * @param refundId  the refund id
     * @param orderId   the order id
     * @param fromState the state from which the transition is made
     * @param toState   the state to which the transition is made
     */
    public InvalidRefundStateTransitionException(int refundId,
                                                 String orderId,
                                                 RefundState fromState,
                                                 RefundState toState) {

        super("Invalid refund state transition from %s to %s for refund: %s. orderId: "
            .formatted(fromState, toState, refundId), orderId);
    }
}
