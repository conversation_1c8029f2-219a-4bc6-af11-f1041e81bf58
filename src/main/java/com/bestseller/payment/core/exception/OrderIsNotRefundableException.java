package com.bestseller.payment.core.exception;

import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.domain.enumeration.PaymentType;

public class OrderIsNotRefundableException extends RuntimeException {
    public OrderIsNotRefundableException(String orderId, PaymentType paymentType, PaymentState paymentState) {
        super("Order with id %s has payment type %s and status %s, which is not refundable."
            .formatted(orderId, paymentType, paymentState));
    }
}
