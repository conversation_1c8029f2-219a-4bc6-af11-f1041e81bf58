package com.bestseller.payment.core.service.refund;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inStoreReturnSettlement.InStoreReturnSettlement;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inStoreReturnSettlement.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequest.PaymentRefundRequest;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundStatusUpdated.RefundStatusUpdated;
import com.bestseller.payment.adapter.api.RefundOptionsService;
import com.bestseller.payment.adapter.stream.messagegenerator.BankPaymentRefundRequestGenerator;
import com.bestseller.payment.adapter.stream.messagegenerator.GiftCardPaymentRefundRequestGenerator;
import com.bestseller.payment.adapter.stream.messagegenerator.RefundStatusUpdatedGenerator;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderEntryAmount;
import com.bestseller.payment.core.domain.Refund;
import com.bestseller.payment.core.domain.RefundLine;
import com.bestseller.payment.core.domain.enumeration.ChargedRefundReason;
import com.bestseller.payment.core.dto.OrderItemToRefund;
import com.bestseller.payment.core.dto.RefundOptions;
import com.bestseller.payment.core.dto.RefundOptionsResponse;
import com.bestseller.payment.core.dto.RefundReason;
import com.bestseller.payment.core.exception.RefundOptionsProviderServiceNotAvailable;
import com.bestseller.payment.core.service.order.OrderService;
import com.bestseller.payment.core.service.price.PriceService;
import com.logistics.statetransition.RefundState;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

@Slf4j
@Service
public class InStoreRefundServiceImpl extends RefundCreationService implements InStoreRefundService {

    private final RefundOptionsService refundOptionsService;
    private final OrderService orderService;

    public InStoreRefundServiceImpl(@Lazy OrderService orderService,
                                    ReturnFeeService returnFeeService,
                                    PriceService priceService,
                                    RefundCalculationService refundCalculationService,
                                    RefundOptionsService refundOptionsService,
                                    QueueProducer<PaymentRefundRequest> paymentRefundRequestProducerQueueProducer,
                                    QueueProducer<RefundStatusUpdated> refundStatusUpdatedProducerQueueProducer,
                                    RefundStatusUpdatedGenerator refundStatusUpdatedGenerator,
                                    GiftCardPaymentRefundRequestGenerator giftCardPaymentRefundRequestGenerator,
                                    BankPaymentRefundRequestGenerator bankPaymentRefundRequestGenerator) {
        super(
            orderService,
            returnFeeService,
            priceService,
            refundCalculationService,
            paymentRefundRequestProducerQueueProducer,
            refundStatusUpdatedProducerQueueProducer,
            refundStatusUpdatedGenerator,
            giftCardPaymentRefundRequestGenerator,
            bankPaymentRefundRequestGenerator
        );
        this.refundOptionsService = refundOptionsService;
        this.orderService = orderService;
    }

    /**
     * Refunds the given items of the order.
     *
     * @param inStoreReturnSettlement the order id
     */
    @SuppressWarnings("IllegalCatch")
    @Transactional
    @Override
    public void issueRefundForInStoreReturn(InStoreReturnSettlement inStoreReturnSettlement) {
        String orderId = inStoreReturnSettlement.getOrderId();

        if (inStoreReturnSettlement.getTotalRefundAmount().equals(BigDecimal.ZERO)) {
            log.info("InStoreReturnSettlement message for order id {} "
                + "has a total refund amount of 0. No refund will be created.", orderId);
            return;
        }

        RefundOptionsResponse refundOptions = refundOptionsService.getRefundOptionsByOrderId(orderId)
            .orElseThrow(() -> new RefundOptionsProviderServiceNotAvailable(orderId));

        Order order = orderService.getOrderById(orderId);

        super.processRefund(order,
            inStoreReturnSettlement.getRefundId().toString(),
            getOrderItemToRefundList(order, inStoreReturnSettlement.getOrderLines()),
            inStoreReturnSettlement.getTotalRefundAmount(),
            RefundOptions.builder()
                .refundReason(RefundReason.RETURN)
                .refundShippingFee(refundOptions.refundShippingFee())
                .chargeReturnFee(refundOptions.chargeReturnFee())
                .build());
    }

    @Override
    protected Refund createRefundObject(Order order, List<OrderItemToRefund> orderItemToRefundList, String requestId,
                                        BigDecimal totalRefundAmount, RefundOptions refundOptions) {
        List<RefundLine> refundLines = new ArrayList<>();

        int numRefundQuantities = orderItemToRefundList.stream()
            .mapToInt(OrderItemToRefund::quantity)
            .sum();


        BigDecimal refundPerLine = totalRefundAmount.divide(BigDecimal.valueOf(numRefundQuantities),
            totalRefundAmount.scale(), RoundingMode.HALF_UP);

        BigDecimal delta = totalRefundAmount.subtract(
            refundPerLine.multiply(BigDecimal.valueOf(numRefundQuantities)).setScale(totalRefundAmount.scale(), RoundingMode.HALF_UP));

        AtomicBoolean isDeltaAdded = new AtomicBoolean(false);

        orderItemToRefundList.forEach(orderItemToRefund -> {
            com.bestseller.payment.core.domain.OrderLine orderLine = order.getOrderLines().stream()
                .filter(ol -> ol.getEan().equals(orderItemToRefund.ean()))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Order line not found for ean : " + orderItemToRefund.ean()));


            RefundLine refundLine = RefundLine.builder()
                .quantity(orderItemToRefund.quantity())
                .orderLine(orderLine)
                .lineNumber(String.valueOf(orderLine.getLineNumber()))
                .refundLineTotal(isDeltaAdded.compareAndSet(false, true)
                    ? OrderEntryAmount.builder()
                    .grossDiscountedUnitPrice(refundPerLine.add(delta))
                    .grossRetailUnitPrice(refundPerLine.add(delta))
                    .build()
                    :
                    OrderEntryAmount.builder()
                        .grossDiscountedUnitPrice(refundPerLine)
                        .grossRetailUnitPrice(refundPerLine)
                        .build())
                .build();
            refundLines.add(refundLine);
        });
        return Refund.builder()
            .requestId(requestId)
            .order(order)
            .csrInitials(refundOptions.csrInitials())
            .refundState(RefundState.CREATED)
            .refundCharges(new ArrayList<>())
            .refundLines(refundLines)
            .build();
    }

    @Override
    protected ChargedRefundReason getChargedRefundReason() {
        return ChargedRefundReason.RETURNED_ITEMS_IN_STORE;
    }

    private List<OrderItemToRefund> getOrderItemToRefundList(Order order, List<OrderLine> refundOrderLines) {
        // create a map between orderLine eans and quantities to refund
        return refundOrderLines.stream()
            .flatMap(refundOrderLine -> order.getOrderLines().stream()
                .filter(orderLine -> orderLine.getEan().equals(refundOrderLine.getEan()))
                .map(orderLine -> OrderItemToRefund.builder()
                    .ean(orderLine.getEan())
                    .quantity(refundOrderLine.getQuantity())
                    .build())
            ).toList();
    }

}
