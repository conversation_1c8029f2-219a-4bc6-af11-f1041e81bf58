package com.bestseller.payment.core.service.refund;

import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.dto.OrderItemToRefund;
import com.bestseller.payment.core.dto.RefundOptions;

import java.util.List;

public interface EcomRefundService {

    void refund(String orderId, List<OrderItemToRefund> itemToRefundList, RefundOptions refundOptions,
                String requestId);

    void issueCancellationRefund(Order order);
}
