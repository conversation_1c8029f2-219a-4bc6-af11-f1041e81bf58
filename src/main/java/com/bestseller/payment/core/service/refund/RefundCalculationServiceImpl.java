package com.bestseller.payment.core.service.refund;

import com.bestseller.payment.core.domain.enumeration.CustomerRefundMethod;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.dto.RefundCalculationResult;
import com.bestseller.payment.core.dto.RefundReason;
import com.bestseller.payment.core.utils.PaymentUtils;
import com.logistics.statetransition.RefundState;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OverallTotal;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.Refund;
import com.bestseller.payment.core.domain.RefundLine;

import java.math.BigDecimal;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Service
public class RefundCalculationServiceImpl implements RefundCalculationService {


    /**
     * Calculate the amount to be refunded by bank transaction and gift card.
     *
     * @param order the order to calculate the refund amount
     * @return the refund calculation result
     */
    @Override
    @NotNull
    public RefundCalculationResult calculate(Order order, RefundReason refundReason) {

        long refundCount = order.getRefunds()
            .stream()
            .filter(r -> r.getRefundState().equals(RefundState.CREATED))
            .count();

        if (refundCount > 1) {
            // Multiple refunds are not supported. You may forget to change the refund state to CREATED.
            throw new IllegalArgumentException("Multiple refunds are not supported for order: " + order.getOrderId());
        }

        int giftCardAuthorisedAmountInCents = PaymentUtils.toIntCents(order.getPayments()
            .stream()
            .filter(payment -> payment.getType().equals(PaymentType.GIFTCARD))
            .map(PaymentInfo::getAuthorisedAmount)
            .map(BigDecimal::new)
            .reduce(BigDecimal.ZERO, BigDecimal::add));

        boolean isPaymentCancelled = order.getPaymentStatus().equals(PaymentState.CANCELLED);

        int nonGiftCardAuthorisedAmountInCents = isPaymentCancelled && refundReason.equals(RefundReason.RETURN)
            ? 0 : PaymentUtils.toIntCents(order.getPayments()
            .stream()
            .filter(payment -> !payment.getType().equals(PaymentType.GIFTCARD))
            .map(PaymentInfo::getAuthorisedAmount)
            .map(BigDecimal::new)
            .reduce(BigDecimal.ZERO, BigDecimal::add));

        int requestedRefundInCents = order.getRefunds()
            .stream()
            .filter(r -> r.getRefundState().equals(RefundState.CREATED))
            .findFirst()
            .map(refund -> PaymentUtils.toIntCents(refund.getRefundTotal().getGrossDiscountedTotal()))
            .orElseThrow(() -> new IllegalArgumentException("No requested refund found for order: " + order.getOrderId()));

        int alreadyRefundedAmountInCents = PaymentUtils.toIntCents(order.getRefunds()
            .stream()
            .filter(r -> r.getRefundState().isGreaterThan(RefundState.CREATED))
            .map(Refund::getRefundTotal)
            .map(OverallTotal::getGrossDiscountedTotal)
            .reduce(BigDecimal.ZERO, BigDecimal::add));

        validate(
            refundReason,
            requestedRefundInCents,
            PaymentUtils.toIntCents(order.getTotalPaidPrice().getGrossDiscountedTotal()),
            nonGiftCardAuthorisedAmountInCents,
            giftCardAuthorisedAmountInCents
        );

        AtomicInteger amountToRefundByBankInCents = new AtomicInteger();
        AtomicInteger amountToRefundByGiftCardInCents = new AtomicInteger();

        boolean hasSettlementRequiredPayments = order.getPayments()
            .stream()
            .anyMatch(payment -> payment.getType().isSettlementRequired());

        int nonGiftCardPotentialRefundInCents = Math.max(0,
            PaymentUtils.toIntCents(order.getTotalPaidPrice().getGrossDiscountedTotal()) - giftCardAuthorisedAmountInCents);

        int openNonGiftCardAmountInCents = Math.max(0,
            (hasSettlementRequiredPayments ? nonGiftCardPotentialRefundInCents : nonGiftCardAuthorisedAmountInCents)
                - alreadyRefundedAmountInCents);

        if (requestedRefundInCents <= openNonGiftCardAmountInCents) {
            amountToRefundByBankInCents.set(requestedRefundInCents);
        } else {
            amountToRefundByBankInCents.set(openNonGiftCardAmountInCents);
            amountToRefundByGiftCardInCents.set(Math.min(
                giftCardAuthorisedAmountInCents,
                requestedRefundInCents - openNonGiftCardAmountInCents));
        }

        // re-calculate the refund amounts based on the customer refund choices
        if (amountToRefundByBankInCents.get() > 0 && refundReason == RefundReason.RETURN) {
            recalculateRefundBasedOnCustomerRefundChoice(order, amountToRefundByBankInCents, amountToRefundByGiftCardInCents);
        }

        return RefundCalculationResult.builder()
            .amountToRefundByGiftCardInCents(amountToRefundByGiftCardInCents.get())
            .amountToRefundByBankTransactionInCents(amountToRefundByBankInCents.get())
            .build();
    }

    private static void recalculateRefundBasedOnCustomerRefundChoice(Order order,
                                                                     AtomicInteger amountToRefundByBankInCents,
                                                                     AtomicInteger amountToRefundByGiftCardInCents
    ) {
        order.getRefunds()
            .stream()
            .filter(refund -> refund.getRefundState() == RefundState.CREATED)
            .findFirst()
            .ifPresent(refund -> {
                refund.getRefundLines()
                    .forEach(refundLine -> {
                        for (int i = 0; i < refundLine.getQuantity(); i++) {
                            applyGiftCardChoiceIfAvailable(amountToRefundByBankInCents, amountToRefundByGiftCardInCents, refundLine);
                        }
                    });
            });
    }

    private static void applyGiftCardChoiceIfAvailable(AtomicInteger amountToRefundByBankInCents,
                                                       AtomicInteger amountToRefundByGiftCardInCents,
                                                       RefundLine refundLine) {
        refundLine.getOrderLine().getCustomerRefundChoices()
            .stream()
            .filter(choice -> choice.getCustomerRefundMethod() == CustomerRefundMethod.GIFT_CARD)
            .filter(choice -> !choice.isUsed())
            .findFirst()
            .ifPresent(choice -> {
                choice.setUsed(true);
                int refundLineAmountInCents = PaymentUtils.toIntCents(refundLine
                    .getRefundLineTotal()
                    .getGrossDiscountedUnitPrice());
                if (refundLineAmountInCents < amountToRefundByBankInCents.get()) {
                    amountToRefundByBankInCents.addAndGet(-refundLineAmountInCents);
                    amountToRefundByGiftCardInCents.addAndGet(refundLineAmountInCents);
                } else {
                    amountToRefundByGiftCardInCents.addAndGet(amountToRefundByBankInCents.get());
                    amountToRefundByBankInCents.set(0);
                }
            });
    }

    private static void validate(RefundReason refundReason,
                                 int requestedRefundInCents,
                                 int openTotalInCents,
                                 int nonGiftCardAuthorisedAmountInCents,
                                 int giftCardAuthorisedAmountInCents) {
        if ((refundReason.equals(RefundReason.RETURN))
            && requestedRefundInCents > openTotalInCents) {
            log.info("requestedRefundInCents: {}, openTotalInCents: {}",
                requestedRefundInCents, openTotalInCents);
            throw new IllegalArgumentException("Requested refund amount exceeds the maximum refundable amount");
        }

        if (requestedRefundInCents > nonGiftCardAuthorisedAmountInCents + giftCardAuthorisedAmountInCents) {
            log.info("requestedRefundInCents: {}, nonGiftCardAuthorisedAmountInCents: {}, giftCardAuthorisedAmountInCents: {}",
                requestedRefundInCents, nonGiftCardAuthorisedAmountInCents, giftCardAuthorisedAmountInCents);
            throw new IllegalArgumentException("Requested refund amount exceeds the total authorised amount");
        }
    }
}
