package com.bestseller.payment.core.service.payment.processors;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.Payment;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.enumeration.PaymentMethod;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;

public interface PaymentProcessor {
    PaymentInfo createPayment(PaymentMethod paymentMethod,
                          boolean isBcmc,
                          String subMethod,
                          String billingCountryCode);

    ProcessorId getProcessorId();

    void validate(Payment payment, String billingCountryCode);
}
