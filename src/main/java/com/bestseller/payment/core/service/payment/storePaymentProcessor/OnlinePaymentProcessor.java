package com.bestseller.payment.core.service.payment.storePaymentProcessor;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.Payment;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.enumeration.PaymentMethod;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import com.bestseller.payment.core.service.payment.factory.PaymentFactory;
import lombok.Getter;

import java.util.List;

@Getter
public class OnlinePaymentProcessor extends PaymentProcessor {
        private final PaymentFactory paymentFactory;

        public OnlinePaymentProcessor(List<Payment> payments, String country, Order order, PaymentFactory paymentFactory) {
                super(payments, country, order);
                this.paymentFactory = paymentFactory;
        }

        @Override
        public PaymentInfo createPayment(Payment payment, String country) {
                PaymentMethod paymentMethod = PaymentMethod.findByName(payment.getMethod());
                ProcessorId processorId = ProcessorId.findByName(payment.getProvider());
                return this.paymentFactory.createPayment(processorId, paymentMethod, payment.getPspReference(),
                        payment.getSubMethod(), country, payment.getAmount());
        }

        @Override
        public PaymentState getPaymentState(Payment payment) {
                return PaymentState.fromDwDescription(payment.getState());
        }
}
