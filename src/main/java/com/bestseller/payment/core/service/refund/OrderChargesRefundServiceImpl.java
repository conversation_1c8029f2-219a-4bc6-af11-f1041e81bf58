package com.bestseller.payment.core.service.refund;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequest.PaymentRefundRequest;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundStatusUpdated.RefundStatusUpdated;
import com.bestseller.payment.adapter.stream.messagegenerator.BankPaymentRefundRequestGenerator;
import com.bestseller.payment.adapter.stream.messagegenerator.GiftCardPaymentRefundRequestGenerator;
import com.bestseller.payment.adapter.stream.messagegenerator.RefundStatusUpdatedGenerator;
import com.bestseller.payment.adapter.stream.validator.PaymentValidation;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.Refund;
import com.bestseller.payment.core.domain.enumeration.ChargedRefundReason;
import com.bestseller.payment.core.dto.OrderChargesRefundRequest;
import com.bestseller.payment.core.dto.OrderItemToRefund;
import com.bestseller.payment.core.dto.RefundOptions;
import com.bestseller.payment.core.dto.RefundReason;
import com.bestseller.payment.core.exception.RefundOperationNotAllowedException;
import com.bestseller.payment.core.exception.SettlementNotYetHappenedException;
import com.bestseller.payment.core.service.order.OrderService;
import com.bestseller.payment.core.service.price.PriceService;
import com.logistics.statetransition.RefundState;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class OrderChargesRefundServiceImpl extends RefundCreationService implements OrderChargesRefundService {
    private final OrderService orderService;
    private final PaymentValidation paymentValidation;

    /**
     * Constructor.
     *
     * @param orderService                              order service
     * @param returnFeeService                          return fee service
     * @param priceService                              price service
     * @param refundCalculationService                  refund calculation service
     * @param paymentRefundRequestProducerQueueProducer payment refund request producer
     * @param refundStatusUpdatedProducerQueueProducer  refund status updated producer
     * @param refundStatusUpdatedGenerator              refund status updated generator
     * @param giftCardPaymentRefundRequestGenerator     gift card payment refund request generator
     * @param bankPaymentRefundRequestGenerator         bank payment refund request generator
     * @param paymentValidation                         payment validation
     */
    public OrderChargesRefundServiceImpl(OrderService orderService,
                                         ReturnFeeService returnFeeService,
                                         PriceService priceService,
                                         RefundCalculationService refundCalculationService,
                                         QueueProducer<PaymentRefundRequest> paymentRefundRequestProducerQueueProducer,
                                         QueueProducer<RefundStatusUpdated> refundStatusUpdatedProducerQueueProducer,
                                         RefundStatusUpdatedGenerator refundStatusUpdatedGenerator,
                                         GiftCardPaymentRefundRequestGenerator giftCardPaymentRefundRequestGenerator,
                                         BankPaymentRefundRequestGenerator bankPaymentRefundRequestGenerator,
                                         PaymentValidation paymentValidation
    ) {
        super(
            orderService,
            returnFeeService,
            priceService,
            refundCalculationService,
            paymentRefundRequestProducerQueueProducer,
            refundStatusUpdatedProducerQueueProducer,
            refundStatusUpdatedGenerator,
            giftCardPaymentRefundRequestGenerator,
            bankPaymentRefundRequestGenerator
        );
        this.orderService = orderService;
        this.paymentValidation = paymentValidation;
    }

    @Override
    protected Refund createRefundObject(Order order,
                                        List<OrderItemToRefund> orderItemToRefundList,
                                        String requestId,
                                        BigDecimal totalRefundAmount,
                                        RefundOptions refundOptions) {
        return Refund.builder()
            .refundLines(new ArrayList<>())
            .refundState(RefundState.CREATED)
            .order(order)
            .csrInitials(refundOptions.csrInitials())
            .refundCharges(new ArrayList<>())
            .requestId(requestId)
            .build();
    }

    @Override
    protected ChargedRefundReason getChargedRefundReason() {
        throw new UnsupportedOperationException("OrderChargesRefundService does not support charged refund reason");
    }

    /**
     * Refund order charges.
     *
     * @param orderChargesRefundRequest the order charges refund request
     */
    @SuppressWarnings("MultipleStringLiterals")
    @Transactional
    public void refundOrderCharges(OrderChargesRefundRequest orderChargesRefundRequest) {
        var order = orderService.getOrderById(orderChargesRefundRequest.orderId());

        if (StringUtils.isEmpty(order.getVatOrderNumber())) {
            throw new RefundOperationNotAllowedException("Refund operation is not allowed for order without VAT number.");
        }

        try {
            if (!paymentValidation.validateRefundRequest(order)) {
                throw new RefundOperationNotAllowedException(
                    "Refund operation is not allowed for order %s in state %s".formatted(
                        order.getOrderId(), order.getPaymentStatus()));
            }
        } catch (SettlementNotYetHappenedException e) {
            throw new RefundOperationNotAllowedException(
                "Refund operation is not allowed for order %s in state %s".formatted(
                    order.getOrderId(), order.getPaymentStatus()));
        }

        super.processRefund(
            order,
            UUID.randomUUID().toString(), // Can we use a constant for this?
            List.of(),
            BigDecimal.ZERO,
            RefundOptions.builder()
                .orderChargeRefundRequests(orderChargesRefundRequest.orderChargesRefundRequestList())
                .chargeReturnFee(false)
                .refundShippingFee(false)
                .refundReason(RefundReason.MANUAL_ORDER_CHARGE_REFUND)
                .csrInitials(orderChargesRefundRequest.csrInitials())
                .build()
        );
    }
}
