package com.bestseller.payment.core.service.vat;

import com.bestseller.payment.core.domain.Order;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.Clock;
import java.time.Instant;
import java.util.Date;

@RequiredArgsConstructor
@Component
public class OrderVatServiceImpl implements OrderVatService {

    private final VatSequenceService vatSequenceService;
    private final Clock cetClock;

    @Override
    public String getNextOrderVatId(Order order) {
        var countryCode = getCountryCode(order);
        Date orderDate = Date.from(order.getOrderDate().toInstant().atZone(cetClock.getZone()).toInstant());
        return vatSequenceService.getNextOrderVatId(countryCode, orderDate);
    }

    @Override
    public String getNextRefundVatId(Order order) {
        var countryCode = getCountryCode(order);
        Date refundDate = Date.from(Instant.now(cetClock));
        return vatSequenceService.getNextRefundVatId(countryCode, refundDate);
    }

    @Override
    public String getNextRefundInStoreVatId(Order order, long refundId) {
        var countryCode = getCountryCode(order);
        return vatSequenceService.getNextRefundInStoreVatId(countryCode, refundId);
    }

    private String getCountryCode(Order order) {
        return order.getShippingCountryCode() != null ? order.getShippingCountryCode() : order.getBillingCountryCode();
    }
}
