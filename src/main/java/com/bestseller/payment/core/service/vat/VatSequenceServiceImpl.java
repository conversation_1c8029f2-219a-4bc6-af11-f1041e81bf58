package com.bestseller.payment.core.service.vat;

import com.bestseller.payment.adapter.repository.VatSequenceRepository;
import com.bestseller.payment.core.domain.enumeration.VatType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

@Service
public class VatSequenceServiceImpl implements VatSequenceService {

    private static final String ONE = "1";

    private final int vatSequenceNumberLength;

    private final String refundInStoreInfix;

    private final VatSequenceRepository vatSequenceRepository;

    /**
     * Constructor.
     *
     * @param vatSequenceNumberLength vat sequence number length
     * @param refundInStoreInfix      refund in store infix
     * @param vatSequenceRepository   vat sequence repository
     */
    public VatSequenceServiceImpl(@Value("${vat.sequence.number.length}") int vatSequenceNumberLength,
                                  @Value("${refund.in-store.infix}") String refundInStoreInfix,
                                  VatSequenceRepository vatSequenceRepository) {
        this.vatSequenceNumberLength = vatSequenceNumberLength;
        this.refundInStoreInfix = refundInStoreInfix;
        this.vatSequenceRepository = vatSequenceRepository;
    }

    /**
     * Get next order vat id.
     * format: countryCode + sequenceYear + 1 + sequence
     *
     * @param countryCode country code
     * @param orderDate   order date
     * @return next sequence
     */
    @Override
    public String getNextOrderVatId(String countryCode, Date orderDate) {
        return getNextSequence(countryCode, getSequenceYear(orderDate), VatType.ORDER);
    }

    /**
     * Get next refund vat id.
     * format: countryCode + sequenceYear + 1 + sequence
     *
     * @param countryCode country code
     * @param refundDate   order date
     * @return next sequence
     */
    @Override
    public String getNextRefundVatId(String countryCode, Date refundDate) {
        return getNextSequence(countryCode, getSequenceYear(refundDate), VatType.REFUND);
    }

    /**
     * Get next refund in store vat id.
     * format: countryCode-REFUND_IN_STORE_INFIX-refundId
     *
     * @param countryCode country code
     * @param refundId    refund id
     * @return next sequence
     */
    @Override
    public String getNextRefundInStoreVatId(String countryCode, long refundId) {
        return countryCode + refundInStoreInfix + refundId;
    }

    /**
     * Get next sequence for vat id.
     * format: countryCode + sequenceYear + 1 + sequence
     *
     * @param countryCode  country code
     * @param sequenceYear sequence year
     * @param vatType      vat type
     * @return next sequence
     */
    private String getNextSequence(String countryCode, int sequenceYear, VatType vatType) {
        Long sequence = vatSequenceRepository.updateSequenceAndGetLastId(countryCode, sequenceYear, vatType.name());
        String sequenceString = addPadding(sequence);
        return countryCode + String.valueOf(sequenceYear).substring(2) + ONE + sequenceString;
    }

    private int getSequenceYear(Date orderDate) {
        LocalDate localDate = orderDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return localDate.getYear();
    }

    private String addPadding(Long sequence) {
        int paddingLength = vatSequenceNumberLength - sequence.toString().length();
        return String.format("%0" + paddingLength + "d%s", 0, sequence);
    }
}
