package com.bestseller.payment.core.service.refund;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequest.PaymentRefundRequest;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundStatusUpdated.RefundStatusUpdated;
import com.bestseller.payment.adapter.stream.messagegenerator.BankPaymentRefundRequestGenerator;
import com.bestseller.payment.adapter.stream.messagegenerator.GiftCardPaymentRefundRequestGenerator;
import com.bestseller.payment.adapter.stream.messagegenerator.RefundStatusUpdatedGenerator;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderCharge;
import com.bestseller.payment.core.domain.OrderLine;
import com.bestseller.payment.core.domain.Refund;
import com.bestseller.payment.core.domain.RefundLine;
import com.bestseller.payment.core.domain.enumeration.ChargedRefundReason;
import com.bestseller.payment.core.domain.enumeration.CustomerRefundMethod;
import com.bestseller.payment.core.domain.enumeration.EntryType;
import com.bestseller.payment.core.domain.enumeration.RefundType;
import com.bestseller.payment.core.dto.GiftCardRefundSource;
import com.bestseller.payment.core.dto.OrderItemToRefund;
import com.bestseller.payment.core.dto.RefundCalculationResult;
import com.bestseller.payment.core.dto.RefundDto;
import com.bestseller.payment.core.dto.RefundOptions;
import com.bestseller.payment.core.dto.RefundReason;
import com.bestseller.payment.core.dto.ReturnFeeCalculationResult;
import com.bestseller.payment.core.exception.OrderChargeAlreadyRefundedException;
import com.bestseller.payment.core.exception.OrderChargeNotFoundToRefundException;
import com.bestseller.payment.core.service.order.OrderService;
import com.bestseller.payment.core.service.price.PriceService;
import com.logistics.statetransition.RefundState;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Slf4j
public abstract class RefundCreationService {
    protected static final String SYSTEM_USER = "SYSTEM";
    private final OrderService orderService;
    private final ReturnFeeService returnFeeService;
    private final PriceService priceService;
    private final RefundCalculationService refundCalculationService;
    private final QueueProducer<PaymentRefundRequest> paymentRefundRequestProducerQueueProducer;
    private final QueueProducer<RefundStatusUpdated> refundStatusUpdatedProducerQueueProducer;
    private final RefundStatusUpdatedGenerator refundStatusUpdatedGenerator;
    private final GiftCardPaymentRefundRequestGenerator giftCardPaymentRefundRequestGenerator;
    private final BankPaymentRefundRequestGenerator bankPaymentRefundRequestGenerator;

    /**
     * Instantiates a new Refund creation service.
     *
     * @param orderService                              the order service
     * @param returnFeeService                          the return fee service
     * @param priceService                              the price service
     * @param refundCalculationService                  the refund calculation service
     * @param paymentRefundRequestProducerQueueProducer the gift card payment refund request producer
     * @param refundStatusUpdatedProducerQueueProducer  the refund status updated producer queue producer
     * @param refundStatusUpdatedGenerator              the refund status updated generator
     * @param giftCardPaymentRefundRequestGenerator     the gift card payment refund request generator
     * @param bankPaymentRefundRequestGenerator         the bank payment refund request generator
     */
    public RefundCreationService(OrderService orderService,
                                 ReturnFeeService returnFeeService,
                                 PriceService priceService,
                                 RefundCalculationService refundCalculationService,
                                 QueueProducer<PaymentRefundRequest> paymentRefundRequestProducerQueueProducer,
                                 QueueProducer<RefundStatusUpdated> refundStatusUpdatedProducerQueueProducer,
                                 RefundStatusUpdatedGenerator refundStatusUpdatedGenerator,
                                 GiftCardPaymentRefundRequestGenerator giftCardPaymentRefundRequestGenerator,
                                 BankPaymentRefundRequestGenerator bankPaymentRefundRequestGenerator) {
        this.orderService = orderService;
        this.returnFeeService = returnFeeService;
        this.priceService = priceService;
        this.refundCalculationService = refundCalculationService;
        this.paymentRefundRequestProducerQueueProducer = paymentRefundRequestProducerQueueProducer;
        this.refundStatusUpdatedProducerQueueProducer = refundStatusUpdatedProducerQueueProducer;
        this.refundStatusUpdatedGenerator = refundStatusUpdatedGenerator;
        this.giftCardPaymentRefundRequestGenerator = giftCardPaymentRefundRequestGenerator;
        this.bankPaymentRefundRequestGenerator = bankPaymentRefundRequestGenerator;
    }

    /**
     * Refunds the given items of the order.
     *
     * @param order                 the order
     * @param requestId             the request id
     * @param orderItemToRefundList the items to refund
     * @param totalRefundAmount     the total refund amount
     * @param refundOptions         the refund options
     */

    @SuppressWarnings("IllegalCatch")
    protected void processRefund(Order order, String requestId,
                                 List<OrderItemToRefund> orderItemToRefundList,
                                 BigDecimal totalRefundAmount, RefundOptions refundOptions) {

        log.info("Start processing refund request. orderId:{}, requestId:{}, refundOptions:{}, itemsToRefund:{}",
            order.getOrderId(), requestId, refundOptions, orderItemToRefundList);


        final Refund refund = createRefundObject(order, orderItemToRefundList, requestId,
            totalRefundAmount, refundOptions);

        // Initial calculation of refund amount before charging return fees
        priceService.processAndSetPriceFieldsForRefundLineAndRefundTotalPaidAmount(refund);

        order.addRefund(refund);

        applyReturnFee(order, refundOptions, refund);

        applyRefundShippingFee(order, refundOptions, refund);

        applyManualOrderChargeRefund(order, refundOptions, refund);

        // Recalculate final refund amount
        priceService.processAndSetPriceFieldsForRefundLineAndRefundTotalPaidAmount(refund);

        final RefundCalculationResult calculationResult = refundCalculationService.calculate(order,
            refundOptions.refundReason());

        refund.setRefundType(RefundType.from(
            calculationResult.isRefundableByBankTransaction(), calculationResult.isRefundableByGiftCard()));

        if (calculationResult.isRefundableByGiftCard()) {
            refund.setGiftCardCorrelationId(UUID.randomUUID());
        }

        /* Save the order with the new Refund.
         * The id of the refund is needed to publish the refund request to the payment provider
         */
        var savedOrder = orderService.save(order, true);
        final Refund savedRefund = savedOrder.getRefunds().getLast();

        // This is a sanity check to make sure that we chose the correct refund object
        if (refund.getRefundState() != RefundState.CREATED) {
            throw new IllegalStateException("Refund state is not CREATED. Refund state is " + refund.getRefundState());
        }

        log.info("Order {} saved with refund {}", order.getOrderId(), savedRefund.getId());

        /* Set the refund state to REFUND_SUCCESS if the refund is created
         * but no `PaymentRefundRequest` message is published. (The amount to refund is 0)
         */

        RefundState refundState = RefundState.REFUND_SUCCESS;

        if (calculationResult.isRefundableByGiftCard()) {
            refundState = processGiftCardRefund(order, savedRefund, calculationResult, refundOptions);
        }

        if (calculationResult.isRefundableByBankTransaction()) {
            refundState = processBankTransactionRefund(order, savedRefund, calculationResult, refundOptions);
        }

        boolean onlyRefundedByGiftCard = calculationResult.isRefundableByGiftCard()
            && !calculationResult.isRefundableByBankTransaction();
        if (!onlyRefundedByGiftCard) {
            RefundStatusUpdated refundStatusUpdated = refundStatusUpdatedGenerator.generate(savedRefund);
            refundStatusUpdatedProducerQueueProducer.enqueue(EnqueueParams.create(refundStatusUpdated));
        }

        savedRefund.setRefundState(refundState);
        orderService.save(savedOrder, true);

        if (!onlyRefundedByGiftCard) {
            RefundStatusUpdated refundStatusUpdated = refundStatusUpdatedGenerator.generate(savedRefund);
            refundStatusUpdatedProducerQueueProducer.enqueue(EnqueueParams.create(refundStatusUpdated));
        }

        log.info("Refund with orderId:{}, requestId:{}, refundReason:{}, totalAmount:{} is processed.",
            order.getOrderId(), requestId, refundOptions.refundReason(), totalRefundAmount);
    }

    private void applyRefundShippingFee(Order order, RefundOptions refundOptions, Refund refund) {
        if (refundOptions.refundShippingFee()) {
            order.getOrderCharges().stream()
                .filter(orderCharge -> orderCharge.getType().equals(EntryType.SHIPMENT_FEE))
                .filter(orderCharge -> !orderCharge.getRefunded())
                .forEach(orderCharge -> {
                    refundOrderCharge(orderCharge, getChargedRefundReason(), refund);
                });
            log.info("Shipping fee refunded for order {}", order.getOrderId());
        }
    }

    private void applyReturnFee(Order order, RefundOptions refundOptions, Refund refund) {
        if (refundOptions.chargeReturnFee()) {
            ReturnFeeCalculationResult returnFeeCalculationResult =
                returnFeeService.calculateReturnFee(order.getOrderDate(),
                    order.getShippingCountryCode(),
                    order.getBrand().getBrandAbbreviation(),
                    refund.getRefundTotal().getGrossDiscountedTotal());
            if (returnFeeCalculationResult.chargeable()) {
                OrderCharge orderCharge = returnFeeService.createReturnFeeCharge(returnFeeCalculationResult.returnFee(),
                    returnFeeCalculationResult.taxRate());
                orderCharge.setRefundReason(getChargedRefundReason());
                order.getOrderCharges().add(orderCharge);
                refund.getRefundCharges().add(orderCharge);
                log.info("Return fee charge <<{}>> created for order {}", returnFeeCalculationResult.returnFee(), order.getOrderId());
            }
        }
    }

    private void applyManualOrderChargeRefund(Order order, RefundOptions refundOptions, Refund refund) {
        if (refundOptions.orderChargeRefundRequests().isEmpty()) {
            return;
        }

        refundOptions.orderChargeRefundRequests()
            .forEach(request -> {
                var orderCharge = order.getOrderCharges().stream()
                    .filter(charge -> charge.getOrderEntryId() == request.orderChargeId())
                    .findFirst()
                    .orElseThrow(()
                        -> new OrderChargeNotFoundToRefundException(order.getOrderId(), request.orderChargeId()));
                if (orderCharge.getRefunded()) {
                    throw new OrderChargeAlreadyRefundedException(order.getOrderId(), request.orderChargeId());
                }
                ChargedRefundReason chargedRefundReason = ChargedRefundReason
                    .fromDescriptionAndEntryType(request.reason(), orderCharge.getType());
                refundOrderCharge(orderCharge, chargedRefundReason, refund);
            });

        log.info("Order charge refunded manually for order {}", order.getOrderId());
    }

    /**
     * Refund order charges.
     *
     * @param orderCharge  the order charge
     * @param refundReason the refund reason
     * @param refund       the refund
     */
    protected void refundOrderCharge(OrderCharge orderCharge,
                                     ChargedRefundReason refundReason,
                                     Refund refund) {
        orderCharge.setRefunded(true);
        orderCharge.setRefundReason(refundReason);
        refund.getRefundCharges().add(orderCharge);
    }

    @SuppressWarnings("IllegalCatch")
    private RefundState processGiftCardRefund(Order order, Refund savedRefund,
                                              RefundCalculationResult calculationResult,
                                              RefundOptions refundOptions) {
        try {
            log.info("Publishing refund request <<GiftCardPaymentRefundRequestProducer>> for order {}",
                order.getOrderId());

            GiftCardRefundSource giftCardRefundSource = hasCustomerGiftCardChoice(savedRefund)
                ? GiftCardRefundSource.CUSTOMER_PREFERENCE
                : GiftCardRefundSource.ORIGINAL_PAYMENT;

            RefundDto refundDto = createRefundDto(
                calculationResult.amountToRefundByGiftCardInCents(),
                savedRefund,
                refundOptions.refundReason(),
                giftCardRefundSource
            );

            PaymentRefundRequest paymentRefundRequest = giftCardPaymentRefundRequestGenerator.generate(refundDto);
            paymentRefundRequestProducerQueueProducer.enqueue(EnqueueParams.create(paymentRefundRequest));
            return RefundState.REFUND_REQUESTING;
        } catch (Exception ex) {
            log.error("Failed to publish refund request <<GiftCardPaymentRefundRequestProducer>> for order {}",
                order.getOrderId(), ex);
            return RefundState.REFUND_FAILED;
        }
    }

    @SuppressWarnings("IllegalCatch")
    private RefundState processBankTransactionRefund(Order order, Refund savedRefund,
                                                    RefundCalculationResult calculationResult,
                                                    RefundOptions refundOptions) {
        try {
            log.info("Publishing refund request <<BankPaymentRefundRequestProducer>> for order {}",
                order.getOrderId());

            RefundDto refundDto = createRefundDto(
                calculationResult.amountToRefundByBankTransactionInCents(),
                savedRefund,
                refundOptions.refundReason(),
                null
            );

            PaymentRefundRequest paymentRefundRequest = bankPaymentRefundRequestGenerator.generate(refundDto);
            paymentRefundRequestProducerQueueProducer.enqueue(EnqueueParams.create(paymentRefundRequest));
            return RefundState.REFUND_REQUESTING;
        } catch (Exception ex) {
            log.error("Failed to publish refund request <<BankPaymentRefundRequestProducer>> for order {}",
                order.getOrderId(), ex);
            return RefundState.REFUND_FAILED;
        }
    }

    private RefundDto createRefundDto(int refundAmountInCents, Refund refund,
                                      RefundReason refundReason, GiftCardRefundSource giftCardRefundSource) {
        return RefundDto
            .builder()
            .refundAmountInCents(refundAmountInCents)
            .refund(refund)
            .refundReason(refundReason)
            .giftCardRefundSource(giftCardRefundSource)
            .build();
    }

    private boolean hasCustomerGiftCardChoice(Refund refund) {
        return refund.getRefundLines()
            .stream()
            .map(RefundLine::getOrderLine)
            .map(OrderLine::getCustomerRefundChoices)
            .filter(Objects::nonNull)
            .flatMap(List::stream)
            .anyMatch(choice -> choice.getCustomerRefundMethod() == CustomerRefundMethod.GIFT_CARD);
    }

    protected abstract Refund createRefundObject(Order loadedOrder, List<OrderItemToRefund> orderItemToRefundList,
                                                 String requestId, BigDecimal totalRefundAmount, RefundOptions refundOptions);

    protected abstract ChargedRefundReason getChargedRefundReason();
}
