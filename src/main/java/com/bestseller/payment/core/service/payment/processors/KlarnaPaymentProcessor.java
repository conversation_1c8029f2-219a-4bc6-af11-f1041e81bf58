package com.bestseller.payment.core.service.payment.processors;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.Payment;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.enumeration.CountryCode2AccountSubMethodMapping;
import com.bestseller.payment.core.domain.enumeration.PaymentMethod;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import com.bestseller.payment.core.domain.payment.KlarnaPayment;
import com.bestseller.payment.core.exception.PaymentValidationException;
import org.springframework.stereotype.Component;

@Component
public class KlarnaPaymentProcessor implements PaymentProcessor {
    private static final String KLARNA_INVOICE_SUB_METHOD = "-1";

    @Override
    public PaymentInfo createPayment(PaymentMethod paymentMethod,
                                 boolean isBcmc,
                                 String subMethod,
                                 String billingCountryCode) {
        String klarnaSubMethod = getKlarnaSubMethod(paymentMethod, billingCountryCode);
        return KlarnaPayment.builder()
                .processorId(getProcessorId())
                .subMethod(klarnaSubMethod)
                .type(PaymentType.KLARNA).build();
    }

    private String getKlarnaSubMethod(PaymentMethod paymentMethod, String billingCountryCode) {
        if (PaymentMethod.KLARNA_ACCOUNT.equals(paymentMethod)) {
            return getCodeByCountry(billingCountryCode);
        } else if (paymentMethod.equals(PaymentMethod.KLARNA_INVOICE)) {
            return KLARNA_INVOICE_SUB_METHOD;
        } else {
            throw new PaymentValidationException(String.format("Payment method %s is invalid for Klarna.",
                    paymentMethod));
        }
    }

    @Override
    public ProcessorId getProcessorId() {
        return ProcessorId.KLARNA_PAYMENTS;
    }

    @Override
    public void validate(Payment payment, String billingCountryCode) {
        getKlarnaSubMethod(PaymentMethod.findByName(payment.getMethod()), billingCountryCode);
    }

    private String getCodeByCountry(String billingCountryCode) {
        String countryCodeMapping = null;
        try {
            countryCodeMapping = CountryCode2AccountSubMethodMapping.getCodeByCountry(billingCountryCode);
        } catch (IllegalArgumentException exception) {
            throw new PaymentValidationException(String.format("%s for payment method KLARNA_ACCOUNT.",
                    exception.getMessage()));
        }
        return countryCodeMapping;
    }
}

