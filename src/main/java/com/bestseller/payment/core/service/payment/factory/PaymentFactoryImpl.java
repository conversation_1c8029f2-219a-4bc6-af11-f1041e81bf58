package com.bestseller.payment.core.service.payment.factory;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.Payment;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.enumeration.PaymentMethod;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import com.bestseller.payment.core.service.payment.processors.PaymentProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class PaymentFactoryImpl implements PaymentFactory {
    private static final String ADYEN_BCMC_SUB_METHOD = "bcmc";
    private static final String ADYEN_BCMC_MOBILE_SUB_METHOD = "bcmc_mobile";
    private static final String WRONG_PROCESSOR_ID_MESSAGE = "Unexpected payment provider: ";
    private final Map<ProcessorId, PaymentProcessor> processorMap = new HashMap<>();

    @Autowired
    public PaymentFactoryImpl(List<PaymentProcessor> paymentProcessors) {
        paymentProcessors.forEach(processor -> processorMap.put(processor.getProcessorId(), processor));
    }

    @Override
    public PaymentInfo createPayment(ProcessorId processorId, PaymentMethod paymentMethod, String pspReference, String subMethod,
                                 String billingCountryCode, BigDecimal authorizedAmount) {

        PaymentProcessor processor = getPaymentProcessor(processorId);
        PaymentInfo payment = processor.createPayment(paymentMethod, isBcmcPayment(subMethod), subMethod, billingCountryCode);

        payment.setSubMethodName(paymentMethod);
        payment.setAuthorisedAmount(authorizedAmount.toString());
        payment.setPaymentReference(pspReference);
        return payment;
    }

    @Override
    public void validateProcessor(Payment payment, String billingCountryCode) {
        PaymentProcessor processor = getPaymentProcessor(ProcessorId.findByName(payment.getProvider()));
        processor.validate(payment, billingCountryCode);
    }

    private boolean isBcmcPayment(String subMethod) {
        return ADYEN_BCMC_SUB_METHOD.equals(subMethod) || ADYEN_BCMC_MOBILE_SUB_METHOD.equals(subMethod);
    }

    private PaymentProcessor getPaymentProcessor(ProcessorId processorId) {
        PaymentProcessor processor = processorMap.get(processorId);
        if (processor == null) {
            throw new IllegalArgumentException(WRONG_PROCESSOR_ID_MESSAGE + processorId.name());
        }
        return processor;
    }
}

