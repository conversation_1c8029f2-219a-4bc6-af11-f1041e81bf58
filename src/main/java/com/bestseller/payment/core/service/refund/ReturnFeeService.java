package com.bestseller.payment.core.service.refund;

import com.bestseller.payment.core.domain.OrderCharge;
import com.bestseller.payment.core.dto.ReturnFeeCalculationResult;

import java.math.BigDecimal;
import java.util.Date;

public interface ReturnFeeService {
    ReturnFeeCalculationResult calculateReturnFee(Date orderDate, String country, String brand, BigDecimal totalRefundAmount);

    OrderCharge createReturnFeeCharge(BigDecimal returnFee, BigDecimal taxRate);
}
