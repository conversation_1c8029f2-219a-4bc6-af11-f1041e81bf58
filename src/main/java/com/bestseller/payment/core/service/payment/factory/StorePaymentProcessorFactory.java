package com.bestseller.payment.core.service.payment.factory;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.OrderDetails;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.Payment;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.service.payment.storePaymentProcessor.PaymentProcessor;

import java.util.List;

public interface StorePaymentProcessorFactory {
    PaymentProcessor createPaymentProcessor(
            List<Payment> payments,
            String country,
            Order order,
            OrderDetails orderDetails,
            PaymentFactory paymentFactory
    );
}
