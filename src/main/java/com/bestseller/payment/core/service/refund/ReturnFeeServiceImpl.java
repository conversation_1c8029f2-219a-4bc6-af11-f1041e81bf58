package com.bestseller.payment.core.service.refund;

import com.bestseller.payment.adapter.repository.CountryToReturnFeePricingRepository;
import com.bestseller.payment.core.domain.BrandSpecificReturnFee;
import com.bestseller.payment.core.domain.CountryToReturnFeePricing;
import com.bestseller.payment.core.domain.OrderCharge;
import com.bestseller.payment.core.domain.OrderEntryAmount;
import com.bestseller.payment.core.domain.enumeration.EntryType;
import com.bestseller.payment.core.dto.ReturnFeeCalculationResult;
import com.bestseller.payment.core.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;

@Service
@RequiredArgsConstructor
public class ReturnFeeServiceImpl implements ReturnFeeService {
    private final CountryToReturnFeePricingRepository countryToReturnFeePricingRepository;

    @Override
    public ReturnFeeCalculationResult calculateReturnFee(Date orderDate, String country, String brand, BigDecimal totalRefundAmount) {
        final CountryToReturnFeePricing countryToReturnFeePricing = countryToReturnFeePricingRepository
            .findFirstByCountryAndEffectiveDateLessThanEqualOrderByEffectiveDateDesc(country, orderDate)
            .stream()
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("No return fee pricing found for country: "
                + country + " and order date: " + DateUtils.formatDate(orderDate)));

        BigDecimal returnFee;
        BigDecimal returnFeeTaxRate;
        if (isAboveCountryThreshold(countryToReturnFeePricing, totalRefundAmount)) {
            returnFee = BigDecimal.ZERO;
            returnFeeTaxRate = BigDecimal.ZERO;
        } else {
            returnFee = findApplicableReturnFeeForBrand(countryToReturnFeePricing, brand, orderDate);
            returnFeeTaxRate = countryToReturnFeePricing.getReturnFeeTaxRate();
        }

        return ReturnFeeCalculationResult.builder()
            .returnFee(returnFee)
            .taxRate(returnFeeTaxRate)
            .build();
    }

    @Override
    public OrderCharge createReturnFeeCharge(BigDecimal returnFee, BigDecimal taxRate) {
        return OrderCharge.builder()
            .refunded(true)
            .ean("RETURN_FEE")
            .name("Return fee")
            .openQty(-1)
            .type(EntryType.RETURN_FEE)
            .taxRate(taxRate)
            .cancelled(false)
            .chargeTotal(OrderEntryAmount.builder()
                .grossRetailUnitPrice(returnFee)
                .grossDiscountedUnitPrice(returnFee)
                .build())
            .build();
    }

    /**
     * Determines the Return Fee depending on the total amount of the return(s) per country.
     *
     * @param countryToReturnFeePricing
     * @param totalRefundAmount
     * @return the applicable return fee
     */
    private boolean isAboveCountryThreshold(CountryToReturnFeePricing countryToReturnFeePricing, BigDecimal totalRefundAmount) {
        return countryToReturnFeePricing != null
            && countryToReturnFeePricing.getThreshold() != null
            && totalRefundAmount.compareTo(countryToReturnFeePricing.getThreshold()) > 0;
    }

    private BigDecimal findApplicableReturnFeeForBrand(CountryToReturnFeePricing countryToReturnFeePricing, String brand, Date orderDate) {
        return countryToReturnFeePricing.getBrandSpecificReturnFees()
            .stream()
            .filter(brandSpecificReturnFee -> brandSpecificReturnFee.getBrand().equals(brand))
            .sorted(Comparator.comparing(BrandSpecificReturnFee::getEffectiveDate).reversed())
            .filter(brandSpecificReturnFee ->
                brandSpecificReturnFee.getEffectiveDate().compareTo(orderDate) < 0
                    || brandSpecificReturnFee.getEffectiveDate().compareTo(orderDate) == 0
            )
            .findFirst()
            .map(BrandSpecificReturnFee::getReturnFee)
            .orElse(countryToReturnFeePricing.getReturnFeeAmount());
    }

}
