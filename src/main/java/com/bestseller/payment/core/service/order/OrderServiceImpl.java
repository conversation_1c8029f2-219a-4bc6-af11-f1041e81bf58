package com.bestseller.payment.core.service.order;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderFinalized;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.Address;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.CustomerInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.OrderDetails;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ShippingInformation;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.adapter.stream.messagegenerator.PaymentStatusUpdatedGenerator;
import com.bestseller.payment.core.converter.OrderChargeMapper;
import com.bestseller.payment.core.converter.OrderLineMapper;
import com.bestseller.payment.core.converter.OrderPromotionMapper;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.enumeration.Brand;
import com.bestseller.payment.core.domain.enumeration.OrderState;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.domain.enumeration.Platform;
import com.bestseller.payment.core.exception.InvalidMessageException;
import com.bestseller.payment.core.exception.MultipleNonGiftcardPaymentsException;
import com.bestseller.payment.core.exception.OrderCancellationMissingOrderLinesException;
import com.bestseller.payment.core.exception.OrderNotFoundException;
import com.bestseller.payment.core.exception.PaymentValidationException;
import com.bestseller.payment.core.exception.StateTransitionException;
import com.bestseller.payment.core.service.payment.PaymentService;
import com.bestseller.payment.core.service.price.PriceService;
import com.bestseller.payment.core.service.vat.OrderVatService;
import com.bestseller.payment.core.utils.DateUtils;
import com.bestseller.payment.core.utils.PlatformUtil;
import jakarta.validation.ConstraintViolationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * All business logic related to orders that have made a payment transaction.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderServiceImpl implements OrderService {
    private static final String VIRTUAL = "VIRTUAL";
    private static final List<PaymentState> VALID_PAYMENT_STATES = List.of(PaymentState.AUTHORISED,
        PaymentState.SETTLED,
        PaymentState.OFFLINE);

    private final OrderRepository orderRepository;
    private final PaymentService paymentService;
    private final QueueProducer<PaymentStatusUpdated> paymentStatusUpdatedProducerQueueProducer;
    private final PriceService priceService;
    private final OrderLineMapper orderLineMapper;
    private final OrderChargeMapper orderChargeMapper;
    private final OrderPromotionMapper orderPromotionMapper;
    private final OrderVatService orderVatService;
    private final PaymentStatusUpdatedGenerator paymentStatusUpdatedGenerator;

    @Override
    @Transactional
    public void processValidOrderPlaced(ValidOrderPlaced validOrderPlaced) {
        try {
            if (findExistingOrder(validOrderPlaced.getOrderId()) == null) {
                Order order = convertValidOrderPlacedToOrder(validOrderPlaced);
                save(order);
                checkAndSendPaymentStatusMessage(order);
            } else {
                log.error("Order with id {} already exist.", validOrderPlaced.getOrderId());
            }
        } catch (StateTransitionException
                 | IllegalArgumentException
                 | MultipleNonGiftcardPaymentsException
                 | ConstraintViolationException
                 | PaymentValidationException e
        ) {
            log.error("Failed to process Valid OrderPlaced message, order id: {}", validOrderPlaced.getOrderId(), e);
        }
    }

    @Override
    public Set<Order> getOrdersWaitingForApproval(int numberOfDays) {
        Instant now = Instant.now();
        ZonedDateTime zonedDateTime = now.atZone(ZoneId.systemDefault());
        ZonedDateTime threshholdDateTime = zonedDateTime.minusDays(numberOfDays);
        Instant thresholdDate = threshholdDateTime.toInstant();

        Set<Order> unsettledOrders = orderRepository.findAllByPaymentStatusEqualsAndLastModifiedTSLessThanEqual(
            PaymentState.REVIEW,
            thresholdDate);

        log.info(String.format("Found %s order(s) that has not been authorised for %s days.",
            unsettledOrders.size(), numberOfDays));

        return unsettledOrders;
    }

    @Transactional
    @Override
    public void processOrderFinalizedMessage(OrderFinalized orderFinalized) {
        if (!isOrderExists(orderFinalized.getOrderId())) {
            log.error("Order with id {} not found.", orderFinalized.getOrderId());
            return;
        }
        Order order = saveOrderFinalized(orderFinalized);
        paymentService.startSettlementProcess(order);
    }

    @Override
    public Order save(Order order) {
        return this.save(order, false);
    }

    @Override
    public Order save(Order order, boolean immediate) {
        if (immediate) {
            return orderRepository.saveAndFlush(order);
        } else {
            return orderRepository.save(order);
        }
    }

    @Override
    public Order getOrderById(String orderId) {
        return orderRepository.findById(orderId)
            .orElseThrow(() -> new OrderNotFoundException(orderId));
    }

    @Override
    public boolean existsByOrderIdAndPaymentStatus(String orderId, PaymentState paymentState) {
        return orderRepository.existsByOrderIdAndPaymentStatus(orderId, paymentState);
    }

    @Transactional
    @Override
    public void cancelOrder(String orderId) {
        Order order = findExistingOrder(orderId);

        if (order == null) {
            log.error("No order with id = {} found.", orderId);
            throw new OrderNotFoundException(orderId);
        }

        if (order.getPaymentStatus() == PaymentState.CANCELLED) {
            log.warn("Order with id {} is already cancelled.", orderId);
            return;
        }

        order.getOrderLines()
            .forEach(orderLine -> cancelOrderLine(orderId, orderLine));

        if (!cancelOrderCharges(order)) {
            log.error("Not all orderLines are cancelled for orderId {}", orderId);
            throw new OrderCancellationMissingOrderLinesException(orderId);
        }
        priceService.processAndSetPriceFieldsForOrderLineAndTotalPaidAmount(order);
        orderRepository.save(order);
        paymentService.updatePaymentStatus(orderId, PaymentState.CANCELLED);
    }

    /**
     * Process the order finalized message. Update the order lines and the order charges.
     *
     * @param orderFinalized the order finalized message
     */
    protected Order saveOrderFinalized(OrderFinalized orderFinalized) {
        Order order = updateOrder(orderFinalized);
        priceService.processAndSetPriceFieldsForOrderLineAndTotalPaidAmount(order);
        orderRepository.save(order);
        return order;
    }

    private Order updateOrder(OrderFinalized orderFinalized) {
        final Order order = findExistingOrder(orderFinalized.getOrderId());

        orderFinalized.getOrderLines().forEach(orderFinalizedOrderLine ->
            order.getOrderLines()
                .stream()
                .filter(orderLine -> orderLine.getEan().equals(orderFinalizedOrderLine.getEan())
                    && orderLine.getLineNumber().equals(orderFinalizedOrderLine.getLineNumber()))
                .findFirst()
                .ifPresentOrElse(orderLine ->
                        orderLine.setOpenQty(calculateOrderLineOpenQuantity(orderFinalizedOrderLine)),
                    () -> {
                        var message = "OrderLine with ean %s and lineNumber %s not found in order with id %s".formatted(
                            orderFinalizedOrderLine.getEan(), orderFinalizedOrderLine.getLineNumber(), order.getOrderId());
                        throw new InvalidMessageException(message);
                    }
                )
        );

        cancelOrderCharges(order);

        boolean assignVatId = order.getOrderLines()
            .stream()
            .anyMatch(orderLine -> orderLine.getOpenQty() > 0);

        if (assignVatId) {
            order.setVatOrderNumber(orderVatService.getNextOrderVatId(order));
            log.info("Assigned VAT order number {} to order with id {}.", order.getVatOrderNumber(), order.getOrderId());
        }

        return order;
    }

    private int calculateOrderLineOpenQuantity(
        com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderLine orderLineInMessage) {
        long openQty = orderLineInMessage
            .getQuantityStatuses()
            .stream()
            .map(OrderState::valueOf)
            .filter(OrderState::isFulfilled)
            .count();
        return Math.toIntExact(openQty);
    }

    private Order convertValidOrderPlacedToOrder(ValidOrderPlaced validOrderPlaced) {
        Order order = Order.builder()
            .orderId(validOrderPlaced.getOrderId())
            .platform(PlatformUtil.getPlatformFromStore(validOrderPlaced.getStore()))
            .orderDate(DateUtils.zonedDateTimeToDate(validOrderPlaced.getPlacedDate()))
            .currency(Optional.ofNullable(validOrderPlaced.getOrderDetails().getCurrency())
                .filter(StringUtils::isNotBlank)
                .orElseGet(() -> null))
            .brand(Optional.of(validOrderPlaced)
                .map(ValidOrderPlaced::getOrderDetails)
                .map(OrderDetails::getBrandedShipping)
                .map(String::toUpperCase)
                .map(Brand::valueOf)
                .orElse(null)
            )
            .billingCountryCode(Optional.of(validOrderPlaced)
                .map(ValidOrderPlaced::getCustomerInformation)
                .map(CustomerInformation::getBillingAddress)
                .map(Address::getCountry)
                .map(String::toUpperCase)
                .orElse(null))
            .shippingCountryCode(Optional.of(validOrderPlaced)
                .map(ValidOrderPlaced::getShippingInformation)
                .map(ShippingInformation::getShippingAddress)
                .map(Address::getCountry)
                .map(String::toUpperCase)
                .orElse(null)
            )
            .offlinePayment(Boolean.TRUE.equals(validOrderPlaced.getOfflinePayment()))
            .build();

        if (!Objects.equals(validOrderPlaced.getOrderDetails().getShippingMethod(), VIRTUAL)) {
            order.setOrderCharges(orderChargeMapper.mapToOrderCharges(validOrderPlaced.getShippingInformation().getShippingCharges(),
                Platform.valueOf(validOrderPlaced.getStore()).name().equals(Platform.DEMANDWARE.name())));
        } else {
            order.setOrderCharges(new ArrayList<>(0));
        }

        order.setPromotions(orderPromotionMapper.mapOrderPromotions(validOrderPlaced.getOrderPromotions()));

        convertValidOrderLines(validOrderPlaced.getOrderLines(), order,
            order.isOfflinePayment());

        paymentService.processPaymentMethodAndState(validOrderPlaced.getPayments(), order.getBillingCountryCode(),
            validOrderPlaced.getOrderDetails(), order);

        priceService.processAndSetPriceFieldsForOrderLineAndTotalPaidAmount(order);

        return order;
    }

    /**
     * Check if the order has a payment status that should be sent to the order payment status topic.
     * if the payment status is none of the above we don't need to inform FS about the payment status.
     * instead we will wait for the payment status to change by the messages that we will receive from payment connector.
     *
     * @param order the order
     */

    private void checkAndSendPaymentStatusMessage(Order order) {
        if (VALID_PAYMENT_STATES.contains(order.getPaymentStatus())) {
            PaymentStatusUpdated paymentStatusUpdated = paymentStatusUpdatedGenerator.generate(order);
            paymentStatusUpdatedProducerQueueProducer.enqueue(EnqueueParams.create(paymentStatusUpdated));
        }
    }

    private void convertValidOrderLines(List<OrderLine> validOrderLines,
                                        Order order,
                                        boolean isOfflinePayment) {
        List<com.bestseller.payment.core.domain.OrderLine> orderLines =
            orderLineMapper.mapOrderLines(
                mergeOrderLinesByEan(validOrderLines, order.getOrderId(), isOfflinePayment));

        orderLines.forEach(orderLine -> {
            orderLine.setLineNumber(orderLine.getLineNumber());
            order.getOrderLines().add(orderLine);
        });
    }

    private List<OrderLine> mergeOrderLinesByEan(List<OrderLine> orderLines,
                                                 String orderId,
                                                 boolean isOfflinePayment) {
        // merging order lines by ean.
        Map<String, OrderLine> eanToOrderLine = new HashMap<>();
        var sortedOrderLines = orderLines.stream()
            .sorted(Comparator.comparing(OrderLine::getId))
            .toList();
        for (OrderLine orderLinePlaced : sortedOrderLines) {
            String ean = orderLinePlaced.getEan();

            if (!isOfflinePayment && eanToOrderLine.containsKey(ean)) {
                throw new IllegalArgumentException(String.format("Order %s contains multiple lines with same ean %s but "
                        + "merging lines for online payment is not allowed. It will be rejected.",
                    orderId, ean)
                );
            }

            eanToOrderLine.merge(
                ean,
                orderLinePlaced,
                (existingLine, newLine) -> {
                    int newQty = existingLine.getQuantity() + orderLinePlaced.getQuantity();
                    existingLine.setQuantity(newQty);
                    return existingLine;
                });
        }
        return new ArrayList<>(eanToOrderLine.values());
    }

    /**
     * Retrieve existing order by orderId.
     *
     * @param orderId the order id
     * @return the existing order, otherwise null.
     */
    private Order findExistingOrder(String orderId) {
        return orderRepository.findById(orderId).orElse(null);
    }

    private boolean isOrderExists(String orderId) {
        return orderRepository.existsById(orderId);
    }

    private boolean cancelOrderCharges(Order order) {
        // If all order lines are cancelled, then cancel all order charges.
        boolean allOrderLinesCancelled = order.getOrderLines()
            .stream()
            .allMatch(orderLine -> orderLine.getOpenQty() == 0);

        if (!allOrderLinesCancelled) {
            return false;
        }

        order.getOrderCharges().forEach(orderCharge ->
            orderCharge.setCancelled(true)
        );

        return true;
    }

    private void cancelOrderLine(String orderId, com.bestseller.payment.core.domain.OrderLine orderLine) {
        if (orderLine.getOpenQty() > 0) {
            orderLine.setOpenQty(0);
        } else {
            log.warn("Order line with EAN {} is already cancelled for orderId {}", orderLine.getEan(), orderId);
        }
    }
}

