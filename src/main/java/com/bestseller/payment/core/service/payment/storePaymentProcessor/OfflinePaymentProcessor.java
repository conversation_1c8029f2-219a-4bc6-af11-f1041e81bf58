package com.bestseller.payment.core.service.payment.storePaymentProcessor;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.OrderDetails;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.Payment;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderCharge;
import com.bestseller.payment.core.domain.OrderLine;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import com.bestseller.payment.core.domain.payment.NonePayment;

import java.math.BigDecimal;
import java.util.List;

public class OfflinePaymentProcessor extends PaymentProcessor {
    private final OrderDetails orderDetails;
    private final List<OrderCharge> orderCharges;
    private final List<OrderLine> orderLines;

    @SuppressWarnings("MissingJavadocMethod")
    public OfflinePaymentProcessor(OrderDetails orderDetails, Order order) {
        super(null, null, order);
        this.orderDetails = orderDetails;
        this.orderCharges = order.getOrderCharges();
        this.orderLines = order.getOrderLines() != null ? order.getOrderLines() : List.of();
        initPayment();
    }

    /**
     * create an offline payment. this payment is instance of ValidOrderPlaced.Payment.
     */
    void initPayment() {
        BigDecimal authorizedAmount = BigDecimal.ZERO;
        for (OrderLine orderLine : this.orderLines) {
            authorizedAmount = authorizedAmount.add(orderLine.getOrderLinePaidAmount()
                .getOriginalGrossDiscountedTotal());
        }

        for (OrderCharge orderCharge: orderCharges) {
            authorizedAmount = authorizedAmount.add(orderCharge.getCostPrice());
        }
        Payment payment = new Payment()
                .withAmount(authorizedAmount)
                .withProvider(ProcessorId.OFFLINE.name());
        this.setPayments(List.of(payment));
    }

    /**
     * create a None payment object.
     *
     * @param payment
     * @param country
     *
     * @return NonePayment
     */
    @Override
    public PaymentInfo createPayment(Payment payment, String country) {
        return NonePayment.builder()
                .authorisedAmount(payment.getAmount().toPlainString())
                .processorId(ProcessorId.OFFLINE)
                .type(PaymentType.NONE)
                .build();
    }

    @Override
    public PaymentState getPaymentState(Payment payment) {
        return PaymentState.OFFLINE;
    }
}
