package com.bestseller.payment.core.service.refund;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.giftCardRefundResponse.GiftCardRefundResponse;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCompleted.RefundCompleted;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundStatusUpdated.RefundStatusUpdated;
import com.bestseller.payment.adapter.repository.RefundRepository;
import com.bestseller.payment.adapter.stream.messagegenerator.RefundCompletedGenerator;
import com.bestseller.payment.adapter.stream.messagegenerator.RefundStatusUpdatedGenerator;
import com.bestseller.payment.core.domain.Refund;
import com.bestseller.payment.core.domain.enumeration.RefundType;
import com.bestseller.payment.core.exception.InvalidRefundStateTransitionException;
import com.bestseller.payment.core.exception.RefundNotFoundException;
import com.bestseller.payment.core.service.vat.OrderVatService;
import com.logistics.statetransition.RefundState;
import com.logistics.statetransition.RefundStateTransition;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

import static java.lang.Long.parseLong;

@Slf4j
@Service
public class RefundServiceImpl implements RefundService {

    private final RefundRepository refundRepository;
    private final QueueProducer<RefundCompleted> refundCompletedProducerQueueProducer;
    private final QueueProducer<RefundStatusUpdated> refundStatusUpdatedProducerQueueProducer;
    private final RefundStatusUpdatedGenerator refundStatusUpdatedGenerator;
    private final RefundCompletedGenerator refundCompletedGenerator;
    private final OrderVatService orderVatService;

    /**
     * Constructor.
     *
     * @param refundRepository                         refund repository
     * @param refundCompletedProducerQueueProducer     refund completed producer
     * @param refundStatusUpdatedProducerQueueProducer refund status updated producer
     * @param refundStatusUpdatedGenerator             refund status updated generator
     * @param refundCompletedGenerator                 refund completed generator
     * @param orderVatService                          order vat service
     */
    public RefundServiceImpl(RefundRepository refundRepository,
                             QueueProducer<RefundCompleted> refundCompletedProducerQueueProducer,
                             QueueProducer<RefundStatusUpdated> refundStatusUpdatedProducerQueueProducer,
                             RefundStatusUpdatedGenerator refundStatusUpdatedGenerator,
                             RefundCompletedGenerator refundCompletedGenerator,
                             OrderVatService orderVatService) {
        this.refundRepository = refundRepository;
        this.refundCompletedProducerQueueProducer = refundCompletedProducerQueueProducer;
        this.refundStatusUpdatedProducerQueueProducer = refundStatusUpdatedProducerQueueProducer;
        this.refundStatusUpdatedGenerator = refundStatusUpdatedGenerator;
        this.refundCompletedGenerator = refundCompletedGenerator;
        this.orderVatService = orderVatService;
    }

    @Override
    public boolean isDuplicateRequest(int refundId, RefundState targetRefundStatus) {
        var refundState = refundRepository.getRefundStateById(refundId)
            .orElseThrow(() -> new RefundNotFoundException(refundId));

        /* Transition from REFUND_SUCCESS to REFUND_FAILED is allowed
         * and REFUND_SUCCESS is greater than REFUND_FAILED
         * that's why I call `!RefundStateTransition.isTransitionAllowed` here
         */
        boolean sourceStateIsGreaterThanTargetState = refundState.isEqualOrGreaterThan(targetRefundStatus);
        if (sourceStateIsGreaterThanTargetState) {
            return !RefundStateTransition.isTransitionAllowed(refundState, targetRefundStatus);
        }
        return false;
    }

    @Transactional
    @Override
    public void updateRefundStatus(int refundId, RefundState targetRefundStatus) {
        Refund refund = refundRepository.findById(refundId)
            .orElseThrow(() -> new RefundNotFoundException(refundId));

        if (targetRefundStatus.equals(refund.getRefundState())) {
            log.warn("Nothing to do for refund {}. refundState {}.", refundId, refund.getRefundState());
            return;
        }

        if (RefundStateTransition.isTransitionAllowed(refund.getRefundState(), targetRefundStatus)) {
            refund.setRefundState(targetRefundStatus);

            boolean isInStoreRefund = isInStoreRefund(refund);
            if (targetRefundStatus == RefundState.REFUND_SUCCESS && StringUtils.isEmpty(refund.getRefundId())) {

                var refundVatId = isInStoreRefund
                    ? orderVatService.getNextRefundInStoreVatId(refund.getOrder(), parseLong(refund.getRequestId()))
                    : orderVatService.getNextRefundVatId(refund.getOrder());
                log.info("Refund VAT ID {} is assigned to refund id {}, orderId={}", refundId, refundVatId, refund
                    .getOrder()
                    .getOrderId());
                refund.setRefundId(refundVatId);
            }
            refundRepository.save(refund);

            if (isInStoreRefund && targetRefundStatus.isFinalState()) {
                RefundCompleted refundCompleted = refundCompletedGenerator.generate(refund);
                refundCompletedProducerQueueProducer.enqueue(EnqueueParams.create(refundCompleted));
            }

            if (RefundType.GIFT_CARD != refund.getRefundType()) {
                RefundStatusUpdated refundStatusUpdated = refundStatusUpdatedGenerator.generate(refund);
                refundStatusUpdatedProducerQueueProducer.enqueue(EnqueueParams.create(refundStatusUpdated));
            }
        } else {
            throw new InvalidRefundStateTransitionException(refundId,
                refund.getOrder().getOrderId(),
                refund.getRefundState(),
                targetRefundStatus);
        }

    }

    @Transactional
    @Override
    public void processGiftCardRefundResponse(GiftCardRefundResponse message) {
        String orderId = message.getOrderId();
        UUID giftCardCorrelationId = UUID.fromString(message.getCorrelationId());
        Refund refund = getRefund(orderId, giftCardCorrelationId);
        if (refund.getRefundType() == null) {
            log.error("Refund type is null for refund with id {}. orderId={}", refund.getId(), orderId);
            return;
        }

        if (refund.getRefundType() == RefundType.GIFT_CARD) {
            updateRefundStatus(
                refund.getId(), message.getStatus() ? RefundState.REFUND_SUCCESS : RefundState.REFUND_FAILED);
        } else {
            log.info("Refund type is '{}' and not GIFT_CARD for refund with id {}. orderId={}",
                refund.getRefundType(), refund.getId(), orderId);
        }
    }

    @Override
    public boolean isExists(int refundId) {
        return refundRepository.existsById(refundId);
    }

    @Override
    public boolean isStateTransitionAllowed(int refundId, RefundState targetRefundStatus) {
        var refundState = refundRepository.getRefundStateById(refundId)
            .orElseThrow(() -> new RefundNotFoundException(refundId));

        return RefundStateTransition.isTransitionAllowed(refundState, targetRefundStatus);
    }

    @Override
    public Refund getRefund(String orderId, UUID giftCardCorrelationId) {
        return refundRepository.getRefundByOrderOrderIdAndGiftCardCorrelationId(orderId, giftCardCorrelationId)
            .orElseThrow(() -> new RefundNotFoundException(orderId, giftCardCorrelationId));
    }

    private boolean isInStoreRefund(Refund refund) {
        boolean isInStoreRefund = true;
        try {
            parseLong(refund.getRequestId());
        } catch (NumberFormatException e) {
            isInStoreRefund = false;
        }
        return isInStoreRefund;
    }
}
