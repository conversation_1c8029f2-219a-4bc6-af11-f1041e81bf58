package com.bestseller.payment.core.service.price;

import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderCharge;
import com.bestseller.payment.core.domain.OrderEntryAmount;
import com.bestseller.payment.core.domain.OverallTotal;
import com.bestseller.payment.core.domain.Refund;
import com.bestseller.payment.core.domain.RefundLine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.List;

@Service
@Slf4j
public class PriceServiceImp implements PriceService {
    private static final RoundingMode ROUNDING_MODE = RoundingMode.HALF_UP;

    private static final MathContext MATH_CTX_VAT_CALCULATE = new MathContext(99, ROUNDING_MODE);
    // Signs after the decimal point
    private static final int SCALE = 2;

    /**
     * This method is used to process the price fields for a valid order placed.
     *
     * @param order the order object
     */
    @Override
    public void processAndSetPriceFieldsForOrderLineAndTotalPaidAmount(Order order) {
        setOrderCharge(order);
        setOrderLinePaidAmount(order);
        setOrderLineCancelledAmount(order);
        setTotalPaidAmount(order);
        setTotalCancelledAmount(order);
    }

    /**
     * This method is used to set the price fields for a refund line and the total paid amount for a refund.
     *
     * @param refund the refund object
     */
    @Override
    public void processAndSetPriceFieldsForRefundLineAndRefundTotalPaidAmount(Refund refund) {
        calculateRefundChargeTotal(refund);
        calculateRefundLineTotal(refund);
        calculateRefundTotal(refund);
    }

    /**
     * This method is used to calculate the order charge amount for an order.
     *
     * @param order the order object
     */
    protected void setOrderCharge(Order order) {
        order.getOrderCharges().forEach(orderCharge -> {
            OrderEntryAmount chargeTotal = orderCharge.getChargeTotal();
            if (chargeTotal != null) {
                calculateAndSetUnitDiscount(chargeTotal);
                calculateAndSetGrossAndOriginalDiscountedTotal(chargeTotal, orderCharge.getOpenQty());
                calculateAndSetUnitVat(chargeTotal, orderCharge.getTaxRate());
                calculateAndSetLineVat(chargeTotal, orderCharge.getOpenQty());
            }
        });
    }

    /**
     * This method is used to calculate the order line amount for an order line.
     *
     * @param order the order object
     */
    private void setOrderLinePaidAmount(Order order) {
        order.getOrderLines().forEach(orderLine -> {
            OrderEntryAmount orderEntryAmount = orderLine.getOrderLinePaidAmount();
            if (orderEntryAmount != null) {
                calculateAndSetUnitDiscount(orderEntryAmount);
                calculateAndSetGrossAndOriginalDiscountedTotal(orderEntryAmount, orderLine.getOpenQty());
                calculateAndSetUnitVat(orderEntryAmount, orderLine.getTaxRate());
                calculateAndSetLineVat(orderEntryAmount, orderLine.getOpenQty());
            }
        });
    }

    /**
     * This method is used to calculate the total paid amount for an order.
     *
     * @param order the order object
     */
    private void setTotalPaidAmount(Order order) {
        OverallTotal totalPaidPrice = order.getTotalPaidPrice();
        if (totalPaidPrice == null) {
            totalPaidPrice = new OverallTotal();
            order.setTotalPaidPrice(totalPaidPrice);
        }

        // Order Lines computation for discounted price and VAT
        BigDecimal totalOriginalGrossDiscountedTotalForOrderLines = BigDecimal.ZERO;
        BigDecimal totalDiscountedPriceForOrderLines = BigDecimal.ZERO;
        BigDecimal totalVATForOrderLines = BigDecimal.ZERO;
        for (com.bestseller.payment.core.domain.OrderLine orderLine : order.getOrderLines()) {
            OrderEntryAmount orderEntryAmount = orderLine.getOrderLinePaidAmount();
            if (orderEntryAmount != null) {
                totalOriginalGrossDiscountedTotalForOrderLines = totalOriginalGrossDiscountedTotalForOrderLines
                        .add(orderEntryAmount.getOriginalGrossDiscountedTotal());
                totalDiscountedPriceForOrderLines = totalDiscountedPriceForOrderLines.add(orderEntryAmount.getGrossDiscountedTotal());
                totalVATForOrderLines = totalVATForOrderLines.add(orderEntryAmount.getLineVAT());
            }
        }
        // Order Charges computation for discounted price and VAT
        BigDecimal totalOriginalGrossDiscountedTotalForOrderCharge = BigDecimal.ZERO;
        BigDecimal totalDiscountedPriceForOrderCharge = BigDecimal.ZERO;
        BigDecimal totalVATForOrderCharge = BigDecimal.ZERO;

        for (OrderCharge orderCharge : order.getOrderCharges()) {
            OrderEntryAmount chargeTotal = orderCharge.getChargeTotal();
            if (orderCharge.getCancelled() || orderCharge.getRefunded()) {
                // We need only OrderCharge objects which are OPEN, so we
                // drop all others at this point.
                continue;
            }
            totalOriginalGrossDiscountedTotalForOrderCharge = totalOriginalGrossDiscountedTotalForOrderCharge
                    .add(chargeTotal.getOriginalGrossDiscountedTotal());
            totalDiscountedPriceForOrderCharge = totalDiscountedPriceForOrderCharge.add(chargeTotal.getGrossDiscountedTotal());
            totalVATForOrderCharge = totalVATForOrderCharge.add(chargeTotal.getLineVAT());
        }

        //Update Total Paid Price
        BigDecimal finalOriginalGrossDiscountedTotal = totalOriginalGrossDiscountedTotalForOrderLines
                .add(totalOriginalGrossDiscountedTotalForOrderCharge);
        BigDecimal finalDiscountedPrice = totalDiscountedPriceForOrderLines.add(totalDiscountedPriceForOrderCharge);
        BigDecimal finalTotalVAT = totalVATForOrderLines.add(totalVATForOrderCharge);
        totalPaidPrice.setGrossSubTotal(totalDiscountedPriceForOrderLines);
        totalPaidPrice.setGrossDiscountedTotal(finalDiscountedPrice);
        totalPaidPrice.setOriginalGrossDiscountedTotal(finalOriginalGrossDiscountedTotal);
        totalPaidPrice.setVat(finalTotalVAT);
    }

    /**
     * This method is used to calculate the order line cancelled amount for an order line.
     *
     * @param order the order object
     */
    private void setOrderLineCancelledAmount(Order order) {
        order.getOrderLines().forEach(orderLine -> {

            int cancelledQuantity = orderLine.getOriginalQty() - orderLine.getOpenQty();
            if (cancelledQuantity > 0) {
                OrderEntryAmount orderLinePaidAmount = orderLine.getOrderLinePaidAmount();
                OrderEntryAmount orderLineCancelledAmount = orderLine.getOrderLineCancelledAmount();

                if (orderLineCancelledAmount == null && orderLinePaidAmount != null) {
                    orderLineCancelledAmount = new OrderEntryAmount();
                    orderLineCancelledAmount.setGrossRetailUnitPrice(orderLinePaidAmount.getGrossRetailUnitPrice());
                    orderLineCancelledAmount.setGrossDiscountedUnitPrice(orderLinePaidAmount.getGrossDiscountedUnitPrice());
                    orderLine.setOrderLineCancelledAmount(orderLineCancelledAmount);
                }
                if (orderLineCancelledAmount != null) {
                    calculateAndSetUnitDiscount(orderLineCancelledAmount);
                    calculateAndSetGrossAndOriginalDiscountedTotal(orderLineCancelledAmount, cancelledQuantity);
                    calculateAndSetUnitVat(orderLineCancelledAmount, orderLine.getTaxRate());
                    calculateAndSetLineVat(orderLineCancelledAmount, cancelledQuantity);
                }
            }

        });
    }

    /**
     * This method is used to calculate the total cancelled amount for an order.
     *
     * @param order the order object
     */
    private void setTotalCancelledAmount(Order order) {
        OverallTotal totalCancelledPrice = order.getTotalCancelledPrice();
        if (totalCancelledPrice == null) {
            totalCancelledPrice = new OverallTotal();
        }
        BigDecimal totalDiscountedPriceForOrderLines = BigDecimal.ZERO;
        BigDecimal totalVATForOrderLines = BigDecimal.ZERO;

        for (com.bestseller.payment.core.domain.OrderLine orderLine : order.getOrderLines()) {
            OrderEntryAmount orderLineCancelledAmount = orderLine.getOrderLineCancelledAmount();
            if (orderLineCancelledAmount != null) {
                totalDiscountedPriceForOrderLines = totalDiscountedPriceForOrderLines.add(orderLineCancelledAmount.getGrossDiscountedTotal());
                totalVATForOrderLines = totalVATForOrderLines.add(orderLineCancelledAmount.getLineVAT());
            }
        }
        // Order Charges computation for discounted price and VAT
        BigDecimal totalDiscountedPriceForOrderCharge = BigDecimal.ZERO;
        BigDecimal totalVATForOrderCharge = BigDecimal.ZERO;
        for (OrderCharge orderCharge : order.getOrderCharges()) {
            if (orderCharge != null && orderCharge.getCancelled()) {
                OrderEntryAmount chargeTotal = orderCharge.getChargeTotal();
                totalDiscountedPriceForOrderCharge = totalDiscountedPriceForOrderCharge.add(chargeTotal.getGrossDiscountedTotal());
                totalVATForOrderCharge = totalVATForOrderCharge.add(chargeTotal.getLineVAT());
            }
        }

        //Update Total Cancelled Price
        BigDecimal finalDiscountedPrice = totalDiscountedPriceForOrderLines.add(totalDiscountedPriceForOrderCharge);
        BigDecimal finalTotalVAT = totalVATForOrderLines.add(totalVATForOrderCharge);
        totalCancelledPrice.setGrossSubTotal(totalDiscountedPriceForOrderLines);
        totalCancelledPrice.setGrossDiscountedTotal(finalDiscountedPrice);
        totalCancelledPrice.setVat(finalTotalVAT);

        // cancellation here is based on any orderLine that might have been cancelled so we need to check if the totalCancelledPrice is not zero
        // then we set the totalCancelledPrice to the order otherwise we would have on overallTotal a record with zero values
        if (totalCancelledPrice.getGrossDiscountedTotal().doubleValue() != 0d) {
            order.setTotalCancelledPrice(totalCancelledPrice);
        }
    }

    /* Demonstrating calculation of unit VAT (Value-Added Tax) from the total price including VAT, as follows:
    Let total price be denoted by P_total and unit VAT by VAT_unit.
    The formula to calculate unit VAT from the total price is:
    VAT_unit = P_total - (P_total / (1 + tax_rate))
    where tax_rate is the percentage of VAT applied to the original price. */

    /**
     * This method is used to calculate the unit discount for an order entry amount.
     *
     * @param orderEntryAmount the order entry amount object
     */
    protected void calculateAndSetUnitVat(OrderEntryAmount orderEntryAmount, BigDecimal taxRate) {
        // Retrieve the gross discounted unit price
        BigDecimal grossDiscountedUnitPrice = orderEntryAmount.getGrossDiscountedUnitPrice();

        // Calculate taxRate + 1
        BigDecimal taxRatePlusOne = taxRate.add(BigDecimal.ONE);

        // Calculate the unitVAT by subtracting the result of division from grossDiscountedUnitPrice
        BigDecimal unitVAT = grossDiscountedUnitPrice.subtract(grossDiscountedUnitPrice.divide(taxRatePlusOne, MATH_CTX_VAT_CALCULATE));

        // Round the unitVAT to the determined scale using the specified rounding mode
        BigDecimal roundedUnitVAT = unitVAT.setScale(SCALE, ROUNDING_MODE);

        // Set the rounded unitVAT value to the orderEntryAmount
        orderEntryAmount.setUnitVAT(roundedUnitVAT);
    }

    /**
     * This method is used to calculate the length of the integer part of a BigDecimal value.
     *
     * @param value the BigDecimal value
     * @return the length of the integer part
     */
    private int integerLength(BigDecimal value) {
        return value.precision() - value.scale();
    }

    /**
     * This method is used to calculate the line VAT for an order entry amount.
     *
     * @param orderEntryAmount the order entry amount object
     * @param quantity         the quantity of the order entry
     */
    protected void calculateAndSetLineVat(OrderEntryAmount orderEntryAmount, Integer quantity) {
        BigDecimal unitVAT = orderEntryAmount.getUnitVAT();
        BigDecimal lineVAT = unitVAT.multiply(new BigDecimal(quantity));
        orderEntryAmount.setLineVAT(lineVAT);
    }

    /**
     * This method is used to calculate the gross and original discounted total for an order entry amount.
     *
     * @param orderEntryAmount the order entry amount object
     * @param quantity         the quantity of the order entry
     */
    protected void calculateAndSetGrossAndOriginalDiscountedTotal(OrderEntryAmount orderEntryAmount, Integer quantity) {
        BigDecimal grossDiscountedTotal = orderEntryAmount.getGrossDiscountedUnitPrice().multiply(new BigDecimal(quantity));
        orderEntryAmount.setGrossDiscountedTotal(grossDiscountedTotal);
    }

    /**
     * This method is used to calculate the unit discount for an order entry amount.
     *
     * @param orderEntryAmount the order entry amount object
     */
    protected void calculateAndSetUnitDiscount(OrderEntryAmount orderEntryAmount) {
        BigDecimal unitDiscount = orderEntryAmount.getGrossRetailUnitPrice().subtract(orderEntryAmount.getGrossDiscountedUnitPrice());
        orderEntryAmount.setUnitDiscount(unitDiscount);
    }

    /**
     * This method is used to calculate the total refund amount for a refund.
     *
     * @param refund the refund object
     */
    protected void calculateRefundTotal(Refund refund) {
        OverallTotal refundTotal = refund.getRefundTotal();

        if (refundTotal == null) {
            refundTotal = new OverallTotal();
            refund.setRefundTotal(refundTotal);
        }

        BigDecimal refundedGrossDiscountedTotal = BigDecimal.ZERO;
        BigDecimal refundedGrossSubTotal = BigDecimal.ZERO;
        BigDecimal totalRefundedVAT = BigDecimal.ZERO;

        for (RefundLine refundLine : refund.getRefundLines()) {
            refundedGrossSubTotal = refundedGrossSubTotal.add(refundLine
                    .getRefundLineTotal().getGrossDiscountedTotal());
            totalRefundedVAT = totalRefundedVAT.add(refundLine
                    .getRefundLineTotal().getLineVAT());
        }
        refundedGrossDiscountedTotal = refundedGrossDiscountedTotal
                .add(refundedGrossSubTotal);
        for (OrderCharge charge : refund.getRefundCharges()) {
            refundedGrossDiscountedTotal = refundedGrossDiscountedTotal
                    .add(charge.getChargeTotal().getGrossDiscountedTotal());
            totalRefundedVAT = totalRefundedVAT.add(charge.getChargeTotal()
                    .getLineVAT());
        }
        refundTotal.setGrossDiscountedTotal(refundedGrossDiscountedTotal
                .compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO
                : refundedGrossDiscountedTotal);
        refundTotal.setGrossSubTotal(refundedGrossSubTotal);
        refundTotal.setVat(totalRefundedVAT);
    }

    /**
     * This method is used to calculate the total refund amount for a refund.
     *
     * @param refund the refund object
     */
    protected void calculateRefundLineTotal(Refund refund) {
        List<RefundLine> refundLines = refund.getRefundLines();

        for (RefundLine refundLine : refundLines) {
            OrderEntryAmount refundLineTotal = refundLine
                    .getRefundLineTotal();
            if (refundLineTotal != null) {
                calculateAndSetUnitDiscount(refundLineTotal);
                calculateAndSetGrossAndOriginalDiscountedTotal(refundLineTotal, refundLine.getQuantity());
                calculateAndSetUnitVat(refundLineTotal, refundLine.getOrderLine().getTaxRate());
                calculateAndSetLineVat(refundLineTotal, refundLine.getQuantity());
            }
        }
    }

    /**
     * This method is used to calculate the total refund amount for a refund.
     *
     * @param refund the refund object
     */
    protected void calculateRefundChargeTotal(Refund refund) {
        setOrderCharge(refund.getOrder());
    }
}
