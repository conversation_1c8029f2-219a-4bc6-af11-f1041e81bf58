package com.bestseller.payment.core.service.payment.factory;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.OrderDetails;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.Payment;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.service.payment.storePaymentProcessor.OfflinePaymentProcessor;
import com.bestseller.payment.core.service.payment.storePaymentProcessor.OnlinePaymentProcessor;
import com.bestseller.payment.core.service.payment.storePaymentProcessor.PaymentProcessor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PaymentProcessorFactoryImpl implements StorePaymentProcessorFactory {

    @Override
    public PaymentProcessor createPaymentProcessor(
            List<Payment> payments,
            String country,
            Order order,
            OrderDetails orderDetails,
            PaymentFactory paymentFactory
    ) {
        if (order.isOfflinePayment()) {
            return new OfflinePaymentProcessor(orderDetails, order);
        } else {
            return new OnlinePaymentProcessor(payments, country, order, paymentFactory);
        }
    }
}
