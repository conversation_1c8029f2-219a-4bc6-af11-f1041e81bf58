package com.bestseller.payment.core.service.payment;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.OrderPlaced;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementRequest.PaymentSettlementRequest;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.OrderDetails;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.Payment;
import com.bestseller.payment.adapter.api.dto.PaymentValidationResponse;
import com.bestseller.payment.adapter.stream.messagegenerator.PaymentStatusUpdatedGenerator;
import com.bestseller.payment.config.queue.paymentsettlementrequestproducer.AbstractPaymentSettlementRequestProducerQueueProducer;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.exception.PaymentValidationException;
import com.bestseller.payment.core.exception.StateTransitionException;
import com.bestseller.payment.core.service.order.OrderService;
import com.bestseller.payment.core.service.payment.factory.PaymentFactory;
import com.bestseller.payment.core.service.payment.factory.StorePaymentProcessorFactory;
import com.bestseller.payment.core.service.payment.storePaymentProcessor.PaymentProcessor;
import com.bestseller.payment.core.service.refund.EcomRefundService;
import com.bestseller.payment.core.validation.OrderPlacedPaymentValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import static com.bestseller.payment.core.domain.enumeration.PaymentStateTransition.isValidTransition;

@Service
@Slf4j
public class PaymentServiceImpl implements PaymentService {

    private final PaymentFactory paymentFactory;

    private final OrderPlacedPaymentValidator orderPlacedPaymentValidator;

    private final OrderService orderService;

    private final QueueProducer<PaymentStatusUpdated> paymentStatusUpdatedProducerQueueProducer;

    private final StorePaymentProcessorFactory storePaymentProcessorFactory;

    private final List<AbstractPaymentSettlementRequestProducerQueueProducer<? extends PaymentInfo>> paymentSettlementRequestProducers;

    private final EcomRefundService ecomRefundService;

    private final Map<String, Lock> orderLocks = new HashMap<>();

    private final PaymentStatusUpdatedGenerator paymentStatusUpdatedGenerator;

    /**
     * Constructor for PaymentServiceImpl.
     *
     * @param paymentFactory                            PaymentFactory
     * @param orderPlacedPaymentValidator               OrderPlacedPaymentValidator
     * @param orderService                              OrderService
     * @param paymentStatusUpdatedProducerQueueProducer PaymentStatusUpdatedProducer
     * @param storePaymentProcessorFactory              StorePaymentProcessorFactory
     * @param paymentSettlementRequestProducers         List of type PaymentSettlementRequestProducer
     * @param ecomRefundService                         EcomRefundService
     */
    public PaymentServiceImpl(PaymentFactory paymentFactory,
                              OrderPlacedPaymentValidator orderPlacedPaymentValidator,
                              @Lazy OrderService orderService,
                              QueueProducer<PaymentStatusUpdated> paymentStatusUpdatedProducerQueueProducer,
                              StorePaymentProcessorFactory storePaymentProcessorFactory,
                              List<AbstractPaymentSettlementRequestProducerQueueProducer<? extends PaymentInfo>> paymentSettlementRequestProducers,
                              EcomRefundService ecomRefundService,
                              PaymentStatusUpdatedGenerator paymentStatusUpdatedGenerator
    ) {
        this.paymentFactory = paymentFactory;
        this.orderPlacedPaymentValidator = orderPlacedPaymentValidator;
        this.orderService = orderService;
        this.paymentStatusUpdatedProducerQueueProducer = paymentStatusUpdatedProducerQueueProducer;
        this.storePaymentProcessorFactory = storePaymentProcessorFactory;
        this.paymentSettlementRequestProducers = paymentSettlementRequestProducers;
        this.ecomRefundService = ecomRefundService;
        this.paymentStatusUpdatedGenerator = paymentStatusUpdatedGenerator;
    }

    /**
     * set an payment method object.
     *
     * @param payments
     * @param country
     * @param orderDetails
     */
    public void processPaymentMethodAndState(List<Payment> payments, String country, OrderDetails orderDetails, Order order) {
        PaymentProcessor paymentProcessor =
            storePaymentProcessorFactory.createPaymentProcessor(payments, country, order, orderDetails, paymentFactory);
        PaymentState paymentState = paymentProcessor.evaluatePaymentMethod();
        applyPaymentStateWithLock(order, paymentState);
    }

    /**
     * Validation logic for payment details in OrderPlaced messages.
     *
     * @param orderPlaced
     * @return PaymentValidationResponse to indicate the error message if it has an invalid payment detail,
     * otherwise it returns a SUCCESS message
     */
    @Override
    public PaymentValidationResponse validateOrderPlaced(OrderPlaced orderPlaced) {
        return orderPlacedPaymentValidator.validate(orderPlaced);
    }

    @Override
    @Transactional
    public void updatePaymentStatus(String orderId, PaymentState state) {
        var order = orderService.getOrderById(orderId);
        if (applyPaymentStateWithLock(order, state)) {
            orderService.save(order);
            PaymentStatusUpdated paymentStatusUpdated = paymentStatusUpdatedGenerator.generate(order);
            paymentStatusUpdatedProducerQueueProducer.enqueue(EnqueueParams.create(paymentStatusUpdated));
            log.info("Payment for order {} has been updated to {}.", orderId, state.name());
        }
    }

    @SuppressWarnings("NestedIfDepth")
    @Override
    public void startSettlementProcess(Order order) {
        if (isSettlementRequired(order)) {
            log.info("Start settlement for order {}", order.getOrderId());
            paymentSettlementRequestProducers.stream()
                .filter(producer -> producer.supports(order))
                .findFirst()
                .ifPresentOrElse(producer -> {
                        final PaymentSettlementRequest message = producer.enqueue(order);
                        final boolean published = message != null;
                        if (published) {
                            /* Skip processing if the payment status is already CANCELLED.
                             * This Indicates the order was manually cancelled and does not need further processing.
                             */
                            if (order.getPaymentStatus() == PaymentState.CANCELLED) {
                                log.info("Skipping sending payment status update: already CANCELLED for orderId={}",
                                    order.getOrderId());
                                return;
                            }

                            PaymentState finalState = PaymentState.SETTLEMENT_REQUESTING;
                            /* If the total amount is 0, settlement does not make any sense.
                             * But, we must send a request with zero amount to the payment provider to cancel the payment.
                             */
                            if (message.getTotalAmount().equals(0)) {
                                finalState = PaymentState.CANCELLED;
                            }

                            if (applyPaymentStateWithLock(order, finalState)) {
                                orderService.save(order);
                                var paymentStatusUpdated = paymentStatusUpdatedGenerator.generate(order);
                                paymentStatusUpdatedProducerQueueProducer
                                    .enqueue(EnqueueParams.create(paymentStatusUpdated));
                                log.info("Settlement process finished for order {}", order.getOrderId());
                                return;
                            }
                        }
                        log.error("Settlement process failed for order {}", order.getOrderId());
                    },
                    () -> log.error("No settlement producer found for order {}", order.getOrderId())
                );
        }
        if (isCancellationRefundRequired(order)) {
            ecomRefundService.issueCancellationRefund(order);
        }
    }

    @Override
    public boolean checkPaymentStatus(String orderId, PaymentState paymentState) {
        return orderService.existsByOrderIdAndPaymentStatus(orderId, paymentState);
    }

    @Override
    public PaymentType determinePaymentType(Order order) {
        String exceptionMessage = String.format("Order %s has no valid payment.", order.getOrderId());
        if (order.getPayments() == null || order.getPayments().isEmpty()) {
            throw new PaymentValidationException(exceptionMessage);
        }

        // If all payments are of type GIFTCARD, set the payment type to GIFTCARD.
        boolean onlyGiftCardPayment = order.getPayments()
            .stream()
            .allMatch(payment -> payment.getType().equals(PaymentType.GIFTCARD));

        if (onlyGiftCardPayment) {
            return PaymentType.GIFTCARD;
        }

        // Find the first payment that is not of type GIFTCARD.
        return order.getPayments()
            .stream()
            .filter(payment -> !payment.getType().equals(PaymentType.GIFTCARD))
            .findFirst()
            .map(PaymentInfo::getType)
            .orElseThrow(() -> new PaymentValidationException(exceptionMessage));

    }

    @Override
    public PaymentValidationResponse validateStateTransition(String orderId, PaymentState targetState) {
        var order = orderService.getOrderById(orderId);

        boolean isAllowed = isValidTransition(order.getPaymentStatus(), determinePaymentType(order), targetState);

        return PaymentValidationResponse.builder()
            .isSuccess(isAllowed)
            .errorMessages(isAllowed ? null : List.of("Transition from %s to %s is not allowed in order %s."
                .formatted(order.getPaymentStatus().name(), targetState, orderId)))
            .build();
    }

    /**
     * Check if the money should be settled for the order.
     *
     * @param order The order to check
     * @return true if the order needs to be settled, otherwise false
     */
    private boolean isSettlementRequired(Order order) {
        // If the order has no payments, no settlement is required.
        final List<PaymentInfo> payments = order.getPayments();
        if (payments == null || payments.isEmpty()) {
            log.warn("No payments found for order {}. Skipping the settlement process.", order.getOrderId());
            return false;
        }

        var paymentType = getMainPayment(order).getType();
        if (paymentType == null) {
            log.error("PaymentType is null for order {}.", order.getOrderId());
            return false;
        }
        return paymentType.isSettlementRequired();
    }

    private boolean isCancellationRefundRequired(Order order) {
        // If the order has no payments, no further action is required.
        final List<PaymentInfo> payments = order.getPayments();
        if (payments == null || payments.isEmpty()) {
            log.warn("No payments found for order {}.", order.getOrderId());
            return false;
        }

        return order.getPayments()
            .stream()
            .map(PaymentInfo::getType)
            .filter(Objects::nonNull)
            .anyMatch(PaymentType::isCancellationRefundRequired);
    }

    /**
     * Applies a new payment state to an order while ensuring exclusive access to the order object.
     *
     * @param order    The Order object to update the payment state for.
     * @param newState The new PaymentState to apply to the order.
     */
    private boolean applyPaymentStateWithLock(Order order, PaymentState newState) {
        String orderId = order.getOrderId();
        Lock orderLock = orderLocks.computeIfAbsent(orderId, k -> new ReentrantLock());
        boolean paymentStatusChanged;
        try {
            orderLock.lock();
            paymentStatusChanged = applyPaymentState(order, newState);
        } finally {
            orderLock.unlock();
        }
        return paymentStatusChanged;
    }

    private boolean applyPaymentState(Order order, PaymentState newState) {

        if (order == null || newState == null) {
            log.warn("Order or newState is null, cannot apply payment state.");
            return false;
        }

        if (order.getPaymentStatus() == null) {
            log.info("The paymentStatus or its identifier is null for order {}! Setting payment state to START.", order.getOrderId());
            order.setPaymentStatus(PaymentState.START);
        }

        PaymentState oldState = order.getPaymentStatus();

        if (oldState.equals(newState)) {
            log.info("Payment state was already {}. No change will be made for order {}.", oldState.name(), order.getOrderId());
            return false;
        }

        log.info("Apply Payment State for order {} and state {}", order.getOrderId(), newState.name());
        PaymentType paymentType = this.determinePaymentType(order);
        if (!isValidTransition(oldState, paymentType, newState)) {
            String errorMessage = String.format("The transition from %s (%s) to %s (%s) is invalid!",
                oldState.getIdentifier(), oldState.name(),
                newState.getIdentifier(), newState.name());
            throw new StateTransitionException(errorMessage, order.getOrderId());
        }

        order.setPrevPaymentStatus(oldState);
        order.setPaymentStatus(newState);
        log.info("Payment state changed from {} to {} for order {}.", oldState.name(), newState.name(), order.getOrderId());

        return true;
    }

    private PaymentInfo getMainPayment(Order order) {
        final PaymentInfo noneGiftCardPayment = order.getPayments()
            .stream()
            .filter(payment -> !PaymentType.GIFTCARD.equals(payment.getType()))
            .findFirst()
            .orElse(null);

        final PaymentInfo giftCardPayment = order.getPayments()
            .stream()
            .filter(payment -> PaymentType.GIFTCARD.equals(payment.getType()))
            .findFirst()
            .orElse(null);

        return noneGiftCardPayment != null ? noneGiftCardPayment : giftCardPayment;
    }
}
