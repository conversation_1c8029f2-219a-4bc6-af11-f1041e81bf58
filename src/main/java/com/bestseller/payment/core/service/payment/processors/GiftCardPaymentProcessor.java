package com.bestseller.payment.core.service.payment.processors;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.Payment;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.enumeration.PaymentMethod;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import com.bestseller.payment.core.domain.payment.GiftcardPayment;
import com.bestseller.payment.core.exception.PaymentValidationException;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class GiftCardPaymentProcessor implements PaymentProcessor {
    @Override
    public PaymentInfo createPayment(PaymentMethod paymentMethod,
                                     boolean isBcmc,
                                     String subMethod,
                                     String billingCountryCode) {
        return GiftcardPayment.builder()
                .processorId(getProcessorId())
                .subMethod(subMethod)
                .type(PaymentType.GIFTCARD)
                .build();
    }

    @Override
    public ProcessorId getProcessorId() {
        return ProcessorId.OPTICARD;
    }

    @Override
    public void validate(Payment payment, String billingCountryCode) {
        // For giftcards, SFCC already checks balance, currency and expireDate by calling GCS APIs
        // just want to make sure important values are available
        if (PaymentMethod.OC_GIFTCARD.equals(PaymentMethod.findByName(payment.getMethod()))) {
            if (StringUtils.isBlank(payment.getGiftCardNumber()) || StringUtils.isBlank(payment.getPspReference())) {
                throw new PaymentValidationException("Missing gift card details.");
            }
        }
    }
}
