package com.bestseller.payment.core.service.customerchoice;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.postPurchaseEventReceived.PostPurchaseEventReceived;
import com.bestseller.interfacecontractannotator.model.postpurchaseeventreceived.ReturnCreatedPayload;
import com.bestseller.payment.adapter.repository.CustomerRefundChoiceRepository;
import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.core.domain.CustomerRefundChoice;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderLine;
import com.bestseller.payment.core.domain.enumeration.CustomerRefundMethod;
import com.bestseller.payment.core.exception.MoreQuantityRequestedToRefundException;
import com.bestseller.payment.core.exception.OrderLineNotFoundException;
import com.bestseller.payment.core.exception.OrderNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.IntStream;

@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerRefundChoiceService {

    private final CustomerRefundChoiceRepository customerRefundChoiceRepository;
    private final OrderRepository orderRepository;

    public void processPostPurchaseEvent(PostPurchaseEventReceived event) {
        // Cast the payload to ReturnCreatedPayload
        ReturnCreatedPayload payload = (ReturnCreatedPayload) event.getData();

        // Get the order
        Order order = orderRepository.findById(event.getOrderId())
            .orElseThrow(() -> new OrderNotFoundException(event.getOrderId()));
        List<CustomerRefundChoice> customerChoices = new ArrayList<>();

        // Process each return request item
        payload.getReturnRequest().forEach(returnItem -> {
            // Find matching order lines by EAN
            OrderLine matchedOrderLine = order.getOrderLines().stream()
                .filter(orderLine -> orderLine.getEan().equals(returnItem.getEan()))
                .findFirst()
                .orElseThrow(() -> new OrderLineNotFoundException(returnItem.getEan()));

            if (matchedOrderLine.getOpenQty() < returnItem.getQuantity()) {
                throw new MoreQuantityRequestedToRefundException(matchedOrderLine.getEan(),
                    returnItem.getQuantity(),
                    matchedOrderLine.getOpenQty(), order.getOrderId(), payload.getReturnId());
            }

            int totalRefundChoicesForGivenEan = customerRefundChoiceRepository.countByOrderEntryId(order.getOrderId(),
                matchedOrderLine.getOrderEntryId());


            if (totalRefundChoicesForGivenEan + returnItem.getQuantity() > matchedOrderLine.getOpenQty()) {
                throw new MoreQuantityRequestedToRefundException(matchedOrderLine.getEan(),
                    returnItem.getQuantity(),
                    matchedOrderLine.getOriginalQty() - totalRefundChoicesForGivenEan, order.getOrderId(), payload.getReturnId());
            }

            customerChoices.addAll(IntStream.range(0, returnItem.getQuantity())
                .<CustomerRefundChoice>mapToObj(i -> CustomerRefundChoice.builder()
                    .orderLine(matchedOrderLine)
                    .customerRefundMethod(CustomerRefundMethod.fromCode(payload.getRefundMethod()))
                    .returnId(payload.getReturnId())
                    .isUsed(false)
                    .build())
                .toList());
        });

        if (customerChoices.isEmpty()) {
            return; // No choices to save
        }

        customerRefundChoiceRepository.saveAll(customerChoices);
        log.info("Created customer choice successfully for order id {} with refund method {}",
            event.getOrderId(), payload.getRefundMethod());
    }
}
