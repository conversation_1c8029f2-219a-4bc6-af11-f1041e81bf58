package com.bestseller.payment.core.service.payment.processors;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.Payment;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.enumeration.PaymentMethod;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import com.bestseller.payment.core.domain.payment.AdyenCardPayment;
import com.bestseller.payment.core.domain.payment.AdyenPayment;
import com.bestseller.payment.core.exception.PaymentValidationException;
import com.bestseller.payment.core.service.payment.factory.AdyenPaymentMethodFactory;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

@Component
public class AdyenPaymentProcessor implements PaymentProcessor {
    @Override
    public PaymentInfo createPayment(PaymentMethod paymentMethod,
                                 boolean isBcmc,
                                 String subMethod,
                                 String billingCountryCode) {
        AdyenPayment payment = AdyenPaymentMethodFactory.createPayment(paymentMethod, isBcmc);
        payment.setProcessorId(getProcessorId());
        payment.setSubMethod(subMethod);
        payment.setType(payment instanceof AdyenCardPayment ? PaymentType.ADYEN_CARD : PaymentType.ADYEN_BANK);
        return payment;
    }

    @Override
    public ProcessorId getProcessorId() {
        return ProcessorId.ADYEN;
    }

    @Override
    public void validate(Payment payment, String billingCountryCode) {
        if (StringUtils.isBlank(payment.getPspReference())) {
            throw new PaymentValidationException("PspReference is missing for Adyen.");
        }
        if (!PaymentMethod.isAdyen(payment.getMethod())) {
            throw new PaymentValidationException(String.format("Payment method %s is invalid for Adyen.",
                    payment.getMethod()));
        }
    }
}
