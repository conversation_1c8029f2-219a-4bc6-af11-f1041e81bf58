package com.bestseller.payment.core.service.refund;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequest.PaymentRefundRequest;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundStatusUpdated.RefundStatusUpdated;
import com.bestseller.payment.adapter.stream.messagegenerator.BankPaymentRefundRequestGenerator;
import com.bestseller.payment.adapter.stream.messagegenerator.GiftCardPaymentRefundRequestGenerator;
import com.bestseller.payment.adapter.stream.messagegenerator.RefundStatusUpdatedGenerator;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.OrderEntryAmount;
import com.bestseller.payment.core.domain.OrderLine;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.Refund;
import com.bestseller.payment.core.domain.RefundLine;
import com.bestseller.payment.core.domain.enumeration.ChargedRefundReason;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.payment.AdyenBankPayment;
import com.bestseller.payment.core.domain.payment.GiftcardPayment;
import com.bestseller.payment.core.dto.OrderItemToRefund;
import com.bestseller.payment.core.dto.RefundOptions;
import com.bestseller.payment.core.dto.RefundReason;
import com.bestseller.payment.core.exception.DoubleRefundException;
import com.bestseller.payment.core.exception.UnknownRefundReasonException;
import com.bestseller.payment.core.service.order.OrderService;
import com.bestseller.payment.core.service.price.PriceService;
import com.bestseller.payment.core.service.vat.OrderVatService;
import com.logistics.statetransition.RefundState;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Service
public class EcomRefundServiceImpl extends RefundCreationService implements EcomRefundService {
    private final OrderVatService orderVatService;
    private final OrderService orderService;

    /**
     * Constructor.
     *
     * @param orderService                              the order service
     * @param returnFeeService                          the return fee service
     * @param orderVatService                           the order vat service
     * @param priceService                              the price service
     * @param refundCalculationService                  the refund calculation service
     * @param paymentRefundRequestProducerQueueProducer the bank payment refund request producer
     * @param refundStatusUpdatedProducerQueueProducer  the refund status updated producer
     * @param refundStatusUpdatedGenerator              the refund status updated generator
     * @param giftCardPaymentRefundRequestGenerator     the gift card payment refund request generator
     * @param bankPaymentRefundRequestGenerator         the bank payment refund request generator
     */
    public EcomRefundServiceImpl(@Lazy OrderService orderService,
                                 ReturnFeeService returnFeeService,
                                 OrderVatService orderVatService,
                                 PriceService priceService,
                                 RefundCalculationService refundCalculationService,
                                 QueueProducer<PaymentRefundRequest> paymentRefundRequestProducerQueueProducer,
                                 QueueProducer<RefundStatusUpdated> refundStatusUpdatedProducerQueueProducer,
                                 RefundStatusUpdatedGenerator refundStatusUpdatedGenerator,
                                 GiftCardPaymentRefundRequestGenerator giftCardPaymentRefundRequestGenerator,
                                 BankPaymentRefundRequestGenerator bankPaymentRefundRequestGenerator) {
        super(
            orderService,
            returnFeeService,
            priceService,
            refundCalculationService,
            paymentRefundRequestProducerQueueProducer,
            refundStatusUpdatedProducerQueueProducer,
            refundStatusUpdatedGenerator,
            giftCardPaymentRefundRequestGenerator,
            bankPaymentRefundRequestGenerator
        );
        this.orderVatService = orderVatService;
        this.orderService = orderService;
    }


    /**
     * Refunds the given items of the order.
     *
     * @param orderId          the order id
     * @param itemToRefundList the items to refund
     * @param refundOptions    the refund options
     * @param requestId        the request id
     */
    @SuppressWarnings("IllegalCatch")
    @Transactional
    @Override
    public void refund(String orderId,
                       List<OrderItemToRefund> itemToRefundList,
                       RefundOptions refundOptions,
                       String requestId) {
        Order order = orderService.getOrderById(orderId);
        super.processRefund(order, requestId, itemToRefundList, BigDecimal.ZERO, refundOptions);
    }

    /**
     * Issues a cancellation refund if needed.
     *
     * @param order the order
     */
    @Transactional
    @Override
    public void issueCancellationRefund(Order order) {
        if (order.getRefunds()
            .stream()
            .anyMatch(refund -> refund.getRequestId() == null)) {
            log.info("Cancellation refund already issued for order {}", order.getOrderId());
            return;
        }

        List<OrderItemToRefund> itemToRefundList = order.getOrderLines()
            .stream()
            .filter(orderline -> orderline.getOpenQty() < orderline.getOriginalQty())
            .map(orderLine -> OrderItemToRefund.builder()
                .ean(orderLine.getEan())
                .quantity(orderLine.getOriginalQty() - orderLine.getOpenQty())
                .build())
            .toList();

        if (!itemToRefundList.isEmpty()) {
            boolean settlementRequired = order.getPayments()
                .stream()
                .anyMatch(payment -> payment.getType().isSettlementRequired());

            boolean isAllCancelled = order.getOrderLines()
                .stream()
                .filter(orderline -> orderline.getOpenQty() == 0)
                .count() == order.getOrderLines().size();

            RefundOptions refundOptions = RefundOptions.builder()
                .refundReason(RefundReason.CANCELLATION)
                .chargeReturnFee(false)
                .refundShippingFee(isAllCancelled)
                .build();

            if (settlementRequired && BigDecimal.ZERO.max(
                order.getTotalPaidPrice().getGrossDiscountedTotal().subtract(getGiftCardAuthorisedAmount(order))
            ).intValue() > 0) {
                log.info("No cancellation refund issued for order {} because the refund is considered in settlement",
                    order.getOrderId());
                return;
            }
            log.info("Issuing cancellation refund for order {}", order.getOrderId());
            refund(order.getOrderId(), itemToRefundList, refundOptions, null);
        }
    }

    /**
     * Creates a refund object with the given order and items to refund.
     *
     * @param order            the order to refund
     * @param itemToRefundList the items to refund
     * @param requestId        the request id
     * @return the refund object
     */
    @NotNull
    @Override
    protected Refund createRefundObject(Order order, List<OrderItemToRefund> itemToRefundList, String requestId,
                                        BigDecimal totalRefundAmount, RefundOptions refundOptions) {
        log.info("Creating refund object for order {}.", order.getOrderId());
        Refund refund = Refund.builder()
            .refundLines(new ArrayList<>())
            .refundState(RefundState.CREATED)
            .order(order)
            .csrInitials(refundOptions.csrInitials())
            .refundCharges(new ArrayList<>())
            .requestId(requestId)
            .build();
        final List<Refund> alreadyCreatedRefunds = order.getRefunds();
        final List<RefundLine> alreadyCreatedRefundLines = alreadyCreatedRefunds
            .stream()
            .flatMap(refundObject -> refundObject.getRefundLines().stream())
            .toList();

        AtomicReference<BigDecimal> manuallyCapturedAmount = new AtomicReference<>(getManuallyCapturedAmount(order));
        RefundReason refundReason = refundOptions.refundReason();
        itemToRefundList.forEach(returnedOrCancelledItem -> {
            if (!validateItemToRefund(order, returnedOrCancelledItem, alreadyCreatedRefundLines, refundReason)) {
                throw new DoubleRefundException(order.getOrderId(), returnedOrCancelledItem.ean(), returnedOrCancelledItem.quantity());
            }
            final OrderLine orderLine = order.getOrderLines()
                .stream()
                .filter(ol -> ol.getEan().equals(returnedOrCancelledItem.ean()))
                .findFirst()
                .orElseThrow(() ->
                    new RuntimeException("Couldn't find the order line with ean " + returnedOrCancelledItem.ean()));

            manuallyCapturedAmount.set(addRefundLines(returnedOrCancelledItem, orderLine, refund, manuallyCapturedAmount.get(), refundReason));
        });

        return refund;
    }

    @Override
    protected ChargedRefundReason getChargedRefundReason() {
        return ChargedRefundReason.SYSTEM;
    }

    private boolean validateItemToRefund(Order order,
                                         OrderItemToRefund itemToRefund,
                                         List<RefundLine> alreadyCreatedRefundLines,
                                         RefundReason refundReason) {
        final int openQuantity = order.getOrderLines()
            .stream()
            .filter(orderLine -> orderLine.getEan().equals(itemToRefund.ean()))
            .findFirst()
            .map(OrderLine::getOpenQty)
            .orElse(0);

        final int originalQuantity = order.getOrderLines()
            .stream()
            .filter(orderLine -> orderLine.getEan().equals(itemToRefund.ean()))
            .findFirst()
            .map(OrderLine::getOriginalQty)
            .orElse(0);

        final int refundedQuantity = alreadyCreatedRefundLines
            .stream()
            .filter(refundLine -> itemToRefund.ean().equals(refundLine.getOrderLine().getEan()))
            .reduce(0, (subtotal, refundLine) -> subtotal + refundLine.getQuantity(), Integer::sum);

        final int cancelledQuantity = originalQuantity - openQuantity;
        final int returnedQuantity = refundedQuantity - cancelledQuantity;

        if (refundReason == RefundReason.RETURN) {
            int totalReturnableQuantity = originalQuantity - returnedQuantity;
            return itemToRefund.quantity() <= totalReturnableQuantity;
        } else if (refundReason == RefundReason.CANCELLATION) {
            return itemToRefund.quantity() <= cancelledQuantity;
        } else {
            log.warn("Could not validate orderId= {} with items {} for refund due to unknown refund reason: {}",
                order.getOrderId(), itemToRefund, refundReason);
            throw new UnknownRefundReasonException(refundReason.name());
        }
    }

    private BigDecimal getGiftCardAuthorisedAmount(Order order) {
        return order.getPayments()
            .stream()
            .filter(payment -> PaymentType.GIFTCARD.equals(payment.getType()))
            .map(payment -> new BigDecimal(payment.getAuthorisedAmount()))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal getManuallyCapturedAmount(Order order) {
        return order.getPayments()
            .stream()
            .filter(payment -> !(payment instanceof AdyenBankPayment))
            .filter(payment -> !(payment instanceof GiftcardPayment))
            .map(PaymentInfo::getAuthorisedAmount)
            .map(BigDecimal::new)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * Adds lines to the refund and updates the manually captured amount.
     *
     * @param itemToRefund           the item to refund
     * @param orderLine              the order line
     * @param refund                 the refund
     * @param manuallyCapturedAmount the manually captured amount
     * @param refundReason           the refund reason
     */
    private BigDecimal addRefundLines(OrderItemToRefund itemToRefund,
                                      OrderLine orderLine,
                                      Refund refund,
                                      BigDecimal manuallyCapturedAmount,
                                      RefundReason refundReason) {
        BigDecimal manuallyCapturedAmountTemp = manuallyCapturedAmount;
        RefundLine refundedLine = RefundLine.builder()
            .orderLine(orderLine)
            .quantity(0)
            .build();
        RefundLine partialRefundLine = RefundLine.builder()
            .orderLine(orderLine)
            .quantity(0)
            .build();

        if (refundReason.equals(RefundReason.RETURN)) {
            refundedLine.setQuantity(itemToRefund.quantity());
        } else if (refundReason.equals(RefundReason.CANCELLATION)) {
            for (int i = 0; i < itemToRefund.quantity(); i++) {
                manuallyCapturedAmountTemp = enrichRefundForCancelledLinesAndReturnUpdatedManuallyCapturedAmount(
                    orderLine, refundedLine, partialRefundLine, manuallyCapturedAmountTemp);
            }
        }

        attachRefundLineToRefundObject(refundedLine, refund);
        attachPartialRefundLineToRefundObject(partialRefundLine, refund);
        return manuallyCapturedAmountTemp;
    }

    /**
     * Creates refund for cancelled line and return the remaining  manuallyCapturedAmount.
     *
     * @param orderLine              the order line
     * @param refundedLine           the refunded line
     * @param partialRefundLine      the partial refund line
     * @param manuallyCapturedAmount the manually captured amount
     */
    private BigDecimal enrichRefundForCancelledLinesAndReturnUpdatedManuallyCapturedAmount(OrderLine orderLine,
                                                                                           RefundLine refundedLine,
                                                                                           RefundLine partialRefundLine,
                                                                                           BigDecimal manuallyCapturedAmount) {
        BigDecimal manuallyCapturedAmountTemp = manuallyCapturedAmount;
        //Amount to-be-settled = 0 -> Create refund.
        if (manuallyCapturedAmount.compareTo(BigDecimal.ZERO) == 0) {
            refundedLine.setQuantity(refundedLine.getQuantity() + 1); // increment
        } else if (manuallyCapturedAmount.compareTo(orderLine.getOrderLinePaidAmount().getGrossDiscountedUnitPrice()) >= 0) {
            //Reducing to-be-settled amount be the cancellation amount.
            manuallyCapturedAmountTemp = manuallyCapturedAmount.subtract(orderLine.getOrderLinePaidAmount().getGrossDiscountedUnitPrice());
        } else {
            //this is the hybrid case: Reduced to-be-settled amount to zero,  partial refund created for the left over part.
            BigDecimal refundAmount = orderLine.getOrderLinePaidAmount().getGrossDiscountedUnitPrice().subtract(manuallyCapturedAmount);
            OrderEntryAmount refundLineTotal = OrderEntryAmount.builder()
                .grossDiscountedUnitPrice(refundAmount)
                .grossRetailUnitPrice(refundAmount)
                .build();
            partialRefundLine.setRefundLineTotal(refundLineTotal);
            partialRefundLine.setQuantity(1);
            manuallyCapturedAmountTemp = BigDecimal.ZERO;
        }
        return manuallyCapturedAmountTemp;
    }

    private void attachRefundLineToRefundObject(RefundLine refundLine, Refund refund) {
        List<RefundLine> refundLines = refund.getRefundLines();
        if (refundLine.getQuantity() > 0) {
            refundLines.add(refundLine);

            refundLine.setLineNumber(refundLine.getOrderLine().getLineNumber().toString());

            OrderEntryAmount orderLinePaidAmount = refundLine.getOrderLine().getOrderLinePaidAmount();
            OrderEntryAmount refundLineTotal = OrderEntryAmount.builder()
                .grossRetailUnitPrice(orderLinePaidAmount.getGrossRetailUnitPrice())
                .grossDiscountedUnitPrice(orderLinePaidAmount.getGrossDiscountedUnitPrice())
                .build();
            refundLine.setRefundLineTotal(refundLineTotal);
            log.debug("Added refund line to refund [ean={}]: quantity = {}",
                refundLine.getQuantity(),
                refundLine.getOrderLine().getEan());
        }
    }

    private void attachPartialRefundLineToRefundObject(RefundLine refundLine, Refund refund) {
        List<RefundLine> refundLines = refund.getRefundLines();
        if (refundLine.getQuantity() > 0) {
            refundLine.setLineNumber(refundLine.getOrderLine().getLineNumber().toString());
            refundLines.add(refundLine);
            log.debug("Added partial refund line to refund for amount: {}", refundLine.getRefundLineTotal().getGrossDiscountedTotal());
        }
    }
}
