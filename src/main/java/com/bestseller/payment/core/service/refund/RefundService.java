package com.bestseller.payment.core.service.refund;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.giftCardRefundResponse.GiftCardRefundResponse;
import com.bestseller.payment.core.domain.Refund;
import com.logistics.statetransition.RefundState;

import java.util.UUID;

public interface RefundService {
    boolean isDuplicateRequest(int refundId, RefundState targetRefundStatus);

    void updateRefundStatus(int refundId, RefundState targetRefundStatus);

    void processGiftCardRefundResponse(GiftCardRefundResponse message);

    boolean isExists(int refundId);

    boolean isStateTransitionAllowed(int refundId, RefundState targetRefundStatus);

    Refund getRefund(String orderId, UUID giftCardCorrelationId);
}
