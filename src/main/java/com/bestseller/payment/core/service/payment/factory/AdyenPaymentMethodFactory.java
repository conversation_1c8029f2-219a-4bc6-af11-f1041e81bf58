package com.bestseller.payment.core.service.payment.factory;

import com.bestseller.payment.core.domain.enumeration.PaymentMethod;
import com.bestseller.payment.core.domain.payment.AdyenBankPayment;
import com.bestseller.payment.core.domain.payment.AdyenCardPayment;
import com.bestseller.payment.core.domain.payment.AdyenPayment;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;

public class AdyenPaymentMethodFactory {
    private static final Map<PaymentMethod, Supplier<AdyenPayment>> PAYMENT_METHOD_MAP = new HashMap<>();

    static {
        PAYMENT_METHOD_MAP.put(PaymentMethod.ADYEN_CREDIT_CARD, () -> new AdyenBankPayment());

        PAYMENT_METHOD_MAP.put(PaymentMethod.ADYEN_IDEAL, () -> new AdyenBankPayment());
        PAYMENT_METHOD_MAP.put(PaymentMethod.ADYEN_SOFORT, () -> new AdyenBankPayment());
        PAYMENT_METHOD_MAP.put(PaymentMethod.ADYEN_PAYPAL, () -> new AdyenBankPayment());
        PAYMENT_METHOD_MAP.put(PaymentMethod.ADYEN_BCMC, () -> new AdyenBankPayment());

        PAYMENT_METHOD_MAP.put(PaymentMethod.ADYEN_AFTERPAY, () -> new AdyenCardPayment());
        PAYMENT_METHOD_MAP.put(PaymentMethod.ADYEN_RATEPAY, () -> new AdyenCardPayment());
        PAYMENT_METHOD_MAP.put(PaymentMethod.ADYEN_KLARNA, () -> new AdyenCardPayment());

    }
    /**
     * create an AdyenPayment object based on the paymentMethod.
     *
     * @param paymentMethod
     * @param isBCMC
     * @return
     */

    public static AdyenPayment
    createPayment(PaymentMethod paymentMethod, boolean isBCMC) {
        if (paymentMethod == null) {
            throw new IllegalArgumentException("Payment method should not be null!");
        }

        Supplier<AdyenPayment> supplier = PAYMENT_METHOD_MAP.get(paymentMethod);
        if (supplier != null) {
            if (paymentMethod == PaymentMethod.ADYEN_CREDIT_CARD) {
                return isBCMC ? supplier.get() : new AdyenCardPayment();
            } else {
                return supplier.get();
            }
        }

        throw new IllegalArgumentException("Unexpected payment Method: " + paymentMethod.name());
    }
}

