package com.bestseller.payment.core.service.payment.factory;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.Payment;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.enumeration.PaymentMethod;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;

import java.math.BigDecimal;

public interface PaymentFactory {

    PaymentInfo createPayment(ProcessorId processorId, PaymentMethod paymentMethod, String pspReference, String subMethod,
                          String billingCountryCode, BigDecimal authorizedAmount);

    void validateProcessor(Payment payment, String billingCountryCode);
}
