package com.bestseller.payment.core.service.order;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderFinalized;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.enumeration.PaymentState;

import java.util.Set;

public interface OrderService {
    void processValidOrderPlaced(ValidOrderPlaced validOrderPlaced);

    Set<Order> getOrdersWaitingForApproval(int numberOfDays);

    void processOrderFinalizedMessage(OrderFinalized orderFinalized);

    Order save(Order order);

    Order save(Order order, boolean immediate);

    Order getOrderById(String orderId);

    boolean existsByOrderIdAndPaymentStatus(String orderId, PaymentState paymentState);

    void cancelOrder(String orderId);
}
