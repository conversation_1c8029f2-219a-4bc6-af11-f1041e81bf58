package com.bestseller.payment.core.service.payment.storePaymentProcessor;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.Payment;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import com.bestseller.payment.core.exception.MultipleNonGiftcardPaymentsException;
import com.bestseller.payment.core.exception.StateTransitionException;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

@Slf4j
@Getter
@Setter
@SuppressWarnings("HiddenField")
public abstract class PaymentProcessor {
    private static final long MORE_THAN_ONE = 1;
    private List<Payment> payments;
    private final String country;
    private final Order order;
    private final Map<String, Lock> orderLocks = new HashMap<>();

    @SuppressWarnings("MissingJavadocMethod")
    public PaymentProcessor(List<Payment> payments, String country, Order order) {
        this.payments = payments;
        this.country = country;
        this.order = order;
    }

    public abstract PaymentInfo createPayment(Payment payment, String country);

    public abstract PaymentState getPaymentState(Payment payment);

    /**
     * map message payment into core payment object and set the payment on the order.
     */
    public PaymentState evaluatePaymentMethod() {

        List<PaymentInfo> paymentList = payments.stream().map(payment -> createPayment(payment, country)).collect(Collectors.toList());
        long nonGiftcardPaymentCount = paymentList.stream().filter(payment -> !payment.getType().equals(PaymentType.GIFTCARD)).count();
        if (nonGiftcardPaymentCount > MORE_THAN_ONE) {
            throw new MultipleNonGiftcardPaymentsException(order.getOrderId());
        }
        List<PaymentInfo> giftcardPaymentList = paymentList
            .stream()
            .filter(payment -> payment.getType().equals(PaymentType.GIFTCARD))
            .collect(Collectors.toList());
        if (giftcardPaymentList.size() > MORE_THAN_ONE) {
            combineGiftCardPayments(paymentList, giftcardPaymentList);
        }
        order.setPayments(paymentList);

        PaymentState paymentState;
        if (isGiftCardPaymentOnly(paymentList)) { // means there is just one Giftcard payment
            paymentState = PaymentState.AUTHORISED;
        } else {
            paymentState = getPaymentState(payments
                .stream()
                .filter(payment ->
                    !payment.getProvider().equals(ProcessorId.OPTICARD.name()))
                .findFirst()
                .orElseThrow(() -> new StateTransitionException("Invalid transition due to no "
                    + "payments found for order: ", order.getOrderId())));
        }

        return paymentState;

    }

    private void combineGiftCardPayments(List<PaymentInfo> paymentInfoList, List<PaymentInfo> giftcardPaymentList) {
        BigDecimal totalgiftCardAuthorizedAmount = giftcardPaymentList.stream()
            .map(paymentInfo -> new BigDecimal(paymentInfo.getAuthorisedAmount()))
            .reduce(BigDecimal.valueOf(0L), BigDecimal::add);
        PaymentInfo firstGiftCardPayment = giftcardPaymentList.getFirst();
        firstGiftCardPayment.setAuthorisedAmount(totalgiftCardAuthorizedAmount.toString());
        paymentInfoList.removeAll(giftcardPaymentList);
        paymentInfoList.add(firstGiftCardPayment);
    }

    private boolean isGiftCardPaymentOnly(List<PaymentInfo> paymentList) {
        return paymentList.size() == 1
            && paymentList.stream().
            anyMatch(paymentInfo -> paymentInfo.getProcessorId().equals(ProcessorId.OPTICARD));
    }
}
