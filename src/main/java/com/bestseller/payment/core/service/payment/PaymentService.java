package com.bestseller.payment.core.service.payment;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.OrderPlaced;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.OrderDetails;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.Payment;
import com.bestseller.payment.adapter.api.dto.PaymentValidationResponse;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.domain.enumeration.PaymentType;

import java.util.List;

public interface PaymentService {
    void processPaymentMethodAndState(List<Payment> payments, String country, OrderDetails orderDetails, Order order);

    PaymentValidationResponse validateOrderPlaced(OrderPlaced orderPlaced);

    void updatePaymentStatus(String orderId, PaymentState state);

    void startSettlementProcess(Order order);

    boolean checkPaymentStatus(String orderId, PaymentState paymentState);

    PaymentType determinePaymentType(Order order);

    PaymentValidationResponse validateStateTransition(String orderId, PaymentState targetState);
}

