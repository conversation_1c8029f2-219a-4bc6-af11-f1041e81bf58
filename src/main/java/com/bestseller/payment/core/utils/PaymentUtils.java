package com.bestseller.payment.core.utils;

import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.payment.GiftcardPayment;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class PaymentUtils {
    private static final BigDecimal BD_100 = BigDecimal.valueOf(100);

    /**
     * Get the main payment from the order. The main payment is the first payment that is not of type GIFTCARD.
     *
     * @param order the order
     */
    public static PaymentInfo getNoneGiftCardPayment(Order order) {
        return order.getPayments().stream()
            .filter(payment -> !PaymentType.GIFTCARD.equals(payment.getType()))
            .findFirst()
            .orElse(null);
    }

    /**
     * Get the gift card payment from the order.
     *
     * @param order the order
     */
    public static GiftcardPayment getGiftCardPayment(Order order) {
        return order.getPayments().stream()
            .filter(payment -> PaymentType.GIFTCARD.equals(payment.getType()))
            .map(payment -> (GiftcardPayment) payment)
            .findFirst()
            .orElse(null);
    }

    /**
     * Convert a BigDecimal amount to cents.
     *
     * @param amount the amount
     */
    public static int toIntCents(BigDecimal amount) {
        return amount
            .multiply(BD_100)
            .setScale(0, RoundingMode.HALF_UP)
            .intValue();
    }
}
