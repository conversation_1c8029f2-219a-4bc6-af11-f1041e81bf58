package com.bestseller.payment.core.utils;

import com.bestseller.payment.core.domain.enumeration.Platform;
import lombok.experimental.UtilityClass;

import java.util.stream.Stream;

@UtilityClass
public class PlatformUtil {

    /**
     * Identify which platform the order came from.
     *
     * @param store
     * @return Platform enum
     */
    public static Platform getPlatformFromStore(String store) {
        return Stream.of(Platform.values())
                .filter(platform -> platform.name().equals(store))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Platform can't be identified by this value: " + store));
    }

    /**
     * Check if the platform is TradeByte.
     *
     * @param platform
     * @return boolean
     */
    public static boolean isTradeByteOrder(Platform platform) {
        return Platform.TRADEBYTE.equals(platform);
    }

    /**
     * Check if the platform is Demandware.
     *
     * @param platform
     * @return boolean
     */
    public static boolean isDemandwareOrder(Platform platform) {
        return Platform.DEMANDWARE.equals(platform);
    }
}
