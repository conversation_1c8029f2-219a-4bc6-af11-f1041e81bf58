package com.bestseller.payment.core.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

@Slf4j
public class BcryptUtils {

    private static final int STRENGTH = 5;

    /**
     * Main method to hash a password using BCryptPasswordEncoder.
     *
     * @param args
     */
    public static void main(String[] args) {
        if (args.length != 1) {
            log.error("Usage: ./bcryptPassword <password>");
            System.exit(1);
        } else {
            log.info("Hashing password: \"{bcrypt}{}\"", new BCryptPasswordEncoder(STRENGTH).encode(args[0]));
        }
    }
}
