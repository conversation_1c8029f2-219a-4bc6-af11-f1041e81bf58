package com.bestseller.payment.core.utils;

import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.text.SimpleDateFormat;
import java.time.ZonedDateTime;
import java.util.Date;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class DateUtils {

    private static final String DATE_FORMAT = "yyyy-MM-dd";

    /**
     * converts {@link ZonedDateTime} to {@link java.util.Date} to have .If input is null will return null.
     *
     * @param zonedDateTime ZonedDateTime
     * @return Date
     */
    public static Date zonedDateTimeToDate(@NotNull ZonedDateTime zonedDateTime) {
        Date date = null;
        if (zonedDateTime != null) {
            date = Date.from(zonedDateTime.toInstant());
        }
        return date;
    }

    /**
     * Format date to yyyy-MM-dd.
     *
     * @param date Date
     * @return String
     */
    public static String formatDate(Date date) {
        return new SimpleDateFormat(DATE_FORMAT).format(date);
    }
}

