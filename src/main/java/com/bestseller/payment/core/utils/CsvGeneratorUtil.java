package com.bestseller.payment.core.utils;

import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.PaymentInfo;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Set;

@Slf4j
@UtilityClass
public class CsvGeneratorUtil {

    private static final String SUFFIX = ".csv";

    /**
     * Generate a csv file from a set of Orders.
     *
     * @param orders
     * @param fileName
     * @return csv file
     * @throws IOException
     */
    public static File generateFile(Set<Order> orders, String fileName) throws IOException {
        // Create a temporary CSV file
        File csvFile = File.createTempFile(fileName, SUFFIX);
        FileWriter writer = new FileWriter(csvFile);

        String headers = "Order ID,Order Date,Payment Status,Previous Payment Status,Payment ID,Authorised Amount,"
            + "Processor ID,Payment Reference,Payment Type,Sub Method,Sub Method Name";
        CSVFormat csvFormat = CSVFormat.DEFAULT.withHeader(headers);

        // Date format for orderDate
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        try (CSVPrinter printer = new CSVPrinter(writer, csvFormat)) {
            for (Order order : orders) {
                for (PaymentInfo payment : order.getPayments()) {
                    if (payment == null) {
                        log.error("Payment is null for order: {}", order.getOrderId());
                    }
                    printer.printRecord(order.getOrderId(),
                        dateFormat.format(order.getOrderDate()),
                        order.getPaymentStatus().toString(),
                        order.getPrevPaymentStatus().toString(),
                        payment.getPaymentId().toString(),
                        payment.getAuthorisedAmount(),
                        payment.getProcessorId().toString(),
                        payment.getPaymentReference(),
                        payment.getType().toString(),
                        payment.getSubMethod(),
                        payment.getSubMethodName());
                }
            }
            // Date format for orderDate
            printer.flush();
        } catch (IOException e) {
            e.printStackTrace();
        }

        return csvFile;
    }
}
