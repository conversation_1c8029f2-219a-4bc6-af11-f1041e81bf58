package com.bestseller.payment.core.domain.enumeration;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import lombok.extern.slf4j.Slf4j;

import java.util.stream.Stream;

@SuppressWarnings("MultipleStringLiterals")
@Slf4j
public enum PaymentState {
    /**
     * Default starting payment state.
     */
    START(0, "START", ""),
    /**
     * Payment review state.
     */
    REVIEW(1000, "REVIEW", "REVIEW"),
    /**
     * Payment authorized state.
     */
    AUTHORISED(2000, "AUTHORISED", "AUTH"),
    /**
     * Payment settlement request has been sent.
     */
    SETTLEMENT_REQUESTING(2500, "SETTLEMENT_REQUESTING", ""),
    /**
     * Payment settlement requested state.
     */
    SETTLEMENT_REQUESTED(3000, "SETTLEMENT_REQUESTED", ""),
    /**
     * Payment settlement failed state.
     */
    SETTLEMENT_DENIED(3500, "SETTLEMENT_DENIED", ""),
    /**
     * Payment cancelled state.
     */
    CANCELLED(4000, "CANCELLED", ""),
    /**
     * Payment settled state.
     */
    SETTLED(5000, "SETTLED", "SETTLED"),
    /**
     * Payment REVERSED state.
     */
    REVERSED(6000, "REVERSED", ""),
    /**
     * Payment refunded state.
     */
    REFUNDED(7000, "REFUNDED", ""),
    /**
     * Payment offline state.
     */
    OFFLINE(9000, "OFFLINE", "");

    private final int identifier;
    private final String description;
    private final String dwDescription;

    PaymentState(Integer identifier, String description, String dwDescription) {
        this.identifier = identifier;
        this.description = description;
        this.dwDescription = dwDescription;
    }

    /**
     * Get payment state from value.
     *
     * @param identifier
     * @return
     */
    public static PaymentState fromIdentifier(Integer identifier) {
        return Stream.of(values())
                .filter(item -> item.getIdentifier().equals(identifier))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("payment state can't be identified by this value: " + identifier));
    }

    /**
     * Get payment state from demandware message description.
     *
     * @param dwDescription
     * @return
     */

    public static PaymentState fromDwDescription(String dwDescription) {
        return Stream.of(values())
                .filter(item -> item.getDwDescription().equals(dwDescription))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Payment state can't be identified by this dwDescription: " + dwDescription));
    }

    public Integer getIdentifier() {
        return identifier;
    }

    public String getDescription() {
        return description;
    }

    public String getDwDescription() {
        return dwDescription;
    }

    /**
     * Convert to PaymentStatusUpdated.PaymentState.
     *
     * @return PaymentStatusUpdated.PaymentState
     */
    public PaymentStatusUpdated.PaymentState toPaymentStatusUpdatedPaymentState() {
        try {
            return PaymentStatusUpdated.PaymentState.fromValue(this.name());
        } catch (IllegalArgumentException e) {
            log.error("Something is wrong. {} can not be converted to any PaymentStatusUpdated.PaymentState item. "
                    + "Check the caller code.", this.name());
            throw new IllegalArgumentException(e);
        }
    }

    /**
     * Check if the state is greater or equal to the given state.
     *
     * @param state
     * @return
     */
    public boolean isGreaterOrEqual(PaymentState state) {
        return this.getIdentifier() >= state.getIdentifier();
    }
}
