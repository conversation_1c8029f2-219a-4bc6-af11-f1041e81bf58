package com.bestseller.payment.core.domain;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Entity
public class OrderLine extends OrderEntry {

    private Boolean bonusProduct;

    private Integer lineNumber;

    private String size;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "orderLinePaidAmountId", referencedColumnName = "id")
    private OrderEntryAmount orderLinePaidAmount;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "orderLineCancelledAmountId", referencedColumnName = "id")
    private OrderEntryAmount orderLineCancelledAmount;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "orderLineDiscountId")
    private OrderLineDiscount orderLineDiscount;

    @Column
    private String channelScopeId;

    @Column(length = 63)
    private String brand;

    @Column(length = 50)
    private String barcodeScanStoreNumber;

    @OneToMany(mappedBy = "orderLine", cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    private List<CustomerRefundChoice> customerRefundChoices;

}
