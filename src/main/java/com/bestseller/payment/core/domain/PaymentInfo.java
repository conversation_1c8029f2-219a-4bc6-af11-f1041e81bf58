package com.bestseller.payment.core.domain;

import com.bestseller.payment.core.domain.enumeration.PaymentMethod;
import com.bestseller.payment.core.domain.enumeration.PaymentType;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorColumn;
import jakarta.persistence.DiscriminatorType;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Inheritance;
import jakarta.persistence.InheritanceType;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Entity
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn(name = "type", discriminatorType = DiscriminatorType.STRING, length = 15)
@Table(name = "Payment")
public abstract class PaymentInfo extends BaseEntity {

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    private Integer paymentId;

    @Column
    private String orderId;

    @Column(length = 12, nullable = false)
    private String authorisedAmount;

    @Column(length = 45, nullable = false)
    @Enumerated(EnumType.STRING)
    private ProcessorId processorId;

    @Column(length = 100)
    private String paymentReference;

    @Column(insertable = false, updatable = false)
    @Enumerated(EnumType.STRING)
    private PaymentType type;

    @Column(length = 20)
    private String subMethod;

    @Column(length = 32)
    @Enumerated(EnumType.STRING)
    private PaymentMethod subMethodName;
}
