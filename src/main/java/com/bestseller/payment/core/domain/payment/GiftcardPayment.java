package com.bestseller.payment.core.domain.payment;

import com.bestseller.payment.core.domain.PaymentInfo;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Entity
@Getter
@Setter
@NoArgsConstructor
@SuperBuilder
@DiscriminatorValue("GIFTCARD")
public class GiftcardPayment extends PaymentInfo {
    @Transient
    private String authCode;
}
