package com.bestseller.payment.core.domain.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum CustomerRefundMethod {
    GIFT_CARD("giftCard"),
    REFUND("refund");

    private final String code;

    public static CustomerRefundMethod fromCode(String code) {
        if (code == null) {
            throw new IllegalArgumentException("Refund method code cannot be null");
        }

        for (CustomerRefundMethod method : values()) {
            if (method.getCode().equalsIgnoreCase(code)) {
                return method;
            }
        }
        throw new IllegalArgumentException("Unknown refund method code: " + code);
    }
}
