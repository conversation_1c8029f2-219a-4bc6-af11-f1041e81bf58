package com.bestseller.payment.core.domain.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

@RequiredArgsConstructor
@Getter
public enum PaymentStateTransition {

    KLARNA__START__TO__REVIEW(PaymentState.START, PaymentType.KLARNA, PaymentState.REVIEW),
    KLARNA__START__TO__AUTHORISED(PaymentState.START, PaymentType.KLARNA, PaymentState.AUTHORISED),
    KLARNA__REVIEW__TO__AUTHORISED(PaymentState.REVIEW, PaymentType.KLA<PERSON>, PaymentState.AUTHORISED),
    <PERSON><PERSON><PERSON>__START__TO__CANCELLED(PaymentState.REVIEW, PaymentType.KLARNA, PaymentState.CANCELLED),
    KLARNA__SETTLEMENT_REQUESTING__TO__SETTLEMENT_DENIED(
        PaymentState.SETTLEMENT_REQUESTING, PaymentType.KLARNA, PaymentState.SETTLEMENT_DENIED),
    K<PERSON><PERSON>__AUTHORISED__TO__SETTLEMENT_REQUESTING(
        PaymentState.AUTHORISED, PaymentType.KLARNA, PaymentState.SETTLEMENT_REQUESTING),
    KLARNA__AUTHORISED__TO__CANCELLED(PaymentState.AUTHORISED, PaymentType.KLARNA, PaymentState.CANCELLED),
    KLARNA__SETTLEMENT_REQUESTING__TO__SETTLED(
        PaymentState.SETTLEMENT_REQUESTING, PaymentType.KLARNA, PaymentState.SETTLED),
    KLARNA__SETTLEMENT_DENIED__TO__SETTLED(PaymentState.SETTLEMENT_DENIED, PaymentType.KLARNA, PaymentState.SETTLED),
    KLARNA__SETTLED__TO__REFUNDED(PaymentState.SETTLED, PaymentType.KLARNA, PaymentState.REFUNDED),


    ADYEN_BANK__START__TO__REVIEW(PaymentState.START, PaymentType.ADYEN_BANK, PaymentState.REVIEW),
    ADYEN_BANK__START__TO__AUTHORISED(PaymentState.START, PaymentType.ADYEN_BANK, PaymentState.AUTHORISED),
    ADYEN_BANK__REVIEW__TO__AUTHORISED(PaymentState.REVIEW, PaymentType.ADYEN_BANK, PaymentState.AUTHORISED),
    ADYEN_BANK__REVIEW__TO__CANCELLED(PaymentState.REVIEW, PaymentType.ADYEN_BANK, PaymentState.CANCELLED),
    ADYEN_BANK__REVIEW__TO__SETTLED(PaymentState.REVIEW, PaymentType.ADYEN_BANK, PaymentState.SETTLED),
    ADYEN_BANK__AUTHORISED__TO__REFUNDED(PaymentState.AUTHORISED, PaymentType.ADYEN_BANK, PaymentState.REFUNDED),
    ADYEN_BANK__AUTHORISED__TO__CANCELLED(PaymentState.AUTHORISED, PaymentType.ADYEN_BANK, PaymentState.CANCELLED),


    ADYEN_CARD__START__TO__REVIEW(PaymentState.START, PaymentType.ADYEN_CARD, PaymentState.REVIEW),
    ADYEN_CARD__START__TO__AUTHORISED(PaymentState.START, PaymentType.ADYEN_CARD, PaymentState.AUTHORISED),
    ADYEN_CARD__REVIEW__TO__AUTHORISED(PaymentState.REVIEW, PaymentType.ADYEN_CARD, PaymentState.AUTHORISED),
    ADYEN_CARD__REVIEW__TO__CANCELLED(PaymentState.REVIEW, PaymentType.ADYEN_CARD, PaymentState.CANCELLED),
    ADYEN_CARD__REVIEW__TO__SETTLED(PaymentState.REVIEW, PaymentType.ADYEN_CARD, PaymentState.SETTLED),
    ADYEN_CARD__AUTHORISED__TO__SETTLEMENT_REQUESTING(
        PaymentState.AUTHORISED, PaymentType.ADYEN_CARD, PaymentState.SETTLEMENT_REQUESTING),
    ADYEN_CARD__AUTHORISED__TO__CANCELLED(PaymentState.AUTHORISED, PaymentType.ADYEN_CARD, PaymentState.CANCELLED),
    ADYEN_CARD__AUTHORISED__TO__REFUNDED(PaymentState.AUTHORISED, PaymentType.ADYEN_CARD, PaymentState.REFUNDED),
    ADYEN_CARD__SETTLEMENT_REQUESTING__TO__SETTLEMENT_DENIED(
        PaymentState.SETTLEMENT_REQUESTING, PaymentType.ADYEN_CARD, PaymentState.SETTLEMENT_DENIED),
    ADYEN_CARD__SETTLEMENT_REQUESTING__TO__SETTLED(
        PaymentState.SETTLEMENT_REQUESTING, PaymentType.ADYEN_CARD, PaymentState.SETTLED),
    ADYEN_CARD__SETTLEMENT_DENIED__TO__SETTLEMENT_REQUESTING(
        PaymentState.SETTLEMENT_DENIED, PaymentType.ADYEN_CARD, PaymentState.SETTLEMENT_REQUESTING),
    ADYEN_CARD__SETTLEMENT_DENIED__TO__SETTLED(
        PaymentState.SETTLEMENT_DENIED, PaymentType.ADYEN_CARD, PaymentState.SETTLED),
    ADYEN_CARD__SETTLED__TO__SETTLEMENT_DENIED(
        PaymentState.SETTLED, PaymentType.ADYEN_CARD, PaymentState.SETTLEMENT_DENIED),

    GIFTCARD__START__TO__AUTHORISED(PaymentState.START, PaymentType.GIFTCARD, PaymentState.AUTHORISED),
    GIFTCARD__START__TO__SETTLED(PaymentState.START, PaymentType.GIFTCARD, PaymentState.SETTLED),


    NONE__START__TO_9000(PaymentState.START, PaymentType.NONE, PaymentState.OFFLINE),


    OFFLINE__START__TO_9000(PaymentState.START, PaymentType.OFFLINE, PaymentState.OFFLINE);

    private final PaymentState fromState;
    private final PaymentType paymentTypeName;
    private final PaymentState toState;

    /**
     * Check if the transition is valid.
     *
     * @param fromState   source state
     * @param paymentType payment type
     * @param toState     target state
     * @return true if the transition is valid
     */
    public static boolean isValidTransition(PaymentState fromState, PaymentType paymentType, PaymentState toState) {
        return Arrays.stream(PaymentStateTransition.values())
            .anyMatch(transition -> transition.fromState.equals(fromState)
                && transition.paymentTypeName.equals(paymentType)
                && transition.toState.equals(toState));
    }
}

