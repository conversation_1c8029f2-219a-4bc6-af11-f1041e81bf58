package com.bestseller.payment.core.domain.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PaymentType {
    KLARNA(true, false, "Klarna invoices"),
    ADYEN_BANK(false, true, "Real-time bank transfers (Adyen)"),
    ADYEN_CARD(true, false, "Credit and debit cards (Adyen)"),
    GIFTCARD(false, true, "Giftcard payment method"),
    NONE(false, false, "No payment method"),
    OFFLINE(false, false, "OTTO payments"),

    @Deprecated
    ESW(false, false, "ESW payments"),

    @Deprecated
    IN_STORE_ORDER(false, false, "In-store order");

    private final boolean isSettlementRequired;
    private final boolean isCancellationRefundRequired;
    private final String description;
}
