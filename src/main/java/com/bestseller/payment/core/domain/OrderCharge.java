package com.bestseller.payment.core.domain;

import com.bestseller.payment.core.domain.enumeration.ChargedRefundReason;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Entity
public class OrderCharge extends OrderEntry {

    private Boolean refunded;

    private Boolean cancelled;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "chargeTotalId", referencedColumnName = "id")
    private OrderEntryAmount chargeTotal;

    @ManyToOne
    @JoinColumn(name = "refundId", insertable = false, updatable = false)
    private Refund refund;

    @ManyToOne
    @JoinColumn(name = "orderId", insertable = false, updatable = false)
    private Order order;

    @Column
    private ChargedRefundReason refundReason;

}
