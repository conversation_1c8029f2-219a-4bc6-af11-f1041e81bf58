package com.bestseller.payment.core.domain.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum ProcessorId {
    /**
     * KLARNA_PAYMENTS (Klarna payments handled by SFCC).
     */
    KLARNA_PAYMENTS(true),
    /**
     * OPTICARD.
     */
    OPTICARD(false),
    /**
     * ADYEN.
     */
    ADYEN(true),
    /**
     * TRADEBYTE_OFFLINE.
     */
    @Deprecated
    TRADEBYTE_OFFLINE(false),

    /**
     * OFFLINE.
     */
    OFFLINE(false),


    @Deprecated
    SHOPIFY_OFFLINE(false),
    @Deprecated
    KLARNA(true),
    @Deprecated
    ESW(false),
    @Deprecated
    IN_STORE_ORDER(false);

    private final boolean supportsSettlement;

    /**
     * Find processorId by name.
     *
     * @param name
     * @return the ProcessorId enum
     */
    public static ProcessorId findByName(String name) {
        return Stream.of(values()).
            filter(processorId -> processorId.name().equalsIgnoreCase(name))
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("processor Id can't be identified by this value: " + name));
    }
}
