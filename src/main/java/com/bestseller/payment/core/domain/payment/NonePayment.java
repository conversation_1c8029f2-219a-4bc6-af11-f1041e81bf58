package com.bestseller.payment.core.domain.payment;

import com.bestseller.payment.core.domain.PaymentInfo;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Entity
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@DiscriminatorValue("NONE")
public class NonePayment extends PaymentInfo {
}
