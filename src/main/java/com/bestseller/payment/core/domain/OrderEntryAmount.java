package com.bestseller.payment.core.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Entity
public class OrderEntryAmount extends BaseEntity {

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    private Integer id;

    @Column(precision = 10, scale = 2)
    private BigDecimal grossRetailUnitPrice;

    @Column(precision = 10, scale = 2)
    private BigDecimal grossDiscountedUnitPrice;

    @Column(precision = 10, scale = 2)
    private BigDecimal unitDiscount;

    @Column(precision = 10, scale = 2)
    private BigDecimal originalGrossDiscountedTotal;

    @Column(precision = 10, scale = 2)
    private BigDecimal grossDiscountedTotal;

    @Column(precision = 10, scale = 2)
    private BigDecimal lineVAT;

    @Column(precision = 10, scale = 2)
    private BigDecimal unitVAT;
}
