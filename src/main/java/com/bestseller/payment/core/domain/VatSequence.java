package com.bestseller.payment.core.domain;

import com.bestseller.payment.core.domain.enumeration.VatType;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Table
@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
public class VatSequence extends BaseEntity {

    @EmbeddedId
    private VatSequenceId vatSequenceId;

    @Column
    private Long currentSequence;

    @Getter
    @Setter
    @AllArgsConstructor
    @NoArgsConstructor
    @Embeddable
    public static class VatSequenceId implements Serializable {
        private String countryCode;
        private String sequenceYear;

        @Enumerated(EnumType.STRING)
        private VatType vatType;
    }
}
