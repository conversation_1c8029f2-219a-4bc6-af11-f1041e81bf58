package com.bestseller.payment.core.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Builder
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(uniqueConstraints = {@UniqueConstraint(columnNames = {"country", "effectiveDate"})})
public class CountryToReturnFeePricing {

    @Id
    @GeneratedValue
    private Integer id;

    @Column
    private String country;

    @Column
    private BigDecimal returnFeeAmount;

    @Column
    private BigDecimal returnFeeTaxRate;

    @Column
    private Date effectiveDate;

    @OneToMany(fetch = FetchType.EAGER)
    @JoinColumn(name = "countryToReturnFeePricingId")
    private List<BrandSpecificReturnFee> brandSpecificReturnFees;

    private BigDecimal threshold;
}
