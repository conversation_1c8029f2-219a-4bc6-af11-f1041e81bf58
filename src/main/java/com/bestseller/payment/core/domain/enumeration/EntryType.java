package com.bestseller.payment.core.domain.enumeration;

import lombok.Getter;

@Getter
public enum EntryType {
    LINE("LINE", "A regular line that contains order items"),
    RETURN_FEE("RETURN_FEE", "Fee for returning the order"),
    RETURN_GOGW("RETURN_GOGW", "Refund for return shipping cost"),
    INVOICE_PAYMENT_FEE("INVOICE_PAYMENT_FEES", "Fee for paying the invoice"),
    NONE("NONE", "No fee"),
    SHIPMENT_FEE("SHIPMENT_FEE", "Fee for shipping the order");

    private final String code;
    private final String description;

    EntryType(String code, String description) {
        this.code = code;
        this.description = description;
    }

}
