package com.bestseller.payment.core.domain.enumeration;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.EnumSet;
import java.util.stream.Stream;

@Getter
@RequiredArgsConstructor
public enum PaymentMethod {

    ADYEN_CREDIT_CARD("Adyen - Credit Card payment"),

    ADYEN_IDEAL("Adyen - iDEAL Online Bank Transfer"),

    ADYEN_KLARNA("Adyen - Klarna Through Adyen (Temporary for Mobile App)"),

    ADYEN_SOFORT("Adyen - SOFORT Online Bank Transfer"),

    ADYEN_PAYPAL("Adyen - PayPal payment"),

    ADYEN_AFTERPAY("Adyen - Afterpay payment"),

    ADYEN_RATEPAY("Adyen - Ratepay payment"),

    KLARNA_INVOICE("Klarna Invoice - Full payment"),

    KLARNA_ACCOUNT("Klarna Account - Installments payment"),

    ADYEN_BCMC("Adyen - Bancontact/Mister Cash payment"),

    OC_GIFTCARD("GiftCard payment"),

    ESW("Worldwide store payments"),

    @Deprecated
    IN_STORE_ORDER("In-store order");

    private static EnumSet<PaymentMethod> klarnaPaymentMethod = EnumSet.of(KLARNA_ACCOUNT, KLARNA_INVOICE);
    private static EnumSet<PaymentMethod> adyenPaymentMethod = EnumSet.of(ADYEN_CREDIT_CARD,
        ADYEN_IDEAL,
        ADYEN_KLARNA,
        ADYEN_SOFORT,
        ADYEN_PAYPAL,
        ADYEN_AFTERPAY,
        ADYEN_RATEPAY,
        ADYEN_BCMC);

    private final String description;

    /**
     * Find payment method given a string name.
     *
     * @param name
     * @return PaymentMethod enum
     */
    public static PaymentMethod findByName(String name) {
        return Stream.of(values())
            .filter(paymentMethod -> paymentMethod.name().equalsIgnoreCase(name))
            .findFirst().orElseThrow(() -> new IllegalArgumentException("Payment method can't be identified by this value: " + name));
    }

    public static boolean isKlarna(String paymentMethod) {
        return klarnaPaymentMethod.contains(findByName(paymentMethod));
    }

    public static boolean isAdyen(String paymentMethod) {
        return adyenPaymentMethod.contains(findByName(paymentMethod));
    }

}
