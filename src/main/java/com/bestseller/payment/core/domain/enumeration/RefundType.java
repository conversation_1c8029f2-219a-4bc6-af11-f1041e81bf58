package com.bestseller.payment.core.domain.enumeration;

public enum RefundType {
    BANK_TRANSACTION,
    GIFT_CARD,
    MIXED,
    NONE; // Orders with 100% discount

    public static RefundType from(boolean refundedByBankTransaction, boolean refundedByGiftCard) {
        if (refundedByBankTransaction && refundedByGiftCard) {
            return MIXED;
        } else if (refundedByBankTransaction) {
            return BANK_TRANSACTION;
        } else if (refundedByGiftCard) {
            return GIFT_CARD;
        } else {
            return NONE;
        }
    }
}
