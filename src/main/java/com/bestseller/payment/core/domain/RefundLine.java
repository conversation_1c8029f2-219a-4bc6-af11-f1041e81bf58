package com.bestseller.payment.core.domain;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Entity
public class RefundLine extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column
    private Integer quantity;

    @Column
    private String lineNumber;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "orderLineId")
    private OrderLine orderLine;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "refundLineTotal")
    private OrderEntryAmount refundLineTotal;

    @ManyToOne
    @JoinColumn(name = "refundId", insertable = false, updatable = false)
    private Refund refund;
}
