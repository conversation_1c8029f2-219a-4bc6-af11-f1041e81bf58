package com.bestseller.payment.core.domain;

import com.bestseller.payment.core.domain.enumeration.RefundType;
import com.logistics.statetransition.RefundState;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.util.List;
import java.util.UUID;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Entity
public class Refund extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(length = 15)
    private String refundId;

    private RefundState refundState;

    @ManyToOne
    @JoinColumn(name = "orderId")
    private Order order;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JoinColumn(name = "refundId")
    private List<RefundLine> refundLines;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JoinColumn(name = "refundId")
    private List<OrderCharge> refundCharges;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "refundTotal")
    private OverallTotal refundTotal;

    @Column(length = 15)
    private String csrInitials;

    @Column(length = 36)
    private String requestId;

    @Enumerated(EnumType.STRING)
    private RefundType refundType;

    @Column(length = 36)
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID giftCardCorrelationId;
}
