package com.bestseller.payment.core.domain.enumeration;

public enum CountryCode2AccountSubMethodMapping {
    DE_1922("DE", "1922"),
    NL_3028("NL", "3028"),
    SE_2947("SE", "2947"),
    NO_2870("NO", "2870"),
    DK_2982("DK", "2982"),
    FI_2561("FI", "2561"),
    AT_MINUS_1("AT", "-1");
    private final String country;
    private final String code;
    CountryCode2AccountSubMethodMapping(String country, String code) {
        this.code = code;
        this.country = country;
    }

    public String getCode() {
        return code;
    }

    public String getCountry() {
        return country;
    }
    /**
     * Get country code by country name.
     *
     * @param targetCountry
     * @return
     */

    public static String getCodeByCountry(String targetCountry) {
        for (CountryCode2AccountSubMethodMapping countryCode : CountryCode2AccountSubMethodMapping.values()) {
            if (countryCode.country.equalsIgnoreCase(targetCountry)) {
                return countryCode.code;
            }
        }
        throw new IllegalArgumentException("Invalid country name: " + targetCountry);
    }
}
