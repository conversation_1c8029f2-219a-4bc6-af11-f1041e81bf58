package com.bestseller.payment.core.domain.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@SuppressWarnings("MultipleStringLiterals")
@AllArgsConstructor
@Getter
public enum ChargedRefundReason {
    SYSTEM(0, "SYSTEM", false, EntryType.NONE),

    FREE_SHIPPING_CODE_DID_NOT_WORK(
        1, "Free shipping code did not work", true, EntryType.SHIPMENT_FEE),
    CUSTOMER_FORGOT_TO_USE_FREE_SHIPPING_CODE(
        2, "Customer forgot to use free shipping code", true, EntryType.SHIPMENT_FEE),
    SHIPPING_FEE_REFUND_TEST_BUY(
        3, "Shipping fee refund as this is a test buy", true, EntryType.SHIPMENT_FEE),
    REFUND_DUE_TO_WRONG_RETURNCODE(
        4, "Refund due to wrong return code", true, EntryType.SHIPMENT_FEE),
    CUSTOMER_REFUSED_PACKAGE_BUT_PAID_RETURN_FEE(
        5,
        "Customer refused the package, but still paid for the return shipping fee",
        true,
        EntryType.SHIPMENT_FEE),
    EXPRESS_DELIVERY_NOT_ON_TIME(
        6, "Express delivery not delivered on time", true, EntryType.SHIPMENT_FEE),
    CUSTOMER_NEVER_RECEIVED_PARCEL(
        7, "Customer never received the parcel", true, EntryType.SHIPMENT_FEE),
    CUSTOMER_PAID_FOR_RETURN_SHIPMENT(
        8, "Customer paid for the return shipment", true, EntryType.SHIPMENT_FEE),
    CUSTOMER_HAS_PAID_INVOICE_SHIPMENT(
        9, "Customer has paid the invoice", true, EntryType.SHIPMENT_FEE),
    RETURNED_ITEMS_VIA_POSTOFFICE_SHIPMENT(
        10,
        "Customer returned items via post office (refund 8 euro)",
        true,
        EntryType.SHIPMENT_FEE),
    GENERAL_RETURN_PROBLEMS_SHIPMENT(
        11, "General problems with return", true, EntryType.SHIPMENT_FEE),
    CUSTOMER_HAS_PAID_INVOICE_PAYMENT(
        12, "Customer has paid the invoice", true, EntryType.INVOICE_PAYMENT_FEE),
    RETURNED_ITEMS_VIA_POSTOFFICE_PAYMENT(
        13, "Customer returned items via post office (refund 8 euro)", true,
        EntryType.INVOICE_PAYMENT_FEE),
    GENERAL_RETURN_PROBLEMS_PAYMENT(
        14, "General problems with return", true, EntryType.INVOICE_PAYMENT_FEE),
    RETURNED_ITEMS_IN_STORE(
        15, "Customer returned items in a store", true, EntryType.INVOICE_PAYMENT_FEE);

    private final Integer identifier;
    private final String description;
    private final Boolean enabled;
    private final EntryType type;

    /**
     * Gets the refund reason by identifier.
     *
     * @param identifier the identifier
     * @return the refund reason
     */
    public static ChargedRefundReason fromIdentifier(Integer identifier) {
        return Arrays.stream(ChargedRefundReason.values()).
            filter(reason -> reason.getIdentifier()
                .equals(identifier)).findFirst()
            .orElseThrow(() -> new IllegalArgumentException("Unknown identifier: " + identifier));
    }

    /**
     * Gets the refund reason by description.
     *
     * @param description the description
     * @return the refund reason
     */
    public static ChargedRefundReason fromDescriptionAndEntryType(String description, EntryType entryType) {
        return Arrays.stream(ChargedRefundReason.values())
            .filter(reason -> reason.getDescription().equals(description))
            .filter(reason -> reason.getType().equals(entryType))
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("Unknown description: " + description));
    }
}
