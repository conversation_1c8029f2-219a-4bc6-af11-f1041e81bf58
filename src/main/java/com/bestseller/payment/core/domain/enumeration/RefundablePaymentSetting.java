package com.bestseller.payment.core.domain.enumeration;

import com.bestseller.payment.core.exception.OrderIsNotRefundableException;
import com.bestseller.payment.core.exception.SettlementNotYetHappenedException;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum RefundablePaymentSetting {
    GIFT_CARD__AUTHORISED(PaymentType.GIFTCARD, PaymentState.AUTHORISED, true, null),

    K<PERSON>RNA__AUTHORISED(PaymentType.KLARNA, PaymentState.AUTHORISED, false, SettlementNotYetHappenedException.class),
    KLARNA__SETTLED(PaymentType.KLARNA, PaymentState.SETTLED, true, null),
    KLA<PERSON>__SETTLEMENT_REQUESTING(PaymentType.KLARNA, PaymentState.SETTLEMENT_REQUESTING, false, SettlementNotYetHappenedException.class),
    <PERSON><PERSON><PERSON>__SETTLEMENT_DENIED(PaymentType.KLARNA, PaymentState.SETTLEMENT_DENIED, false, OrderIsNotRefundableException.class),

    ADYEN_BANK__AUTHORISED(PaymentType.ADYEN_BANK, PaymentState.AUTHORISED, true, null),

    ADYEN_CARD__SETTLED(PaymentType.ADYEN_CARD, PaymentState.SETTLED, true, null),
    ADYEN_CARD__SETTLEMENT_REQUESTING(PaymentType.ADYEN_CARD, PaymentState.SETTLEMENT_REQUESTING, false, SettlementNotYetHappenedException.class),
    ADYEN_CARD__AUTHORISED(PaymentType.ADYEN_CARD, PaymentState.AUTHORISED, false, SettlementNotYetHappenedException.class);

    private final PaymentType paymentType;
    private final PaymentState paymentState;
    private final boolean refundable;
    private final Class<? extends RuntimeException> exceptionClass;

    /**
     * Find a refundable payment setting by payment type and payment state.
     *
     * @param paymentType  the payment type
     * @param paymentState the payment state
     * @return the refundable payment setting
     */
    public static RefundablePaymentSetting find(PaymentType paymentType, PaymentState paymentState) {
        for (RefundablePaymentSetting validPayment : values()) {
            if (validPayment.paymentType.equals(paymentType) && validPayment.paymentState.equals(paymentState)) {
                return validPayment;
            }
        }
        return null;
    }

    /**
     * Creates and throws the appropriate exception for this payment setting if it's not refundable.
     *
     * @param orderId the order ID
     * @throws RuntimeException if the payment setting is not refundable
     */
    public void throwExceptionIfNotRefundable(String orderId) {
        if (!refundable) {
            if (exceptionClass == OrderIsNotRefundableException.class) {
                throw new OrderIsNotRefundableException(orderId, paymentType, paymentState);
            } else {
                throw new SettlementNotYetHappenedException(orderId, paymentState);
            }
        }
    }
}
