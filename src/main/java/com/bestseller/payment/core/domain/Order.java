package com.bestseller.payment.core.domain;

import com.bestseller.payment.core.domain.enumeration.Brand;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.domain.enumeration.Platform;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.OrderBy;
import jakarta.persistence.OrderColumn;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.IndexColumn;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Table(name = "Orders")
public class Order extends BaseEntity {

    @Id
    @Column(nullable = false)
    private String orderId;

    @Column
    private Date orderDate;

    @Column
    private PaymentState paymentStatus;

    @Column
    private PaymentState prevPaymentStatus;

    @Column
    private String currency;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "totalPaidPriceId", referencedColumnName = "id")
    private OverallTotal totalPaidPrice;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "totalCancelledPriceId", referencedColumnName = "id")
    private OverallTotal totalCancelledPrice;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JoinColumn(name = "orderId", referencedColumnName = "orderId")
    @OrderColumn(name = "indexCol")
    private List<PaymentInfo> payments;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JoinColumn(name = "orderId", referencedColumnName = "orderId")
    @OrderColumn(name = "indexCol")
    @Builder.Default
    private List<OrderLine> orderLines = new ArrayList<>();

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JoinColumn(name = "orderId", referencedColumnName = "orderId")
    @IndexColumn(name = "indexCol")
    private List<OrderCharge> orderCharges;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JoinColumn(name = "orderId", referencedColumnName = "orderId")
    private List<Promotion> promotions;

    @OrderBy("id ASC")
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER, mappedBy = "order")
    private List<Refund> refunds;

    @Enumerated(EnumType.STRING)
    private Brand brand;

    @Column
    private String billingCountryCode;

    @Column
    private String shippingCountryCode;
    @Column
    @Enumerated(EnumType.STRING)
    private Platform platform;

    @Column
    private boolean offlinePayment;

    @Column(length = 60)
    private String vatOrderNumber;

    public void addRefund(Refund refund) {
        this.refunds.add(refund);
        refund.setOrder(this);
    }
}
