package com.bestseller.payment.core.domain.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderState {
    BEGIN(false),
    PLACED(false),

    DISPATCHED(true),
    RETURNED(true),
    POS_RETURNED_IN_STORE(true),
    REFUND_CREATED(true),
    RETURN_PROCESSED(true),

    CANCELLED(false),
    CANCELLATION_REFUND(false);

    private final boolean isFulfilled;
}
