package com.bestseller.payment.core.domain;

import com.bestseller.payment.core.domain.enumeration.EntryType;
import jakarta.persistence.Column;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@MappedSuperclass
public abstract class OrderEntry extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer orderEntryId;

    @Column
    @Enumerated(EnumType.STRING)
    private EntryType type;

    @Column(length = 256)
    private String name;

    @Column(length = 64)
    private String skuId;

    @Column(length = 24)
    private String ean;

    @Column(precision = 10, scale = 4)
    private BigDecimal taxRate;

    @Column(length = 100)
    private String vatClassId;

    private Integer originalQty;

    private Integer openQty;

    @Column(length = 256)
    private String promotionId;

    @Column(length = 256)
    private String campaignId;

    @Column(length = 256)
    private String couponId;

    @Column(precision = 10, scale = 2)
    private BigDecimal standardRetailPrice;

    @Column(precision = 10, scale = 2)
    private BigDecimal costPrice;

    @Column(length = 256)
    private String partnerReference;
}
