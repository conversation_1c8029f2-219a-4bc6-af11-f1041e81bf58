package com.bestseller.payment.core.domain;

import com.bestseller.payment.core.domain.enumeration.CustomerRefundMethod;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class CustomerRefundChoice extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private CustomerRefundMethod customerRefundMethod;

    @Column(nullable = false)
    private String returnId;

    @Column(nullable = false)
    private boolean isUsed;

    @ManyToOne
    @JoinColumn(name = "orderEntryId", nullable = false)
    private OrderLine orderLine;
}
