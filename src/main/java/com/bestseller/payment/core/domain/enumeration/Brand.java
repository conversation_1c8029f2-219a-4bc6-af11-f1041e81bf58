package com.bestseller.payment.core.domain.enumeration;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public enum Brand {
    MB("general", null, "Multibrand"),
    J<PERSON>("jack-jones", "J<PERSON>", "Jack & Jones"),
    MM("mama-licious", "MM", "Mamalicious"),
    NI("name-it", "NI", "Name it"),
    OC("object-collectors-item", "OC", "Object"),
    OF("outfitters-nation", "OF", "Outfitters nation"),
    ON("only", "ON", "Only"),
    PC("pieces", "PC", "Pieces"),
    SL("selected", "SL", "Selected"),
    VL("vila", "VL", "Vila"),
    VM("vero-moda", "VM", "Vero Moda"),
    JL("jlindeberg", "<PERSON><PERSON>", "<PERSON><PERSON>"),
    <PERSON>("junarose", "<PERSON>", "Junarose"),
    GV(null, null, "All gift-vouchers"),
    BC("bestseller-com", null, "Bestseller"),
    LP("little-pieces", null, "Little Pieces"),
    YS("yas", "YS", "Yas"),
    NM("noisy-may", "NM", "Noisy"),
    OS("only-and-sons", "OS", "Only & sons"),
    AD("adpt", null, "Adapt"),
    PT("produkt", null, "Produkt"),
    BI("bianco", null, "Bianco"),
    OH("outfit-hustlers", null, "Outfit Hustlers"),
    JX("jjxx", "JX", "JJXX"),
    IQ("iiqual", "IQ", "IIQUAL"),
    TF("thefounded", null, "The Founded"),
    LA("lilatelier", "LA", "Lil Atelier"),
    RD("royal-denim-division", null, "Royal Denim Division"),
    AP("aprel", null, "Aprel"),
    AN("annarr", null, "Annarr"),
    TS("twosoon", "TS", "TwoSoon"),
    RE("rougeedit", "RE", "Rouge Edit"),
    TB("tradebyte", "TB", "TradeByte");

    private final String demandwareName;
    private final String brandAbbreviation;
    private final String description;

    public static Brand getDefaultBrand() {
        return MB;
    }
}
