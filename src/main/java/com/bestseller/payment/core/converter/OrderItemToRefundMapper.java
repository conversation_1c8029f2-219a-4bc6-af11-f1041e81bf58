package com.bestseller.payment.core.converter;

import com.bestseller.payment.core.dto.OrderItemToRefund;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper
public interface OrderItemToRefundMapper {
    @Mapping(target = "ean", source = "ean")
    @Mapping(target = "quantity", source = "quantity")
    OrderItemToRefund mapToOrderItemToRefund(
            com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.ItemsToRefund itemsToRefund);

    List<OrderItemToRefund> mapToOrderItemToRefundList(
            List<com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.ItemsToRefund> itemsToRefundList);
}
