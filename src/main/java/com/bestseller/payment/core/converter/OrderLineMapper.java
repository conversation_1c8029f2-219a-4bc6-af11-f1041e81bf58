package com.bestseller.payment.core.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.OrderLine;
import com.bestseller.payment.core.domain.OrderEntryAmount;
import com.bestseller.payment.core.domain.OrderLineDiscount;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.math.BigDecimal;
import java.util.List;

/**
 * This class maps the OrderLine from the ValidOrderPlaced message to the OrderLine in the Order domain object.
 */
@Mapper
public interface OrderLineMapper {

    @Mapping(target = "skuId", source = "ean")
    @Mapping(target = "lineNumber", source = "lineNumber")
    @Mapping(target = "name", source = "productName")
    @Mapping(target = "standardRetailPrice", source = "validOrderLine", qualifiedByName = "setStandardRetailPrice")
    @Mapping(target = "originalQty", source = "quantity")
    @Mapping(target = "openQty", source = "quantity")
    @Mapping(target = "type", expression = "java(com.bestseller.payment.core.domain.enumeration.EntryType.LINE)")
    @Mapping(target = "taxRate", source = "vat")
    @Mapping(target = "vatClassId", source = "vatClass")
    @Mapping(target = "bonusProduct", source = "bonusProduct", defaultValue = "false")
    @Mapping(target = "promotionId", source = "orderLinePromotion.promotionId")
    @Mapping(target = "campaignId", source = "orderLinePromotion.campaignId")
    @Mapping(target = "couponId", source = "orderLinePromotion.couponId")
    @Mapping(target = "orderLinePaidAmount", source = "validOrderLine", qualifiedByName = "setOrderLinePaidAmount")
    @Mapping(target = "orderLineDiscount", source = "validOrderLine", qualifiedByName = "setOrderLineDiscount")
    com.bestseller.payment.core.domain.OrderLine mapOrderLine(OrderLine validOrderLine);

    List<com.bestseller.payment.core.domain.OrderLine> mapOrderLines(List<OrderLine> validOrderLine);

    @Named("setStandardRetailPrice")
    default BigDecimal setStandardRetailPrice(OrderLine validOrderLine) {
        return validOrderLine.getListPrice() != null ? validOrderLine.getListPrice() : validOrderLine.getRetailPrice();
    }

    /**
     * This method sets the OrderLinePaidAmount object for the OrderLine in the Order domain object.
     *
     * @param validOrderLine
     * @return
     */
    @Named("setOrderLinePaidAmount")
    default OrderEntryAmount setOrderLinePaidAmount(OrderLine validOrderLine) {
        BigDecimal quantity = new BigDecimal(validOrderLine.getQuantity());
        BigDecimal retailPrice = validOrderLine.getRetailPrice();
        BigDecimal discountedUnitPrice = validOrderLine.getDiscountedUnitPrice() == null
                ? retailPrice
                : validOrderLine.getDiscountedUnitPrice();
        BigDecimal grossDiscountedTotal = validOrderLine.getDiscountedTotalPrice() == null
                ? discountedUnitPrice.multiply(quantity)
                : validOrderLine.getDiscountedTotalPrice();
        return OrderEntryAmount
                .builder()
                .grossRetailUnitPrice(retailPrice)
                .originalGrossDiscountedTotal(grossDiscountedTotal)
                .grossDiscountedUnitPrice(discountedUnitPrice)
                .unitVAT(BigDecimal.ZERO)
                .build();
    }

    /**
     * This method sets the OrderLineDiscount object for the OrderLine in the Order domain object.
     *
     * @param validOrderLine
     * @return
     */
    @Named("setOrderLineDiscount")
    default OrderLineDiscount setOrderLineDiscount(OrderLine validOrderLine) {
        BigDecimal discountValue = validOrderLine.getDiscountValue();
        if (discountValue != null && discountValue.compareTo(BigDecimal.ZERO) > 0) {
            return OrderLineDiscount.builder().discountAmount(discountValue).build();
        }
        return null;
    }
}
