package com.bestseller.payment.core.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ShippingCharge;
import com.bestseller.payment.core.domain.OrderCharge;
import com.bestseller.payment.core.domain.OrderEntryAmount;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.ArrayList;
import java.util.List;

/**
 * this class is used to map the shipping charge to order charge.
 */
@Mapper
public interface OrderChargeMapper {
    @Mapping(target = "name", source = "shippingCharge.shippingName")
    @Mapping(target = "ean", source = "shippingCharge.shippingFeesEan")
    @Mapping(target = "taxRate", source = "shippingCharge.shippingTaxRate")
    @Mapping(target = "originalQty", constant = "1")
    @Mapping(target = "openQty", constant = "1")
    @Mapping(target = "cancelled", constant = "false")
    @Mapping(target = "refunded", constant = "false")
    @Mapping(target = "promotionId", source = "shippingCharge.promotionId")
    @Mapping(target = "campaignId", source = "shippingCharge.campaignId")
    @Mapping(target = "couponId", source = "shippingCharge.couponId")
    @Mapping(target = "type", expression = "java(com.bestseller.payment.core.domain.enumeration.EntryType.SHIPMENT_FEE)")
    @Mapping(target = "vatClassId", expression = "java(!isDmwOrder ? \"service-fees\" : null)")
    @Mapping(target = "costPrice", source = "shippingCharge.baseGrossUnitPrice")
    @Mapping(target = "standardRetailPrice", source = "shippingCharge.baseGrossUnitPrice")
    @Mapping(target = "chargeTotal", source = "shippingCharge", qualifiedByName = "setOrderChargeTotal")
    OrderCharge mapToOrderCharge(ShippingCharge shippingCharge, boolean isDmwOrder);

    /**
     * This method maps the list of shipping charges to the list of order charges.
     *
     * @param shippingCharges
     * @param isDmwOrder
     * @return
     */
    default List<OrderCharge> mapToOrderCharges(List<ShippingCharge> shippingCharges, boolean isDmwOrder) {
        List<OrderCharge> orderCharges = new ArrayList<>();
        for (ShippingCharge shippingCharge : shippingCharges) {
            orderCharges.add(mapToOrderCharge(shippingCharge, isDmwOrder));
        }
        return orderCharges;
    }


    /**
     * This method sets the OrderChargeTotal object for the OrderCharge in the Order domain object.
     *
     * @param shippingCharge
     * @return
     */
    @Named("setOrderChargeTotal")
    default OrderEntryAmount setOrderChargeTotal(ShippingCharge shippingCharge) {
        return OrderEntryAmount.builder()
                .grossDiscountedUnitPrice(shippingCharge.getGrossUnitPriceAfterDiscounts())
                .grossDiscountedTotal(shippingCharge.getGrossUnitPriceAfterDiscounts())
                .originalGrossDiscountedTotal(shippingCharge.getGrossPriceAfterDiscounts())
                .grossRetailUnitPrice(shippingCharge.getBaseGrossUnitPrice())
                .unitVAT(shippingCharge.getShippingVatAmount())
                .build();
    }
}
