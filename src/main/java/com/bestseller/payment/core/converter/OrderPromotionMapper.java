package com.bestseller.payment.core.converter;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.OrderPromotion;
import com.bestseller.payment.core.domain.Promotion;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper
public interface OrderPromotionMapper {

    Promotion mapOrderPromotion(OrderPromotion orderPromotion);

    List<Promotion> mapOrderPromotions(List<OrderPromotion> orderPromotion);
}
