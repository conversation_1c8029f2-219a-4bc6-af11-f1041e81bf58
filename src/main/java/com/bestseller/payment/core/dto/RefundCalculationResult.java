package com.bestseller.payment.core.dto;

import lombok.Builder;

@Builder
public record RefundCalculationResult(int amountToRefundByBankTransactionInCents, int amountToRefundByGiftCardInCents) {
    public boolean isRefundableByBankTransaction() {
        return amountToRefundByBankTransactionInCents > 0;
    }

    public boolean isRefundableByGiftCard() {
        return amountToRefundByGiftCardInCents > 0;
    }
}
