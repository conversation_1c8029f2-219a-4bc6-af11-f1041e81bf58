package com.bestseller.payment.core.dto;

import lombok.Builder;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Builder
public record RefundOptions(boolean chargeReturnFee,
                            boolean refundShippingFee,
                            RefundReason refundReason,
                            List<OrderChargeRefund> orderChargeRefundRequests,
                            String csrInitials) {
    public String csrInitials() {
        return Objects.requireNonNullElse(csrInitials, "SYSTEM");
    }

    /**
     * Returns the refund reason.
     *
     * @return the refund reason
     */
    public List<OrderChargeRefund> orderChargeRefundRequests() {
        return Objects.requireNonNullElseGet(orderChargeRefundRequests, ArrayList::new);
    }
}
