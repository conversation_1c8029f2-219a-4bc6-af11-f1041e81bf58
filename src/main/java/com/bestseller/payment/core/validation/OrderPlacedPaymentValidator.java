package com.bestseller.payment.core.validation;

import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.OrderLine;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.OrderPlaced;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.Payment;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderPlaced.ShippingCharge;
import com.bestseller.payment.adapter.api.dto.PaymentValidationResponse;
import com.bestseller.payment.core.domain.enumeration.PaymentMethod;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.domain.enumeration.Platform;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import com.bestseller.payment.core.exception.PaymentValidationException;
import com.bestseller.payment.core.service.payment.factory.PaymentFactory;
import com.bestseller.payment.core.utils.PlatformUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;

@Component
@Slf4j
public class OrderPlacedPaymentValidator implements PaymentValidator<PaymentValidationResponse, OrderPlaced> {

    private static final int MAX_NON_GC_PAYMENT = 1;
    private static final int INT_ZERO = 0;

    private final PaymentFactory paymentFactory;

    public OrderPlacedPaymentValidator(PaymentFactory paymentFactory) {
        this.paymentFactory = paymentFactory;
    }

    @Override
    public PaymentValidationResponse validate(OrderPlaced message) {

        String validationMessage = null;
        boolean validationFlag = true;

        try {
            Platform platform = PlatformUtil.getPlatformFromStore(message.getStore());
            if (Platform.DEMANDWARE.equals(platform)) {
                log.debug("Validating order {} as demandware order", message.getOrderId());
                doStorefrontValidation(message);
            } else if (Platform.TRADEBYTE.equals(platform)) {
                log.debug("Validating order {} as tradebyte order", message.getOrderId());
                doTradeByteValidation(message);
            }
        } catch (PaymentValidationException | NullPointerException | IllegalArgumentException e) {
            validationMessage = e.getMessage();
            validationFlag = false;
        }

        return PaymentValidationResponse
            .builder()
            .isSuccess(validationFlag)
            .errorMessages(validationMessage == null ? null : List.of(validationMessage))
            .build();

    }

    /**
     * Validate message for tradebyte orders.
     *
     * @param orderPlaced incoming message
     */
    private void doTradeByteValidation(OrderPlaced orderPlaced) {

        if (orderPlaced.getOrderDetails().getOrderValue() == null) {
            throw new PaymentValidationException("Order value must not be empty or null.");
        }

        validateShippingCharge(orderPlaced.getShippingInformation().getShippingCharges());

    }

    /**
     * Validate payment details for store front orders.
     * Giftcards are being validated already in the storefront by calling the checkbalance and redeem API.
     *
     * @param orderPlaced incoming message
     */
    private void doStorefrontValidation(OrderPlaced orderPlaced) {

        if (StringUtils.isBlank(orderPlaced.getCustomerInformation().getBillingAddress().getCountry())) {
            throw new PaymentValidationException("Billing address does not have a country.");
        }

        validateShippingCharge(orderPlaced.getShippingInformation().getShippingCharges());

        if (isOnlinePayment(orderPlaced)) {
            validateOnlinePayment(orderPlaced);
        }

    }

    private void validateOnlinePayment(OrderPlaced orderPlaced) {
        if (orderPlaced.getPayments() == null || orderPlaced.getPayments().isEmpty()) {
            throw new PaymentValidationException("Payment details should not be empty.");
        }

        if (orderPlaced.getPayments()
            .stream()
            .filter(Predicate.not(p -> p.getProvider().equals(ProcessorId.OPTICARD.name())))
            .count() > MAX_NON_GC_PAYMENT) {
            throw new PaymentValidationException("Invalid payment combination.");
        }

        validateCurrency(orderPlaced);

        BigDecimal paymentAmount = BigDecimal.ZERO;
        for (Payment payment : orderPlaced.getPayments()) {
            //validate payment state

            PaymentState.fromDwDescription(payment.getState());

            PaymentMethod.findByName(payment.getMethod());

            //validate payment based on ProcessorId
            paymentFactory.validateProcessor(payment, orderPlaced.getCustomerInformation().getBillingAddress().getCountry());

            if (payment.getAmount() == null) {
                throw new PaymentValidationException(String.format("Payment amount is invalid for %s.",
                    payment.getMethod()));
            }

            paymentAmount = paymentAmount.add(payment.getAmount());
        }

        BigDecimal orderCharges = getOrderLinesTotal(orderPlaced.getOrderLines());
        orderCharges = orderCharges.add(orderPlaced.getShippingInformation().getShippingCharges().stream().reduce(BigDecimal.ZERO,
            (subtotal, shippingCharge) -> subtotal.add(shippingCharge.getGrossPriceAfterDiscounts()), BigDecimal::add));

        if (paymentAmount.compareTo(orderCharges) != INT_ZERO) {
            throw new PaymentValidationException("Payment amount is inconsistent with the total order charges.");
        }
    }

    private void validateCurrency(OrderPlaced orderPlaced) {
        if (StringUtils.isBlank(orderPlaced.getOrderDetails().getCurrency())) {
            throw new PaymentValidationException("Currency is missing at `OrderDetails.currency`.");
        }
    }

    private static boolean isOnlinePayment(OrderPlaced orderPlaced) {
        // We need to consider when orderPlaced.getOfflinePayment() is null.
        return !Optional.ofNullable(orderPlaced.getOfflinePayment())
            .orElse(false);
    }

    private void validateShippingCharge(List<ShippingCharge> shippingCharges) {
        shippingCharges.forEach(shippingCharge -> {
            if (shippingCharge.getBaseGrossUnitPrice() == null
                || shippingCharge.getGrossPriceAfterDiscounts() == null
                || shippingCharge.getGrossUnitPriceAfterDiscounts() == null) {
                throw new PaymentValidationException("Shipping fee should not be empty.");
            }
            if (shippingCharge.getShippingTaxRate() == null) {
                throw new PaymentValidationException("Shipping VAT should not be empty.");
            }
            if (shippingCharge.getShippingName() == null
                || shippingCharge.getShippingName().isBlank()) {
                throw new PaymentValidationException("Shipping name should not be empty.");
            }
            if (shippingCharge.getShippingFeesEan().isBlank()
                || shippingCharge.getShippingFeesEan() == null
            ) {
                throw new PaymentValidationException("Shipping fees EAN should not be empty.");
            }
        });
    }

    private BigDecimal getOrderLinesTotal(List<OrderLine> orderLines) {

        BigDecimal orderLineTotal = BigDecimal.ZERO;
        for (OrderLine orderLine : orderLines) {

            // validate pricing and taxes
            if (orderLine.getVat() == null) {
                throw new PaymentValidationException(String.format("There is no vat for orderLine %s.",
                    orderLine.getLineNumber()));
            }
            if (orderLine.getRetailPrice() == null) {
                throw new PaymentValidationException(String.format("There is no retail price for orderLine %s.",
                    orderLine.getLineNumber()));
            }
            BigDecimal discountedUnitPrice = orderLine.getDiscountedUnitPrice() == null
                ? orderLine.getRetailPrice()
                : orderLine.getDiscountedUnitPrice();
            BigDecimal quantity = new BigDecimal(orderLine.getQuantity());
            BigDecimal grossDiscountedTotal = orderLine.getDiscountedTotalPrice() == null
                ? discountedUnitPrice.multiply(quantity)
                : orderLine.getDiscountedTotalPrice();

            orderLineTotal = orderLineTotal.add(grossDiscountedTotal);
        }
        return orderLineTotal;
    }

}
