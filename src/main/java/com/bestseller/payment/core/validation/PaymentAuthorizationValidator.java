package com.bestseller.payment.core.validation;

import com.bestseller.payment.adapter.repository.OrderRepository;
import com.bestseller.payment.core.domain.enumeration.ProcessorId;
import com.bestseller.payment.core.exception.OrderNotFoundException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

@Slf4j
@AllArgsConstructor
@Component
public class PaymentAuthorizationValidator {

    private final OrderRepository orderRepository;

    /**
     * Validate paymentauthorized and paymentrejected messages.
     *
     * @param orderId
     * @param provider
     * @param pspReference
     * @return True if valid, otherwise False
     */
    public boolean validate(String orderId, String provider, String pspReference) {
        if (StringUtils.isBlank(pspReference)) {
            return false;
        }

        if (ProcessorId.findByName(provider) == null) {
            return false;
        }

        if (!orderRepository.existsByOrderIdAndPaymentsPaymentReference(orderId, pspReference)) {
            log.warn("Order not found for orderId: {} and pspReference: {}", orderId, pspReference);
            throw new OrderNotFoundException(orderId);
        }
        return true;
    }
}
