package com.bestseller.payment.core.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundFailed.PaymentRefundFailed;
import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;

public class PaymentRefundFailedQueueConsumer extends AbstractValidatorQueueConsumer<PaymentRefundFailed> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "correlationId=%s";

    private final RefundService refundService;

    public PaymentRefundFailedQueueConsumer(
        IdempotencyChecker<PaymentRefundFailed> idempotencyChecker,
        QueueConfig queueConfig,
        TaskPayloadTransformer<PaymentRefundFailed> taskPayloadTransformer,
        RefundService refundService,
        MessageValidator<PaymentRefundFailed> messageValidator
    ) {
        super(queueConfig, taskPayloadTransformer, idempotencyChecker, messageValidator);
        this.refundService = refundService;
    }

    @Override
    protected String getMessageDetails(PaymentRefundFailed message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getCorrelationId());
    }

    @Override
    public void process(PaymentRefundFailed message) {
        refundService.updateRefundStatus(Integer.parseInt(message.getCorrelationId()), RefundState.REFUND_FAILED);
    }

}
