package com.bestseller.payment.core.queue;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducerQueueConsumer;
import com.bestseller.dbqueue.kafka.producer.queue.KafkaMessageProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundStatusUpdated.RefundStatusUpdated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class RefundStatusUpdatedProducerQueueConsumer
    extends AbstractKafkaMessageProducerQueueConsumer<RefundStatusUpdated> {

    private static final Logger LOGGER = LoggerFactory.getLogger(RefundStatusUpdatedProducerQueueConsumer.class);
    private static final String MESSAGE_DETAILS_TEMPLATE = "refundId=%s, status=%s";

    public RefundStatusUpdatedProducerQueueConsumer(
        QueueConfig queueConfig,
        TaskPayloadTransformer<RefundStatusUpdated> taskPayloadTransformer,
        KafkaMessageProducer<RefundStatusUpdated> kafkaMessageProducer) {
        super(queueConfig, taskPayloadTransformer, LOGGER, kafkaMessageProducer);
    }

    @Override
    protected String getMessageDetails(RefundStatusUpdated message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getRefundId(), message.getStatus());
    }
}
