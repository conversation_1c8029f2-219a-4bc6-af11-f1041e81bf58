package com.bestseller.payment.core.queue;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducerQueueConsumer;
import com.bestseller.dbqueue.kafka.producer.queue.KafkaMessageProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementRequest.PaymentSettlementRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class PaymentSettlementRequestProducerQueueConsumer
    extends AbstractKafkaMessageProducerQueueConsumer<PaymentSettlementRequest> {

    private static final Logger LOGGER = LoggerFactory.getLogger(PaymentSettlementRequestProducerQueueConsumer.class);
    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, correlationId=%s";

    public PaymentSettlementRequestProducerQueueConsumer(
        QueueConfig queueConfig,
        TaskPayloadTransformer<PaymentSettlementRequest> taskPayloadTransformer,
        KafkaMessageProducer<PaymentSettlementRequest> kafkaMessageProducer) {
        super(queueConfig, taskPayloadTransformer, LOGGER, kafkaMessageProducer);
    }

    @Override
    protected String getMessageDetails(PaymentSettlementRequest message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getCorrelationId());
    }
}
