package com.bestseller.payment.core.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRejected.PaymentRejected;
import com.bestseller.payment.core.service.order.OrderService;

public class PaymentRejectedQueueConsumer extends AbstractValidatorQueueConsumer<PaymentRejected> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s";

    private final OrderService orderService;

    public PaymentRejectedQueueConsumer(
        IdempotencyChecker<PaymentRejected> idempotencyChecker,
        QueueConfig queueConfig,
        TaskPayloadTransformer<PaymentRejected> taskPayloadTransformer,
        OrderService orderService,
        MessageValidator<PaymentRejected> messageValidator
    ) {
        super(queueConfig, taskPayloadTransformer, idempotencyChecker, messageValidator);
        this.orderService = orderService;
    }

    @Override
    protected String getMessageDetails(PaymentRejected message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId());
    }

    @Override
    public void process(PaymentRejected message) {
        orderService.cancelOrder(message.getOrderId());
    }
}
