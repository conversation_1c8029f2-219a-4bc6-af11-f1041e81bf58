package com.bestseller.payment.core.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.RefundCreationRequested;
import com.bestseller.payment.core.converter.OrderItemToRefundMapper;
import com.bestseller.payment.core.dto.RefundOptions;
import com.bestseller.payment.core.dto.RefundReason;
import com.bestseller.payment.core.service.refund.EcomRefundService;

public class RefundCreationRequestedQueueConsumer extends AbstractValidatorQueueConsumer<RefundCreationRequested> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, refundCreationRequestedId=%s, itemsToRefund=%s";

    private final EcomRefundService refundService;
    private final OrderItemToRefundMapper orderItemToRefundMapper;

    public RefundCreationRequestedQueueConsumer(
        IdempotencyChecker<RefundCreationRequested> idempotencyChecker,
        QueueConfig queueConfig,
        TaskPayloadTransformer<RefundCreationRequested> taskPayloadTransformer,
        EcomRefundService refundService,
        OrderItemToRefundMapper orderItemToRefundMapper,
       MessageValidator<RefundCreationRequested> messageValidator
    ) {
        super(queueConfig, taskPayloadTransformer, idempotencyChecker, messageValidator);
        this.refundService = refundService;
        this.orderItemToRefundMapper = orderItemToRefundMapper;
    }

    @Override
    protected String getMessageDetails(RefundCreationRequested message) {
        return MESSAGE_DETAILS_TEMPLATE
            .formatted(message.getOrderId(), message.getRefundCreationRequestedId(), message.getItemsToRefund());
    }

    @Override
    public void process(RefundCreationRequested message) {
        refundService.refund(
            message.getOrderId(),
            orderItemToRefundMapper.mapToOrderItemToRefundList(message.getItemsToRefund()),
            RefundOptions.builder()
                .chargeReturnFee(message.getChargeReturnFee())
                .refundShippingFee(message.getRefundShippingFee())
                .refundReason(RefundReason.RETURN)
                .build(),
            message.getRefundCreationRequestedId().toString()
        );
    }
}
