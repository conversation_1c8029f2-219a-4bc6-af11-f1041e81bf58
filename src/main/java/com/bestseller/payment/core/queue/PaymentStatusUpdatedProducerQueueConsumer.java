package com.bestseller.payment.core.queue;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducerQueueConsumer;
import com.bestseller.dbqueue.kafka.producer.queue.KafkaMessageProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class PaymentStatusUpdatedProducerQueueConsumer
    extends AbstractKafkaMessageProducerQueueConsumer<PaymentStatusUpdated> {

    private static final Logger LOGGER = LoggerFactory.getLogger(PaymentStatusUpdatedProducerQueueConsumer.class);
    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, status=%s";

    public PaymentStatusUpdatedProducerQueueConsumer(
        QueueConfig queueConfig,
        TaskPayloadTransformer<PaymentStatusUpdated> taskPayloadTransformer,
        KafkaMessageProducer<PaymentStatusUpdated> kafkaMessageProducer) {
        super(queueConfig, taskPayloadTransformer, LOGGER, kafkaMessageProducer);
    }

    @Override
    protected String getMessageDetails(PaymentStatusUpdated message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getPaymentState());
    }
}
