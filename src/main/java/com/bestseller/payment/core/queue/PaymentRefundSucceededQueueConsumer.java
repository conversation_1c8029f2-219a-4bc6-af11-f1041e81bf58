package com.bestseller.payment.core.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundSucceeded.PaymentRefundSucceeded;
import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;

public class PaymentRefundSucceededQueueConsumer extends AbstractValidatorQueueConsumer<PaymentRefundSucceeded> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "correlationId=%s";

    private final RefundService refundService;

    public PaymentRefundSucceededQueueConsumer(
        IdempotencyChecker<PaymentRefundSucceeded> idempotencyChecker,
        QueueConfig queueConfig,
        TaskPayloadTransformer<PaymentRefundSucceeded> taskPayloadTransformer,
        RefundService refundService,
        MessageValidator<PaymentRefundSucceeded> messageValidator
    ) {
        super(queueConfig, taskPayloadTransformer, idempotencyChecker, messageValidator);
        this.refundService = refundService;
    }

    @Override
    protected String getMessageDetails(PaymentRefundSucceeded message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getCorrelationId());
    }

    @Override
    public void process(PaymentRefundSucceeded message) {
        refundService.updateRefundStatus(Integer.parseInt(message.getCorrelationId()), RefundState.REFUND_SUCCESS);
    }

}
