package com.bestseller.payment.core.queue;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducerQueueConsumer;
import com.bestseller.dbqueue.kafka.producer.queue.KafkaMessageProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCompleted.RefundCompleted;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class RefundCompletedProducerQueueConsumer
    extends AbstractKafkaMessageProducerQueueConsumer<RefundCompleted> {

    private static final Logger LOGGER = LoggerFactory.getLogger(RefundCompletedProducerQueueConsumer.class);
    private static final String MESSAGE_DETAILS_TEMPLATE = "refundId=%s, refundStatus=%s";

    public RefundCompletedProducerQueueConsumer(
        QueueConfig queueConfig,
        TaskPayloadTransformer<RefundCompleted> taskPayloadTransformer,
        KafkaMessageProducer<RefundCompleted> kafkaMessageProducer) {
        super(queueConfig, taskPayloadTransformer, LOGGER, kafkaMessageProducer);
    }

    @Override
    protected String getMessageDetails(RefundCompleted message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getRefundId(), message.getRefundStatus());
    }
}
