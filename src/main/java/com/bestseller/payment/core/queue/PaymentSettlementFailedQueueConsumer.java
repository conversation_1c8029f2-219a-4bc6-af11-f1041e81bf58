package com.bestseller.payment.core.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementFailed.PaymentSettlementFailed;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.service.payment.PaymentService;

public class PaymentSettlementFailedQueueConsumer extends AbstractValidatorQueueConsumer<PaymentSettlementFailed> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "correlationId=%s";

    private final PaymentService paymentService;

    public PaymentSettlementFailedQueueConsumer(
        IdempotencyChecker<PaymentSettlementFailed> idempotencyChecker,
        QueueConfig queueConfig,
        TaskPayloadTransformer<PaymentSettlementFailed> taskPayloadTransformer,
        PaymentService paymentService,
        MessageValidator<PaymentSettlementFailed> messageValidator
    ) {
        super(queueConfig, taskPayloadTransformer, idempotencyChecker, messageValidator);
        this.paymentService = paymentService;
    }

    @Override
    protected String getMessageDetails(PaymentSettlementFailed message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getCorrelationId());
    }

    @Override
    public void process(PaymentSettlementFailed message) {
        paymentService.updatePaymentStatus(message.getCorrelationId(), PaymentState.SETTLEMENT_DENIED);
    }
}
