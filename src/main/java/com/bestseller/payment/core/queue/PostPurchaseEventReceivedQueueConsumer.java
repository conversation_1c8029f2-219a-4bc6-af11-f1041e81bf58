package com.bestseller.payment.core.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.postPurchaseEventReceived.PostPurchaseEventReceived;
import com.bestseller.interfacecontractannotator.model.postpurchaseeventreceived.ReturnCreatedPayload;
import com.bestseller.payment.core.service.customerchoice.CustomerRefundChoiceService;

public class PostPurchaseEventReceivedQueueConsumer extends AbstractValidatorQueueConsumer<PostPurchaseEventReceived> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, returnId=%s, refundMethod=%s";

    private final CustomerRefundChoiceService customerRefundChoiceService;

    public PostPurchaseEventReceivedQueueConsumer(
        IdempotencyChecker<PostPurchaseEventReceived> idempotency<PERSON>hecker,
        QueueConfig queueConfig,
        TaskPayloadTransformer<PostPurchaseEventReceived> taskPayloadTransformer,
        CustomerRefundChoiceService customerRefundChoiceService,
        MessageValidator<PostPurchaseEventReceived> messageValidator
    ) {
        super(queueConfig, taskPayloadTransformer, idempotencyChecker, messageValidator);
        this.customerRefundChoiceService = customerRefundChoiceService;
    }

    @Override
    protected String getMessageDetails(PostPurchaseEventReceived message) {
        ReturnCreatedPayload payload = (ReturnCreatedPayload) message.getData();
        return MESSAGE_DETAILS_TEMPLATE.formatted(
            message.getOrderId(),
            payload.getReturnId(),
            payload.getRefundMethod()
        );
    }

    @Override
    public void process(PostPurchaseEventReceived message) {
        customerRefundChoiceService.processPostPurchaseEvent(message);
    }
}
