package com.bestseller.payment.core.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentAuthorized.PaymentAuthorized;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.service.payment.PaymentService;

public class PaymentAuthorizedQueueConsumer extends AbstractValidatorQueueConsumer<PaymentAuthorized> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s";

    private final PaymentService paymentService;

    public PaymentAuthorizedQueueConsumer(
        IdempotencyChecker<PaymentAuthorized> idempotencyChecker,
        QueueConfig queueConfig,
        TaskPayloadTransformer<PaymentAuthorized> taskPayloadTransformer,
        PaymentService paymentService,
        MessageValidator<PaymentAuthorized> messageValidator
    ) {
        super(queueConfig, taskPayloadTransformer, idempotencyChecker, messageValidator);
        this.paymentService = paymentService;
    }

    @Override
    protected String getMessageDetails(PaymentAuthorized message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId());
    }

    @Override
    public void process(PaymentAuthorized message) {
        paymentService.updatePaymentStatus(message.getOrderId(), PaymentState.AUTHORISED);
    }

}
