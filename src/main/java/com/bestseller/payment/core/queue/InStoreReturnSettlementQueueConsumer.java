package com.bestseller.payment.core.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inStoreReturnSettlement.InStoreReturnSettlement;
import com.bestseller.payment.core.service.refund.InStoreRefundService;

public class InStoreReturnSettlementQueueConsumer extends AbstractValidatorQueueConsumer<InStoreReturnSettlement> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, refundId=%s, orderLines=%s";

    private final InStoreRefundService inStoreRefundService;

    public InStoreReturnSettlementQueueConsumer(
        IdempotencyChecker<InStoreReturnSettlement> idempotency<PERSON>hecker,
        QueueConfig queueConfig,
        TaskPayloadTransformer<InStoreReturnSettlement> taskPayloadTransformer,
        InStoreRefundService inStoreRefundService,
        MessageValidator<InStoreReturnSettlement> messageValidator
    ) {
        super(queueConfig, taskPayloadTransformer, idempotencyChecker, messageValidator);
        this.inStoreRefundService = inStoreRefundService;
    }

    @Override
    protected String getMessageDetails(InStoreReturnSettlement message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getRefundId(), message.getOrderLines());
    }

    @Override
    public void process(InStoreReturnSettlement inStoreReturnSettlement) {
        inStoreRefundService.issueRefundForInStoreReturn(inStoreReturnSettlement);
    }

}
