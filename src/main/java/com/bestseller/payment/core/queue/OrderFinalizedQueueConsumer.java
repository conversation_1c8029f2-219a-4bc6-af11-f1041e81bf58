package com.bestseller.payment.core.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderFinalized;
import com.bestseller.payment.core.service.order.OrderService;

public class OrderFinalizedQueueConsumer extends AbstractValidatorQueueConsumer<OrderFinalized> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, orderLines=%s";

    private final OrderService orderService;

    public OrderFinalizedQueueConsumer(
        IdempotencyChecker<OrderFinalized> idempotencyChecker,
        QueueConfig queueConfig,
        TaskPayloadTransformer<OrderFinalized> taskPayloadTransformer,
        OrderService orderService,
        MessageValidator<OrderFinalized> messageValidator
    ) {
        super(queueConfig, taskPayloadTransformer, idempotencyChecker, messageValidator);
        this.orderService = orderService;
    }

    @Override
    protected String getMessageDetails(OrderFinalized message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getOrderLines());
    }

    @Override
    public void process(OrderFinalized message) {
        orderService.processOrderFinalizedMessage(message);
    }

}
