package com.bestseller.payment.core.queue;

import com.bestseller.dbqueue.consumer.AbstractQueueConsumer;
import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class AbstractValidatorQueueConsumer<T> extends AbstractQueueConsumer<T> {

    private final MessageValidator<T> messageValidator;

    public AbstractValidatorQueueConsumer(
        QueueConfig queueConfig,
        TaskPayloadTransformer<T> taskPayloadTransformer,
        IdempotencyChecker<T> idempotencyChecker,
        MessageValidator<T> messageValidator) {
        super(idempotencyChecker, queueConfig, taskPayloadTransformer);
        this.messageValidator = messageValidator;
    }

    public abstract void process(T message);

    @Override
    protected void consume(T message) {
        if (messageValidator.isValid(message)) {
            process(message);
        }
    }
}
