package com.bestseller.payment.core.queue;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.kafka.producer.queue.AbstractKafkaMessageProducerQueueConsumer;
import com.bestseller.dbqueue.kafka.producer.queue.KafkaMessageProducer;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequest.PaymentRefundRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class PaymentRefundRequestProducerQueueConsumer
    extends AbstractKafkaMessageProducerQueueConsumer<PaymentRefundRequest> {

    private static final Logger LOGGER = LoggerFactory.getLogger(PaymentRefundRequestProducerQueueConsumer.class);
    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, correlationId=%s";

    public PaymentRefundRequestProducerQueueConsumer(
        QueueConfig queueConfig,
        TaskPayloadTransformer<PaymentRefundRequest> taskPayloadTransformer,
        KafkaMessageProducer<PaymentRefundRequest> kafkaMessageProducer) {
        super(queueConfig, taskPayloadTransformer, LOGGER, kafkaMessageProducer);
    }

    @Override
    protected String getMessageDetails(PaymentRefundRequest message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId(), message.getCorrelationId());
    }
}
