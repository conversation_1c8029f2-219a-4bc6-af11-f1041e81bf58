package com.bestseller.payment.core.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.payment.core.service.order.OrderService;

public class ValidOrderPlacedQueueConsumer extends AbstractValidatorQueueConsumer<ValidOrderPlaced> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s";

    private final OrderService orderService;

    public ValidOrderPlacedQueueConsumer(
        IdempotencyChecker<ValidOrderPlaced> idempotencyChecker,
        QueueConfig queueConfig,
        TaskPayloadTransformer<ValidOrderPlaced> taskPayloadTransformer,
        OrderService orderService,
        MessageValidator<ValidOrderPlaced> messageValidator
    ) {
        super(queueConfig, taskPayloadTransformer, idempotencyChecker, messageValidator);
        this.orderService = orderService;
    }

    @Override
    protected String getMessageDetails(ValidOrderPlaced message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getOrderId());
    }

    @Override
    public void process(ValidOrderPlaced message) {
        orderService.processValidOrderPlaced(message);
    }

}
