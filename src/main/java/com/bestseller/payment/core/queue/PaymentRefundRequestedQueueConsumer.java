package com.bestseller.payment.core.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequested.PaymentRefundRequested;
import com.bestseller.payment.core.service.refund.RefundService;
import com.logistics.statetransition.RefundState;

public class PaymentRefundRequestedQueueConsumer extends AbstractValidatorQueueConsumer<PaymentRefundRequested> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "correlationId=%s";

    private final RefundService refundService;

    public PaymentRefundRequestedQueueConsumer(
        IdempotencyChecker<PaymentRefundRequested> idempotencyChecker,
        QueueConfig queueConfig,
        TaskPayloadTransformer<PaymentRefundRequested> taskPayloadTransformer,
        RefundService refundService,
        MessageValidator<PaymentRefundRequested> messageValidator
    ) {
        super(queueConfig, taskPayloadTransformer, idempotencyChecker, messageValidator);
        this.refundService = refundService;
    }

    @Override
    protected String getMessageDetails(PaymentRefundRequested message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getCorrelationId());
    }

    @Override
    public void process(PaymentRefundRequested message) {
        refundService.updateRefundStatus(Integer.parseInt(message.getCorrelationId()), RefundState.REFUND_REQUESTED);
    }

}
