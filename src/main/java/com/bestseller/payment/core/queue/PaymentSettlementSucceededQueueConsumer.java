package com.bestseller.payment.core.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementSucceeded.PaymentSettlementSucceeded;
import com.bestseller.payment.core.domain.enumeration.PaymentState;
import com.bestseller.payment.core.service.payment.PaymentService;

public class PaymentSettlementSucceededQueueConsumer extends AbstractValidatorQueueConsumer<PaymentSettlementSucceeded> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "correlationId=%s";

    private final PaymentService paymentService;

    public PaymentSettlementSucceededQueueConsumer(
        IdempotencyChecker<PaymentSettlementSucceeded> idempotencyChecker,
        QueueConfig queueConfig,
        TaskPayloadTransformer<PaymentSettlementSucceeded> taskPayloadTransformer,
        PaymentService paymentService,
        MessageValidator<PaymentSettlementSucceeded> messageValidator
    ) {
        super(queueConfig, taskPayloadTransformer, idempotencyChecker, messageValidator);
        this.paymentService = paymentService;
    }

    @Override
    protected String getMessageDetails(PaymentSettlementSucceeded message) {
        return MESSAGE_DETAILS_TEMPLATE.formatted(message.getCorrelationId());
    }

    @Override
    public void process(PaymentSettlementSucceeded message) {
        paymentService.updatePaymentStatus(message.getCorrelationId(), PaymentState.SETTLED);
    }

}
