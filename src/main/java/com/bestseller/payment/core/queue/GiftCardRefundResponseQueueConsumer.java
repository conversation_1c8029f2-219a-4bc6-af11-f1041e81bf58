package com.bestseller.payment.core.queue;

import com.bestseller.dbqueue.consumer.AbstractQueueConsumer;
import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.giftCardRefundResponse.GiftCardRefundResponse;
import com.bestseller.payment.core.service.refund.RefundService;

public class GiftCardRefundResponseQueueConsumer extends AbstractQueueConsumer<GiftCardRefundResponse> {

    private static final String MESSAGE_DETAILS_TEMPLATE = "orderId=%s, correlationId=%s, status=%s";

    private final RefundService refundService;

    public GiftCardRefundResponseQueueConsumer(
        IdempotencyChecker<GiftCardRefundResponse> idempotencyChecker,
        QueueConfig queueConfig,
        TaskPayloadTransformer<GiftCardRefundResponse> taskPayloadTransformer,
        RefundService refundService
    ) {
        super(idempotencyChecker, queueConfig, taskPayloadTransformer);
        this.refundService = refundService;
    }

    @Override
    protected String getMessageDetails(GiftCardRefundResponse message) {
        return MESSAGE_DETAILS_TEMPLATE
            .formatted(message.getOrderId(), message.getCorrelationId(), message.getStatus());
    }

    @Override
    protected void consume(GiftCardRefundResponse message) {
        refundService.processGiftCardRefundResponse(message);
    }
}
