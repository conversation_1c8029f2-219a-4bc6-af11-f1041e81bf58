package com.bestseller.payment.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Duration;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ServiceCredential {
    private String url;
    private String username;
    private String password;
    private boolean auth;
    private long maxAttempts;
    private Duration backoffDuration;
}
