package com.bestseller.payment.config;

import com.logistics.statetransition.RefundState;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter(autoApply = true)
public class RefundStateConverter implements AttributeConverter<RefundState, Integer> {

    @Override
    public Integer convertToDatabaseColumn(RefundState state) {
        if (state == null) {
            return null;
        }
        return state.getIdentifier();
    }

    @Override
    public RefundState convertToEntityAttribute(Integer identifier) {
        if (identifier == null) {
            return null;
        }
        return RefundState.fromIdentifier(identifier);
    }
}
