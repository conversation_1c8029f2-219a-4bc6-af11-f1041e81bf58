package com.bestseller.payment.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Clock;
import java.time.ZoneId;

/**
 * Main app configuration.
 */
@Configuration
public class AppConfig {
    /**
     * Clock instance to use across the application.
     *
     * @return clock instance
     */
    @Bean
    public Clock cetClock() {
        return Clock.system(ZoneId.of("Europe/Amsterdam"));
    }
}
