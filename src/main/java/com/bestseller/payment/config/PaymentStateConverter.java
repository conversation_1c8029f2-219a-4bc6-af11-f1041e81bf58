package com.bestseller.payment.config;

import com.bestseller.payment.core.domain.enumeration.PaymentState;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter(autoApply = true)
public class PaymentStateConverter implements AttributeConverter<PaymentState, Integer> {

    @Override
    public Integer convertToDatabaseColumn(PaymentState state) {
        if (state == null) {
            return null;
        }
        return state.getIdentifier();
    }

    @Override
    public PaymentState convertToEntityAttribute(Integer indentifier) {
        if (indentifier == null) {
            return null;
        }
        return PaymentState.fromIdentifier(indentifier);
    }
}
