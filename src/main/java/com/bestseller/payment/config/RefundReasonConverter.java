package com.bestseller.payment.config;

import com.bestseller.payment.core.domain.enumeration.ChargedRefundReason;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter(autoApply = true)
public class RefundReasonConverter implements AttributeConverter<ChargedRefundReason, Integer> {

    @Override
    public Integer convertToDatabaseColumn(ChargedRefundReason reason) {
        if (reason == null) {
            return null;
        }
        return reason.getIdentifier();
    }

    @Override
    public ChargedRefundReason convertToEntityAttribute(Integer identifier) {
        if (identifier == null) {
            return null;
        }
        return ChargedRefundReason.fromIdentifier(identifier);
    }
}
