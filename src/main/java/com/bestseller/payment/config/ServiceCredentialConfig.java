package com.bestseller.payment.config;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;

@Getter
@Setter
@ConfigurationProperties("gateway")
@AllArgsConstructor
public class ServiceCredentialConfig {

    private static final String FULFILLMENT_CORE_SERVICE = "fulfilment-core-service";

    private Map<String, ServiceCredential> services;

    public ServiceCredential getFulfillmentCoreServiceCredential() {
        return services.get(FULFILLMENT_CORE_SERVICE);
    }
}
