package com.bestseller.payment.config.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementFailed.PaymentSettlementFailed;
import com.bestseller.payment.core.queue.PaymentSettlementFailedQueueConsumer;
import com.bestseller.payment.core.service.payment.PaymentService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@SuppressWarnings("MissingJavadocMethod")
@Configuration
public class PaymentSettlementFailedQueueConfiguration {

    @Bean
    public QueueConfig paymentSettlementFailedQueueConfig(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        return QueueConfigUtils
            .getQueueConfig(databaseQueueConfigurationProperties.paymentSettlementFailedQueueProperties());
    }

    @Bean
    public TaskPayloadTransformer<PaymentSettlementFailed> paymentSettlementFailedTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, PaymentSettlementFailed.class);
    }

    @Bean
    public QueueProducer<PaymentSettlementFailed> paymentSettlementFailedQueueProducer(
        QueueConfig paymentSettlementFailedQueueConfig,
        TaskPayloadTransformer<PaymentSettlementFailed> paymentSettlementFailedTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            paymentSettlementFailedQueueConfig,
            paymentSettlementFailedTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public PaymentSettlementFailedQueueConsumer paymentSettlementFailedQueueConsumer(
        QueueConfig paymentSettlementFailedQueueConfig,
        TaskPayloadTransformer<PaymentSettlementFailed> paymentSettlementFailedTaskPayloadTransformer,
        PaymentService paymentService,
        IdempotencyChecker<PaymentSettlementFailed> idempotencyCheck,
        MessageValidator<PaymentSettlementFailed> messageValidator) {
        return new PaymentSettlementFailedQueueConsumer(
            idempotencyCheck,
            paymentSettlementFailedQueueConfig,
            paymentSettlementFailedTaskPayloadTransformer,
            paymentService,
            messageValidator
        );
    }
}
