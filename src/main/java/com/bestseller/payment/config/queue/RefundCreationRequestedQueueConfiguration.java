package com.bestseller.payment.config.queue;

import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCreationRequested.RefundCreationRequested;
import com.bestseller.payment.adapter.stream.validator.idempotency.RefundCreationRequestedIdempotencyCheck;
import com.bestseller.payment.core.converter.OrderItemToRefundMapper;
import com.bestseller.payment.core.queue.RefundCreationRequestedQueueConsumer;
import com.bestseller.payment.core.service.refund.EcomRefundService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@SuppressWarnings("MissingJavadocMethod")
@Configuration
public class RefundCreationRequestedQueueConfiguration {
    private final QueueProperties queueProperties;

    public RefundCreationRequestedQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.refundCreationRequestedQueueProperties();
    }

    @Bean
    public QueueConfig refundCreationRequestedQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<RefundCreationRequested> refundCreationRequestedTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, RefundCreationRequested.class);
    }

    @Bean
    public QueueProducer<RefundCreationRequested> refundCreationRequestQueueProducer(
        QueueConfig refundCreationRequestedQueueConfig,
        TaskPayloadTransformer<RefundCreationRequested> refundCreationRequestedTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            refundCreationRequestedQueueConfig,
            refundCreationRequestedTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public RefundCreationRequestedQueueConsumer refundCreationRequestedQueueConsumer(
        QueueConfig refundCreationRequestedQueueConfig,
        TaskPayloadTransformer<RefundCreationRequested> refundCreationRequestedTaskPayloadTransformer,
        RefundCreationRequestedIdempotencyCheck idempotencyChecker,
        EcomRefundService refundService,
        OrderItemToRefundMapper orderItemToRefundMapper,
        MessageValidator<RefundCreationRequested> messageValidator) {
        return new RefundCreationRequestedQueueConsumer(
            idempotencyChecker,
            refundCreationRequestedQueueConfig,
            refundCreationRequestedTaskPayloadTransformer,
            refundService,
            orderItemToRefundMapper,
            messageValidator
        );
    }
}
