package com.bestseller.payment.config.queue;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentStatusUpdated.PaymentStatusUpdated;
import com.bestseller.payment.adapter.stream.producer.PaymentStatusUpdatedProducer;
import com.bestseller.payment.core.queue.PaymentStatusUpdatedProducerQueueConsumer;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class PaymentStatusUpdatedProducerQueueConfiguration {

    private final QueueProperties queueProperties;

    public PaymentStatusUpdatedProducerQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.paymentStatusUpdatedProducerQueueProperties();
    }

    @Bean
    public QueueConfig paymentStatusUpdatedProducerQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<PaymentStatusUpdated> paymentStatusUpdatedTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, PaymentStatusUpdated.class);
    }

    @Bean
    public QueueProducer<PaymentStatusUpdated> paymentStatusUpdatedProducerQueueProducer(
        QueueConfig paymentStatusUpdatedProducerQueueConfig,
        TaskPayloadTransformer<PaymentStatusUpdated> paymentStatusUpdatedTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            paymentStatusUpdatedProducerQueueConfig,
            paymentStatusUpdatedTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public PaymentStatusUpdatedProducerQueueConsumer paymentStatusUpdatedProducerQueueConsumer(
        QueueConfig paymentStatusUpdatedProducerQueueConfig,
        TaskPayloadTransformer<PaymentStatusUpdated> paymentStatusUpdatedTaskPayloadTransformer,
        PaymentStatusUpdatedProducer paymentStatusUpdatedProducer) {
        return new PaymentStatusUpdatedProducerQueueConsumer(
            paymentStatusUpdatedProducerQueueConfig,
            paymentStatusUpdatedTaskPayloadTransformer,
            paymentStatusUpdatedProducer
        );
    }

}
