package com.bestseller.payment.config.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementSucceeded.PaymentSettlementSucceeded;
import com.bestseller.payment.core.queue.PaymentSettlementSucceededQueueConsumer;
import com.bestseller.payment.core.service.payment.PaymentService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@SuppressWarnings("MissingJavadocMethod")
@Configuration
public class PaymentSettlementSucceededQueueConfiguration {

    @Bean
    public QueueConfig paymentSettlementSucceededQueueConfig(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        return QueueConfigUtils
            .getQueueConfig(databaseQueueConfigurationProperties.paymentSettlementSucceededQueueProperties());
    }

    @Bean
    public TaskPayloadTransformer<PaymentSettlementSucceeded> paymentSettlementSucceededTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, PaymentSettlementSucceeded.class);
    }

    @Bean
    public QueueProducer<PaymentSettlementSucceeded> paymentSettlementSucceededQueueProducer(
        QueueConfig paymentSettlementSucceededQueueConfig,
        TaskPayloadTransformer<PaymentSettlementSucceeded> paymentSettlementSucceededTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            paymentSettlementSucceededQueueConfig,
            paymentSettlementSucceededTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public PaymentSettlementSucceededQueueConsumer paymentSettlementSucceededQueueConsumer(
        QueueConfig paymentSettlementSucceededQueueConfig,
        TaskPayloadTransformer<PaymentSettlementSucceeded> paymentSettlementSucceededTaskPayloadTransformer,
        PaymentService paymentService,
        IdempotencyChecker<PaymentSettlementSucceeded> idempotencyCheck,
        MessageValidator<PaymentSettlementSucceeded> messageValidator) {
        return new PaymentSettlementSucceededQueueConsumer(
            idempotencyCheck,
            paymentSettlementSucceededQueueConfig,
            paymentSettlementSucceededTaskPayloadTransformer,
            paymentService,
            messageValidator
        );
    }
}
