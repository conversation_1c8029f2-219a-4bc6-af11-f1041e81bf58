package com.bestseller.payment.config.queue;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundCompleted.RefundCompleted;
import com.bestseller.payment.adapter.stream.producer.RefundCompletedProducer;
import com.bestseller.payment.core.queue.RefundCompletedProducerQueueConsumer;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RefundCompletedProducerQueueConfiguration {

    private final QueueProperties queueProperties;

    public RefundCompletedProducerQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.refundCompletedProducerQueueProperties();
    }

    @Bean
    public QueueConfig refundCompletedProducerQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<RefundCompleted> refundCompletedTaskPayloadTransformer(ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, RefundCompleted.class);
    }

    @Bean
    public QueueProducer<RefundCompleted> refundCompletedProducerQueueProducer(
        QueueConfig refundCompletedProducerQueueConfig,
        TaskPayloadTransformer<RefundCompleted> refundCompletedTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            refundCompletedProducerQueueConfig,
            refundCompletedTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public RefundCompletedProducerQueueConsumer refundCompletedProducerQueueConsumer(
        QueueConfig refundCompletedProducerQueueConfig,
        TaskPayloadTransformer<RefundCompleted> refundCompletedTaskPayloadTransformer,
        RefundCompletedProducer refundCompletedProducer) {
        return new RefundCompletedProducerQueueConsumer(
            refundCompletedProducerQueueConfig,
            refundCompletedTaskPayloadTransformer,
            refundCompletedProducer
        );
    }

}
