package com.bestseller.payment.config.queue;

import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.inStoreReturnSettlement.InStoreReturnSettlement;
import com.bestseller.payment.adapter.stream.validator.idempotency.InStoreReturnSettlementIdempotencyCheck;
import com.bestseller.payment.core.queue.InStoreReturnSettlementQueueConsumer;
import com.bestseller.payment.core.service.refund.InStoreRefundService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@SuppressWarnings("MissingJavadocMethod")
@Configuration
public class InStoreReturnSettlementQueueConfiguration {
    private final QueueProperties queueProperties;

    public InStoreReturnSettlementQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.inStoreReturnSettlementQueueProperties();
    }

    @Bean
    public QueueConfig inStoreReturnSettlementQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<InStoreReturnSettlement> inStoreReturnSettlementTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, InStoreReturnSettlement.class);
    }

    @Bean
    public QueueProducer<InStoreReturnSettlement> inStoreReturnSettlementQueueProducer(
        QueueConfig inStoreReturnSettlementQueueConfig,
        TaskPayloadTransformer<InStoreReturnSettlement> inStoreReturnSettlementTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            inStoreReturnSettlementQueueConfig,
            inStoreReturnSettlementTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public InStoreReturnSettlementQueueConsumer inStoreReturnSettlementQueueConsumer(
        QueueConfig inStoreReturnSettlementQueueConfig,
        TaskPayloadTransformer<InStoreReturnSettlement> inStoreReturnSettlementTaskPayloadTransformer,
        InStoreRefundService inStoreRefundService,
        InStoreReturnSettlementIdempotencyCheck idempotencyCheck,
        MessageValidator<InStoreReturnSettlement> messageValidator
    ) {
        return new InStoreReturnSettlementQueueConsumer(
            idempotencyCheck,
            inStoreReturnSettlementQueueConfig,
            inStoreReturnSettlementTaskPayloadTransformer,
            inStoreRefundService,
            messageValidator
        );
    }
}
