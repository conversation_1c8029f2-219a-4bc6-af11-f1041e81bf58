package com.bestseller.payment.config.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.validOrderPlaced.ValidOrderPlaced;
import com.bestseller.payment.core.queue.ValidOrderPlacedQueueConsumer;
import com.bestseller.payment.core.service.order.OrderService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@SuppressWarnings("MissingJavadocMethod")
@Configuration
public class ValidOrderPlacedQueueConfiguration {

    @Bean
    public QueueConfig validOrderPlacedQueueConfig(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        return QueueConfigUtils.getQueueConfig(databaseQueueConfigurationProperties.validOrderPlacedQueueProperties());
    }

    @Bean
    public TaskPayloadTransformer<ValidOrderPlaced> validOrderPlacedTaskPayloadTransformer(ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, ValidOrderPlaced.class);
    }

    @Bean
    public QueueProducer<ValidOrderPlaced> validOrderPlacedQueueProducer(
        QueueConfig validOrderPlacedQueueConfig,
        TaskPayloadTransformer<ValidOrderPlaced> validOrderPlacedTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            validOrderPlacedQueueConfig,
            validOrderPlacedTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public ValidOrderPlacedQueueConsumer validOrderPlacedQueueConsumer(
        QueueConfig validOrderPlacedQueueConfig,
        TaskPayloadTransformer<ValidOrderPlaced> validOrderPlacedTaskPayloadTransformer,
        OrderService orderService,
        IdempotencyChecker<ValidOrderPlaced> idempotencyCheck,
        MessageValidator<ValidOrderPlaced> messageValidator) {
        return new ValidOrderPlacedQueueConsumer(
            idempotencyCheck,
            validOrderPlacedQueueConfig,
            validOrderPlacedTaskPayloadTransformer,
            orderService,
            messageValidator
        );
    }
}
