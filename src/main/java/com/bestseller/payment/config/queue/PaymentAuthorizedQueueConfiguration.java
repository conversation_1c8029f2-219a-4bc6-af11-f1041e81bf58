package com.bestseller.payment.config.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentAuthorized.PaymentAuthorized;
import com.bestseller.payment.core.queue.PaymentAuthorizedQueueConsumer;
import com.bestseller.payment.core.service.payment.PaymentService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@SuppressWarnings("MissingJavadocMethod")
@Configuration
public class PaymentAuthorizedQueueConfiguration {

    @Bean
    public QueueConfig paymentAuthorizedQueueConfig(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        return QueueConfigUtils.getQueueConfig(databaseQueueConfigurationProperties.paymentAuthorizedQueueProperties());
    }

    @Bean
    public TaskPayloadTransformer<PaymentAuthorized> paymentAuthorizedTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, PaymentAuthorized.class);
    }

    @Bean
    public QueueProducer<PaymentAuthorized> paymentAuthorizedQueueProducer(
        QueueConfig paymentAuthorizedQueueConfig,
        TaskPayloadTransformer<PaymentAuthorized> paymentAuthorizedTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            paymentAuthorizedQueueConfig,
            paymentAuthorizedTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public PaymentAuthorizedQueueConsumer paymentAuthorizedQueueConsumer(
        QueueConfig paymentAuthorizedQueueConfig,
        TaskPayloadTransformer<PaymentAuthorized> paymentAuthorizedTaskPayloadTransformer,
        PaymentService paymentService,
        IdempotencyChecker<PaymentAuthorized> idempotencyCheck,
        MessageValidator<PaymentAuthorized> messageValidator) {
        return new PaymentAuthorizedQueueConsumer(
            idempotencyCheck,
            paymentAuthorizedQueueConfig,
            paymentAuthorizedTaskPayloadTransformer,
            paymentService,
            messageValidator
        );
    }
}
