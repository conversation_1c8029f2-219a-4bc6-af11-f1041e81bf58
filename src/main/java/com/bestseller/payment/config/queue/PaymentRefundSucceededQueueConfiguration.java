package com.bestseller.payment.config.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundSucceeded.PaymentRefundSucceeded;
import com.bestseller.payment.core.queue.PaymentRefundSucceededQueueConsumer;
import com.bestseller.payment.core.service.refund.RefundService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@SuppressWarnings("MissingJavadocMethod")
@Configuration
public class PaymentRefundSucceededQueueConfiguration {

    @Bean
    public QueueConfig paymentRefundSucceededQueueConfig(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        return QueueConfigUtils
            .getQueueConfig(databaseQueueConfigurationProperties.paymentRefundSucceededQueueProperties());
    }

    @Bean
    public TaskPayloadTransformer<PaymentRefundSucceeded> paymentRefundSucceededTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, PaymentRefundSucceeded.class);
    }

    @Bean
    public QueueProducer<PaymentRefundSucceeded> paymentRefundSucceededQueueProducer(
        QueueConfig paymentRefundSucceededQueueConfig,
        TaskPayloadTransformer<PaymentRefundSucceeded> paymentRefundSucceededTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            paymentRefundSucceededQueueConfig,
            paymentRefundSucceededTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public PaymentRefundSucceededQueueConsumer paymentRefundSucceededQueueConsumer(
        QueueConfig paymentRefundSucceededQueueConfig,
        TaskPayloadTransformer<PaymentRefundSucceeded> paymentRefundSucceededTaskPayloadTransformer,
        RefundService refundService,
        IdempotencyChecker<PaymentRefundSucceeded> idempotencyCheck,
        MessageValidator<PaymentRefundSucceeded> messageValidator) {
        return new PaymentRefundSucceededQueueConsumer(
            idempotencyCheck,
            paymentRefundSucceededQueueConfig,
            paymentRefundSucceededTaskPayloadTransformer,
            refundService,
            messageValidator
        );
    }
}
