package com.bestseller.payment.config.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundFailed.PaymentRefundFailed;
import com.bestseller.payment.core.queue.PaymentRefundFailedQueueConsumer;
import com.bestseller.payment.core.service.refund.RefundService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@SuppressWarnings("MissingJavadocMethod")
@Configuration
public class PaymentRefundFailedQueueConfiguration {

    @Bean
    public QueueConfig paymentRefundFailedQueueConfig(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        return QueueConfigUtils
            .getQueueConfig(databaseQueueConfigurationProperties.paymentRefundFailedQueueProperties());
    }

    @Bean
    public TaskPayloadTransformer<PaymentRefundFailed> paymentRefundFailedTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, PaymentRefundFailed.class);
    }

    @Bean
    public QueueProducer<PaymentRefundFailed> paymentRefundFailedQueueProducer(
        QueueConfig paymentRefundFailedQueueConfig,
        TaskPayloadTransformer<PaymentRefundFailed> paymentRefundFailedTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            paymentRefundFailedQueueConfig,
            paymentRefundFailedTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public PaymentRefundFailedQueueConsumer paymentRefundFailedQueueConsumer(
        QueueConfig paymentRefundFailedQueueConfig,
        TaskPayloadTransformer<PaymentRefundFailed> paymentRefundFailedTaskPayloadTransformer,
        RefundService refundService,
        IdempotencyChecker<PaymentRefundFailed> idempotencyCheck,
        MessageValidator<PaymentRefundFailed> messageValidator) {
        return new PaymentRefundFailedQueueConsumer(
            idempotencyCheck,
            paymentRefundFailedQueueConfig,
            paymentRefundFailedTaskPayloadTransformer,
            refundService,
            messageValidator
        );
    }
}
