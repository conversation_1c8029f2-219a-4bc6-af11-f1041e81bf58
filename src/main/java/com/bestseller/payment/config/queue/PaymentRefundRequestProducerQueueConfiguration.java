package com.bestseller.payment.config.queue;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequest.PaymentRefundRequest;
import com.bestseller.payment.adapter.stream.producer.PaymentRefundRequestProducer;
import com.bestseller.payment.core.queue.PaymentRefundRequestProducerQueueConsumer;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class PaymentRefundRequestProducerQueueConfiguration {

    private final QueueProperties queueProperties;

    public PaymentRefundRequestProducerQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.paymentRefundRequestProducerQueueProperties();
    }

    @Bean
    public QueueConfig paymentRefundRequestProducerQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<PaymentRefundRequest> paymentRefundRequestTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, PaymentRefundRequest.class);
    }

    @Bean
    public QueueProducer<PaymentRefundRequest> paymentRefundRequestProducerQueueProducer(
        QueueConfig paymentRefundRequestProducerQueueConfig,
        TaskPayloadTransformer<PaymentRefundRequest> paymentRefundRequestTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            paymentRefundRequestProducerQueueConfig,
            paymentRefundRequestTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public PaymentRefundRequestProducerQueueConsumer paymentRefundRequestProducerQueueConsumer(
        QueueConfig paymentRefundRequestProducerQueueConfig,
        TaskPayloadTransformer<PaymentRefundRequest> paymentRefundRequestTaskPayloadTransformer,
        PaymentRefundRequestProducer paymentRefundRequestProducer) {
        return new PaymentRefundRequestProducerQueueConsumer(
            paymentRefundRequestProducerQueueConfig,
            paymentRefundRequestTaskPayloadTransformer,
            paymentRefundRequestProducer
        );
    }

}
