package com.bestseller.payment.config.queue.paymentsettlementrequestproducer;

import com.bestseller.dbqueue.core.api.QueueShardRouter;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementRequest.PaymentSettlementRequest;
import com.bestseller.payment.adapter.stream.messagegenerator.KlarnaPaymentSettlementRequestGenerator;
import com.bestseller.payment.core.domain.payment.KlarnaPayment;

public class KlarnaPaymentSettlementRequestProducerQueueProducer
    extends AbstractPaymentSettlementRequestProducerQueueProducer<KlarnaPayment> {

    public KlarnaPaymentSettlementRequestProducerQueueProducer(
        QueueConfig queueConfig,
        TaskPayloadTransformer<PaymentSettlementRequest> payloadTransformer,
        QueueShardRouter<PaymentSettlementRequest, DatabaseAccessLayer> queueShardRouter,
        KlarnaPaymentSettlementRequestGenerator klarnaPaymentSettlementRequestGenerator) {
        super(
            queueConfig,
            payloadTransformer,
            queueShardRouter,
            KlarnaPayment.class,
            klarnaPaymentSettlementRequestGenerator
        );
    }
}
