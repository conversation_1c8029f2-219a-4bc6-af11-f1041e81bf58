package com.bestseller.payment.config.queue.paymentsettlementrequestproducer;

import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementRequest.PaymentSettlementRequest;
import com.bestseller.payment.adapter.stream.messagegenerator.AdyenPaymentSettlementRequestGenerator;
import com.bestseller.payment.adapter.stream.messagegenerator.KlarnaPaymentSettlementRequestGenerator;
import com.bestseller.payment.adapter.stream.producer.PaymentSettlementRequestProducer;
import com.bestseller.payment.config.queue.DatabaseQueueConfigurationProperties;
import com.bestseller.payment.core.queue.PaymentSettlementRequestProducerQueueConsumer;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class PaymentSettlementRequestProducerQueueConfiguration {

    private final QueueProperties queueProperties;

    public PaymentSettlementRequestProducerQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.paymentSettlementRequestProducerQueueProperties();
    }

    @Bean
    public QueueConfig paymentSettlementRequestProducerQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<PaymentSettlementRequest> paymentSettlementRequestTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, PaymentSettlementRequest.class);
    }

    @Bean
    public AdyenPaymentSettlementRequestProducerQueueProducer adyenPaymentSettlementRequestProducerQueueProducer(
        QueueConfig paymentSettlementRequestProducerQueueConfig,
        TaskPayloadTransformer<PaymentSettlementRequest> paymentSettlementRequestTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard,
        AdyenPaymentSettlementRequestGenerator adyenPaymentSettlementRequestGenerator) {
        return new AdyenPaymentSettlementRequestProducerQueueProducer(
            paymentSettlementRequestProducerQueueConfig,
            paymentSettlementRequestTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard),
            adyenPaymentSettlementRequestGenerator
        );
    }

    @Bean
    public KlarnaPaymentSettlementRequestProducerQueueProducer klarnaPaymentSettlementRequestProducerQueueProducer(
        QueueConfig paymentSettlementRequestProducerQueueConfig,
        TaskPayloadTransformer<PaymentSettlementRequest> paymentSettlementRequestTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard,
        KlarnaPaymentSettlementRequestGenerator klarnaPaymentSettlementRequestGenerator) {
        return new KlarnaPaymentSettlementRequestProducerQueueProducer(
            paymentSettlementRequestProducerQueueConfig,
            paymentSettlementRequestTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard),
            klarnaPaymentSettlementRequestGenerator
        );
    }

    @Bean
    public PaymentSettlementRequestProducerQueueConsumer paymentSettlementRequestProducerQueueConsumer(
        QueueConfig paymentSettlementRequestProducerQueueConfig,
        TaskPayloadTransformer<PaymentSettlementRequest> paymentSettlementRequestTaskPayloadTransformer,
        PaymentSettlementRequestProducer paymentSettlementRequestProducer) {
        return new PaymentSettlementRequestProducerQueueConsumer(
            paymentSettlementRequestProducerQueueConfig,
            paymentSettlementRequestTaskPayloadTransformer,
            paymentSettlementRequestProducer
        );
    }

}
