package com.bestseller.payment.config.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.orderFinalized.OrderFinalized;
import com.bestseller.payment.core.queue.OrderFinalizedQueueConsumer;
import com.bestseller.payment.core.service.order.OrderService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@SuppressWarnings("MissingJavadocMethod")
@Configuration
public class OrderFinalizedQueueConfiguration {

    @Bean
    public QueueConfig orderFinalizedQueueConfig(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        return QueueConfigUtils.getQueueConfig(databaseQueueConfigurationProperties.orderFinalizedQueueProperties());
    }

    @Bean
    public TaskPayloadTransformer<OrderFinalized> orderFinalizedTaskPayloadTransformer(ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, OrderFinalized.class);
    }

    @Bean
    public QueueProducer<OrderFinalized> orderFinalizedQueueProducer(
        QueueConfig orderFinalizedQueueConfig,
        TaskPayloadTransformer<OrderFinalized> orderFinalizedTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            orderFinalizedQueueConfig,
            orderFinalizedTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public OrderFinalizedQueueConsumer orderFinalizedQueueConsumer(
        QueueConfig orderFinalizedQueueConfig,
        TaskPayloadTransformer<OrderFinalized> orderFinalizedTaskPayloadTransformer,
        OrderService orderService,
        IdempotencyChecker<OrderFinalized> idempotencyCheck,
        MessageValidator<OrderFinalized> messageValidator) {
        return new OrderFinalizedQueueConsumer(
            idempotencyCheck,
            orderFinalizedQueueConfig,
            orderFinalizedTaskPayloadTransformer,
            orderService,
            messageValidator
        );
    }
}
