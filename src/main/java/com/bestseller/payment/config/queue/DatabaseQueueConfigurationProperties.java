package com.bestseller.payment.config.queue;

import com.bestseller.dbqueue.spring.QueueProperties;
import jakarta.validation.Valid;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

@ConfigurationProperties(prefix = "database-queue")
public record DatabaseQueueConfigurationProperties(
    @Valid @NestedConfigurationProperty QueueProperties refundCreationRequestedQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties inStoreReturnSettlementQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties paymentRefundRequestProducerQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties paymentSettlementRequestProducerQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties paymentStatusUpdatedProducerQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties refundCompletedProducerQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties refundStatusUpdatedProducerQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties orderFinalizedQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties paymentAuthorizedQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties paymentRefundFailedQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties paymentRefundRequestedQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties paymentRefundSucceededQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties paymentRejectedQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties paymentSettlementFailedQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties paymentSettlementSucceededQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties validOrderPlacedQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties giftCardRefundResponseQueueProperties,
    @Valid @NestedConfigurationProperty QueueProperties postPurchaseEventReceivedQueueProperties
) {

}

