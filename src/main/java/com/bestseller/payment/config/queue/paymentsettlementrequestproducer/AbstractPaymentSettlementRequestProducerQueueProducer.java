package com.bestseller.payment.config.queue.paymentsettlementrequestproducer;

import com.bestseller.dbqueue.core.api.EnqueueParams;
import com.bestseller.dbqueue.core.api.QueueShardRouter;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementRequest.PaymentSettlementRequest;
import com.bestseller.payment.adapter.stream.messagegenerator.PaymentSettlementRequestGenerator;
import com.bestseller.payment.core.domain.Order;
import com.bestseller.payment.core.domain.PaymentInfo;
import com.bestseller.payment.core.utils.PaymentUtils;

public abstract class AbstractPaymentSettlementRequestProducerQueueProducer<T>
    extends ShardingQueueProducer<PaymentSettlementRequest, DatabaseAccessLayer>
    implements PaymentSettlementSupport {

    private final Class<T> paymentType;

    private final PaymentSettlementRequestGenerator paymentSettlementRequestGenerator;

    public AbstractPaymentSettlementRequestProducerQueueProducer(
        QueueConfig queueConfig,
        TaskPayloadTransformer<PaymentSettlementRequest> payloadTransformer,
        QueueShardRouter<PaymentSettlementRequest, DatabaseAccessLayer> queueShardRouter,
        Class<T> paymentType,
        PaymentSettlementRequestGenerator paymentSettlementRequestGenerator) {
        super(queueConfig, payloadTransformer, queueShardRouter);
        this.paymentType = paymentType;
        this.paymentSettlementRequestGenerator = paymentSettlementRequestGenerator;
    }

    @Override
    public boolean supports(Order order) {
        PaymentInfo noneGiftCardPayment = PaymentUtils.getNoneGiftCardPayment(order);
        return noneGiftCardPayment.getClass().isAssignableFrom(paymentType);
    }

    public PaymentSettlementRequest enqueue(Order order) {
        var paymentSettlementRequest = paymentSettlementRequestGenerator.generate(order);
        super.enqueue(EnqueueParams.create(paymentSettlementRequest));
        return paymentSettlementRequest;
    }
}
