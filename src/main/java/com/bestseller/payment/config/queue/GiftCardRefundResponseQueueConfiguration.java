package com.bestseller.payment.config.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.giftCardRefundResponse.GiftCardRefundResponse;
import com.bestseller.payment.core.queue.GiftCardRefundResponseQueueConsumer;
import com.bestseller.payment.core.service.refund.RefundService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@SuppressWarnings("MissingJavadocMethod")
@Configuration
public class GiftCardRefundResponseQueueConfiguration {

    @Bean
    public QueueConfig giftCardRefundResponseQueueConfig(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        return QueueConfigUtils
            .getQueueConfig(databaseQueueConfigurationProperties.giftCardRefundResponseQueueProperties());
    }

    @Bean
    public TaskPayloadTransformer<GiftCardRefundResponse> giftCardRefundResponseTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, GiftCardRefundResponse.class);
    }

    @Bean
    public QueueProducer<GiftCardRefundResponse> giftCardRefundResponseQueueProducer(
        QueueConfig giftCardRefundResponseQueueConfig,
        TaskPayloadTransformer<GiftCardRefundResponse> giftCardRefundResponseTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            giftCardRefundResponseQueueConfig,
            giftCardRefundResponseTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public GiftCardRefundResponseQueueConsumer giftCardRefundResponseQueueConsumer(
        QueueConfig giftCardRefundResponseQueueConfig,
        TaskPayloadTransformer<GiftCardRefundResponse> giftCardRefundResponseTaskPayloadTransformer,
        RefundService refundService,
        IdempotencyChecker<GiftCardRefundResponse> idempotencyCheck) {
        return new GiftCardRefundResponseQueueConsumer(
            idempotencyCheck,
            giftCardRefundResponseQueueConfig,
            giftCardRefundResponseTaskPayloadTransformer,
            refundService
        );
    }
}
