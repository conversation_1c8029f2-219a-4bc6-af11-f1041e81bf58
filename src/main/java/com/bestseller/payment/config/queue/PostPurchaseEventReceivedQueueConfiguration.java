package com.bestseller.payment.config.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.postPurchaseEventReceived.PostPurchaseEventReceived;
import com.bestseller.payment.core.queue.PostPurchaseEventReceivedQueueConsumer;
import com.bestseller.payment.core.service.customerchoice.CustomerRefundChoiceService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class PostPurchaseEventReceivedQueueConfiguration {

    @Bean
    public QueueConfig postPurchaseEventReceivedQueueConfig(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        return QueueConfigUtils.getQueueConfig(databaseQueueConfigurationProperties.postPurchaseEventReceivedQueueProperties());
    }

    @Bean
    public TaskPayloadTransformer<PostPurchaseEventReceived> postPurchaseEventReceivedTaskPayloadTransformer(ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, PostPurchaseEventReceived.class);
    }

    @Bean
    public QueueProducer<PostPurchaseEventReceived> postPurchaseEventReceivedQueueProducer(
        QueueConfig postPurchaseEventReceivedQueueConfig,
        TaskPayloadTransformer<PostPurchaseEventReceived> postPurchaseEventReceivedTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            postPurchaseEventReceivedQueueConfig,
            postPurchaseEventReceivedTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public PostPurchaseEventReceivedQueueConsumer postPurchaseEventReceivedQueueConsumer(
        QueueConfig postPurchaseEventReceivedQueueConfig,
        TaskPayloadTransformer<PostPurchaseEventReceived> postPurchaseEventReceivedTaskPayloadTransformer,
        CustomerRefundChoiceService customerRefundChoiceService,
        IdempotencyChecker<PostPurchaseEventReceived> idempotencyChecker,
        MessageValidator<PostPurchaseEventReceived> messageValidator) {
        return new PostPurchaseEventReceivedQueueConsumer(
            idempotencyChecker,
            postPurchaseEventReceivedQueueConfig,
            postPurchaseEventReceivedTaskPayloadTransformer,
            customerRefundChoiceService,
            messageValidator
        );
    }
}
