package com.bestseller.payment.config.queue;

import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.dbqueue.spring.QueueProperties;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.refundStatusUpdated.RefundStatusUpdated;
import com.bestseller.payment.adapter.stream.producer.RefundStatusUpdatedProducer;
import com.bestseller.payment.core.queue.RefundStatusUpdatedProducerQueueConsumer;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RefundStatusUpdatedProducerQueueConfiguration {

    private final QueueProperties queueProperties;

    public RefundStatusUpdatedProducerQueueConfiguration(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        this.queueProperties = databaseQueueConfigurationProperties.refundStatusUpdatedProducerQueueProperties();
    }

    @Bean
    public QueueConfig refundStatusUpdatedProducerQueueConfig() {
        return QueueConfigUtils.getQueueConfig(queueProperties);
    }

    @Bean
    public TaskPayloadTransformer<RefundStatusUpdated> refundStatusUpdatedTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, RefundStatusUpdated.class);
    }

    @Bean
    public QueueProducer<RefundStatusUpdated> refundStatusUpdatedProducerQueueProducer(
        QueueConfig refundStatusUpdatedProducerQueueConfig,
        TaskPayloadTransformer<RefundStatusUpdated> refundStatusUpdatedTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            refundStatusUpdatedProducerQueueConfig,
            refundStatusUpdatedTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public RefundStatusUpdatedProducerQueueConsumer refundStatusUpdatedProducerQueueConsumer(
        QueueConfig refundStatusUpdatedProducerQueueConfig,
        TaskPayloadTransformer<RefundStatusUpdated> refundStatusUpdatedTaskPayloadTransformer,
        RefundStatusUpdatedProducer refundStatusUpdatedProducer) {
        return new RefundStatusUpdatedProducerQueueConsumer(
            refundStatusUpdatedProducerQueueConfig,
            refundStatusUpdatedTaskPayloadTransformer,
            refundStatusUpdatedProducer
        );
    }

}
