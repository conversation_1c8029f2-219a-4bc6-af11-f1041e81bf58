package com.bestseller.payment.config.queue.paymentsettlementrequestproducer;

import com.bestseller.dbqueue.core.api.QueueShardRouter;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentSettlementRequest.PaymentSettlementRequest;
import com.bestseller.payment.adapter.stream.messagegenerator.AdyenPaymentSettlementRequestGenerator;
import com.bestseller.payment.core.domain.payment.AdyenCardPayment;

public class AdyenPaymentSettlementRequestProducerQueueProducer
    extends AbstractPaymentSettlementRequestProducerQueueProducer<AdyenCardPayment> {

    public AdyenPaymentSettlementRequestProducerQueueProducer(
        QueueConfig queueConfig,
        TaskPayloadTransformer<PaymentSettlementRequest> payloadTransformer,
        QueueShardRouter<PaymentSettlementRequest, DatabaseAccessLayer> queueShardRouter,
        AdyenPaymentSettlementRequestGenerator adyenPaymentSettlementRequestGenerator) {
        super(
            queueConfig,
            payloadTransformer,
            queueShardRouter,
            AdyenCardPayment.class,
            adyenPaymentSettlementRequestGenerator
        );
    }
}
