package com.bestseller.payment.config.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRefundRequested.PaymentRefundRequested;
import com.bestseller.payment.core.queue.PaymentRefundRequestedQueueConsumer;
import com.bestseller.payment.core.service.refund.RefundService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@SuppressWarnings("MissingJavadocMethod")
@Configuration
public class PaymentRefundRequestedQueueConfiguration {

    @Bean
    public QueueConfig paymentRefundRequestedQueueConfig(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        return QueueConfigUtils
            .getQueueConfig(databaseQueueConfigurationProperties.paymentRefundRequestedQueueProperties());
    }

    @Bean
    public TaskPayloadTransformer<PaymentRefundRequested> paymentRefundRequestedTaskPayloadTransformer(
        ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, PaymentRefundRequested.class);
    }

    @Bean
    public QueueProducer<PaymentRefundRequested> paymentRefundRequestedQueueProducer(
        QueueConfig paymentRefundRequestedQueueConfig,
        TaskPayloadTransformer<PaymentRefundRequested> paymentRefundRequestedTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            paymentRefundRequestedQueueConfig,
            paymentRefundRequestedTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public PaymentRefundRequestedQueueConsumer paymentRefundRequestedQueueConsumer(
        QueueConfig paymentRefundRequestedQueueConfig,
        TaskPayloadTransformer<PaymentRefundRequested> paymentRefundRequestedTaskPayloadTransformer,
        RefundService refundService,
        IdempotencyChecker<PaymentRefundRequested> idempotencyCheck,
        MessageValidator<PaymentRefundRequested> messageValidator) {
        return new PaymentRefundRequestedQueueConsumer(
            idempotencyCheck,
            paymentRefundRequestedQueueConfig,
            paymentRefundRequestedTaskPayloadTransformer,
            refundService,
            messageValidator
        );
    }
}
