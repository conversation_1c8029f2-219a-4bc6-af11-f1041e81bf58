package com.bestseller.payment.config.queue;

import com.bestseller.dbqueue.consumer.wrapper.IdempotencyChecker;
import com.bestseller.dbqueue.consumer.wrapper.MessageValidator;
import com.bestseller.dbqueue.core.api.QueueProducer;
import com.bestseller.dbqueue.core.api.TaskPayloadTransformer;
import com.bestseller.dbqueue.core.api.impl.ShardingQueueProducer;
import com.bestseller.dbqueue.core.api.impl.SingleQueueShardRouter;
import com.bestseller.dbqueue.core.config.DatabaseAccessLayer;
import com.bestseller.dbqueue.core.config.QueueShard;
import com.bestseller.dbqueue.core.settings.QueueConfig;
import com.bestseller.dbqueue.spring.JsonTaskPayloadTransformer;
import com.bestseller.dbqueue.spring.QueueConfigUtils;
import com.bestseller.generated.interfacecontracts.kafkamessages.pojos.paymentRejected.PaymentRejected;
import com.bestseller.payment.core.queue.PaymentRejectedQueueConsumer;
import com.bestseller.payment.core.service.order.OrderService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@SuppressWarnings("MissingJavadocMethod")
@Configuration
public class PaymentRejectedQueueConfiguration {

    @Bean
    public QueueConfig paymentRejectedQueueConfig(
        DatabaseQueueConfigurationProperties databaseQueueConfigurationProperties) {
        return QueueConfigUtils.getQueueConfig(databaseQueueConfigurationProperties.paymentRejectedQueueProperties());
    }

    @Bean
    public TaskPayloadTransformer<PaymentRejected> paymentRejectedTaskPayloadTransformer(ObjectMapper objectMapper) {
        return new JsonTaskPayloadTransformer<>(objectMapper, PaymentRejected.class);
    }

    @Bean
    public QueueProducer<PaymentRejected> paymentRejectedQueueProducer(
        QueueConfig paymentRejectedQueueConfig,
        TaskPayloadTransformer<PaymentRejected> paymentRejectedTaskPayloadTransformer,
        QueueShard<DatabaseAccessLayer> queueShard) {
        return new ShardingQueueProducer<>(
            paymentRejectedQueueConfig,
            paymentRejectedTaskPayloadTransformer,
            new SingleQueueShardRouter<>(queueShard)
        );
    }

    @Bean
    public PaymentRejectedQueueConsumer paymentRejectedQueueConsumer(
        QueueConfig paymentRejectedQueueConfig,
        TaskPayloadTransformer<PaymentRejected> paymentRejectedTaskPayloadTransformer,
        OrderService orderService,
        IdempotencyChecker<PaymentRejected> idempotencyCheck,
        MessageValidator<PaymentRejected> messageValidator) {
        return new PaymentRejectedQueueConsumer(
            idempotencyCheck,
            paymentRejectedQueueConfig,
            paymentRejectedTaskPayloadTransformer,
            orderService,
            messageValidator
        );
    }
}
