<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.5</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.bestseller</groupId>
    <artifactId>payment-management-service</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>payment-management-service</name>
    <description>Payment Management Service</description>
    <properties>
        <java.version>21</java.version>
        <springdoc-openapi-starter-webmvc-ui.version>2.8.11</springdoc-openapi-starter-webmvc-ui.version>
        <spring-cloud.version>2024.0.1</spring-cloud.version>
        <bse-commons.interface-contracts.version>5.2.6</bse-commons.interface-contracts.version>
        <datadog-java-agent.version>1.52.1</datadog-java-agent.version>
        <checkstyle-maven-plugin.version>3.6.0</checkstyle-maven-plugin.version>
        <bse-commons.checkstyle.version>1.4.292</bse-commons.checkstyle.version>
        <jacoco.version>0.8.13</jacoco.version>
        <logstash-logback-encoder.version>8.1</logstash-logback-encoder.version>
        <aws.s3.version>1.12.782</aws.s3.version>
        <apache-commons-csv.version>1.14.1</apache-commons-csv.version>
        <mapstruct.version>1.6.3</mapstruct.version>
        <mapstruct.binding>0.2.0</mapstruct.binding>
        <commons-text.version>1.14.0</commons-text.version>
        <wiremock-testcontainers.version>1.0-alpha-15</wiremock-testcontainers.version>
        <aws-java-sdk.version>2.31.11</aws-java-sdk.version>
        <database-queue.version>6.0.0</database-queue.version>
        <spring-base-configuration.version>0.3.0</spring-base-configuration.version>
        <state-transition.version>0.4.0</state-transition.version>
    </properties>
    <dependencies>
        <!-- bse-commons-->
        <dependency>
            <groupId>com.bestseller</groupId>
            <artifactId>bse-interface-contracts-jakarta</artifactId>
            <version>${bse-commons.interface-contracts.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!--flyway-->
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-mysql</artifactId>
        </dependency>
        <!--mysql-->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

        <!--spring doc-->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>${springdoc-openapi-starter-webmvc-ui.version}</version>
        </dependency>

        <!--kafka-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-stream-kafka</artifactId>
        </dependency>

        <!--web flux-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>

        <!--templating-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <!--datadog-->
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>${logstash-logback-encoder.version}</version>
        </dependency>
        <dependency>
            <groupId>com.datadoghq</groupId>
            <artifactId>dd-java-agent</artifactId>
            <version>${datadog-java-agent.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.datadoghq</groupId>
            <artifactId>dd-trace-api</artifactId>
            <version>${datadog-java-agent.version}</version>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-datadog</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>

        <!--aws-->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <version>${apache-commons-csv.version}</version>
        </dependency>

        <!-- Database queue -->
        <dependency>
            <groupId>com.bestseller.dbqueue</groupId>
            <artifactId>consumer</artifactId>
            <version>${database-queue.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bestseller.dbqueue</groupId>
            <artifactId>spring</artifactId>
            <version>${database-queue.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bestseller.dbqueue</groupId>
            <artifactId>kafka-producer-queue</artifactId>
            <version>${database-queue.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bestseller.dbqueue</groupId>
            <artifactId>monitoring</artifactId>
            <version>${database-queue.version}</version>
        </dependency>

        <!--conversion-->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>${commons-text.version}</version>
        </dependency>

        <dependency>
            <groupId>com.logistics</groupId>
            <artifactId>spring-base-configuration</artifactId>
            <version>${spring-base-configuration.version}</version>
        </dependency>
        <dependency>
            <groupId>com.logistics</groupId>
            <artifactId>state-transition</artifactId>
            <version>${state-transition.version}</version>
        </dependency>

        <!--testing-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-testcontainers</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>mysql</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>kafka</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.bestseller.dbqueue</groupId>
            <artifactId>spring-test</artifactId>
            <version>${database-queue.version}</version>
            <scope>test</scope>
        </dependency>
        <!-- wiremock -->
        <dependency>
            <groupId>org.wiremock.integrations.testcontainers</groupId>
            <artifactId>wiremock-testcontainers-module</artifactId>
            <version>${wiremock-testcontainers.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.wiremock</groupId>
            <artifactId>wiremock-standalone</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${mapstruct.binding}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version> <!-- Update to latest version -->
                        </path>
                    </annotationProcessorPaths>
                    <compilerArgs>
                        <compilerArg>
                            -Amapstruct.defaultComponentModel=spring
                        </compilerArg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <!-- including the Datadog agent as a jar-->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>copy</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>com.datadoghq</groupId>
                                    <artifactId>dd-java-agent</artifactId>
                                    <type>jar</type>
                                    <outputDirectory>${project.build.directory}</outputDirectory>
                                    <destFileName>dd-java-agent.jar</destFileName>
                                </artifactItem>
                            </artifactItems>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>${checkstyle-maven-plugin.version}</version>
                <configuration>
                    <configLocation>config/checkstyle/checkstyle.xml</configLocation>
                    <suppressionsLocation>config/checkstyle/suppression.xml</suppressionsLocation>
                    <includeTestSourceDirectory>false</includeTestSourceDirectory>
                </configuration>
                <executions>
                    <execution>
                        <id>checkstyle</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <configuration>
                            <failOnViolation>true</failOnViolation>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <configuration>
                    <excludes>
                        <exclude>**/Application.class</exclude>
                        <exclude>**/config/**</exclude>
                        <exclude>**/exception/**</exclude>
                        <exclude>**/repository/**</exclude>
                        <exclude>**/utils/**</exclude>
                        <exclude>**/domain/**</exclude>
                        <exclude>**/factory/PaymentFactoryImpl.class</exclude>
                        <exclude>**/service/price/PriceServiceImp.class</exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>jacoco-check</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <rule>
                                    <element>CLASS</element>
                                    <limits>
                                        <limit>
                                            <counter>LINE</counter>
                                            <value>COVEREDRATIO</value>
                                            <minimum>.7000</minimum>
                                        </limit>
                                        <limit>
                                            <counter>BRANCH</counter>
                                            <value>COVEREDRATIO</value>
                                            <minimum>0.700000</minimum>
                                        </limit>
                                    </limits>
                                </rule>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>