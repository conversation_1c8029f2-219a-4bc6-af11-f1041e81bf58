version: '3.0'
services:
  zookeeper:
    image: 381841683508.dkr.ecr.eu-west-1.amazonaws.com/bse-zookeeper:1
    hostname: zookeeper
    ports:
      - "2181:2181"

  kafka:
    image: 381841683508.dkr.ecr.eu-west-1.amazonaws.com/bse-kafka:2.6.0
    hostname: kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      - KAFKA_ADVERTISED_HOST_NAME=localhost
      - KAFKA_ADVERTISED_PORT=9092
      - KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181
      - <PERSON><PERSON><PERSON>_CREATE_TOPICS=ValidOrderPlaced:1:1, PaymentAuthorized:1:1, PaymentRejected:1:1, PaymentStatusUpdated:1:1, PaymentSettlementRequest:1:1, GiftCardRefundResponse:1:1, OrderFinalized:1:1, PaymentSettlementSucceeded:1:1, PaymentSettlementFailed:1:1, RefundCreationRequested:1:1, PaymentRefundRequested:1:1, PaymentRefundFailed:1:1, PaymentRefundSucceeded:1:1, InStoreReturnSettlement:1:1

  mysql:
    platform: "linux/amd64"
    image: mysql:8.0
    ports:
      - "13306:3306"
    depends_on:
      - kafka
    environment:
      MYSQL_DATABASE: pmsdb
      MYSQL_ALLOW_EMPTY_PASSWORD: "yes"
      MYSQL_USER: pms
      MYSQL_PASSWORD: pms
      MYSQL_ROOT_PASSWORD: pms

  localstack:
    image: localstack/localstack
    ports:
      - "4566:4566"
    environment:
      - SERVICES=s3
      - DEFAULT_REGION=eu-west-1
      - EDGE_PORT=4566
      - DATA_DIR=/tmp/localstack/data
      - S3_BUCKET_NAME=pms-bucket
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      - EXTRA_CORS_ALLOWED_ORIGINS=http://localhost:8080
    volumes:
      - ./init-localstack.sh:/etc/localstack/init/ready.d/init-localstack.sh