module "standard_monitors" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/standard-monitors/datadog"
  version = "~> 4.4.1"

  owner                 = local.owner
  project               = local.project
  env                   = local.env
  vcs                   = local.vcs
  jira_integration_name = "ets-fulfilment-incident"

  kafka_consumer_lag_configs = [
    {
      topic_name         = "PaymentAuthorized"
      threshold_warning  = 250
      threshold_critical = 500
    },
    {
      topic_name         = "PaymentRejected"
      threshold_warning  = 250
      threshold_critical = 500
    },
    {
      topic_name         = "ValidOrderPlaced"
      threshold_warning  = 250
      threshold_critical = 500
    },
    {
      topic_name         = "OrderFinalized"
      threshold_warning  = 250
      threshold_critical = 500
    },
    {
      topic_name         = "PaymentSettlementSucceeded"
      threshold_warning  = 250
      threshold_critical = 500
    },
    {
      topic_name         = "PaymentSettlementFailed"
      threshold_warning  = 250
      threshold_critical = 500
    },
    {
      topic_name         = "RefundCreationRequested"
      threshold_warning  = 250
      threshold_critical = 500
    },
    {
      topic_name         = "PaymentRefundRequested"
      threshold_warning  = 250
      threshold_critical = 500
    },
    {
      topic_name         = "PaymentRefundFailed"
      threshold_warning  = 250
      threshold_critical = 500
    },
    {
      topic_name         = "PaymentRefundSucceeded"
      threshold_warning  = 250
      threshold_critical = 500
    },
    {
      topic_name         = "InStoreReturnSettlement"
      threshold_warning  = 250
      threshold_critical = 500
    },
    {
      topic_name         = "GiftCardRefundResponse"
      threshold_warning  = 250
      threshold_critical = 500
    },
    {
      topic_name         = "PostPurchaseEventReceived"
      threshold_warning  = 250
      threshold_critical = 500
    }
  ]

  no_kafka_message_produced_configs = [
    {
      topic_name = "PaymentStatusUpdated",
      interval   = "1h"
    },
    {
      topic_name = "PaymentSettlementRequest",
      interval   = "12h"
    },
    {
      topic_name = "PaymentRefundRequest",
      interval   = "1d"
    },
    {
      topic_name = "RefundStatusUpdated",
      interval   = "1d"
    }
  ]

  no_kafka_message_consumed_configs = [
    {
      topic_name = "PaymentAuthorized",
      interval   = "1d"
    },
    {
      topic_name = "ValidOrderPlaced",
      interval   = "1h"
    },
    {
      topic_name = "OrderFinalized",
      interval   = "6h"
    },
    {
      topic_name = "PaymentSettlementSucceeded",
      interval   = "12h"
    },
    {
      topic_name = "RefundCreationRequested",
      interval   = "1d"
    },
    {
      topic_name = "PaymentRefundSucceeded",
      interval   = "1d"
    },
    {
      topic_name = "GiftCardRefundResponse",
      interval   = "1d"
    }
  ]

  too_much_kafka_message_consumed_configs = [
    {
      topic_name = "PaymentSettlementFailed"
      interval   = "1d"
      threshold  = 10
    },
    {
      topic_name = "PaymentRejected"
      interval   = "1d"
      threshold  = 100
    },
    {
      topic_name = "PaymentRefundFailed"
      interval   = "1d"
      threshold  = 10
    }
  ]
}
