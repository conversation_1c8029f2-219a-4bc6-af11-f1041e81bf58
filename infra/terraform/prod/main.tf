// Terraform state configuration
terraform {
  backend "remote" {
    hostname     = "tfe.mng.bseint.io"
    organization = "bestseller-ecom"

    workspaces {
      name = "pms-prod"
    }
  }
}

// Configure AWS provider
provider "aws" {
  region              = local.aws_region
  allowed_account_ids = [local.aws_account_id]

  assume_role {
    session_name = "terraform"
    role_arn     = "arn:aws:iam::${local.aws_account_id}:role/terraform-admin-role"
  }
}

module "kafka_nodes" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/ec2-kafka-data/aws"
  version = "~> 1.3.0"

  env = local.env
}

module "metadata" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/metadata/bestseller"
  version = "~> 2.0.0"
}

module "ecr" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/ecr/aws"
  version = "~> 1.3.2"

  env     = local.env
  project = local.project
  owner   = local.owner
  vcs     = local.vcs
}

// Define variables
locals {
  // Setup - DO NOT CHANGE!
  aws_account_id = "************" # BSE-AWS-PROD
  aws_region     = "eu-west-1"

  // Project
  bseint_domain       = "bseint.io"
  env                 = "prod"
  project_information = module.metadata.projects["pms"]["metadata"]
  project             = local.project_information["project"]
  project_name        = local.project_information["project_name"]
  vcs                 = local.project_information["github_vcs"]
  owner               = local.project_information["owner"]

  cost_estimate    = "100"
  container_cpu    = 640  #The minimum number of cpu units the AWS ECS container agent will reserve for the container.
  container_memory = 1280 #The hard limit of memory (in MiB) usage for container.
  container_port   = 8080
  host_port        = 8080

  default_tags = {
    env         = local.env
    project     = local.project
    owner       = local.owner
    Terraformed = "true"
    Ansible     = "false"
  }

  container_definitions = [
    {
      name      = "application"
      image     = "${module.ecr.ecr_image_name}:master"
      command   = []
      essential = true

      logConfiguration = {
        logDriver = "journald"
        options = {
          labels = "service"
        }
      }
      dockerLabels = {
        service                      = local.project
        "com.datadoghq.tags.env"     = local.env,
        "com.datadoghq.tags.service" = local.project,
      }

      portMappings = [
        {
          hostPort      = local.host_port
          protocol      = "tcp"
          containerPort = local.container_port
        }
      ]

      environment = local.env_vars
      secrets     = local.secret_vars
      cpu         = local.container_cpu
      memory      = local.container_memory
      mountPoints = []
      linuxParameters = {
        initProcessEnabled = true
      }
    }
  ]

  env_vars = [
    {
      name  = "JAVA_TOOL_OPTIONS"
      value = "-XX:MaxRAMPercentage=70"
    },
    {
      name  = "SPRING_PROFILES_ACTIVE"
      value = local.env
    },
    {
      name  = "PMS_DB_USERNAME"
      value = "app_pms_user"
    },
    {
      name  = "PMS_DB_URL"
      value = "***********************.${local.env}.bseint.io:3306/pms?serverTimezone=UTC&characterEncoding=UTF-8"
    },
    {
      name  = "PMS_FLYWAY_USERNAME"
      value = "migration_pms_user"
    },
    {
      name  = "PMS_KAFKA_BROKER_LIST"
      value = join(",", module.kafka_nodes.kafka_full_names)
    },
    {
      name  = "FCS_API_URL"
      value = "https://fcs.${local.env}.${local.bseint_domain}"
    },
    {
      name  = "FCS_API_USERNAME"
      value = "admin"
    },
    {
      name  = "DD_ENABLED"
      value = true
    },
    {
      name  = "DD_SERVICE_NAME"
      value = local.project
    },
    {
      name  = "DD_TRACE_ENABLED"
      value = "true"
    },
    {
      name  = "DD_TRACE_GLOBAL_TAGS"
      value = "service:${local.project},project:${local.project},env:${local.env},owner:${local.owner},vcs:${local.vcs}"
    },
    {
      name  = "DD_AGENT_STATSD_PORT"
      value = "8125"
    },
    {
      name  = "DD_SERVICE_MAPPING"
      value = "mysql:${local.project}-mysql"
    },
    {
      name  = "AWS_REGION"
      value = local.aws_region
    },
  ]

  secret_vars = [
    {
      name      = "PMS_DB_PASSWORD"
      valueFrom = data.aws_secretsmanager_secret_version.pms_app_passwords.arn
    },
    {
      name      = "PMS_FLYWAY_PASSWORD"
      valueFrom = data.aws_secretsmanager_secret_version.pms_migration_passwords.arn
    },
    {
      name      = "FCS_API_PASSWORD"
      valueFrom = module.fcs_api_password_secret.secret_arn
    },
    {
      name      = "DD_API_KEY"
      valueFrom = data.aws_secretsmanager_secret_version.dd_api_key.arn
    }
  ]
}

data "aws_lb" "load_balancer" {
  name = "alb-logistics-${local.env}"
}

// ECS
module "payment-management-service-ecs-service" {
  source  = "tfe.mng.bseint.io/bestseller-ecom/ecs/aws"
  version = "~> 1.2.0"

  env          = local.env
  project      = local.project
  project_name = local.project_name
  owner        = local.owner
  vcs          = local.vcs

  cluster_name                                = "logistics-${local.env}"
  alb_arn                                     = data.aws_lb.load_balancer.arn
  alb_listener_port                           = "443"
  alb_listener_rule_path                      = "/${local.project}/*"
  container_image                             = "${local.aws_account_id}.dkr.ecr.eu-west-1.amazonaws.com/${local.project}:master"
  container_memory_allocation                 = local.container_memory
  security_group_id_list                      = []
  container_health_check_path                 = "/actuator/health"
  container_health_check_unhealthy_threshold  = 6
  container_health_check_grace_period_seconds = 60
  container_definitions                       = local.container_definitions
  container_environment_vars                  = local.env_vars

  iam_task_execution_custom_policy_json = data.aws_iam_policy_document.task_execution_policy.json
}

data "aws_iam_policy_document" "task_execution_policy" {
  statement {
    sid    = "AllowSecretsManagerAccess"
    effect = "Allow"
    actions = [
      "secretsmanager:GetSecretValue"
    ]
    resources = [
      data.aws_secretsmanager_secret_version.pms_app_passwords.arn,
      data.aws_secretsmanager_secret_version.pms_migration_passwords.arn
    ]
  }
}

data "aws_secretsmanager_secret" "dd_api_key_metadata" {
  name = "DATADOG_API_KEY"
}
data "aws_secretsmanager_secret_version" "dd_api_key" {
  secret_id = data.aws_secretsmanager_secret.dd_api_key_metadata.name
}
data "aws_secretsmanager_secret_version" "pms_migration_passwords" {
  secret_id = "oms2-db-MIGRATION_PMS_PASSWORD"
}

data "aws_secretsmanager_secret_version" "pms_app_passwords" {
  secret_id = "oms2-db-APP_PMS_PASSWORD"
}
